import React, { useState, useContext } from "react";
import { useNavigate, Link } from "react-router-dom";
import { Box, TextField, Typography, Paper, MenuItem } from "@mui/material";
import { AuthContext } from "../App";

const roleOptions = [
  "general_manager",
  "account_manager",
  "videographer",
  "video_editor",
  "graphic_designer",
  "financial_manager",
];
const tierOptions = [1, 2, 3];

function Signup() {
  const { setIsAuthenticated } = useContext(AuthContext);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [role, setRole] = useState("");
  const [tier, setTier] = useState("");
  const [phone, setPhone] = useState("");
  const [profilePicture, setProfilePicture] = useState(null);
  const [previewUrl, setPreviewUrl] = useState("");
  const navigate = useNavigate();

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setProfilePicture(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSignup = async (e) => {
    e.preventDefault();

    if (!name || !email || !password || !role || !tier) {
      alert("Please fill in all required fields.");
      return;
    }

    if (password !== confirmPassword) {
      alert("Passwords don't match!");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("email", email);
      formData.append("password", password);
      formData.append("role", role);
      formData.append("tier", Number(tier));
      formData.append("phone", phone);
      if (profilePicture) {
        formData.append("profilePicture", profilePicture);
      }

      // Use the teams signup endpoint
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/auth/signup",
        {
          method: "POST",
          body: formData,
        }
      );

      const data = await response.json();

      if (response.ok) {
        alert(data.message || "Signup successful! Please log in.");
        navigate("/admin/login");
      } else {
        alert(data.message || "Signup failed, please try again.");
      }
    } catch (error) {
      console.error("Signup error:", error);
      alert("An error occurred, please try again later.");
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        color: "white",
        backgroundColor: "black",
      }}
    >
      <img
        src="/assets/young-logo-white.webp"
        alt="logo"
        style={{
          width: "200px",
          zIndex: 1,
          position: "absolute",
          top: "5%",
          left: "5%",
        }}
      />

      <Paper
        elevation={24}
        sx={{
          background: "rgba(255, 255, 255, 0.1)",
          backdropFilter: "blur(10px)",
          padding: "40px",
          borderRadius: "15px",
          width: "400px",
          maxWidth: "90%",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Formula Bold",
            fontSize: "2.5rem",
            color: "white",
            textAlign: "center",
          }}
          variant="h4"
          gutterBottom
        >
          Create Account
        </Typography>
        <form
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "20px",
          }}
          onSubmit={handleSignup}
        >
          <TextField
            label="Name"
            type="text"
            variant="outlined"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <TextField
            label="Email"
            type="email"
            variant="outlined"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <TextField
            label="Phone (Optional)"
            type="tel"
            variant="outlined"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <TextField
            label="Password"
            type="password"
            variant="outlined"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <TextField
            label="Confirm Password"
            type="password"
            variant="outlined"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <TextField
            select
            label="Role"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{ style: { color: "white" } }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          >
            {roleOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {option
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
              </MenuItem>
            ))}
          </TextField>
          <TextField
            select
            label="Tier"
            value={tier}
            onChange={(e) => setTier(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{ style: { color: "white" } }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          >
            {tierOptions.map((option) => (
              <MenuItem key={option} value={option}>
                {`Tier ${option}`}
              </MenuItem>
            ))}
          </TextField>

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: 2,
            }}
          >
            <label
              htmlFor="profile-picture"
              style={{
                cursor: "pointer",
                padding: "10px 15px",
                backgroundColor: "rgba(255, 255, 255, 0.2)",
                borderRadius: "5px",
                width: "100%",
                textAlign: "center",
              }}
            >
              Upload Profile Picture
            </label>
            <input
              id="profile-picture"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              style={{ display: "none" }}
            />

            {previewUrl && (
              <Box
                sx={{
                  width: "100px",
                  height: "100px",
                  borderRadius: "50%",
                  overflow: "hidden",
                  border: "2px solid white",
                }}
              >
                <img
                  src={previewUrl}
                  alt="Profile Preview"
                  style={{ width: "100%", height: "100%", objectFit: "cover" }}
                />
              </Box>
            )}
          </Box>

          <button
            style={{
              padding: "12px",
              backgroundColor: "#db4a41",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontWeight: "bold",
              fontSize: "16px",
            }}
            type="submit"
          >
            Sign Up
          </button>
          <Typography sx={{ color: "white", textAlign: "center", mt: 2 }}>
            Already have an account?{" "}
            <Link
              to="/admin/login"
              style={{ color: "#db4a41", textDecoration: "none" }}
            >
              Log In
            </Link>
          </Typography>
        </form>
      </Paper>
    </Box>
  );
}

export default Signup;
