import React from "react";
import { Box } from "@mui/material";
import BlurText from "./animations/BlurText";
import EnhancedImageScrollSection from "./newAbout/EnhancedImageScrollSection";
import ProjectCard from "./newAbout/halfSection";
import CameraMilestoneTimeline from "./newAbout/milestones";
const handleAnimationComplete = () => {
  console.log("Animation completed!");
};
export default function NewAbout() {
  return (
    <Box sx={{ backgroundColor: "#000" }}>
      {/* INTRO TEXT */}
      <Box sx={{ px: { xs: 3, md: 10 }, pt: 16, maxWidth: 7200 }}>
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="We're A Team of people driven"
            delay={100}
            animateBy="words"
            direction="top"
            onAnimationComplete={handleAnimationComplete}
            colorMap={["#fff", "#fff"]}
          />
        </Box>

        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            marginBottom: "-0.6em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="by a shared vision. Doing the best work "
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#fff", "#fff", "#fff"]}
          />
        </Box>

        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            lineHeight: 1.6,
          }}
        >
          <BlurText
            text="of our lives and enjoying every step."
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#fff", "#fff", "#fff"]}
          />
        </Box>
      </Box>
      <EnhancedImageScrollSection />
      <ProjectCard /> {/* ✨ هنا */}
      {/* <TransitionWrapper></TransitionWrapper> */}
      <CameraMilestoneTimeline />
    </Box>
  );
}
