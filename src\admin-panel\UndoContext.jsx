import React, { createContext, useContext, useState, useRef } from "react";
import { Snackbar, Button } from "@mui/material";

const UndoContext = createContext();

export function useUndo() {
  return useContext(UndoContext);
}

export function UndoProvider({ children }) {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const actionRef = useRef(null);
  const timerRef = useRef(null);

  const triggerUndo = (undoMessage, action) => {
    setMessage(undoMessage);
    setOpen(true);
    actionRef.current = action;

    // Start 5s timer
    timerRef.current = setTimeout(() => {
      if (actionRef.current) {
        actionRef.current(); // Execute the action if not undone
        actionRef.current = null;
      }
      setOpen(false);
    }, 5000);
  };

  const handleUndo = () => {
    clearTimeout(timerRef.current);
    actionRef.current = null;
    setOpen(false);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <UndoContext.Provider value={{ triggerUndo }}>
      {children}
      <Snackbar
        open={open}
        message={message}
        action={
          <Button color="secondary" size="small" onClick={handleUndo}>
            UNDO
          </Button>
        }
        autoHideDuration={5000}
        onClose={handleClose}
      />
    </UndoContext.Provider>
  );
}
