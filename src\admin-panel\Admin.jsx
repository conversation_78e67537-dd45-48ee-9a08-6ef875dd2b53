import React, { useState } from "react";
import { Routes, Route } from "react-router-dom";
import Dashboard from "./Dashboard";
import Users from "./Users";
import Settings from "./Settings";
import SidePanel from "./SidePanel";
import Events from "./CMS/Work";
import Testimonials from "./CMS/Testimonials";
import ContactSubmissions from "./ContactSubmissions";
import JobApplications from "./CMS/JobApplications";
import Employees from "./System/Employees";
import Profile from "./Profile";
import { UndoProvider } from "./UndoContext";
import TeamAdmin from "./CMS/CMSpeople";
import HeroVideos from "./CMS/Herovideos";
import Work from "./CMS/Work";
import ClientsLogos from "./CMS/Clientslogos";
import JobManagement from "./CMS/JobManagement";
import GalleryManager from "./CMS/Featuredwork";
import Projects from "./System/Project";
import ClientManagement from "./System/clientManagement";
import NewDashboard from "./newDashboard";
import Shoots from "./System/shoots";
import EmployeeHistoryProfile from "./System/history";
import ReelGallery from "./CMS/ReelGallery";
import TaskManagement from "./System/TaskManagement";
import Meetings from "./System/Meetings";
import ClientAccount from "./Finance/ClientAccount";
import SubscriptionCycle from "./Finance/SubscriptionCycle";
import Payments from "./Finance/payments";
import AgencyFinance from "./Finance/agencyFinance";
import Quotations from "./Finance/Quotations";
import Salaries from "./Finance/Salaries";
import Payroll from "./Finance/Payroll";
import AgencyExpenses from "./Finance/agencyExpenses";
import CycleFinance from "./Finance/CycleFinance";
import Expenses from "./Finance/expenses";
import Adjustments from "./Finance/adjustments";
import Records from "./Finance/records";
import DetailedRecord from "./Finance/detailedRecord";
import ClientDashboardAnalysis from "./Finance/clientDashboardAnalysis";
import Database from "./System/Database";
import Archives from "./Finance/archives";
import ArchivesClientAccounts from "./Finance/archivesClientAccounts";
import ArchivesSubscriptionCycles from "./Finance/archivesSubscriptionCycles";
import OneTimeProject from "./Finance/OneTimeProject";
import Forecasting from "./Finance/forecasting";
import ClientPortalManagement from "./System/ClientPortalManagement";
import ClientEventManagement from "./System/clientEvent";
import PostingScheduleManagement from "./System/postingSchedule";
import YoungPeopleAdmin from "./CMS/youngPeople";

const Admin = () => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <UndoProvider>
      <div style={{ display: "flex", width: "100vw", height: "100vh" }}>
        <SidePanel isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
        <div
          style={{
            overflowY: "auto",
            flexGrow: 1,
            scrollBehavior: "smooth",
            backgroundColor: "black",
            width: "100vw",
          }}
        >
          <Routes>
            <Route index element={<NewDashboard />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="users" element={<Users />} />
            <Route path="settings" element={<Settings />} />
            <Route path="events" element={<Events />} />
            <Route path="CMS/clients-logos" element={<ClientsLogos />} />
            <Route path="CMS/testimonials" element={<Testimonials />} />
            <Route path="CMS/people" element={<TeamAdmin />} />
            <Route path="CMS/heroVideos" element={<HeroVideos />} />
            <Route path="CMS/works" element={<Work />} />
            <Route path="CMS/job-management" element={<JobManagement />} />
            <Route path="CMS/Feature-work" element={<GalleryManager />} />
            <Route path="system/projects" element={<Projects />} />
            <Route path="newdashboard" element={<NewDashboard />} />
            <Route path="CMS/reels-gallery" element={<ReelGallery />} />
            <Route path="shoots" element={<Shoots />} />
            <Route path="system/profile" element={<EmployeeHistoryProfile />} />
            <Route
              path="contact-submissions"
              element={<ContactSubmissions />}
            />
            <Route path="CMS/job-applications" element={<JobApplications />} />
            <Route path="system/employees" element={<Employees />} />
            <Route
              path="system/clients-management"
              element={<ClientManagement />}
            />
            <Route path="profile" element={<Profile />} />
            <Route path="meetings" element={<Meetings />} />
            <Route path="tasks" element={<TaskManagement />} />
            <Route path="system/database" element={<Database />} />

            {/* Financial */}
            <Route
              path="financial/clients-account"
              element={<ClientAccount />}
            />
            <Route
              path="financial/subscription-cycle"
              element={<SubscriptionCycle />}
            />
            <Route
              path="financial/subscription-cycle/:id"
              element={<CycleFinance />}
            />
            <Route path="financial/payments" element={<Payments />} />
            <Route
              path="financial/agency-finance"
              element={<AgencyFinance />}
            />
            <Route path="financial/quotations" element={<Quotations />} />
            <Route path="financial/salaries" element={<Salaries />} />
            <Route path="financial/payroll" element={<Payroll />} />
            <Route
              path="financial/agency-expenses"
              element={<AgencyExpenses />}
            />
            <Route path="financial/expenses" element={<Expenses />} />
            <Route path="financial/adjustments" element={<Adjustments />} />
            <Route path="financial/records" element={<Records />} />
            <Route
              path="financial/records/:period"
              element={<DetailedRecord />}
            />
            <Route
              path="financial/client-dashboard/:clientId"
              element={<ClientDashboardAnalysis />}
            />
            <Route path="financial/archives" element={<Archives />} />
            <Route
              path="financial/archives/client-accounts"
              element={<ArchivesClientAccounts />}
            />
            <Route
              path="financial/archives/cycles"
              element={<ArchivesSubscriptionCycles />}
            />
            <Route
              path="financial/archives/subscription-cycles/:client_id"
              element={<ArchivesSubscriptionCycles />}
            />
            <Route
              path="financial/one-time-projects"
              element={<OneTimeProject />}
            />
            <Route path="financial/forecasting" element={<Forecasting />} />
            <Route
              path="system/client-portal-management"
              element={<ClientPortalManagement />}
            />
            <Route
              path="system/client-events"
              element={<ClientEventManagement />}
            />
            <Route
              path="system/posting-schedule"
              element={<PostingScheduleManagement />}
            />
            <Route path="/CMS/young-people" element={<YoungPeopleAdmin />} />
          </Routes>
        </div>
      </div>
    </UndoProvider>
  );
};

export default Admin;
