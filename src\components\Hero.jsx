import React from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { Typewriter } from "react-simple-typewriter";

const Hero = ({
  text,
  subtext,
  highlightText,
  bgColor,
  textColor,
  videoSrc,
  fallbackImage,
}) => {
  return (
    <Box
      className="hero-container"
      sx={{
        position: "relative",
        overflow: "hidden",

        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        background: bgColor,
      }}
    >
      {/* Video Background */}
      {videoSrc && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex: 0,
            "&::after": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(0, 0, 0, 0.3)", // Optional overlay
            },
          }}
        >
          <video
            autoPlay
            muted
            loop
            playsInline
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            poster={fallbackImage}
          >
            <source src={videoSrc} type="video/mp4" />
            {/* Add additional source formats for better browser support */}
            <source src={videoSrc.replace(".mp4", ".webm")} type="video/webm" />
            Your browser does not support the video tag.
          </video>
        </Box>
      )}

      {/* Content */}
      <Box
        sx={{
          position: "relative",
          zIndex: 1,
          textAlign: "center",
          padding: "2rem",
          maxWidth: "90%",
        }}
      >
        <Typography
          variant="h1"
          sx={{
            fontFamily: "Formula Bold",
            color: textColor,
            marginBottom: "-1em",
          }}
          className="hero-text"
        >
          {text}
          <span style={{ color: "#DB4A41" }}>
            <Typewriter
              words={[highlightText]}
              loop={1}
              cursor
              cursorStyle="_"
              typeSpeed={80}
              deleteSpeed={30}
              delaySpeed={1000}
            />
          </span>
        </Typography>

        {subtext && (
          <Typography
            variant="body1"
            sx={{
              fontFamily: "Formula Bold",
              color: textColor,
              fontSize: "1.2rem",
            }}
          >
            {subtext}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default Hero;
