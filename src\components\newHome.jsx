import React, { Suspense, lazy, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import HeroParallax from "./newHome/newhero";
import Clients from "./Clients";
// Performance validation will be handled inline
// Lazy-load heavy video components
const GalleryGrid = lazy(() => import("./newHome/featureWorks"));
const ReelGallery = lazy(() => import("./newHome/reelGallery"));

function NewHome() {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
  });

  // Initialize performance validation
  useEffect(() => {
    console.log("🎯 Performance validation active for homepage");
    console.log(
      "📊 Monitoring: LCP <1.5s, Video Start <2s, ≤1 video, ≤15 requests, ≤2MB",
    );

    // Simple performance validation
    const validatePerformance = () => {
      const navigation = performance.getEntriesByType("navigation")[0];
      const resources = performance.getEntriesByType("resource");

      const videoRequests = resources.filter(
        (entry) =>
          entry.name.includes(".mp4") ||
          entry.name.includes(".webm") ||
          entry.initiatorType === "video",
      );

      const totalBytes = resources.reduce(
        (sum, entry) =>
          sum + (entry.transferSize || entry.encodedBodySize || 0),
        0,
      );

      console.log("📊 Performance Metrics:", {
        totalRequests: resources.length,
        totalBytes: Math.round(totalBytes / 1024) + "KB",
        videoRequests: videoRequests.length,
        loadTime: navigation
          ? Math.round(navigation.loadEventEnd) + "ms"
          : "N/A",
      });

      // Check budgets
      if (resources.length > 15) {
        console.warn(
          "⚠️ Request budget exceeded:",
          resources.length,
          "requests",
        );
      }
      if (totalBytes > 2 * 1024 * 1024) {
        console.warn(
          "⚠️ Bytes budget exceeded:",
          Math.round(totalBytes / 1024 / 1024) + "MB",
        );
      }
      if (videoRequests.length > 1) {
        console.warn(
          "⚠️ Video count exceeded:",
          videoRequests.length,
          "videos",
        );
      }
    };

    // Run validation after 3 seconds
    setTimeout(validatePerformance, 3000);
  }, []);

  return (
    <div style={{ backgroundColor: "black" }}>
      <div
        ref={heroRef}
        style={{ height: "150vh", position: "relative", zIndex: 10000 }}
      >
        <HeroParallax />
      </div>

      {/* Spacer */}
      <div style={{ height: "65vh", backgroundColor: "black" }}></div>
      {/* CLIENT */}
      <Clients />
      {/* FEATURE WORKS */}
      <Suspense
        fallback={
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginTop: "0.5vh",
            }}
          >
            Loading Featured Works...
          </div>
        }
      >
        <GalleryGrid />
      </Suspense>

      {/* REEL GALLERY */}
      <Suspense
        fallback={
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginTop: "4vh",
            }}
          >
            Loading Reels...
          </div>
        }
      >
        <ReelGallery />
      </Suspense>
    </div>
  );
}

export default NewHome;
