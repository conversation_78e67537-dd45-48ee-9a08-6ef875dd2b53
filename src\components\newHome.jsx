import React, { Suspense, lazy } from "react";
import { useInView } from "react-intersection-observer";
import HeroParallax from "./newHome/newhero";
import Clients from "./Clients";
// Lazy-load heavy video components
const GalleryGrid = lazy(() => import("./newHome/featureWorks"));
const ReelGallery = lazy(() => import("./newHome/reelGallery"));

function NewHome() {
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
  });

  return (
    <div style={{ backgroundColor: "black" }}>
      <div
        ref={heroRef}
        style={{ height: "150vh", position: "relative", zIndex: 10000 }}
      >
        <HeroParallax />
      </div>

      {/* Spacer */}
      <div style={{ height: "65vh", backgroundColor: "black" }}></div>
      {/* CLIENT */}
      <Clients />
      {/* FEATURE WORKS */}
      <Suspense
        fallback={
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginTop: "0.5vh",
            }}
          >
            Loading Featured Works...
          </div>
        }
      >
        <GalleryGrid />
      </Suspense>

      {/* REEL GALLERY */}
      <Suspense
        fallback={
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginTop: "4vh",
            }}
          >
            Loading Reels...
          </div>
        }
      >
        <ReelGallery />
      </Suspense>
    </div>
  );
}

export default NewHome;
