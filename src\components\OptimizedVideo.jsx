import React, { useState, useRef, useEffect, forwardRef } from "react";
import { useInView } from "react-intersection-observer";

const OptimizedVideo = forwardRef(
  (
    {
      src,
      poster,
      autoPlay = false,
      loop = false,
      muted = true,
      playsInline = true,
      preload = "metadata",
      className = "",
      style = {},
      onCanPlay,
      onLoadedData,
      onMouseEnter,
      onMouseLeave,
      onEnded,
      controls = false,
      lazy = true,
      rootMargin = "50px",
      threshold = 0.1,
      fallbackImage,
      ...props
    },
    ref
  ) => {
    const [isLoaded, setIsLoaded] = useState(!lazy);
    const [isPlaying, setIsPlaying] = useState(false);
    const [hasError, setHasError] = useState(false);
    const videoRef = useRef(null);

    // Use intersection observer for lazy loading
    const [inViewRef, inView] = useInView({
      threshold,
      rootMargin,
      triggerOnce: true,
      skip: !lazy,
    });

    // Combine refs
    const combinedRef = (node) => {
      videoRef.current = node;
      inViewRef(node);
      if (ref) {
        if (typeof ref === "function") {
          ref(node);
        } else {
          ref.current = node;
        }
      }
    };

    // Load video when in view
    useEffect(() => {
      if (lazy && inView && !isLoaded) {
        setIsLoaded(true);
      }
    }, [inView, isLoaded, lazy]);

    // Handle autoplay when video can play
    const handleCanPlay = (e) => {
      if (autoPlay && !isPlaying) {
        e.target.play().catch((error) => {
          console.warn("Autoplay failed:", error);
        });
        setIsPlaying(true);
      }
      onCanPlay?.(e);
    };

    // Handle loaded data
    const handleLoadedData = (e) => {
      onLoadedData?.(e);
    };

    // Handle mouse enter
    const handleMouseEnter = (e) => {
      if (!autoPlay && !isPlaying) {
        e.target.play().catch((error) => {
          console.warn("Play on hover failed:", error);
        });
        setIsPlaying(true);
      }
      onMouseEnter?.(e);
    };

    // Handle mouse leave
    const handleMouseLeave = (e) => {
      if (!autoPlay && isPlaying) {
        e.target.pause();
        setIsPlaying(false);
      }
      onMouseLeave?.(e);
    };

    // Handle video end
    const handleEnded = (e) => {
      setIsPlaying(false);
      onEnded?.(e);
    };

    // Handle video error
    const handleError = (e) => {
      console.error("Video loading error:", e);
      setHasError(true);
    };

    // Transform video URL for CDN optimization
    const replacements = [
      "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young",
      "https://youngproductionss.com",
    ];

    let optimizedSrc = src;
    replacements.forEach((base) => {
      optimizedSrc = optimizedSrc?.replace(
        base,
        "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
      );
    });

    // Always render video element for React reconciliation
    // Only delay src assignment when lazy loading
    const finalSrc = lazy && !isLoaded ? undefined : optimizedSrc;

    return (
      <video
        ref={combinedRef}
        src={finalSrc}
        poster={poster}
        autoPlay={autoPlay}
        loop={loop}
        muted={muted}
        playsInline={playsInline}
        preload={lazy && !isLoaded ? "none" : preload}
        controls={controls}
        className={className}
        style={style}
        onCanPlay={handleCanPlay}
        onLoadedData={handleLoadedData}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onEnded={handleEnded}
        onError={handleError}
        {...props}
      >
        {finalSrc && <source src={finalSrc} type="video/mp4" />}
        Your browser does not support the video tag.
      </video>
    );
  }
);

OptimizedVideo.displayName = "OptimizedVideo";

export default OptimizedVideo;
