import React from "react";
import { useNavigate } from "react-router-dom";
import { Box, Typography, Card, CardContent, Grid } from "@mui/material";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import LoopIcon from "@mui/icons-material/Loop";
import PaymentIcon from "@mui/icons-material/Payment";
import { motion } from "framer-motion";

function Archives() {
  const navigate = useNavigate();

  const cards = [
    {
      title: "Client Accounts",
      icon: <AccountBalanceIcon sx={{ fontSize: 60, color: "#db4a41" }} />,
      description: "View archived and deleted client accounts",
      path: "/admin/financial/archives/client-accounts",
    },
    {
      title: "Cycles",
      icon: <LoopIcon sx={{ fontSize: 60, color: "#db4a41" }} />,
      description: "Manage subscription cycles",
      //   path: "/admin/financial/archives/cycles", // Placeholder
    },
    {
      title: "Payments",
      icon: <PaymentIcon sx={{ fontSize: 60, color: "#db4a41" }} />,
      description: "Review payment records",
      //   path: "/admin/financial/archives/payments", // Placeholder
    },
  ];

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Archives
          </Typography>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          <Grid container spacing={3}>
            {cards.map((card, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      cursor: "pointer",
                      "&:hover": {
                        background: "rgba(255, 255, 255, 0.08)",
                        borderColor: "rgba(219, 74, 65, 0.3)",
                        transform: "translateY(-5px)",
                      },
                      transition: "all 0.3s ease",
                    }}
                    onClick={() => navigate(card.path)}
                  >
                    <CardContent
                      sx={{
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        textAlign: "center",
                        p: 3,
                      }}
                    >
                      <Box sx={{ mb: 2 }}>{card.icon}</Box>
                      <Typography
                        variant="h5"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "white",
                          mb: 1,
                        }}
                      >
                        {card.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.7)",
                          fontFamily: "Anton",
                        }}
                      >
                        {card.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Box>
  );
}

export default Archives;
