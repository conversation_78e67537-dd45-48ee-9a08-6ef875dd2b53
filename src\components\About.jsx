import React from "react";
import { Box, Typography, Grid } from "@mui/material";
import Hero from "./Hero";
import People from "./People";
import CountUp from "./animations/countUp";
import { color } from "framer-motion";
import TeamGrid from "./newHome/newPeople";

const teamPhoto = process.env.PUBLIC_URL + "/assets/Team-Photo-1.webp";
const TeamVideo = process.env.PUBLIC_URL + "/assets/test-about.mp4";
const About = () => {
  return (
    <>
      <Hero
        text="We’re a team of people driven by a shared vision—doing the best work of our lives and enjoying every step."
        highlightText="and always enjoy the ride."
        bgColor="white"
        textColor="black"
        videoSrc={TeamVideo}
        fallbackImage={teamPhoto} // Optional
      />

      {/* <Box className="team-photo">
        <img src={teamPhoto} alt="team photo #1" style={{ width: "100%" }} />
      </Box> */}

      <Box
        className="services"
        sx={{
          backgroundColor: "black",
          color: "white",
          textAlign: "left",
          padding: "30px 20px",
        }}
      >
        <Typography
          variant="h4"
          sx={{ fontFamily: "Formula Bold", color: "#777777" }}
        >
          Our Services
        </Typography>

        <Box className="services-list" sx={{ marginTop: "20px" }}>
          <Box className="service-item">
            <Box className="service-item-header" sx={{ flex: 2 }}>
              <Typography
                variant="h2"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "white",
                }}
              >
                Production
              </Typography>
            </Box>

            <Box className="service-item-body" sx={{ flex: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#white7",
                  fontFamily: "Anton",
                  fontSize: "clamp(1.2rem, 1.6vw, 1.6rem) !important",
                  textAlign: "justify",
                }}
              >
                Lights, camera, action! Our production team thrives on bringing
                ideas to life in the most captivating and visually stunning way
                possible. Whether it's crafting compelling videos, shooting
                breathtaking{" "}
                <span style={{ color: "#db4a41" }}>photographs</span>, or
                producing immersive multimedia experiences, we've got the
                expertise and the enthusiasm to make your vision a reality. From
                concept development to post-production, we handle every step
                with precision and panache, ensuring that your project shines
                bright like a Hollywood star.
              </Typography>

              <Box sx={{ marginTop: "20px" }}>
                <Grid container spacing={1}>
                  {/* List items */}
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Advertising
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Documentaries
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Post Production
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Photography
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Short Films
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Campaigns
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Box>

          <Box className="service-item">
            <Box className="service-item-header" sx={{ flex: 2 }}>
              <Typography
                variant="h2"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "white",
                }}
              >
                Marketing
              </Typography>
            </Box>

            <Box className="service-item-body" sx={{ flex: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#white7",
                  fontFamily: "Anton",
                  fontSize: "clamp(1.2rem, 1.6vw, 1.6rem) !important",
                  textAlign: "justify",
                }}
              >
                In a world buzzing with noise, how do you make your voice heard?
                That's where our marketing wizards come into play. Armed with
                ingenious strategies and{" "}
                <span style={{ color: "#db4a41" }}>
                  out-of-the-box thinking
                </span>
                , we'll help you cut through the clutter and connect with your
                audience in meaningful ways. From crafting engaging content to
                devising strategic campaigns, we're here to elevate your brand
                and amplify your message. With our tailored approach and
                data-driven insights, get ready to stand out from the crowd and
                leave a lasting impression.
              </Typography>

              <Box sx={{ marginTop: "20px" }}>
                <Grid container spacing={1}>
                  {/* List items */}
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Media Buying
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      SEO & Content Strategy
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Platform Campaigns
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Email Marketing
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Box>

          <Box className="service-item">
            <Box className="service-item-header" sx={{ flex: 2 }}>
              <Typography
                variant="h2"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "white",
                }}
              >
                Digital Solutions
              </Typography>
            </Box>

            <Box className="service-item-body" sx={{ flex: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#white7",
                  fontFamily: "Anton",
                  fontSize: "clamp(1.2rem, 1.6vw, 1.6rem) !important",
                  textAlign: "justify",
                }}
              >
                In today's digital age, your online presence is more crucial
                than ever. Luckily, we're here to make sure you shine bright in
                the digital realm. Whether you're in need of a sleek and
                responsive website,
                <span style={{ color: "#db4a41" }}>
                  {" "}
                  a captivating brand identity{" "}
                </span>
                , or a robust digital strategy, our team of tech-savvy experts
                has got you covered. We specialize in creating bespoke digital
                solutions that not only look stunning but also drive results.
                From{" "}
                <span style={{ color: "#db4a41" }}>
                  {" "}
                  pixel-perfect designs
                </span>{" "}
                to seamless user experiences, we'll help you leave a mark in the
                digital landscape and take your online presence to new heights.
              </Typography>

              <Box sx={{ marginTop: "20px" }}>
                <Grid container spacing={1}>
                  {/* List items */}
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Web Design
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Full-Stack Development
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      UI/ UX Design
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Motion & Animation Design
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      eCommerce
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Hosting & Maintainance
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Box>

          <Box className="service-item">
            <Box className="service-item-header" sx={{ flex: 2 }}>
              <Typography
                variant="h2"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "white",
                }}
              >
                Branding
              </Typography>
            </Box>

            <Box className="service-item-body" sx={{ flex: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#white7",
                  fontFamily: "Anton",
                  fontSize: "clamp(1.2rem, 1.6vw, 1.6rem) !important",
                  textAlign: "justify",
                }}
              >
                Welcome to the essence of your identity. At Young Productions,
                we craft brands that resonate, captivate, and endure. From logos
                to brand strategy, we distill your story into a visual and
                verbal language that sets you apart. Let's create a brand that
                leaves a lasting impression. Ready to make your mark?{" "}
                <span style={{ color: "#db4a41" }}>Let's talk.</span>
              </Typography>

              <Box sx={{ marginTop: "20px" }}>
                <Grid container spacing={1}>
                  {/* List items */}
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Brand Strategy
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Brand Identity
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Brand Postioning
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Logo Design
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Art Direction
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#777777", fontFamily: "Anton" }}
                    >
                      Marketing Assets
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          backgroundColor: "black",
          color: "white",
          textAlign: "left",
          padding: "30px 20px",
        }}
      >
        <Typography
          variant="h4"
          sx={{ fontFamily: "Formula Bold", color: "#777777" }}
        >
          By The Numbers..
        </Typography>

        <Box className="numbers-list" sx={{ marginTop: "20px" }}>
          <Box className="numbers-item">
            <Typography variant="h3">Founded</Typography>
            <Typography
              variant="h4"
              sx={{
                fontFamily: "Formula Bold",
                fontSize: "6rem",
                color: "#db4a41 ",
              }}
            >
              2019
            </Typography>
          </Box>

          <Box className="numbers-item">
            <Typography variant="h3">Team Members</Typography>
            <Typography
              variant="h4"
              sx={{ fontFamily: "Formula Bold", fontSize: "6rem" }}
            >
              <CountUp
                from={0}
                to={20}
                separator=","
                direction="up"
                duration={1}
                className="count-up-text"
              />
              +
            </Typography>
          </Box>

          <Box className="numbers-item">
            <Typography variant="h3">Projects Completed</Typography>
            <Typography
              variant="h4"
              sx={{ fontFamily: "Formula Bold", fontSize: "6rem" }}
            >
              <CountUp
                from={0}
                to={100}
                separator=","
                direction="up"
                duration={1}
                className="count-up-text"
              />
              +
            </Typography>
          </Box>
        </Box>
      </Box>

      <TeamGrid />
    </>
  );
};

export default About;
