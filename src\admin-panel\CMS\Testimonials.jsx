import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Grid,
  Ty<PERSON>graphy,
  Box,
  TextField,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Fade,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { motion, AnimatePresence } from "framer-motion";

function Testimonials() {
  const [testimonials, setTestimonials] = useState([]);
  const [clientLogos, setClientLogos] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingTestimonial, setEditingTestimonial] = useState(null);
  const [newTestimonial, setNewTestimonial] = useState({
    name: "",
    role: "",
    comment: "",
    clientLogoId: "",
  });
  const [selectedLogoUrl, setSelectedLogoUrl] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  useEffect(() => {
    fetchTestimonials();
    fetchClientLogos();
  }, []);

  const fetchTestimonials = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/testimonials",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setTestimonials(data);
    } catch (error) {
      console.error("Error fetching testimonials:", error);
      showSnackbar("Failed to fetch testimonials", "error");
    }
  };

  const fetchClientLogos = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/clientslogos",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setClientLogos(data);
    } catch (error) {
      console.error("Error fetching client logos:", error);
      showSnackbar("Failed to fetch client logos", "error");
    }
  };

  const handleDelete = async (id) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/testimonials/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setTestimonials(
          testimonials.filter((testimonial) => testimonial._id !== id)
        );
        showSnackbar("Testimonial deleted successfully", "success");
      } else {
        throw new Error("Failed to delete testimonial");
      }
    } catch (error) {
      console.error("Error deleting testimonial:", error);
      showSnackbar("Failed to delete testimonial", "error");
    }
  };

  const handleAdd = () => {
    setEditingTestimonial(null);
    setNewTestimonial({ name: "", role: "", comment: "", clientLogoId: "" });
    setSelectedLogoUrl("");
    setOpenModal(true);
  };

  const handleEdit = (testimonial) => {
    setEditingTestimonial(testimonial);
    setNewTestimonial({
      name: testimonial.name,
      role: testimonial.role,
      comment: testimonial.comment,
      clientLogoId: testimonial.clientLogoId || "",
    });
    setSelectedLogoUrl(testimonial.logourl || "");
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingTestimonial(null);
    setNewTestimonial({ name: "", role: "", comment: "", clientLogoId: "" });
    setSelectedLogoUrl("");
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTestimonial({
      ...newTestimonial,
      [name]: value,
    });
  };

  const handleClientLogoChange = (e) => {
    const clientLogoId = e.target.value;
    setNewTestimonial({
      ...newTestimonial,
      clientLogoId: clientLogoId,
    });

    // Find the selected logo URL for preview
    const selectedLogo = clientLogos.find((logo) => logo._id === clientLogoId);
    setSelectedLogoUrl(selectedLogo ? selectedLogo.logoUrl : "");
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");
      const requestBody = {
        name: newTestimonial.name,
        role: newTestimonial.role,
        comment: newTestimonial.comment,
        clientLogoId: newTestimonial.clientLogoId,
      };

      const url = editingTestimonial
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/testimonials/${editingTestimonial._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/testimonials";

      const method = editingTestimonial ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error("Failed to save testimonial");
      }

      const data = await response.json();

      if (editingTestimonial) {
        setTestimonials(
          testimonials.map((t) => (t._id === editingTestimonial._id ? data : t))
        );
        showSnackbar("Testimonial updated successfully", "success");
      } else {
        setTestimonials([...testimonials, data]);
        showSnackbar("Testimonial added successfully", "success");
      }

      handleCloseModal();
    } catch (error) {
      console.error("Error saving testimonial:", error);
      showSnackbar("Failed to save testimonial", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            textAlign: "center",
            marginBottom: { xs: "20px", sm: "25px", md: "30px" },
            fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
            textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          Testimonials
        </Typography>

        <Tooltip title="Add New Testimonial">
          <IconButton
            onClick={handleAdd}
            sx={{
              position: "absolute",
              right: { xs: "8px", sm: "12px", md: "15px" },
              top: { xs: "8px", sm: "12px", md: "15px" },
              background: "rgba(219, 74, 65, 0.1)",
              "&:hover": {
                background: "rgba(219, 74, 65, 0.2)",
              },
            }}
          >
            <AddIcon
              sx={{
                color: "#db4a41",
                fontSize: { xs: "1.2rem", sm: "1.4rem" },
              }}
            />
          </IconButton>
        </Tooltip>

        <Grid
          container
          spacing={{ xs: 1.5, sm: 2, md: 2.5 }}
          sx={{ maxWidth: "100%" }}
        >
          <AnimatePresence>
            {testimonials.map((testimonial) => (
              <Grid item xs={12} sm={6} md={4} key={testimonial._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      borderRadius: "12px",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      transition: "transform 0.3s ease, box-shadow 0.3s ease",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      "&:hover": {
                        transform: "translateY(-5px)",
                        boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
                      },
                    }}
                  >
                    <CardContent
                      sx={{ p: { xs: 1.5, sm: 2, md: 2.5 }, flexGrow: 1 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          mb: { xs: 1.5, sm: 2 },
                          flexDirection: { xs: "column", sm: "row" },
                          gap: { xs: 1, sm: 0 },
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            gap: { xs: 1.5, sm: 2 },
                            width: { xs: "100%", sm: "auto" },
                          }}
                        >
                          <Avatar
                            src={testimonial.logourl}
                            alt={testimonial.name}
                            sx={{
                              width: { xs: 45, sm: 50, md: 55 },
                              height: { xs: 45, sm: 50, md: 55 },
                              border: "2px solid rgba(255, 255, 255, 0.1)",
                            }}
                          />
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography
                              variant="h6"
                              sx={{
                                color: "white",
                                fontWeight: "bold",
                                mb: 0.5,
                                fontSize: {
                                  xs: "0.9rem",
                                  sm: "1rem",
                                  md: "1.1rem",
                                },
                              }}
                            >
                              {testimonial.name}
                            </Typography>
                            <Typography
                              variant="subtitle1"
                              sx={{
                                color: "#db4a41",
                                fontSize: {
                                  xs: "0.75rem",
                                  sm: "0.8rem",
                                  md: "0.85rem",
                                },
                              }}
                            >
                              {testimonial.role}
                            </Typography>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            gap: 0.5,
                            alignSelf: { xs: "flex-end", sm: "flex-start" },
                          }}
                        >
                          <Tooltip title="Edit">
                            <IconButton
                              onClick={() => handleEdit(testimonial)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "white" },
                                padding: { xs: "4px", sm: "8px" },
                              }}
                            >
                              <EditIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              onClick={() => handleDelete(testimonial._id)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "#db4a41" },
                                padding: { xs: "4px", sm: "8px" },
                              }}
                            >
                              <DeleteIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{
                          color: "rgba(255, 255, 255, 0.8)",
                          lineHeight: 1.5,
                          fontSize: {
                            xs: "0.85rem",
                            sm: "0.9rem",
                            md: "0.95rem",
                          },
                        }}
                      >
                        {testimonial.comment}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </AnimatePresence>
        </Grid>
      </Box>

      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
            m: { xs: 1.5, sm: 2, md: 2.5 },
            maxWidth: {
              xs: "calc(100% - 32px)",
              sm: "calc(100% - 48px)",
              md: "600px",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            color: "white",
            fontSize: { xs: "1.1rem", sm: "1.25rem", md: "1.35rem" },
            py: { xs: 1.5, sm: 2 },
          }}
        >
          {editingTestimonial ? "Edit Testimonial" : "Add New Testimonial"}
        </DialogTitle>
        <DialogContent>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: { xs: 1.5, sm: 2 },
              mt: { xs: 1, sm: 1.5 },
            }}
          >
            {/* Client Logo Preview */}
            {selectedLogoUrl && (
              <Avatar
                src={selectedLogoUrl}
                alt="Selected logo"
                sx={{
                  width: { xs: 70, sm: 80, md: 90 },
                  height: { xs: 70, sm: 80, md: 90 },
                  border: "2px solid rgba(255, 255, 255, 0.1)",
                  mb: 2,
                }}
              />
            )}

            {/* Client Logo Selection */}
            <FormControl
              fullWidth
              margin="normal"
              size="small"
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.23)",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                },
                "& .MuiInputLabel-root": {
                  color: "rgba(255, 255, 255, 0.7)",
                },
              }}
            >
              <InputLabel>Select Client Logo</InputLabel>
              <Select
                value={newTestimonial.clientLogoId}
                onChange={handleClientLogoChange}
                label="Select Client Logo"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {clientLogos.map((logo) => (
                  <MenuItem key={logo._id} value={logo._id}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Avatar
                        src={logo.logoUrl}
                        alt={logo.name}
                        sx={{ width: 24, height: 24 }}
                      />
                      {logo.name}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          <TextField
            fullWidth
            label="Name"
            name="name"
            value={newTestimonial.name}
            onChange={handleInputChange}
            margin="normal"
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.23)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.5)",
                },
              },
              "& .MuiInputLabel-root": {
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          />
          <TextField
            fullWidth
            label="Role"
            name="role"
            value={newTestimonial.role}
            onChange={handleInputChange}
            margin="normal"
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.23)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.5)",
                },
              },
              "& .MuiInputLabel-root": {
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          />
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Comment"
            name="comment"
            value={newTestimonial.comment}
            onChange={handleInputChange}
            margin="normal"
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.23)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.5)",
                },
              },
              "& .MuiInputLabel-root": {
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          />
        </DialogContent>
        <DialogActions sx={{ p: { xs: 1.5, sm: 2 } }}>
          <Button
            onClick={handleCloseModal}
            size="small"
            sx={{
              color: "rgba(255, 255, 255, 0.7)",
              "&:hover": { color: "white" },
              fontSize: { xs: "0.85rem", sm: "0.9rem" },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            size="small"
            sx={{
              background: "#db4a41",
              "&:hover": {
                background: "#c43a31",
              },
              fontSize: { xs: "0.85rem", sm: "0.9rem" },
            }}
          >
            {editingTestimonial ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        TransitionComponent={Fade}
        sx={{
          left: { xs: "72px", sm: "72px", md: "260px" },
          width: {
            xs: "calc(100% - 72px)",
            sm: "calc(100% - 72px)",
            md: "calc(100% - 260px)",
          },
        }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background: snackbar.severity === "success" ? "#2e7d32" : "#d32f2f",
            color: "white",
            fontSize: { xs: "0.85rem", sm: "0.9rem" },
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Testimonials;
