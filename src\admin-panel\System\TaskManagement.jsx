import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Avatar,
  Tabs,
  Tab,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { motion, AnimatePresence } from "framer-motion";
import TaskForm from "./popups/TaskForm";
import TaskDetails from "./popups/TaskDetails";
import { useUser } from "../../contexts/UserContext";

function TaskManagement() {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [viewingTask, setViewingTask] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const { user } = useUser();

  const API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/tasks";

  const fetchTasks = useCallback(async () => {
    console.log("fetchTasks called - refreshing task list");
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (!response.ok) {
        throw new Error("Failed to fetch tasks");
      }
      const data = await response.json();
      console.log("Tasks fetched:", data.length, "tasks");
      console.log(
        "Sample task structure:",
        data[0]
          ? {
              id: data[0]._id,
              createdBy: data[0].createdBy,
              title: data[0].title,
            }
          : "No tasks"
      );
      console.log("Current user:", user);
      setTasks(data);
    } catch (error) {
      console.error("Error fetching tasks:", error);
      showSnackbar("Failed to fetch tasks", "error");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const handleAdd = () => {
    setEditingTask(null);
    setOpenModal(true);
  };

  const handleEdit = (task) => {
    setEditingTask(task);
    setOpenModal(true);
  };

  const handleView = (task) => {
    setViewingTask(task);
    setOpenViewModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingTask(null);
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingTask(null);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this task?")) {
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_URL}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        setTasks(tasks.filter((task) => task._id !== id));
        showSnackbar("Task deleted successfully", "success");
      } else {
        throw new Error("Failed to delete task");
      }
    } catch (error) {
      console.error("Error deleting task:", error);
      showSnackbar("Failed to delete task", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "#f44336";
      case "medium":
        return "#ff9800";
      case "low":
        return "#4caf50";
      default:
        return "#9e9e9e";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "#ff9800";
      case "in_progress":
        return "#2196f3";
      case "needs_info":
        return "#9c27b0";
      case "completed":
        return "#4caf50";
      case "on_hold":
        return "#607d8b";
      case "cancelled":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  // Determine if the logged-in user is the creator of the task
  const isTaskCreator = (task) => {
    const creator = task?.createdBy;
    const creatorId =
      typeof creator === "object" && creator !== null
        ? creator._id || creator.id
        : creator;
    const loggedInUserId = user?._id || user?.id;
    if (!creatorId || !loggedInUserId) return false;
    return String(creatorId) === String(loggedInUserId);
  };

  // Determine if the logged-in user is assigned to the task
  const isTaskAssignedToUser = (task) => {
    const loggedInUserId = user?._id || user?.id;
    if (!loggedInUserId || !task?.assignedTo) return false;

    return task.assignedTo.some((assignee) => {
      const assigneeId =
        typeof assignee === "object" && assignee !== null
          ? assignee._id || assignee.id
          : assignee;
      return String(assigneeId) === String(loggedInUserId);
    });
  };

  // Check if user is general manager
  const isGeneralManager = () => {
    return user?.role === "general_manager";
  };

  // Filter tasks based on active tab, search term, and filters
  const getFilteredTasks = () => {
    let filteredTasks = [];

    // First filter by tab
    switch (activeTab) {
      case 0: // Assigned to me
        filteredTasks = tasks.filter((task) => isTaskAssignedToUser(task));
        break;
      case 1: // Created by me
        filteredTasks = tasks.filter((task) => isTaskCreator(task));
        break;
      case 2: // General (all tasks) - only for general managers
        filteredTasks = isGeneralManager() ? tasks : [];
        break;
      default:
        filteredTasks = tasks.filter((task) => isTaskCreator(task));
    }

    // Apply search filter
    if (searchTerm.trim()) {
      filteredTasks = filteredTasks.filter(
        (task) =>
          task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          task.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filteredTasks = filteredTasks.filter(
        (task) => task.status === statusFilter
      );
    }

    // Apply priority filter
    if (priorityFilter) {
      filteredTasks = filteredTasks.filter(
        (task) => task.priority === priorityFilter
      );
    }

    // Apply type filter
    if (typeFilter) {
      filteredTasks = filteredTasks.filter((task) => task.type === typeFilter);
    }

    return filteredTasks;
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setPriorityFilter("");
    setTypeFilter("");
  };

  const filteredTasks = getFilteredTasks();

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Task Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Task
          </Button>
        </Box>

        {/* Tabs Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#db4a41",
              },
              "& .MuiTab-root": {
                color: "rgba(255, 255, 255, 0.7)",
                fontFamily: "Formula Bold",
                textTransform: "none",
                fontSize: "1rem",
                "&.Mui-selected": {
                  color: "#db4a41",
                },
                "&:hover": {
                  color: "rgba(219, 74, 65, 0.8)",
                },
              },
            }}
          >
            <Tab label="Assigned to Me" />
            <Tab label="Created by Me" />
            {isGeneralManager() && <Tab label="General (All Tasks)" />}
          </Tabs>
        </Box>

        {/* Search and Filters Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Grid container spacing={2} alignItems="center">
            {/* Search Bar */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search tasks by title or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "rgba(255, 255, 255, 0.7)" }} />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setSearchTerm("")}
                        size="small"
                        sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                      >
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                  },
                  "& .MuiInputBase-input::placeholder": {
                    color: "rgba(255, 255, 255, 0.5)",
                  },
                }}
              />
            </Grid>

            {/* Status Filter */}
            <Grid item xs={12} sm={4} md={2}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Status
                </InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#db4a41",
                    },
                    "& .MuiSelect-icon": { color: "white" },
                  }}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="in_progress">In Progress</MenuItem>
                  <MenuItem value="needs_info">Needs Info</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="on_hold">On Hold</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Priority Filter */}
            <Grid item xs={12} sm={4} md={2}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Priority
                </InputLabel>
                <Select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#db4a41",
                    },
                    "& .MuiSelect-icon": { color: "white" },
                  }}
                >
                  <MenuItem value="">All Priorities</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Type Filter */}
            <Grid item xs={12} sm={4} md={2}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Type
                </InputLabel>
                <Select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#db4a41",
                    },
                    "& .MuiSelect-icon": { color: "white" },
                  }}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="creative_strategy">
                    Creative Strategy
                  </MenuItem>
                  <MenuItem value="content_schedule">Content Schedule</MenuItem>
                  <MenuItem value="shoot">Shoot</MenuItem>
                  <MenuItem value="video_editing">Video Editing</MenuItem>
                  <MenuItem value="photo_editing">Photo Editing</MenuItem>
                  <MenuItem value="coloring">Coloring</MenuItem>
                  <MenuItem value="proposal">Proposal</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Clear Filters Button */}
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={clearFilters}
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Task Count Display */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: "rgba(255, 255, 255, 0.8)",
                    fontFamily: "Formula Bold",
                  }}
                >
                  {activeTab === 0 &&
                    `Tasks Assigned to Me (${filteredTasks.length})`}{" "}
                  {activeTab === 1 &&
                    `Tasks Created by Me (${filteredTasks.length})`}
                  {activeTab === 2 &&
                    isGeneralManager() &&
                    `All Tasks (${filteredTasks.length})`}
                </Typography>
              </Box>

              {filteredTasks.length === 0 ? (
                <Box
                  sx={{
                    textAlign: "center",
                    py: 8,
                    px: 4,
                  }}
                >
                  <Typography
                    variant="h5"
                    sx={{
                      color: "rgba(255, 255, 255, 0.6)",
                      fontFamily: "Formula Bold",
                      mb: 2,
                    }}
                  >
                    No tasks found
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "rgba(255, 255, 255, 0.4)",
                      mb: 3,
                    }}
                  >
                    {activeTab === 0 && "No tasks have been assigned to you."}
                    {activeTab === 1 && "You haven't created any tasks yet."}
                    {activeTab === 2 &&
                      isGeneralManager() &&
                      "No tasks exist in the system."}
                  </Typography>
                  {activeTab === 1 && (
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleAdd}
                      sx={{
                        backgroundColor: "#db4a41",
                        color: "white",
                        fontFamily: "Formula Bold",
                        "&:hover": {
                          backgroundColor: "#c62828",
                        },
                      }}
                    >
                      Create Your First Task
                    </Button>
                  )}
                </Box>
              ) : (
                <Grid container spacing={3}>
                  <AnimatePresence>
                    {filteredTasks.map((task) => (
                      <Grid item xs={12} sm={6} md={4} key={task._id}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card
                            sx={{
                              background: "rgba(255, 255, 255, 0.05)",
                              backdropFilter: "blur(10px)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                              borderRadius: "12px",
                              height: "100%",
                              display: "flex",
                              flexDirection: "column",
                              position: "relative",
                              "&:hover": {
                                background: "rgba(255, 255, 255, 0.08)",
                                borderColor: "rgba(219, 74, 65, 0.3)",
                              },
                            }}
                          >
                            <Box
                              sx={{
                                position: "absolute",
                                top: 8,
                                right: 8,
                                display: "flex",
                                gap: 0.5,
                                zIndex: 10,
                              }}
                            >
                              <Tooltip title="View">
                                <IconButton
                                  onClick={() => handleView(task)}
                                  size="small"
                                  sx={{
                                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    color: "white",
                                    "&:hover": {
                                      backgroundColor: "rgba(219, 74, 65, 0.8)",
                                    },
                                  }}
                                >
                                  <VisibilityIcon sx={{ fontSize: "1rem" }} />
                                </IconButton>
                              </Tooltip>
                              {isTaskCreator(task) && (
                                <Tooltip title="Edit">
                                  <IconButton
                                    onClick={() => handleEdit(task)}
                                    size="small"
                                    sx={{
                                      backgroundColor: "rgba(0, 0, 0, 0.7)",
                                      color: "white",
                                      "&:hover": {
                                        backgroundColor:
                                          "rgba(219, 74, 65, 0.8)",
                                      },
                                    }}
                                  >
                                    <EditIcon sx={{ fontSize: "1rem" }} />
                                  </IconButton>
                                </Tooltip>
                              )}
                              {isTaskCreator(task) && (
                                <Tooltip title="Delete">
                                  <IconButton
                                    onClick={() => handleDelete(task._id)}
                                    size="small"
                                    sx={{
                                      backgroundColor: "rgba(0, 0, 0, 0.7)",
                                      color: "white",
                                      "&:hover": {
                                        backgroundColor:
                                          "rgba(244, 67, 54, 0.8)",
                                      },
                                    }}
                                  >
                                    <DeleteIcon sx={{ fontSize: "1rem" }} />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>

                            <CardContent sx={{ flexGrow: 1, padding: "20px" }}>
                              <Typography
                                variant="h5"
                                component="h5"
                                sx={{
                                  fontFamily: "Formula Bold",
                                  color: "white",
                                  mb: 1,
                                }}
                              >
                                {task.title}
                              </Typography>
                              <Typography
                                variant="body2"
                                color="rgba(255, 255, 255, 0.7)"
                                sx={{ mb: 2 }}
                              >
                                {task.description?.substring(0, 100)}
                                {task.description?.length > 100 && "..."}
                              </Typography>

                              <Box
                                sx={{
                                  display: "flex",
                                  gap: 1,
                                  flexWrap: "wrap",
                                  mb: 2,
                                }}
                              >
                                <Chip
                                  label={task.status}
                                  size="small"
                                  sx={{
                                    backgroundColor: getStatusColor(
                                      task.status
                                    ),
                                    color: "white",
                                    textTransform: "capitalize",
                                  }}
                                />
                                <Chip
                                  label={task.priority}
                                  size="small"
                                  sx={{
                                    backgroundColor: getPriorityColor(
                                      task.priority
                                    ),
                                    color: "white",
                                    textTransform: "capitalize",
                                  }}
                                />
                                <Chip
                                  label={(task.type || "").replace("_", " ")}
                                  size="small"
                                  sx={{
                                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                                    color: "white",
                                    textTransform: "capitalize",
                                  }}
                                />
                              </Box>

                              <Typography
                                variant="body2"
                                color="rgba(255, 255, 255, 0.5)"
                                sx={{ mb: 1, fontSize: "0.8rem" }}
                              >
                                Client: {task.clientId?.name || "N/A"}
                              </Typography>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  mb: 1,
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  color="rgba(255, 255, 255, 0.5)"
                                  sx={{ fontSize: "0.8rem", mr: 1 }}
                                >
                                  Assigned to:
                                </Typography>
                                <Box sx={{ display: "flex" }}>
                                  {Array.isArray(task.assignedTo) &&
                                  task.assignedTo.length > 0
                                    ? task.assignedTo.map((user) => (
                                        <Tooltip
                                          title={user.name}
                                          key={user._id}
                                        >
                                          <Avatar
                                            sx={{
                                              bgcolor: stringToColor(user.name),
                                              width: 24,
                                              height: 24,
                                              fontSize: "0.75rem",
                                              ml: -1,
                                              border: "2px solid #424242",
                                            }}
                                          >
                                            {user.name.charAt(0)}
                                          </Avatar>
                                        </Tooltip>
                                      ))
                                    : "N/A"}
                                </Box>
                              </Box>
                              <Typography
                                variant="body2"
                                color="rgba(255, 255, 255, 0.5)"
                                sx={{ fontSize: "0.8rem" }}
                              >
                                Due Date:{" "}
                                {task.dueDate
                                  ? new Date(task.dueDate).toLocaleDateString()
                                  : "Not set"}
                              </Typography>
                            </CardContent>
                          </Card>
                        </motion.div>
                      </Grid>
                    ))}
                  </AnimatePresence>
                </Grid>
              )}
            </>
          )}
        </Box>
      </Box>

      <TaskForm
        open={openModal}
        onClose={handleCloseModal}
        task={editingTask}
        refreshTasks={fetchTasks}
        showSnackbar={showSnackbar}
      />
      <TaskDetails
        open={openViewModal}
        onClose={handleCloseViewModal}
        task={viewingTask}
      />

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background: snackbar.severity === "success" ? "#2e7d32" : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default TaskManagement;
