import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Button,
} from "@mui/material";
import { Download, Close } from "@mui/icons-material";

const CLOUDFLARE_BASE =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/projects/documents";

const ProjectViewPopup = ({ open, onClose, project }) => {
  if (!project) return null;

  const clientName = project.clientId?.name || "Unknown client";
  const clientEmail = project.clientId?.email || "No email available";

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      sx={{
        "& .MuiPaper-root": {
          background: "rgba(0,0,0,0.9)",
          color: "white",
          borderRadius: "12px",
        },
      }}
    >
      <DialogTitle sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}>
        {project.title}
      </DialogTitle>

      <DialogContent dividers>
        <Typography
          variant="body1"
          sx={{ mb: 2, color: "rgba(255,255,255,0.8)" }}
        >
          {project.description}
        </Typography>

        <Typography
          variant="body2"
          sx={{ mb: 1, color: "rgba(255,255,255,0.6)" }}
        >
          Client: {clientName}
        </Typography>
        <Typography
          variant="body2"
          sx={{ mb: 2, color: "rgba(255,255,255,0.6)" }}
        >
          Email: {clientEmail}
        </Typography>

        <Typography
          variant="body2"
          sx={{ mb: 2, color: "rgba(255,255,255,0.6)" }}
        >
          {new Date(project.startDate).toLocaleDateString()} –{" "}
          {new Date(project.endDate).toLocaleDateString()}
        </Typography>

        <Typography
          variant="h6"
          sx={{
            mt: 3,
            mb: 1,
            fontFamily: "Formula Bold",
            color: "#db4a41",
          }}
        >
          Documents
        </Typography>

        {project.documents && project.documents.length > 0 ? (
          <List>
            {project.documents.map((docUrl, idx) => {
              const parts = docUrl.split("/");
              const fileName = parts[parts.length - 1]; // get filename from URL

              return (
                <ListItem
                  key={idx}
                  sx={{ borderBottom: "1px solid rgba(255,255,255,0.1)" }}
                  secondaryAction={
                    <IconButton
                      edge="end"
                      component="a"
                      href={docUrl}
                      download={fileName}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ color: "#4caf50" }}
                    >
                      <Download />
                    </IconButton>
                  }
                >
                  <ListItemText
                    primary={fileName}
                    primaryTypographyProps={{
                      sx: { color: "rgba(255,255,255,0.9)" },
                    }}
                  />
                </ListItem>
              );
            })}
          </List>
        ) : (
          <Typography
            variant="body2"
            sx={{ color: "rgba(255,255,255,0.6)", fontStyle: "italic" }}
          >
            No documents uploaded
          </Typography>
        )}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={onClose}
          startIcon={<Close />}
          sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectViewPopup;
