import React, { useState, useEffect } from "react";
import {
  Ava<PERSON>,
  Badge,
  Box,
  List,
  ListItemIcon,
  ListItemText,
  Typography,
  ListItemButton,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Divider,
  Collapse,
} from "@mui/material";
import { Link, useNavigate } from "react-router-dom";
import MenuIcon from "@mui/icons-material/Menu";
import EventIcon from "@mui/icons-material/Event";
import PeopleAltIcon from "@mui/icons-material/PeopleAlt";
import AttachEmailIcon from "@mui/icons-material/AttachEmail";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import HomeIcon from "@mui/icons-material/Home";
import GroupIcon from "@mui/icons-material/Group";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import CallToActionIcon from "@mui/icons-material/CallToAction";
import GroupsIcon from "@mui/icons-material/Groups";
import LogoutIcon from "@mui/icons-material/Logout";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import axios from "axios";
import { useUser } from "../contexts/UserContext";
import VideocamIcon from "@mui/icons-material/Videocam";
import CameraRollIcon from "@mui/icons-material/CameraRoll";
import { BsCameraReelsFill } from "react-icons/bs";
import { FaMoneyBillAlt, FaCalculator } from "react-icons/fa";
import WorkIcon from "@mui/icons-material/Work";
import PhotoCameraFrontIcon from "@mui/icons-material/PhotoCameraFront";
import { MeetingRoom } from "@mui/icons-material";
import { GrFolderCycle } from "react-icons/gr";
import { GiPayMoney, GiExpense } from "react-icons/gi";
import { GrMoney } from "react-icons/gr";
import { RiBillLine } from "react-icons/ri";
import UnarchiveIcon from "@mui/icons-material/Unarchive";
import CurrencyExchangeIcon from "@mui/icons-material/CurrencyExchange";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import PostAddIcon from "@mui/icons-material/PostAdd";
import { LuBaby } from "react-icons/lu";

const SidePanel = ({ isExpanded, setIsExpanded }) => {
  const [contactFormSubmissions, setContactFormSubmissions] = useState([]);
  const [jobApplications, setJobApplications] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [cmsSectionOpen, setCmsSectionOpen] = useState(true);
  const [systemSectionOpen, setSystemSectionOpen] = useState(true);
  const [financialSectionOpen, setFinancialSectionOpen] = useState(true);
  const [clientPortalSectionOpen, setClientPortalSectionOpen] = useState(true); // NEW
  const navigate = useNavigate();
  const { user, logout } = useUser();

  useEffect(() => {
    const token = localStorage.getItem("token");
    const headers = { Authorization: `Bearer ${token}` };

    axios
      .get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/contactform",
        { headers }
      )
      .then((res) => setContactFormSubmissions(res.data))
      .catch(() => setContactFormSubmissions([]));

    axios
      .get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/jobApplications"
      )
      .then((res) => setJobApplications(res.data))
      .catch(() => setJobApplications([]));
  }, []);

  const notificationsCount = {
    contactFormSubmissions: contactFormSubmissions.length,
    jobApplications: jobApplications.length,
  };

  const handleAvatarClick = (event) => setAnchorEl(event.currentTarget);
  const handleMenuClose = () => setAnchorEl(null);
  const handleProfile = () => {
    handleMenuClose();
    navigate("/admin/profile");
  };
  const handleLogout = () => logout();

  /** ------------------------
   * Tabs Definitions by Tier
   --------------------------*/
  const tier1Tabs = [
    { icon: <HomeIcon />, text: "Dashboard", path: "/admin/newdashboard" },
    { icon: <AssignmentIcon />, text: "Task Management", path: "/admin/tasks" },
    { icon: <VideocamIcon />, text: "Shoots", path: "/admin/shoots" },
    { icon: <MeetingRoom />, text: "Meetings", path: "/admin/meetings" },
  ];

  // const tier2ExtraTabs = [
  //   { icon: <MeetingRoom />, text: "Meetings", path: "/admin/meetings" },
  // ];

  const tier2FinancialTabs = [
    {
      icon: <RiBillLine />,
      text: "Quotations",
      path: "/admin/financial/quotations",
    },
    {
      icon: <GiExpense />,
      text: "Expenses",
      path: "/admin/financial/expenses",
    },
    {
      icon: <GrMoney />,
      text: "Adjustments",
      path: "/admin/financial/adjustments",
    },
  ];

  const tier3FinancialTabs = [
    ...tier2FinancialTabs,
    {
      icon: <GroupIcon />,
      text: "Clients Account",
      path: "/admin/financial/clients-account",
    },
    {
      icon: <GrFolderCycle />,
      text: "Subscription Cycle",
      path: "/admin/financial/subscription-cycle",
    },
    {
      icon: <FaMoneyBillAlt />,
      text: "Payments",
      path: "/admin/financial/payments",
    },
    {
      icon: <GiPayMoney />,
      text: "One time projects",
      path: "/admin/financial/one-time-projects",
    },
    { text: "__________________" },
    {
      icon: <GiPayMoney />,
      text: "Agency Finance",
      path: "/admin/financial/agency-finance",
    },
    {
      icon: <FaCalculator />,
      text: "Agency Expenses",
      path: "/admin/financial/agency-expenses",
    },
    { icon: <GiPayMoney />, text: "Records", path: "/admin/financial/records" },
    { text: "__________________" },
    {
      icon: <GiPayMoney />,
      text: "Salaries",
      path: "/admin/financial/salaries",
    },
    { icon: <GiPayMoney />, text: "Payroll", path: "/admin/financial/payroll" },
    { text: "__________________" },

    {
      icon: <UnarchiveIcon />,
      text: "Archives",
      path: "/admin/financial/archives",
    },
    {
      icon: <CurrencyExchangeIcon />,
      text: "Forecasting",
      path: "/admin/financial/forecasting",
    },
  ];
  const tier1SystemTabs = [
    { icon: <GroupIcon />, text: "Employees", path: "/admin/system/employees" },
    {
      icon: <AccountCircleIcon />,
      text: "Profile",
      path: "/admin/system/profile",
    },
    {
      icon: <EventIcon />,
      text: "Client Timeline",
      path: "/admin/system/client-events",
    },
    {
      icon: <PostAddIcon />,
      text: "Posting Schedule",
      path: "/admin/system/posting-schedule",
    },
    { icon: <WorkIcon />, text: "Database", path: "/admin/system/database" },
    {
      icon: <PhotoCameraFrontIcon />,
      text: "Clients Management",
      path: "/admin/system/clients-management",
    },
  ];

  const tier3ClientPortalTabs = [
    {
      icon: <PersonAddIcon />,
      text: "Client Portal Credentials",
      path: "/admin/system/client-portal-management",
    },
    {
      icon: <EventIcon />,
      text: "Client Calendar Deadlines",
      path: "/admin/system/client-events",
    },
    {
      icon: <PostAddIcon />,
      text: "Client Posting Calendar",
      path: "/admin/system/posting-schedule",
    },
  ];

  const tier3SystemTabs = [
    { icon: <GroupIcon />, text: "Employees", path: "/admin/system/employees" },
    {
      icon: <AccountCircleIcon />,
      text: "Profile",
      path: "/admin/system/profile",
    },
    {
      icon: <PhotoCameraFrontIcon />,
      text: "Clients Management",
      path: "/admin/system/clients-management",
    },

    { icon: <WorkIcon />, text: "Projects", path: "/admin/system/projects" },

    { icon: <WorkIcon />, text: "Database", path: "/admin/system/database" },
    // ✅ Only show these two for General Managers (any tier)
    //    or Account Managers (tier 3 only)
    ...(user?.role === "general_manager" ||
    (user?.role === "account_manager" && user?.tier === 3)
      ? [
          {
            icon: <WorkHistoryIcon />,
            text: "Job Management",
            path: "/admin/CMS/job-management",
          },
          {
            icon: (
              <Badge
                badgeContent={notificationsCount.jobApplications}
                color="error"
              >
                <WorkHistoryIcon />
              </Badge>
            ),
            text: "Job Applications",
            path: "/admin/CMS/job-applications",
          },
        ]
      : []),
  ];

  const tier3CmsTabs = [
    { icon: <GroupsIcon />, text: "CMS People", path: "/admin/CMS/people" },
    { icon: <LuBaby />, text: "Young People", path: "/admin/CMS/young-people" },
    {
      icon: <CallToActionIcon />,
      text: "Testimonials",
      path: "/admin/CMS/testimonials",
    },
    {
      icon: <PeopleAltIcon />,
      text: "Clients Logos",
      path: "/admin/CMS/clients-logos",
    },
    {
      icon: <VideocamIcon />,
      text: "Hero Videos",
      path: "/admin/CMS/heroVideos",
    },
    {
      icon: <BsCameraReelsFill />,
      text: "Reels Gallery",
      path: "/admin/CMS/reels-gallery",
    },
    { icon: <EventIcon />, text: "Work", path: "/admin/CMS/works" },
    {
      icon: <CameraRollIcon />,
      text: "Feature Work",
      path: "/admin/CMS/Feature-work",
    },
    {
      icon: (
        <Badge
          badgeContent={notificationsCount.contactFormSubmissions}
          color="error"
        >
          <AttachEmailIcon />
        </Badge>
      ),
      text: "Contact Form Submissions",
      path: "/admin/contact-submissions",
    },
  ];

  /** ------------------------
   * Render Helpers
   --------------------------*/
  const renderSection = (title, tabs) => (
    <>
      {isExpanded && title && (
        <Typography
          sx={{
            fontSize: "0.75rem",
            fontWeight: 600,
            opacity: 0.6,
            mt: 1.5,
            mb: 0.5,
            px: 2,
            textTransform: "uppercase",
          }}
        >
          {title}
        </Typography>
      )}
      {tabs.map((item) => (
        <Tooltip
          key={item.text}
          title={!isExpanded ? item.text : ""}
          placement="right"
          arrow
        >
          <ListItemButton
            component={Link}
            to={item.path}
            sx={{
              minHeight: 40,
              px: isExpanded ? 2 : 1,
              borderRadius: "8px",
              mb: 0.5,
              justifyContent: isExpanded ? "flex-start" : "center",
              "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.08)" },
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 0,
                mr: isExpanded ? 1.5 : 0,
                color: "white",
              }}
            >
              {item.icon}
            </ListItemIcon>
            {isExpanded && (
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{
                  fontSize: "0.85rem",
                  fontWeight: 500,
                }}
              />
            )}
          </ListItemButton>
        </Tooltip>
      ))}
    </>
  );

  const renderCollapsibleSection = (title, tabs, isOpen, setIsOpen) => (
    <>
      <ListItemButton
        onClick={() => setIsOpen(!isOpen)}
        sx={{
          minHeight: 40,
          px: isExpanded ? 2 : 1,
          borderRadius: "8px",
          mb: 0.5,
          justifyContent: isExpanded ? "flex-start" : "center",
          "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.08)" },
        }}
      >
        <ListItemIcon
          sx={{
            minWidth: 0,
            mr: isExpanded ? 1.5 : 0,
            color: "white",
          }}
        >
          {isExpanded ? (
            isOpen ? (
              <ExpandLessIcon />
            ) : (
              <ExpandMoreIcon />
            )
          ) : isOpen ? (
            <ExpandLessIcon />
          ) : (
            <ExpandMoreIcon />
          )}
        </ListItemIcon>
        {isExpanded && (
          <ListItemText
            primary={title}
            primaryTypographyProps={{
              fontSize: "0.75rem",
              fontWeight: 600,
              opacity: 0.6,
              textTransform: "uppercase",
            }}
          />
        )}
      </ListItemButton>
      <Collapse in={isOpen && isExpanded} timeout="auto" unmountOnExit>
        {tabs.map((item) => (
          <Tooltip
            key={item.text}
            title={!isExpanded ? item.text : ""}
            placement="right"
            arrow
          >
            <ListItemButton
              component={Link}
              to={item.path}
              sx={{
                minHeight: 40,
                px: isExpanded ? 2 : 1,
                pl: isExpanded ? 4 : 1,
                borderRadius: "8px",
                mb: 0.5,
                justifyContent: isExpanded ? "flex-start" : "center",
                "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.08)" },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: isExpanded ? 1.5 : 0,
                  color: "white",
                }}
              >
                {item.icon}
              </ListItemIcon>
              {isExpanded && (
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: "0.85rem",
                    fontWeight: 500,
                  }}
                />
              )}
            </ListItemButton>
          </Tooltip>
        ))}
      </Collapse>
    </>
  );

  /** ------------------------
   * Main Render
   --------------------------*/
  return (
    <Box
      sx={{
        backgroundColor: "black",
        color: "white",
        boxShadow: "1px 5px 10px rgba(0, 0, 0, 0.9)",
        zIndex: 1000,
        position: "relative",
        height: "100vh",
        transition: "width 0.3s ease",
        width: isExpanded ? "280px" : "64px",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Top brand & toggle */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1,
          p: 1.5,
          pb: 0.5,
          justifyContent: isExpanded ? "flex-start" : "center",
        }}
      >
        <IconButton
          onClick={() => setIsExpanded(!isExpanded)}
          sx={{ color: "white" }}
          size="small"
        >
          <MenuIcon fontSize="medium" />
        </IconButton>
        {isExpanded && (
          <Typography
            variant="subtitle2"
            sx={{ fontSize: "0.95rem", fontFamily: "Formula bold" }}
          >
            Young Productions
          </Typography>
        )}
      </Box>

      {/* Profile */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2,
          p: 1.5,
          pt: 1,
          pb: 1,
          cursor: "pointer",
          justifyContent: isExpanded ? "flex-start" : "center",
        }}
        onClick={handleAvatarClick}
      >
        <Avatar
          alt={user?.name || "User"}
          src={
            user?.profilePicture
              ? user?.profilePicture
              : "/assets/default-avatar.webp"
          }
          sx={{ width: 36, height: 36, border: "2px solid #222" }}
        />
        {isExpanded && (
          <Box>
            <Typography
              variant="subtitle2"
              sx={{ fontSize: "0.9rem", fontWeight: 600 }}
            >
              {user?.name || "User"}
            </Typography>
            <Typography
              variant="caption"
              sx={{ fontSize: "0.75rem", color: "rgba(255,255,255,0.7)" }}
            >
              {user?.role || "Admin"}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Profile Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            background: "#181818",
            color: "white",
            minWidth: 160,
            boxShadow: 3,
          },
        }}
      >
        <MenuItem onClick={handleProfile} sx={{ fontSize: "0.9rem" }}>
          <AccountCircleIcon fontSize="small" sx={{ mr: 1 }} /> Profile
        </MenuItem>
        <Divider sx={{ background: "#333" }} />
        <MenuItem
          onClick={handleLogout}
          sx={{ color: "#db4a41", fontSize: "0.9rem" }}
        >
          <LogoutIcon fontSize="small" sx={{ mr: 1 }} /> Logout
        </MenuItem>
      </Menu>

      {/* Navigation */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          "&::-webkit-scrollbar": { width: "6px" },
          "&::-webkit-scrollbar-track": {
            background: "rgba(255, 255, 255, 0.1)",
            borderRadius: "3px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "rgba(255, 255, 255, 0.3)",
            borderRadius: "3px",
            "&:hover": { background: "rgba(255, 255, 255, 0.5)" },
          },
        }}
      >
        <List sx={{ px: isExpanded ? 1 : 0, mt: 0 }}>
          {/* Always render Tier 1 */}
          {renderSection("", tier1Tabs)}
          {user?.tier === 1 && (
            <>
              {renderCollapsibleSection(
                "System",
                [
                  ...tier1SystemTabs,
                  ...(user?.role === "account_manager"
                    ? [
                        {
                          icon: <WorkIcon />,
                          text: "Projects",
                          path: "/admin/system/projects",
                        },
                        // {
                        //   icon: (
                        //     <Badge
                        //       badgeContent={notificationsCount.jobApplications}
                        //       color="error"
                        //     >
                        //       <WorkHistoryIcon />
                        //     </Badge>
                        //   ),
                        //   text: "Job Applications",
                        //   path: "/admin/CMS/job-applications",
                        // },
                      ]
                    : []),
                ],
                systemSectionOpen,
                setSystemSectionOpen
              )}
            </>
          )}
          {/* Tier 2 and above */}
          {user?.tier >= 2 && (
            <>
              {(user?.role === "account_manager" ||
                user?.role === "general_manager") &&
                renderCollapsibleSection(
                  "Financial",
                  user?.role === "account_manager"
                    ? tier2FinancialTabs
                    : tier3FinancialTabs,
                  financialSectionOpen,
                  setFinancialSectionOpen
                )}
              {renderCollapsibleSection(
                "System",
                tier3SystemTabs,
                systemSectionOpen,
                setSystemSectionOpen
              )}

              {/* Client Portal Section */}
              {renderCollapsibleSection(
                "Client Portal",
                tier3ClientPortalTabs,
                clientPortalSectionOpen,
                setClientPortalSectionOpen
              )}
            </>
          )}

          {/* Tier 3 General Manager: show CMS */}
          {(user?.tier >= 3 && user?.role === "general_manager") ||
            (user?.role === "account_manager" && (
              <>
                {renderCollapsibleSection(
                  "CMS",
                  tier3CmsTabs,
                  cmsSectionOpen,
                  setCmsSectionOpen
                )}
              </>
            ))}
        </List>
      </Box>
    </Box>
  );
};

export default SidePanel;
