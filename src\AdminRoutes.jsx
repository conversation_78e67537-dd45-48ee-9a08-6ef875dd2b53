import React, { useState } from "react";
import { Routes, Route } from "react-router-dom";
import SidePanel from "./admin-panel/SidePanel";
import Dashboard from "./admin-panel/Dashboard";
import Events from "./admin-panel/Events";
import LatestEvents from "./admin-panel/LatestEvents";
import Clients from "./admin-panel/Clients";
import Testimonials from "./admin-panel/Testimonials";
import ContactSubmissions from "./admin-panel/ContactSubmissions";
import JobApplications from "./admin-panel/JobApplications";
import Employees from "./admin-panel/Employees";
import Profile from "./admin-panel/Profile";
import TaskManagement from "./admin-panel/TaskManagement";
import Onboarding from "./admin-panel/Onboarding";
import ClientDetails from "./admin-panel/ClientDetails";

const AdminRoutes = () => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <div style={{ display: "flex", width: "100%", height: "100vh" }}>
      <SidePanel isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
      <div
        style={{
          overflowY: "auto",
          flexGrow: 1,
          scrollBehavior: "smooth",
          width: "100vw",
        }}
      >
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/events" element={<Events />} />
          <Route path="/latest-events" element={<LatestEvents />} />
          <Route path="/clients" element={<Clients />} />
          <Route path="/clients/:id" element={<ClientDetails />} />
          <Route path="/employees" element={<Employees />} />
          <Route path="/testimonials" element={<Testimonials />} />
          <Route path="/contact-submissions" element={<ContactSubmissions />} />
          <Route path="/job-applications" element={<JobApplications />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/tasks" element={<TaskManagement />} />
          <Route path="/onboarding" element={<Onboarding />} />
        </Routes>
      </div>
    </div>
  );
};

export default AdminRoutes;
