import React, { useRef, useState, useEffect, forwardRef } from "react";
import SkeletonPlaceholder from "../components/newHome/SkeletonPlaceholder";

const OptimizedVideo = forwardRef(
  (
    {
      src,
      lazy = true,
      maxRetries = 2,
      poster,
      delay = 0,
      style = {},
      ...props
    },
    ref
  ) => {
    const internalRef = useRef();
    const videoRef = ref || internalRef;

    const [shouldLoad, setShouldLoad] = useState(!lazy);
    const [retryCount, setRetryCount] = useState(0);
    const [videoSrc, setVideoSrc] = useState(shouldLoad ? src : "");
    const [isVisible, setIsVisible] = useState(false);

    // Lazy load when in view with staggered delay
    useEffect(() => {
      if (!lazy) return;

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              if (delay > 0) {
                setTimeout(() => {
                  setShouldLoad(true);
                  setIsVisible(true);
                  setVideoSrc(src);
                  observer.disconnect();
                }, delay);
              } else {
                setShouldLoad(true);
                setIsVisible(true);
                setVideoSrc(src);
                observer.disconnect();
              }
            }
          });
        },
        { rootMargin: "200px" }
      );

      if (videoRef.current) observer.observe(videoRef.current);

      return () => observer.disconnect();
    }, [lazy, src, videoRef, delay]);

    // Retry failed loads
    const handleError = () => {
      if (retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount((r) => r + 1);
          setVideoSrc("");
          setTimeout(() => setVideoSrc(src), 50);
        }, 1000);
      } else {
        console.error("Video failed to load after retries:", src);
      }
    };

    const hasPlaceholder = !videoSrc && lazy;
    const containerStyle = hasPlaceholder
      ? { position: "relative", width: "100%", height: "100%", ...style }
      : style;

    return (
      <div style={containerStyle}>
        {hasPlaceholder && (
          <SkeletonPlaceholder
            width="100%"
            height="100%"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              zIndex: 1,
            }}
          />
        )}
        <video
          ref={videoRef}
          src={videoSrc || ""}
          poster={poster}
          preload={lazy && !videoSrc ? "none" : lazy ? "metadata" : "auto"}
          onError={handleError}
          onLoadedData={(e) => {
            if (e.target && e.target.parentElement && hasPlaceholder) {
              const skeleton =
                e.target.parentElement.querySelector("[data-skeleton]");
              if (skeleton) skeleton.style.opacity = 0;
              e.target.style.opacity = 1;
            }
          }}
          style={{
            position: hasPlaceholder ? "absolute" : "relative",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            objectFit: style.objectFit || "cover",
            zIndex: hasPlaceholder ? 2 : "auto",
            opacity: videoSrc && !hasPlaceholder ? 1 : hasPlaceholder ? 0 : 1,
            transition: "opacity 0.3s ease",
            ...style,
          }}
          {...props}
        />
      </div>
    );
  }
);

export default OptimizedVideo;
