import React, { forwardRef, useState } from "react";
import OptimizedVideoV2 from "./OptimizedVideoV2";

/**
 * Tier 0: Critical Hero Video
 * - Loads immediately after first paint
 * - Single resolution, max 300KB initial chunk
 * - Highest priority in queue
 */
export const HeroVideo = forwardRef((props, ref) => {
  return (
    <OptimizedVideoV2
      ref={ref}
      tier={0}
      autoPlay={true}
      muted={true}
      loop={true}
      playsInline={true}
      {...props}
    />
  );
});

/**
 * Tier 1: Interactive Reel Video
 * - Loads only on hover/click
 * - No autoplay preload
 * - Medium priority
 */
export const ReelVideo = forwardRef(({ userIntent = false, ...props }, ref) => {
  return (
    <OptimizedVideoV2
      ref={ref}
      tier={1}
      userIntent={userIntent}
      autoPlay={false}
      muted={true}
      loop={true}
      playsInline={true}
      {...props}
    />
  );
});

/**
 * Tier 2: Decorative Feature Work Video
 * - Loads when visible (simplified conditions)
 * - Lowest priority
 * - Autoplay when loaded
 */
export const FeatureVideo = forwardRef(
  ({ userIntent = false, ...props }, ref) => {
    return (
      <OptimizedVideoV2
        ref={ref}
        tier={2}
        userIntent={userIntent}
        autoPlay={true}
        muted={true}
        loop={true}
        playsInline={true}
        {...props}
      />
    );
  },
);

/**
 * Thumbnail Video - Zero bytes until explicit user action
 * - Never preloads metadata
 * - Uses static poster frame
 * - Only loads on direct click/hover
 */
export const ThumbnailVideo = forwardRef(
  ({ dataSrc, posterSrc, onClick, onHover, ...props }, ref) => {
    const [userInteracted, setUserInteracted] = useState(false);

    const handleClick = (e) => {
      setUserInteracted(true);
      onClick?.(e);
    };

    const handleMouseEnter = (e) => {
      if (onHover) {
        setUserInteracted(true);
        onHover(e);
      }
    };

    // If no user interaction, show static poster
    if (!userInteracted) {
      return (
        <div
          ref={ref}
          className="thumbnail-placeholder"
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          style={{
            cursor: "pointer",
            backgroundColor: "#000",
            ...props.style,
          }}
          {...props}
        >
          {posterSrc && (
            <img
              src={posterSrc}
              alt="Video thumbnail"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              loading="lazy"
              decoding="async"
            />
          )}
        </div>
      );
    }

    // Load video only after user interaction
    return (
      <OptimizedVideoV2
        ref={ref}
        tier={1}
        dataSrc={dataSrc}
        userIntent={true}
        autoPlay={false}
        muted={true}
        loop={false}
        playsInline={true}
        {...props}
      />
    );
  },
);

/**
 * Adaptive Video Quality Selector
 * Chooses optimal video source based on device capabilities
 */
export const getAdaptiveVideoSrc = (baseSrc, options = {}) => {
  const { webmSrc, mp4Src, lowQualitySrc, highQualitySrc } = options;

  // Device capability detection
  const deviceMemory = navigator.deviceMemory || 4; // GB
  const hardwareConcurrency = navigator.hardwareConcurrency || 4; // CPU cores
  const connection = navigator.connection;

  let effectiveType = "4g";
  let saveData = false;

  if (connection) {
    effectiveType = connection.effectiveType || "4g";
    saveData = connection.saveData || false;
  }

  // Determine optimal quality
  const isLowEndDevice = deviceMemory <= 2 || hardwareConcurrency <= 2;
  const isSlowNetwork =
    effectiveType === "slow-2g" ||
    effectiveType === "2g" ||
    effectiveType === "3g";
  const shouldUseLowQuality = isLowEndDevice || isSlowNetwork || saveData;

  // Choose quality
  let qualitySrc = shouldUseLowQuality ? lowQualitySrc : highQualitySrc;
  if (!qualitySrc) qualitySrc = baseSrc;

  // Choose codec (prefer WebM AV1 > VP9 > MP4)
  const video = document.createElement("video");
  const supportsWebM = video.canPlayType("video/webm") !== "";
  const supportsAV1 =
    video.canPlayType('video/webm; codecs="av01.0.08M.08"') !== "";
  const supportsVP9 = video.canPlayType('video/webm; codecs="vp9"') !== "";

  // Log codec support for debugging
  if (process.env.NODE_ENV === "development") {
    console.log("🎥 Codec Support:", {
      webm: supportsWebM,
      av1: supportsAV1,
      vp9: supportsVP9,
      device: { memory: deviceMemory, cores: hardwareConcurrency },
      network: { type: effectiveType, saveData },
      quality: shouldUseLowQuality ? "low" : "high",
    });
  }

  if (supportsAV1 && webmSrc && !shouldUseLowQuality) {
    return webmSrc.replace(".webm", "_av1.webm"); // Prefer AV1 if available
  } else if (supportsVP9 && webmSrc) {
    return webmSrc; // VP9 WebM
  } else {
    return mp4Src || qualitySrc; // MP4 fallback
  }
};

/**
 * Video Source Optimizer
 * Applies CDN optimization and adaptive quality selection
 */
export const optimizeVideoSrc = (src, options = {}) => {
  if (!src) return "";

  // Apply CDN optimization
  const optimizedSrc = src
    .replace(
      "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
      "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/",
    )
    .replace(
      "https://youngproductionss.com/",
      "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/",
    );

  // Apply adaptive quality selection
  return getAdaptiveVideoSrc(optimizedSrc, options);
};

/**
 * Byte-range streaming helper
 * Adds fast-start parameters for quick playback
 */
export const addFastStartParams = (src) => {
  if (!src) return "";

  const url = new URL(src);

  // Add parameters for fast-start delivery
  url.searchParams.set("start", "0");
  url.searchParams.set("end", "524288"); // 512KB initial chunk

  return url.toString();
};

/**
 * Static poster frame extractor
 * Generates poster URL from video with specific timestamp
 */
export const getVideoPoster = (videoSrc, timestamp = 0.1) => {
  if (!videoSrc) return "";

  // For videos that support fragment identifiers
  return `${videoSrc}#t=${timestamp}`;
};

// Export tier components with display names
HeroVideo.displayName = "HeroVideo";
ReelVideo.displayName = "ReelVideo";
FeatureVideo.displayName = "FeatureVideo";
ThumbnailVideo.displayName = "ThumbnailVideo";
