/**
 * Request Budget Enforcer
 * Enforces strict budgets on initial homepage load
 * Fails build if budget exceeded in development
 */

class RequestBudgetEnforcer {
  constructor() {
    this.budgets = {
      initialRequests: 50, // Temporarily increased for debugging
      initialBytes: 10 * 1024 * 1024, // Temporarily increased to 10MB
      initialVideos: 5, // Temporarily allow more videos for debugging
      timeWindow: 3000, // First 3 seconds
    };

    this.metrics = {
      requests: 0,
      bytes: 0,
      videos: 0,
      violations: [],
    };

    this.startTime = performance.now();
    this.isInitialLoad = true;

    this.initMonitoring();
  }

  initMonitoring() {
    // Monitor all network requests
    if ("PerformanceObserver" in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackRequest(entry);
        }
      });

      observer.observe({ entryTypes: ["resource"] });
    }

    // Set timeout for initial load window
    setTimeout(() => {
      this.isInitialLoad = false;
      this.generateFinalReport();
    }, this.budgets.timeWindow);
  }

  trackRequest(entry) {
    if (!this.isInitialLoad) return;

    const isVideo = this.isVideoRequest(entry);
    const bytes = entry.transferSize || entry.encodedBodySize || 0;

    this.metrics.requests++;
    this.metrics.bytes += bytes;

    if (isVideo) {
      this.metrics.videos++;
    }

    // Debug logging to see what requests are being made
    console.log(
      `📊 Request #${this.metrics.requests}: ${entry.name} (${Math.round(bytes / 1024)}KB) ${isVideo ? "[VIDEO]" : ""}`,
    );

    // Check budgets immediately
    this.checkBudgets(entry, isVideo, bytes);
  }

  isVideoRequest(entry) {
    const url = entry.name.toLowerCase();
    return (
      url.includes(".mp4") ||
      url.includes(".webm") ||
      url.includes(".mov") ||
      entry.initiatorType === "video"
    );
  }

  checkBudgets(entry, isVideo, bytes) {
    const violations = [];

    // Check request count budget
    if (this.metrics.requests > this.budgets.initialRequests) {
      violations.push({
        type: "REQUEST_COUNT_EXCEEDED",
        current: this.metrics.requests,
        budget: this.budgets.initialRequests,
        resource: entry.name,
      });
    }

    // Check bytes budget
    if (this.metrics.bytes > this.budgets.initialBytes) {
      violations.push({
        type: "BYTES_BUDGET_EXCEEDED",
        current: this.metrics.bytes,
        budget: this.budgets.initialBytes,
        currentFormatted: this.formatBytes(this.metrics.bytes),
        budgetFormatted: this.formatBytes(this.budgets.initialBytes),
        resource: entry.name,
      });
    }

    // Check video count budget
    if (this.metrics.videos > this.budgets.initialVideos) {
      violations.push({
        type: "VIDEO_COUNT_EXCEEDED",
        current: this.metrics.videos,
        budget: this.budgets.initialVideos,
        resource: entry.name,
      });
    }

    // Record violations
    violations.forEach((violation) => {
      this.metrics.violations.push({
        ...violation,
        timestamp: Date.now(),
        timeSinceStart: performance.now() - this.startTime,
      });

      console.error(`🚨 BUDGET VIOLATION: ${violation.type}`, violation);

      // In development, fail immediately (re-enabled with higher budgets)
      if (process.env.NODE_ENV === "development") {
        console.warn(
          "� Budget violation detected (with relaxed limits for debugging):",
          violation.type,
        );
        this.failBuild(violation);
      }
    });
  }

  failBuild(violation) {
    const message = `
🚨 PERFORMANCE BUDGET VIOLATION 🚨

Type: ${violation.type}
Current: ${violation.current}
Budget: ${violation.budget}
Resource: ${violation.resource}

The homepage has exceeded performance budgets during initial load.
This will cause poor user experience on mobile devices.

Fix this by:
1. Reducing the number of videos that load initially
2. Using lazy loading for non-critical resources
3. Implementing proper video tier management
4. Ensuring only 1 critical video loads on initial page load

Build failed to prevent performance regression.
    `;

    console.error(message);

    // Show modal in development
    this.showViolationModal(violation);

    // Throw error to fail build
    throw new Error(`Performance budget violation: ${violation.type}`);
  }

  showViolationModal(violation) {
    // Create modal overlay
    const modal = document.createElement("div");
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.9);
      color: white;
      font-family: monospace;
      font-size: 14px;
      padding: 20px;
      z-index: 999999;
      overflow: auto;
    `;

    modal.innerHTML = `
      <div style="max-width: 800px; margin: 0 auto;">
        <h1 style="color: #ff4444;">🚨 Performance Budget Violation</h1>
        <p><strong>Type:</strong> ${violation.type}</p>
        <p><strong>Current:</strong> ${violation.current}</p>
        <p><strong>Budget:</strong> ${violation.budget}</p>
        <p><strong>Resource:</strong> ${violation.resource}</p>
        
        <h2>Current Metrics:</h2>
        <ul>
          <li>Requests: ${this.metrics.requests}/${this.budgets.initialRequests}</li>
          <li>Bytes: ${this.formatBytes(this.metrics.bytes)}/${this.formatBytes(this.budgets.initialBytes)}</li>
          <li>Videos: ${this.metrics.videos}/${this.budgets.initialVideos}</li>
        </ul>
        
        <h2>Fix This By:</h2>
        <ul>
          <li>Ensuring only 1 critical video loads initially</li>
          <li>Using ThumbnailVideo for grid items (zero bytes)</li>
          <li>Implementing proper video tier management</li>
          <li>Adding user intent detection before loading</li>
        </ul>
        
        <button onclick="this.parentElement.parentElement.remove()" 
                style="background: #ff4444; color: white; border: none; padding: 10px 20px; margin-top: 20px; cursor: pointer;">
          Close (Fix Required)
        </button>
      </div>
    `;

    document.body.appendChild(modal);
  }

  generateFinalReport() {
    const report = {
      budgets: this.budgets,
      metrics: this.metrics,
      passed: this.metrics.violations.length === 0,
      summary: {
        requestsStatus:
          this.metrics.requests <= this.budgets.initialRequests ? "✅" : "❌",
        bytesStatus:
          this.metrics.bytes <= this.budgets.initialBytes ? "✅" : "❌",
        videosStatus:
          this.metrics.videos <= this.budgets.initialVideos ? "✅" : "❌",
      },
    };

    console.log("📊 Performance Budget Report:", report);

    if (report.passed) {
      console.log("✅ All performance budgets passed!");
    } else {
      console.error("❌ Performance budget violations detected");
    }

    return report;
  }

  formatBytes(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Get current status
  getStatus() {
    return {
      ...this.metrics,
      budgets: this.budgets,
      isInitialLoad: this.isInitialLoad,
      timeSinceStart: performance.now() - this.startTime,
    };
  }
}

// Create singleton instance
const requestBudgetEnforcer = new RequestBudgetEnforcer();

// Expose to window for debugging
if (typeof window !== "undefined") {
  window.requestBudgetEnforcer = requestBudgetEnforcer;
}

export default requestBudgetEnforcer;
