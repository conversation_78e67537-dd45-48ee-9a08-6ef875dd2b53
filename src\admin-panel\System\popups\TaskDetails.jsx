import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Chip,
  Grid,
  TextField,
  IconButton,
  Paper,
  Avatar,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
} from "@mui/material";
import SendIcon from "@mui/icons-material/Send";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import { useUser } from "../../../contexts/UserContext";

function TaskDetails({ open, onClose, task }) {
  const [communications, setCommunications] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const [messageType, setMessageType] = useState("message"); // Default message type
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const { user } = useUser();
  const [detailedTask, setDetailedTask] = useState(task);

  const API_URL = "https://youngproductions-768ada043db3.herokuapp.com/api";

  // Normalize Cloudflare R2 private bucket URL to public URL
  const normalizeR2Url = (url) => {
    if (!url) return url;
    return url.replace(
      "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
      "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
    );
  };

  useEffect(() => {
    const fetchTaskDetails = async () => {
      if (task?._id) {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(`${API_URL}/tasks/${task._id}`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          if (response.ok) {
            const data = await response.json();
            setDetailedTask(data);
          }
        } catch (err) {
          console.error("Failed to fetch task details", err);
        }
      }
    };
    if (open) {
      fetchTaskDetails();
    }
  }, [task, open]);

  useEffect(() => {
    const fetchCommunications = async () => {
      if (task?._id) {
        setLoading(true);
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(
            `${API_URL}/tasks/${task._id}/communications`,
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          );
          if (response.ok) {
            const data = await response.json();
            setCommunications(data);
          }
        } catch (error) {
          console.error("Failed to fetch communications", error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchCommunications();
  }, [task]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !task?._id) return;

    try {
      const token = localStorage.getItem("token");

      // Create FormData for file uploads
      const formData = new FormData();
      formData.append("message", newMessage);
      formData.append("type", messageType);
      formData.append("senderId", user?.id);

      // Append all selected files
      selectedFiles.forEach((file) => {
        formData.append("attachments", file);
      });

      const response = await fetch(
        `${API_URL}/tasks/${task._id}/communications`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            // Don't set Content-Type header when using FormData
            // The browser will set it automatically with the correct boundary
          },
          body: formData,
        }
      );

      if (response.ok) {
        const newComm = await response.json();
        setCommunications([...communications, newComm]);
        setNewMessage("");
        setSelectedFiles([]); // Clear selected files after successful send
      } else {
        console.error(
          "Failed to send message:",
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error("Failed to send message", error);
    }
  };

  const handleDownloadAttachment = (attachmentName, commId) => {
    // Create the URL pattern similar to ClientManagement
    // Assuming attachments are stored with full URLs in the database
    // If the attachmentName is already a full URL, use it directly
    // Otherwise, we need to construct the URL based on your storage pattern

    // For now, let's assume attachmentName contains the full URL
    // This is similar to how ClientManagement handles it
    const fileUrl = normalizeR2Url(attachmentName);

    if (!fileUrl) return;

    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = attachmentName.split("/").pop() || "download";
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleMarkComplete = async () => {
    if (!task?._id) return;
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(`${API_URL}/tasks/${task._id}/complete`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });
      if (!response.ok) {
        throw new Error("Failed to mark task as complete");
      }
      // Show success message and close the dialog
      alert("Task marked as complete successfully!");
      onClose();
    } catch (error) {
      console.error("Error marking task as complete:", error);
      alert("Failed to mark task as complete: " + error.message);
    }
  };

  if (!task) return null;

  const getPriorityColor = (priority) =>
    ({ high: "#f44336", medium: "#ff9800", low: "#4caf50" }[priority] ||
    "#9e9e9e");
  const getStatusColor = (status) =>
    ({
      pending: "#ff9800",
      in_progress: "#2196f3",
      needs_info: "#9c27b0",
      completed: "#4caf50",
      on_hold: "#607d8b",
      cancelled: "#f44336",
    }[status] || "#9e9e9e");
  const isTaskLocked =
    task?.status === "completed" || task?.status === "passed";

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          background: "rgba(0,0,0,0.9)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255,255,255,0.1)",
          borderRadius: "12px",
          color: "white",
        },
      }}
    >
      <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
        {task.title}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {task.description}
            </Typography>
            <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap", mb: 2 }}>
              <Chip
                label={`Status: ${task.status}`}
                size="small"
                sx={{
                  backgroundColor: getStatusColor(task.status),
                  color: "white",
                }}
              />
              <Chip
                label={`Priority: ${task.priority}`}
                size="small"
                sx={{
                  backgroundColor: getPriorityColor(task.priority),
                  color: "white",
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography>
              <strong>Client:</strong> {task.clientId?.name}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography>
              <strong>Project:</strong> {task.projectId?.title}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                flexDirection: "column",
              }}
            >
              <Box
                sx={{ display: "flex", alignItems: "center", gap: 1, mb: 1 }}
              >
                <Typography>
                  <strong>Assigned To:</strong>{" "}
                </Typography>
                <Box sx={{ display: "flex", ml: 1 }}>
                  {Array.isArray(task.assignedTo) && task.assignedTo.length > 0
                    ? task.assignedTo.map((user) => (
                        <Tooltip title={user.name} key={user._id}>
                          <Avatar
                            sx={{
                              bgcolor: stringToColor(user.name),
                              width: 24,
                              height: 24,
                              fontSize: "0.75rem",
                              ml: -1,
                              border: "2px solid #424242",
                            }}
                          >
                            {user.name.charAt(0)}
                          </Avatar>
                        </Tooltip>
                      ))
                    : "N/A"}
                </Box>{" "}
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Typography>
                  <strong>Created By:</strong>
                </Typography>
                <Box sx={{ display: "flex", ml: 1 }}>
                  {task.createdBy ? (
                    <Tooltip title={task.createdBy.name}>
                      <Avatar
                        sx={{
                          bgcolor: stringToColor(task.createdBy.name),
                          width: 24,
                          height: 24,
                          fontSize: "0.75rem",
                          ml: -1,
                          border: "2px solid #424242",
                        }}
                      >
                        {task.createdBy.name.charAt(0)}
                      </Avatar>
                    </Tooltip>
                  ) : (
                    "N/A"
                  )}
                </Box>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography>
              <strong>Due Date:</strong>{" "}
              {new Date(task.dueDate).toLocaleDateString()}
            </Typography>
          </Grid>
        </Grid>

        {Array.isArray(detailedTask?.briefDocument) &&
          detailedTask.briefDocument.length > 0 && (
            <Box mt={2}>
              <Typography
                variant="h6"
                sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 1 }}
              >
                Brief documents
              </Typography>
              <Paper
                sx={{
                  p: 2,
                  background: "rgba(255,255,255,0.05)",
                  border: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                {detailedTask.briefDocument.map((docUrl, idx) => {
                  const normalized = normalizeR2Url(docUrl);
                  const fileName = (normalized || docUrl || "")
                    .split("/")
                    .pop();
                  return (
                    <Typography
                      key={idx}
                      variant="body2"
                      onClick={() =>
                        handleDownloadAttachment(normalized || docUrl, null)
                      }
                      sx={{
                        color: "#db4a41",
                        cursor: "pointer",
                        textDecoration: "underline",
                        display: "block",
                        mb: 0.5,
                        "&:hover": { color: "#ff6b6b" },
                      }}
                    >
                      📄 {fileName || docUrl}
                    </Typography>
                  );
                })}
              </Paper>
            </Box>
          )}

        <Box mt={4}>
          <Typography
            variant="h6"
            sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
          >
            Communication
          </Typography>
          <Paper
            sx={{
              maxHeight: 300,
              overflow: "auto",
              p: 2,
              background: "rgba(255,255,255,0.05)",
              border: "1px solid rgba(255,255,255,0.1)",
            }}
          >
            {communications.map((comm) => (
              <Box
                key={comm._id}
                sx={{ mb: 2, display: "flex", alignItems: "flex-start" }}
              >
                <Avatar sx={{ bgcolor: "#db4a41", mr: 2 }}>
                  {comm.senderId?.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle2" sx={{ color: "white" }}>
                    {comm.senderId?.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255,255,255,0.8)" }}
                  >
                    {comm.message}
                  </Typography>
                  {comm.attachments && comm.attachments.length > 0 && (
                    <Box sx={{ mt: 1 }}>
                      <Typography
                        variant="caption"
                        sx={{ color: "#db4a41", fontWeight: "bold" }}
                      >
                        Attachments:
                      </Typography>
                      {comm.attachments.map((attachment, index) => (
                        <Typography
                          key={index}
                          variant="caption"
                          onClick={() =>
                            handleDownloadAttachment(
                              normalizeR2Url(attachment),
                              comm._id
                            )
                          }
                          sx={{
                            color: "#db4a41",
                            display: "block",
                            ml: 1,
                            cursor: "pointer",
                            textDecoration: "underline",
                            "&:hover": {
                              color: "#ff6b6b",
                            },
                          }}
                        >
                          📎 {attachment}
                        </Typography>
                      ))}
                    </Box>
                  )}
                  <Typography
                    variant="caption"
                    sx={{
                      color: "rgba(255,255,255,0.5)",
                      display: "block",
                      mt: 0.5,
                    }}
                  >
                    {new Date(comm.createdAt).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Paper>

          <Box sx={{ display: "flex", mt: 2, gap: 1, alignItems: "center" }}>
            <input
              type="file"
              multiple
              onChange={(e) => {
                const files = Array.from(e.target.files);
                setSelectedFiles(files);
              }}
              style={{ display: "none" }}
              id="file-upload"
            />
            <label htmlFor="file-upload">
              <IconButton component="span" sx={{ color: "#db4a41" }}>
                <AttachFileIcon />
              </IconButton>
            </label>
            {selectedFiles.length > 0 && (
              <Typography variant="body2" sx={{ color: "white" }}>
                {selectedFiles.map((file) => file.name).join(", ")}
              </Typography>
            )}
            <TextField
              fullWidth
              variant="outlined"
              placeholder={
                isTaskLocked
                  ? "Messaging disabled for completed/passed tasks"
                  : "Type a message..."
              }
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255,255,255,0.23)" },
                },
              }}
            />
            <FormControl variant="outlined" sx={{ minWidth: 120 }}>
              <Select
                value={messageType}
                onChange={(e) => setMessageType(e.target.value)}
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255,255,255,0.23)",
                  },
                }}
              >
                <MenuItem value="message">message</MenuItem>
                <MenuItem value="request_info">Request Info</MenuItem>
                <MenuItem value="info_provided">Info Provided</MenuItem>
              </Select>
            </FormControl>
            <IconButton
              onClick={handleSendMessage}
              sx={{ color: "#db4a41", ml: 1 }}
              disabled={isTaskLocked || !newMessage.trim()}
            >
              <SendIcon />
            </IconButton>
          </Box>

          {isTaskLocked && (
            <Typography
              variant="body2"
              sx={{ color: "rgba(255,255,255,0.6)", mt: 1 }}
            >
              ✋ This task is <strong>{task.status}</strong>, so new
              communications are disabled.
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        {task.status !== "needs_info" && task.status !== "completed" && (
          <Button
            onClick={handleMarkComplete}
            variant="contained"
            sx={{
              backgroundColor: "#4caf50",
              color: "white",
              "&:hover": { backgroundColor: "#388e3c" },
            }}
          >
            Mark as Complete
          </Button>
        )}
        <Button onClick={onClose} sx={{ color: "white" }}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default TaskDetails;
