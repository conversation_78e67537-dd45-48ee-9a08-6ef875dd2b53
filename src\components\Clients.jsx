import React, { useEffect, useMemo } from "react";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import { Typewriter } from "react-simple-typewriter";
import LazyMotionLogo from "../contexts/LazyMotionImg";
import { useClients } from "../hooks/useApi";

const Clients = React.memo(() => {
  // Use React Query for clients with automatic caching and retries
  const { data: clients = [], isLoading, error } = useClients();

  // Memoize processed clients to prevent unnecessary re-renders
  const processedClients = useMemo(() => {
    return clients.map((client) => ({
      ...client,
      logoUrl: client.logoUrl.replace(
        "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
        "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
      ),
    }));
  }, [clients]);

  // Non-blocking overflow management
  useEffect(() => {
    if (isLoading) {
      document.body.classList.add("loading-clients");
    } else {
      document.body.classList.remove("loading-clients");
    }
    return () => {
      document.body.classList.remove("loading-clients");
    };
  }, [isLoading]);

  if (error) {
    return (
      <Box
        sx={{
          backgroundColor: "black",
          padding: "50px",
          textAlign: "center",
        }}
      >
        <Typography variant="h6" color="red">
          Failed to load clients. Please try again later.
        </Typography>
      </Box>
    );
  }
  return (
    <Box
      sx={{
        backgroundColor: "black",
        padding: "50px",
        textAlign: "left",
      }}
    >
      <Box sx={{ width: "100%" }}>
        <Typography variant="body2" color="lightgray">
          Our clients... we call them{" "}
          <span style={{ color: "#DB4A41", fontWeight: "bold" }}>Friends</span>{" "}
          now.
        </Typography>

        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "white",
            margin: "15px 0",
            fontSize: { xs: "1.7rem", sm: "3rem", md: "4rem" },
            width: { xs: "100%", md: "80%" },
          }}
        >
          Some are ambitious startups, others are already part of the
          establishment.{" "}
          <span style={{ color: "#DB4A41" }}>
            <Typewriter
              words={["All of them are going places."]}
              loop={1}
              cursor
              cursorStyle="_"
              typeSpeed={80}
              deleteSpeed={30}
              delaySpeed={1000}
            />
          </span>
        </Typography>

        <motion.button
          className="btn btn-primary"
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 4px 10px rgba(0,0,0,0.2)",
          }}
          whileTap={{ scale: 0.97 }}
          transition={{ type: "spring", stiffness: 300 }}
          onClick={() => {
            window.location.href = "/test/contact";
          }}
          style={{ marginTop: "20px", borderRadius: "10px" }}
        >
          {" "}
          work with us{" "}
        </motion.button>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          gap: "45px",
          marginTop: "40px",
          justifyContent: "space-between",
          padding: {
            xs: "20px 10px",
            sm: "40px 30px",
            md: "50px",
            lg: "40px 200px 200px 200px",
          },
        }}
      >
        {processedClients.map((client, index) => (
          <Box
            key={client._id || index}
            sx={{
              width: { xs: "calc(30% - 32px)", sm: "auto" },
              display: "flex",
              justifyContent: "center",
            }}
          >
            <LazyMotionLogo
              index={index}
              src={client.logoUrl}
              alt={client.alt}
              size={{ xs: 70, sm: 90, md: 110 }}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
});

Clients.displayName = "Clients";

export default Clients;
