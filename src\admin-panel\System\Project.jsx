import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Box,
  Modal,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  IconButton,
} from "@mui/material";
import axios from "axios";
import { motion, AnimatePresence } from "framer-motion";
import { Edit, Visibility, Delete } from "@mui/icons-material";
import ProjectEditPopup from "./popups/ProjectEditPopup";
import ProjectViewPopup from "./popups/ProjectViewPopup";
import { useUser } from "../../contexts/UserContext";

// Server endpoint
const API_URL_projects =
  "https://youngproductions-768ada043db3.herokuapp.com/api/projects";

const API_URI_clients =
  "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement";

const statusColors = {
  planning: "#db4a41",
  shooting: "#ff6b35",
  editing: "#ffa726",
  delivered: "#4caf50",
};

export default function ProjectsGrid() {
  const [projects, setProjects] = useState([]);
  const [open, setOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    title: "",
    clientId: "",
    description: "",
    startDate: "",
    endDate: "",
    status: "planning",
  });
  const [clients, setClients] = useState([]);
  const [editOpen, setEditOpen] = useState(false);
  const [viewOpen, setViewOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [editForm, setEditForm] = useState({
    title: "",
    clientId: "",
    description: "",
    startDate: "",
    endDate: "",
    status: "planning",
  });
  const [newDocuments, setNewDocuments] = useState([]);
  const { user } = useUser();

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState("");
  const [clientFilter, setClientFilter] = useState("");
  const [startDateFilter, setStartDateFilter] = useState("");
  const [endDateFilter, setEndDateFilter] = useState("");
  // Fetch projects
  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      const res = await axios.get(API_URL_projects);
      setProjects(res.data);
    } catch (error) {
      console.error("Error fetching projects:", error);
    }
  };

  const fetchClients = async () => {
    try {
      const res = await axios.get(API_URI_clients);
      setClients(res.data);
    } catch (error) {
      console.error("Error fetching clients:", error);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  // Create project
  const handleCreateProject = async () => {
    try {
      const formData = new FormData();
      formData.append("title", newProject.title);
      formData.append("clientId", newProject.clientId);
      formData.append("description", newProject.description);
      formData.append("startDate", newProject.startDate);
      formData.append("endDate", newProject.endDate);
      formData.append("status", newProject.status);

      if (newProject.documents) {
        Array.from(newProject.documents).forEach((file) =>
          formData.append("documents", file)
        );
      }

      await axios.post(API_URL_projects, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      fetchProjects();
      setOpen(false);
      setNewProject({
        title: "",
        clientId: "",
        description: "",
        startDate: "",
        endDate: "",
        status: "planning",
      });
    } catch (error) {
      console.error("Error creating project:", error);
    }
  };

  // Edit project
  const handleEdit = (project) => {
    setSelectedProject(project);
    setEditForm({
      title: project.title || "",
      clientId: project.clientId?._id || "",
      description: project.description || "",
      startDate: project.startDate
        ? new Date(project.startDate).toISOString().split("T")[0]
        : "",
      endDate: project.endDate
        ? new Date(project.endDate).toISOString().split("T")[0]
        : "",
      status: project.status || "planning",
    });
    setEditOpen(true);
  };

  const handleUpdateProject = async () => {
    try {
      const formData = new FormData();

      // Append text fields
      formData.append("title", editForm.title);
      formData.append("description", editForm.description);
      formData.append("clientId", editForm.clientId);
      formData.append("startDate", editForm.startDate);
      formData.append("endDate", editForm.endDate);
      formData.append("status", editForm.status);

      // Append new documents if any
      if (newDocuments && newDocuments.length > 0) {
        Array.from(newDocuments).forEach((file) =>
          formData.append("documents", file)
        );
      }

      await axios.put(`${API_URL_projects}/${selectedProject._id}`, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      fetchProjects(); // Refresh list
      setEditOpen(false); // Close popup
      setSelectedProject(null);
      setNewDocuments([]); // Clear selected files
    } catch (err) {
      console.error("Error updating project:", err);
    }
  };

  const handleEditFormChange = (field, value) => {
    setEditForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (files) => setNewDocuments(files);

  // View project details
  const handleView = (project) => {
    setSelectedProject(project);
    setViewOpen(true);
  };

  const handleDownloadDocument = async (document) => {
    try {
      const response = await axios.get(
        `${API_URL_projects}/${selectedProject._id}/documents/${document.filename}`,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", document.originalName || document.filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Error downloading document:", error);
    }
  };

  const handleDelete = async (projectId) => {
    if (window.confirm("Are you sure you want to delete this project?")) {
      try {
        await axios.delete(`${API_URL_projects}/${projectId}`);
        fetchProjects(); // refresh the list after deletion
      } catch (err) {
        console.error("Failed to delete project:", err);
      }
    }
  };

  // Filter and search logic
  const filteredProjects = React.useMemo(() => {
    let filtered = [...projects];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (project) =>
          project.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          project.description
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          project.clientId?.name
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase())
      );
    }

    // Client filter
    if (clientFilter) {
      filtered = filtered.filter(
        (project) => project.clientId?._id === clientFilter
      );
    }

    // Date range filter
    if (startDateFilter) {
      filtered = filtered.filter(
        (project) => new Date(project.startDate) >= new Date(startDateFilter)
      );
    }

    if (endDateFilter) {
      filtered = filtered.filter(
        (project) => new Date(project.endDate) <= new Date(endDateFilter)
      );
    }

    return filtered;
  }, [projects, searchTerm, clientFilter, startDateFilter, endDateFilter]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1, p: 3 }}>
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            mb: 3,
            textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          Client Projects
        </Typography>
        {(user?.role === "general_manager" ||
          (user?.role === "account_manager" && user?.tier === 3) ||
          user?.tier === 2) && (
          <Button
            variant="contained"
            onClick={() => setOpen(true)}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              mb: 4,
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add New Project
          </Button>
        )}

        {/* Filter Section */}
        <Card
          sx={{
            background: "rgba(255, 255, 255, 0.05)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
            mb: 4,
          }}
        >
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              {/* Search Bar */}
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  placeholder="Search by project name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputBase-input::placeholder": {
                      color: "rgba(255, 255, 255, 0.5)",
                    },
                  }}
                />
              </Grid>

              {/* Client Filter */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Filter by Client
                  </InputLabel>
                  <Select
                    value={clientFilter}
                    onChange={(e) => setClientFilter(e.target.value)}
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  >
                    <MenuItem value="">All Clients</MenuItem>
                    {clients.map((client) => (
                      <MenuItem key={client._id} value={client._id}>
                        {client.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Start Date Filter */}
              <Grid item xs={12} md={2}>
                <TextField
                  fullWidth
                  label="Start Date From"
                  type="date"
                  value={startDateFilter}
                  onChange={(e) => setStartDateFilter(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              {/* End Date Filter */}
              <Grid item xs={12} md={2}>
                <TextField
                  fullWidth
                  label="End Date To"
                  type="date"
                  value={endDateFilter}
                  onChange={(e) => setEndDateFilter(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              {/* Clear Filters */}
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => {
                    setSearchTerm("");
                    setClientFilter("");
                    setStartDateFilter("");
                    setEndDateFilter("");
                  }}
                  sx={{
                    borderColor: "#db4a41",
                    color: "#db4a41",
                    "&:hover": {
                      borderColor: "#c62828",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Clear Filters
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Grid container spacing={3}>
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <Grid item xs={12} sm={6} md={4} key={project._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                      transition: "transform 0.3s ease, box-shadow 0.3s ease",
                      "&:hover": {
                        transform: "translateY(-5px)",
                        boxShadow: "0 8px 32px rgba(219, 74, 65, 0.3)",
                      },
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#db4a41",
                          mb: 1,
                        }}
                      >
                        {project.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.7)",
                          mb: 2,
                          lineHeight: 1.5,
                        }}
                      >
                        {project.description}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.9)",
                          mb: 2,
                          fontSize: "0.9rem",
                        }}
                      >
                        {new Date(project.startDate).toLocaleDateString()} -{" "}
                        {new Date(project.endDate).toLocaleDateString()}
                      </Typography>
                      <Chip
                        label={project.status.toUpperCase()}
                        sx={{
                          backgroundColor: statusColors[project.status],
                          color: "white",
                          fontFamily: "Formula Bold",
                          fontSize: "0.75rem",
                        }}
                      />
                      <Box
                        sx={{
                          mt: 2,
                          display: "flex",
                          gap: 1,
                          justifyContent: "flex-end",
                        }}
                      >
                        <IconButton
                          onClick={() => handleView(project)}
                          sx={{ color: "#4caf50", padding: "6px" }}
                          title="View Details"
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                        {user?.role === "general_manager" && (
                          <>
                            <IconButton
                              onClick={() => handleEdit(project)}
                              sx={{ color: "#ffa726", padding: "6px" }}
                              title="Edit Project"
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                            <IconButton
                              onClick={() => handleDelete(project._id)}
                              sx={{ color: "#db4a41", float: "right" }}
                              title="Delete Project"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </AnimatePresence>
        </Grid>
        {/* Add Project Modal */}
        <Modal
          open={open}
          onClose={() => setOpen(false)}
          closeAfterTransition
          BackdropProps={{
            sx: {
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              backdropFilter: "blur(5px)",
            },
          }}
        >
          <Box
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              width: { xs: "90%", sm: 500 },
              maxHeight: "90vh",
              overflowY: "auto",
              bgcolor: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              boxShadow: "0 8px 32px rgba(219, 74, 65, 0.3)",
              p: 4,
            }}
          >
            <Typography
              variant="h5"
              sx={{
                fontFamily: "Formula Bold",
                color: "#db4a41",
                mb: 3,
                textAlign: "center",
              }}
            >
              Create New Project
            </Typography>
            <TextField
              fullWidth
              label="Title"
              value={newProject.title}
              onChange={(e) =>
                setNewProject({ ...newProject, title: e.target.value })
              }
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={newProject.description}
              onChange={(e) =>
                setNewProject({ ...newProject, description: e.target.value })
              }
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Client
              </InputLabel>
              <Select
                value={newProject.clientId}
                onChange={(e) =>
                  setNewProject({ ...newProject, clientId: e.target.value })
                }
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.23)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                  "& .MuiSvgIcon-root": {
                    color: "white",
                  },
                }}
              >
                {clients.map((client) => (
                  <MenuItem key={client._id} value={client._id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Start Date"
              type="date"
              value={newProject.startDate}
              onChange={(e) =>
                setNewProject({ ...newProject, startDate: e.target.value })
              }
              InputLabelProps={{ shrink: true }}
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <TextField
              fullWidth
              label="End Date"
              name="End Date"
              type="date"
              margin="normal"
              value={newProject.endDate}
              onChange={(e) =>
                setNewProject({ ...newProject, endDate: e.target.value })
              }
              InputLabelProps={{ shrink: true }}
              sx={{
                mb: 2,
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                "& .MuiSvgIcon-root": {
                  color: "white",
                },
              }}
            />
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Status
              </InputLabel>
              <Select
                value={newProject.status}
                onChange={(e) =>
                  setNewProject({ ...newProject, status: e.target.value })
                }
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.23)",
                    color: "white",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                  "& .MuiSvgIcon-root": {
                    color: "white",
                  },
                }}
              >
                <MenuItem value="planning">Planning</MenuItem>
                <MenuItem value="shooting">Shooting</MenuItem>
                <MenuItem value="editing">Editing</MenuItem>
                <MenuItem value="delivered">Delivered</MenuItem>
              </Select>
            </FormControl>

            <Typography
              variant="h6"
              sx={{
                fontFamily: "Formula Bold",
                color: "#db4a41",
                mb: 1,
              }}
            >
              Project Documents
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                mb: 2,
              }}
            >
              Upload briefs, contracts, or any relevant project files
            </Typography>
            <Button
              variant="outlined"
              component="label"
              fullWidth
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
                mb: 3,
                "&:hover": {
                  borderColor: "#c62828",
                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                },
              }}
            >
              Choose Files
              <input
                type="file"
                multiple
                hidden
                onChange={(e) =>
                  setNewProject({ ...newProject, documents: e.target.files })
                }
              />
            </Button>
            {newProject.documents && newProject.documents.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                >
                  Selected files:
                </Typography>
                {Array.from(newProject.documents).map((file, index) => (
                  <Typography
                    key={index}
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.9)" }}
                  >
                    • {file.name}
                  </Typography>
                ))}
              </Box>
            )}
            <Button
              variant="contained"
              onClick={handleCreateProject}
              fullWidth
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                fontFamily: "Formula Bold",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
            >
              Save Project
            </Button>
          </Box>
        </Modal>
        <ProjectEditPopup
          open={editOpen}
          onClose={() => setEditOpen(false)}
          project={selectedProject}
          formData={editForm}
          onFormChange={handleEditFormChange}
          onFileChange={handleFileChange}
          newDocuments={newDocuments}
          onUpdate={handleUpdateProject}
          clients={clients}
        />

        <ProjectViewPopup
          open={viewOpen}
          onClose={() => setViewOpen(false)}
          project={selectedProject}
          clients={clients}
          onDownloadDocument={handleDownloadDocument}
        />
      </Box>
    </Box>
  );
}
