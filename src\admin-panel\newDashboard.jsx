import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Typography,
  Card,
  CardContent,
  Grid,
  Box,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  Chip,
  Avatar,
  Badge,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  Stack,
  Button,
  Popover,
  List,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Snackbar,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Event as EventIcon,
  Work as WorkIcon,
  Business as BusinessIcon,
  ArrowForward as ArrowForwardIcon,
  CalendarToday as CalendarTodayIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationsIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { useUser } from "../contexts/UserContext";
import axios from "axios";

// API Base URL
const API_BASE_URL = "https://youngproductions-768ada043db3.herokuapp.com/api";

// Notification type configurations
const getNotificationConfig = (type) => {
  switch (type) {
    case "reminder":
      return {
        icon: <AccessTimeIcon />,
        color: "#FF9800",
        bgColor: "rgba(255, 152, 0, 0.1)",
      };
    case "deadline":
      return {
        icon: <ScheduleIcon />,
        color: "#f44336",
        bgColor: "rgba(244, 67, 54, 0.1)",
      };
    case "newThing":
      return {
        icon: <NotificationsIcon />,
        color: "#2196F3",
        bgColor: "rgba(33, 150, 243, 0.1)",
      };
    case "approval":
      return {
        icon: <CheckCircleIcon />,
        color: "#4CAF50",
        bgColor: "rgba(76, 175, 80, 0.1)",
      };
    case "rejection":
      return {
        icon: <CloseIcon />,
        color: "#f44336",
        bgColor: "rgba(244, 67, 54, 0.1)",
      };
    default:
      return {
        icon: <NotificationsIcon />,
        color: "#757575",
        bgColor: "rgba(117, 117, 117, 0.1)",
      };
  }
};

// Function to calculate relative time
const getRelativeTime = (createdAt) => {
  const now = new Date();
  const notificationTime = new Date(createdAt);
  const diffInSeconds = Math.floor((now - notificationTime) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? "s" : ""} ago`;
  }
};

// Helper function to render calendar events
const renderCalendarEvents = (eventsToRender, tabType, isLoading = false) => {
  console.log(
    `Rendering ${tabType} events:`,
    eventsToRender.length,
    eventsToRender
  );

  if (isLoading) {
    return (
      <Box
        sx={{
          textAlign: "center",
          py: 3,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: 1,
        }}
      >
        <CircularProgress size={24} sx={{ color: "#FF9800" }} />
        <Typography
          sx={{ color: "rgba(255, 255, 255, 0.7)", fontSize: "0.8rem" }}
        >
          Loading team events...
        </Typography>
      </Box>
    );
  }

  if (!eventsToRender || eventsToRender.length === 0) {
    return (
      <Box sx={{ textAlign: "center", py: 2 }}>
        <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
          {tabType === "team" ? "No team events found" : "No events found"}
        </Typography>
      </Box>
    );
  }

  return eventsToRender
    .filter((event) => {
      // Only show events that are today or in the future
      if (!event.start) return false;
      const eventDate = new Date(event.start);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day
      return eventDate >= today;
    })
    .slice(0, 4)
    .map((event) => {
      // Check if event is completed (for TaskManagement type events, check referenceId status)
      const isCompleted =
        (event.type === "TaskManagement" &&
          event.referenceId?.status === "completed") ||
        event.referenceId?.status === "passed";

      return (
        <Box
          key={event._id}
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 1,
            mb: 1,
            p: 1,
            borderRadius: "8px",
            backgroundColor: "rgba(255, 255, 255, 0.05)",
            opacity: isCompleted ? 0.6 : 1,
          }}
        >
          <EventIcon
            sx={{
              color: isCompleted
                ? "#4CAF50"
                : tabType === "team"
                ? "#FF9800"
                : "#db4a41",
              fontSize: "1rem",
            }}
          />
          <Box flex={1}>
            <Typography
              variant="body2"
              sx={{
                color: "white",
                fontSize: "0.8rem",
                textDecoration: isCompleted ? "line-through" : "none",
              }}
            >
              {event.title}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                textDecoration: isCompleted ? "line-through" : "none",
              }}
            >
              {new Date(event.start).toLocaleDateString()} at{" "}
              {new Date(event.start).toLocaleTimeString()}
            </Typography>
          </Box>
          {/* Show assignedTo avatars for general events, or event type chip for others */}
          {tabType === "general" && event.assignedTo ? (
            <Stack
              direction="row"
              spacing={-2}
              sx={{
                alignItems: "center",
                justifyContent: "flex-end",
              }}
            >
              {event.assignedTo.map((assignee) => (
                <Tooltip key={assignee._id} title={assignee.name}>
                  <Avatar
                    sx={{
                      bgcolor: "#db4a41",
                      color: "#fff",
                      width: 34,
                      height: 34,
                    }}
                  >
                    {assignee.name && assignee.name[0]}
                  </Avatar>
                </Tooltip>
              ))}
            </Stack>
          ) : (
            <Chip
              label={tabType === "team" ? `${event.type} (Team)` : event.type}
              size="small"
              sx={{
                backgroundColor: `${
                  tabType === "team"
                    ? "#FF9800"
                    : event.type === "TaskManagement"
                    ? "#2196F3"
                    : event.type === "Shoot"
                    ? "#FF9800"
                    : event.type === "Meeting"
                    ? "#9C27B0"
                    : "#4CAF50"
                }20`,
                color:
                  tabType === "team"
                    ? "#FF9800"
                    : event.type === "TaskManagement"
                    ? "#2196F3"
                    : event.type === "Shoot"
                    ? "#FF9800"
                    : event.type === "Meeting"
                    ? "#9C27B0"
                    : "#4CAF50",
                fontSize: "0.6rem",
                height: "18px",
              }}
            />
          )}
        </Box>
      );
    });
};

const CalendarCard = ({
  events,
  tasks,
  onDateClick,
  loading,
  createCalendarEvent,
  fetchUserCalendarEvents,
  user,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  // Check if user can see General tab
  const canSeeGeneralTab = () => {
    if (!user) return false;
    // General managers (all tiers) can see General tab
    if (user.role === "general_manager") return true;
    // Account managers tier 3 only can see General tab
    if (user.role === "account_manager" && user.tier === 3) return true;
    return false;
  };

  // Check if user can see Team tab (tier 3 from any role)
  const canSeeTeamTab = () => {
    if (!user) return false;
    return user.tier === 3;
  };

  // Determine initial tab based on permissions
  const getInitialTab = () => {
    if (canSeeGeneralTab()) return 0; // General tab
    if (canSeeTeamTab()) return 1; // Team tab (if available)
    return canSeeTeamTab() ? 2 : 1; // Individual tab
  };

  // Set initial tab based on permissions
  const [calendarTab, setCalendarTab] = useState(getInitialTab());
  const [viewMode, setViewMode] = useState("month"); // month or year
  const [individualEvents, setIndividualEvents] = useState([]);
  const [teamEvents, setTeamEvents] = useState([]);
  const [teamLoading, setTeamLoading] = useState(false);

  const today = new Date();
  const currentDate = today.getDate();
  const todayMonth = today.getMonth();
  const todayYear = today.getFullYear();

  // Function to fetch team calendar events
  const fetchTeamCalendarEvents = React.useCallback(async () => {
    if (!user?.role || !user?.tier) return [];

    setTeamLoading(true);
    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      console.log("Fetching team events for role:", user.role);

      // Try the dedicated team endpoint first
      try {
        const response = await axios.get(
          `${API_BASE_URL}/system/calendar/team/${user.role}`,
          config
        );
        console.log("Team events from dedicated endpoint:", response.data);
        return response.data;
      } catch (endpointError) {
        console.log(
          "Dedicated team endpoint not available, using fallback method"
        );

        // Fallback: Filter general events by team members with same role
        const allEventsResponse = await axios.get(
          `${API_BASE_URL}/system/calendar`,
          config
        );

        // Get all employees to find team members
        const employeesResponse = await axios.get(
          `${API_BASE_URL}/system/employees`,
          config
        );

        const allEvents = allEventsResponse.data;
        const allEmployees = employeesResponse.data;

        // Find team members (users with same role)
        const teamMembers = allEmployees.filter(
          (emp) => emp.role === user.role
        );
        const teamMemberIds = teamMembers.map(
          (member) => member._id || member.id
        );

        console.log("Team members found:", teamMembers.length);
        console.log("Team member IDs:", teamMemberIds);

        // Filter events where any team member is assigned or created the event
        const teamEvents = allEvents.filter((event) => {
          // Check if any team member is assigned to this event
          const hasTeamMemberAssigned = event.assignedTo?.some((assignee) => {
            const assigneeId =
              typeof assignee === "string" ? assignee : assignee._id;
            return teamMemberIds.includes(assigneeId);
          });

          // Check if event was created by a team member
          const createdByTeamMember = teamMemberIds.includes(
            typeof event.createdBy === "string"
              ? event.createdBy
              : event.createdBy?._id
          );

          return hasTeamMemberAssigned || createdByTeamMember;
        });

        console.log("Filtered team events:", teamEvents.length);
        return teamEvents;
      }
    } catch (error) {
      console.error("Error fetching team calendar events:", error);
      return [];
    } finally {
      setTeamLoading(false);
    }
  }, [user?.role, user?.tier]);

  // Fetch events based on selected tab
  React.useEffect(() => {
    const getTabType = () => {
      if (canSeeGeneralTab() && calendarTab === 0) return "general";
      if (canSeeTeamTab() && canSeeGeneralTab() && calendarTab === 1)
        return "team";
      if (canSeeTeamTab() && !canSeeGeneralTab() && calendarTab === 0)
        return "team";
      return "individual";
    };

    const tabType = getTabType();
    console.log("Current tab type:", tabType, "Calendar tab:", calendarTab);

    if (tabType === "individual" && fetchUserCalendarEvents && user) {
      console.log("Fetching individual events");
      fetchUserCalendarEvents().then(setIndividualEvents);
    } else if (tabType === "team" && user) {
      console.log("Fetching team events for user:", user.role, user.tier);
      fetchTeamCalendarEvents().then((events) => {
        console.log("Team events received:", events);
        setTeamEvents(events);
      });
    }
  }, [calendarTab, fetchUserCalendarEvents, fetchTeamCalendarEvents, user]);

  // Debug team events
  React.useEffect(() => {
    console.log("Team events state updated:", teamEvents.length, teamEvents);
  }, [teamEvents]);

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  // Generate calendar days for current month
  const firstDay = new Date(currentYear, currentMonth, 1);
  const lastDay = new Date(currentYear, currentMonth + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  const calendarDays = [];

  // Add empty cells for days before the first day of the month
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null);
  }

  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  const getEventsForDate = (day) => {
    if (!day) return [];
    const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(
      2,
      "0"
    )}-${String(day).padStart(2, "0")}`;

    // Determine which tab is active
    const getTabType = () => {
      if (canSeeGeneralTab() && calendarTab === 0) return "general";
      if (canSeeTeamTab() && canSeeGeneralTab() && calendarTab === 1)
        return "team";
      if (canSeeTeamTab() && !canSeeGeneralTab() && calendarTab === 0)
        return "team";
      return "individual";
    };

    const tabType = getTabType();

    if (tabType === "general") {
      // General - all calendar events
      return events.filter((event) => {
        if (!event.start) return false;
        const eventDate = new Date(event.start).toISOString().split("T")[0];
        return eventDate === dateStr;
      });
    } else if (tabType === "team") {
      // Team - team calendar events
      return teamEvents.filter((event) => {
        if (!event.start) return false;
        const eventDate = new Date(event.start).toISOString().split("T")[0];
        return eventDate === dateStr;
      });
    } else {
      // Individual - only individual calendar events
      return individualEvents.filter((event) => {
        if (!event.start) return false;
        const eventDate = new Date(event.start).toISOString().split("T")[0];
        return eventDate === dateStr;
      });
    }
  };

  const navigateMonth = (direction) => {
    if (direction === "prev") {
      if (currentMonth === 0) {
        setCurrentMonth(11);
        setCurrentYear(currentYear - 1);
      } else {
        setCurrentMonth(currentMonth - 1);
      }
    } else {
      if (currentMonth === 11) {
        setCurrentMonth(0);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
    }
  };

  const navigateYear = (direction) => {
    setCurrentYear(currentYear + (direction === "next" ? 1 : -1));
  };

  const isToday = (day) => {
    return (
      day === currentDate &&
      currentMonth === todayMonth &&
      currentYear === todayYear
    );
  };

  const hasEvents = (day) => {
    return getEventsForDate(day).length > 0;
  };

  const renderMonthView = () => (
    <>
      {/* Calendar Header */}
      <Box sx={{ mb: 2 }}>
        <Box display="flex" alignItems="center" justifyContent="between" mb={1}>
          <IconButton
            onClick={() => navigateMonth("prev")}
            sx={{ color: "white" }}
          >
            <ChevronLeftIcon />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              color: "#db4a41",
              textAlign: "center",
              flex: 1,
              cursor: "pointer",
            }}
            onClick={() => setViewMode("year")}
          >
            {monthNames[currentMonth]} {currentYear}
          </Typography>
          <IconButton
            onClick={() => navigateMonth("next")}
            sx={{ color: "white" }}
          >
            <ChevronRightIcon />
          </IconButton>
        </Box>
        <Grid container spacing={0}>
          {dayNames.map((day) => (
            <Grid item xs={12 / 7} key={day}>
              <Typography
                variant="caption"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  textAlign: "center",
                  display: "block",
                  fontWeight: "bold",
                }}
              >
                {day}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Calendar Grid */}
      <Grid container spacing={0} sx={{ mb: 2 }}>
        {calendarDays.map((day, index) => (
          <Grid item xs={12 / 7} key={index}>
            <Box
              onClick={() =>
                day &&
                onDateClick(
                  day,
                  currentMonth,
                  currentYear,
                  getEventsForDate(day)
                )
              }
              sx={{
                height: 45,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                cursor: day ? "pointer" : "default",
                borderRadius: "4px",
                backgroundColor: isToday(day) ? "#db4a41" : "transparent",
                color: isToday(day) ? "white" : "rgba(255, 255, 255, 0.8)",
                border: hasEvents(day)
                  ? "2px solid #db4a41"
                  : "1px solid transparent",
                position: "relative",
                "&:hover": day
                  ? {
                      backgroundColor: isToday(day)
                        ? "#db4a41"
                        : "rgba(255, 255, 255, 0.1)",
                    }
                  : {},
              }}
            >
              <Typography variant="body2">{day}</Typography>
              {hasEvents(day) && (
                <Box
                  sx={{
                    width: 6,
                    height: 6,
                    borderRadius: "50%",
                    backgroundColor: isToday(day) ? "white" : "#db4a41",
                    mt: 0.5,
                  }}
                />
              )}
            </Box>
          </Grid>
        ))}
      </Grid>
    </>
  );

  const renderYearView = () => (
    <Box>
      <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
        <IconButton
          onClick={() => navigateYear("prev")}
          sx={{ color: "white" }}
        >
          <ChevronLeftIcon />
        </IconButton>
        <Typography
          variant="h5"
          sx={{
            color: "#db4a41",
            textAlign: "center",
            flex: 1,
            cursor: "pointer",
          }}
          onClick={() => setViewMode("month")}
        >
          {currentYear}
        </Typography>
        <IconButton
          onClick={() => navigateYear("next")}
          sx={{ color: "white" }}
        >
          <ChevronRightIcon />
        </IconButton>
      </Box>

      <Grid container spacing={1}>
        {monthNames.map((month, index) => (
          <Grid item xs={3} key={index}>
            <Paper
              sx={{
                background: "rgba(255, 255, 255, 0.05)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                borderRadius: "8px",
                p: 1,
                textAlign: "center",
                cursor: "pointer",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
              onClick={() => {
                setCurrentMonth(index);
                setViewMode("month");
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: index === currentMonth ? "#db4a41" : "white",
                  fontWeight: index === currentMonth ? "bold" : "normal",
                }}
              >
                {month}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        height: "100%",
      }}
    >
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
        >
          <Typography
            variant="h6"
            sx={{
              color: "white",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <CalendarTodayIcon sx={{ color: "#db4a41" }} />
            Calendar
          </Typography>
        </Box>

        {/* Calendar Tabs */}
        <Box
          sx={{
            borderBottom: 1,
            borderColor: "rgba(255, 255, 255, 0.1)",
            mb: 2,
          }}
        >
          <Tabs
            value={calendarTab}
            onChange={(e, newValue) => setCalendarTab(newValue)}
            sx={{
              "& .MuiTab-root": {
                color: "rgba(255, 255, 255, 0.7)",
                "&.Mui-selected": {
                  color: "#db4a41",
                },
              },
              "& .MuiTabs-indicator": {
                backgroundColor: "#db4a41",
              },
            }}
          >
            {canSeeGeneralTab() && (
              <Tab label="General" icon={<BusinessIcon />} />
            )}
            {canSeeTeamTab() && <Tab label="Team" icon={<WorkIcon />} />}
            <Tab label="Individual" icon={<PersonIcon />} />
          </Tabs>
        </Box>

        <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

        {viewMode === "month" ? renderMonthView() : renderYearView()}

        {/* Upcoming Events/Tasks */}
        <Typography variant="subtitle2" sx={{ color: "#db4a41", mb: 1 }}>
          {(() => {
            const getTabType = () => {
              if (canSeeGeneralTab() && calendarTab === 0) return "general";
              if (canSeeTeamTab() && canSeeGeneralTab() && calendarTab === 1)
                return "team";
              if (canSeeTeamTab() && !canSeeGeneralTab() && calendarTab === 0)
                return "team";
              return "individual";
            };

            const tabType = getTabType();
            if (tabType === "general") return "Upcoming Events";
            if (tabType === "team")
              return `Upcoming Team Events (${user?.role
                ?.replace("_", " ")
                .toUpperCase()})`;
            return "Upcoming Individual Tasks";
          })()}
        </Typography>
        <Box sx={{ maxHeight: 200, overflowY: "auto" }}>
          {loading ? (
            <Box sx={{ textAlign: "center", py: 2 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Loading...
              </Typography>
            </Box>
          ) : (
            (() => {
              const getTabType = () => {
                if (canSeeGeneralTab() && calendarTab === 0) return "general";
                if (canSeeTeamTab() && canSeeGeneralTab() && calendarTab === 1)
                  return "team";
                if (canSeeTeamTab() && !canSeeGeneralTab() && calendarTab === 0)
                  return "team";
                return "individual";
              };

              const tabType = getTabType();

              if (tabType === "general") {
                return renderCalendarEvents(events, "general");
              } else if (tabType === "team") {
                return renderCalendarEvents(teamEvents, "team", teamLoading);
              } else {
                return renderCalendarEvents(individualEvents, "individual");
              }
            })()
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

const TasksCard = ({ tasks, loading, onMarkComplete }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "#d32f2f";
      case "in_progress":
        return "#1D5AE7FF";
      case "needs_info":
        return "#9c27b0";
      case "completed":
        return "#4caf50";
      case "on_hold":
        return "#607d8b";
      case "cancelled":
        return "#f44336";
      default:
        return "#757575";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "#d32f2f";
      case "medium":
        return "#ed6c02";
      case "low":
        return "#2e7d32";
      default:
        return "#757575";
    }
  };

  const handleTaskComplete = async (task) => {
    // Only allow completion if task is in_progress
    if (task.status !== "in_progress") {
      return;
    }

    try {
      await onMarkComplete(task._id);
    } catch (error) {
      console.error("Failed to mark task complete:", error);
    }
  };

  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        height: "100%",
      }}
    >
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
        >
          <Typography
            variant="h6"
            sx={{
              color: "white",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <AssignmentIcon sx={{ color: "#db4a41" }} />
            My Tasks
          </Typography>
          <Chip
            label={`${
              tasks.filter((t) => t.status !== "completed").length
            } pending`}
            size="small"
            sx={{
              backgroundColor: "rgba(219, 74, 65, 0.2)",
              color: "#db4a41",
              border: "1px solid #db4a41",
            }}
          />
        </Box>
        <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

        <Box sx={{ maxHeight: "100%", overflowY: "auto" }}>
          {loading ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Loading tasks...
              </Typography>
            </Box>
          ) : tasks.length === 0 ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                No tasks assigned
              </Typography>
            </Box>
          ) : (
            tasks.map((task) => {
              const isCompleted =
                task.status === "completed" || task.status === "passed";

              return (
                <Box
                  key={task._id}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    mb: 1.5,
                    p: 1.5,
                    borderRadius: "8px",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    opacity: isCompleted ? 0.6 : 1,
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={() => handleTaskComplete(task)}
                    disabled={
                      task.status === "completed" || task.status === "passed"
                    }
                    sx={{
                      color: isCompleted
                        ? "#4CAF50"
                        : task.status === "in_progress"
                        ? "#2196F3"
                        : "rgba(255, 255, 255, 0.5)",
                      cursor:
                        task.status === "in_progress" ||
                        task.status === "pending"
                          ? "pointer"
                          : "default",
                      "&:hover": {
                        backgroundColor:
                          task.status === "in_progress" ||
                          task.status === "pending"
                            ? "rgba(33, 150, 243, 0.1)"
                            : "transparent",
                      },
                    }}
                  >
                    {isCompleted && task.status !== "passed" ? (
                      <CheckCircleIcon />
                    ) : task.status === "in_progress" ? (
                      <IconButton
                        size="small"
                        onClick={() => handleTaskComplete(task)}
                        sx={{
                          color: "#2196F3",
                          cursor: "pointer",
                          "&:hover": {
                            backgroundColor: "rgba(33, 150, 243, 0.1)",
                          },
                        }}
                      >
                        <RadioButtonUncheckedIcon />
                      </IconButton>
                    ) : (
                      <RadioButtonUncheckedIcon />
                    )}
                  </IconButton>
                  <Box flex={1}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "white",
                        textDecoration: isCompleted ? "line-through" : "none",
                      }}
                    >
                      {task.title}
                    </Typography>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        mt: 0.5,
                      }}
                    >
                      <Chip
                        label={task.priority || "medium"}
                        size="small"
                        sx={{
                          backgroundColor: `${getPriorityColor(
                            task.priority || "medium"
                          )}20`,
                          color: getPriorityColor(task.priority || "medium"),
                          fontSize: "0.7rem",
                          height: "20px",
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                      >
                        Due:{" "}
                        {task.dueDate
                          ? new Date(task.dueDate).toLocaleDateString()
                          : "No due date"}
                      </Typography>
                    </Box>
                    {task.clientId && (
                      <Typography
                        variant="caption"
                        sx={{
                          color: "rgba(255, 255, 255, 0.5)",
                          display: "block",
                          mt: 0.5,
                        }}
                      >
                        Client: {task.clientId.name}
                      </Typography>
                    )}
                  </Box>
                  <Box
                    onClick={(e) => {
                      e.preventDefault();
                      window.location.href = `/admin/tasks`;
                    }}
                  >
                    <Chip
                      label={task.status || "pending"}
                      size="small"
                      sx={{
                        backgroundColor: `${getStatusColor(
                          task.status || "pending"
                        )}20`,
                        color: getStatusColor(task.status || "pending"),
                        fontSize: "0.7rem",
                        height: "20px",
                      }}
                    />
                    <ArrowForwardIcon
                      sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                    />
                  </Box>
                </Box>
              );
            })
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

const DayDetailsDialog = ({
  open,
  onClose,
  selectedDay,
  selectedDate,
  events,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          background: "rgba(0, 0, 0, 0.9)",
          backdropFilter: "blur(20px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "15px",
        },
      }}
    >
      <DialogTitle
        sx={{
          color: "white",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <CalendarTodayIcon sx={{ color: "#db4a41" }} />
          <Typography variant="h6">{selectedDate}</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: "white" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {events && events.length > 0 ? (
          <Box>
            <Typography
              variant="subtitle1"
              sx={{ color: "#db4a41", mb: 2, fontFamily: "formula bold" }}
            >
              {events[0].type === "Meeting" || events[0].type === "Presentation"
                ? "Events"
                : events[0].type === "Shoot"
                ? "Shoots"
                : "Tasks"}{" "}
              for this day:
            </Typography>
            {events.map((item) => {
              // Handle different data structures
              const isTaskManagement = item.type === "TaskManagement";
              const isShoot = item.type === "Shoot";
              const isCompleted =
                (isTaskManagement &&
                  item.referenceId?.status === "completed") ||
                item.referenceId?.status === "passed";

              return (
                <Paper
                  key={item._id || item.id}
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "8px",
                    p: 2,
                    mb: 1,
                    position: "relative",
                    opacity: isCompleted ? 0.6 : 1,
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                    },
                  }}
                >
                  {/* Created By Avatar - Only show if createdBy exists */}
                  {item.createdBy && (
                    <Stack
                      direction="row"
                      alignItems="center"
                      spacing={1}
                      sx={{
                        position: "absolute",
                        top: 10,
                        right: 10,
                        marginBottom: "40px",
                      }}
                    >
                      <Tooltip
                        key={item.createdBy._id}
                        title={item.createdBy.name}
                      >
                        <Avatar
                          sx={{ bgcolor: "#00BFFF", color: "#191970" }}
                          src={item.createdBy.profilePicture}
                        >
                          {item.createdBy.name && item.createdBy.name[0]}
                        </Avatar>
                      </Tooltip>
                    </Stack>
                  )}
                  {/* Title with strikethrough for completed tasks */}
                  <Typography
                    variant="h5"
                    sx={{
                      color: "white",
                      fontFamily: "Formula Bold",
                      textDecoration: isCompleted ? "line-through" : "none",
                    }}
                  >
                    {item.title}
                  </Typography>

                  {/* Type */}
                  <Typography
                    variant="subtitle2"
                    sx={{ color: "white", fontFamily: "Anton" }}
                  >
                    Type: {item.type}
                  </Typography>

                  {/* Client and Project Info - Different for TaskManagement vs Shoot */}
                  {isTaskManagement && item.referenceId?.clientId && (
                    <Typography
                      variant="subtitle2"
                      sx={{
                        color: "white",
                        fontFamily: "Anton",
                        textDecoration: isCompleted ? "line-through" : "none",
                      }}
                    >
                      {item.referenceId.clientId.name || "No client"} /{" "}
                      {item.referenceId.projectId?.title || "No project"}
                    </Typography>
                  )}

                  {/* Shoot specific info */}
                  {isShoot && (
                    <>
                      <Typography
                        variant="subtitle2"
                        sx={{ color: "white", fontFamily: "Anton" }}
                      >
                        Location:{" "}
                        {item.location ||
                          item.referenceId?.location ||
                          "No location"}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.7)",
                          fontFamily: "Anton",
                        }}
                      >
                        Time: {item.referenceId?.startTime || "N/A"} -{" "}
                        {item.referenceId?.endTime || "N/A"}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "rgba(255, 255, 255, 0.7)",
                          fontFamily: "Anton",
                        }}
                      >
                        Status: {item.referenceId?.status || "Unknown"}
                      </Typography>
                    </>
                  )}

                  {/* Description */}
                  {item.referenceId?.description && (
                    <Typography
                      variant="body2"
                      sx={{
                        color: "rgba(255, 255, 255, 0.6)",
                        mt: 1,
                        fontFamily: "Anton",
                        textDecoration: isCompleted ? "line-through" : "none",
                      }}
                    >
                      {item.referenceId.description}
                    </Typography>
                  )}

                  {/* Priority Chip - Only for TaskManagement */}
                  {isTaskManagement && item.referenceId?.priority && (
                    <Chip
                      label={item.referenceId.priority}
                      size="small"
                      sx={{
                        backgroundColor: `${
                          item.referenceId.priority === "high"
                            ? "#d32f2f"
                            : item.referenceId.priority === "medium"
                            ? "#ed6c02"
                            : "#2e7d32"
                        }20`,
                        color:
                          item.referenceId.priority === "high"
                            ? "#d32f2f"
                            : item.referenceId.priority === "medium"
                            ? "#ed6c02"
                            : "#2e7d32",
                        mt: 1,
                        fontFamily: "Anton",
                      }}
                    />
                  )}

                  {/* Status Chip for completed tasks */}
                  {isCompleted && (
                    <Chip
                      label="Completed"
                      size="small"
                      sx={{
                        backgroundColor: "#4CAF5020",
                        color: "#4CAF50",
                        mt: 1,
                        fontFamily: "Anton",
                      }}
                    />
                  )}
                  {/* Assigned To Avatars */}
                  <div>
                    {" "}
                    <br></br>
                  </div>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={-2}
                    sx={{
                      position: "absolute",
                      bottom: 10,
                      right: 10,
                      marginTop: 2,
                    }}
                  >
                    {item.assignedTo?.slice(0, 4).map((user, index) => (
                      <Tooltip key={user._id} title={user.name}>
                        <Avatar
                          sx={{
                            bgcolor: `hsl(${index * 30}, 70%, 50%)`,
                            color: `hsl(${index * 30}, 70%, 20%)`,
                            width: 30,
                            height: 30,
                            fontSize: "0.8rem",
                            border: "2px solid #fff",
                          }}
                          src={user.profilePicture}
                        >
                          {user.name && user.name[0]}
                        </Avatar>
                      </Tooltip>
                    ))}

                    {/* Show +N if more than 4 */}
                    {item.assignedTo?.length > 4 && (
                      <Avatar
                        sx={{
                          bgcolor: "#9e9e9e",
                          color: "#fff",
                          width: 30,
                          height: 30,
                          fontSize: "0.8rem",
                          border: "2px solid #fff",
                        }}
                      >
                        +{item.assignedTo.length - 4}
                      </Avatar>
                    )}
                  </Stack>
                </Paper>
              );
            })}
          </Box>
        ) : (
          <Typography
            sx={{
              color: "rgba(255, 255, 255, 0.7)",
              textAlign: "center",
              py: 4,
            }}
          >
            No events or tasks scheduled for this day.
          </Typography>
        )}
      </DialogContent>
    </Dialog>
  );
};

const RecentProjectsCard = ({ projects, loading }) => {
  const navigate = useNavigate();
  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "#4CAF50";
      case "in-progress":
        return "#2196F3";
      case "review":
        return "#FF9800";
      case "planning":
        return "#9C27B0";
      default:
        return "#757575";
    }
  };

  const handleProjectClick = () => {
    navigate("/admin/system/projects");
  };

  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        height: "100%",
      }}
    >
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
        >
          <Typography
            variant="h6"
            sx={{
              color: "white",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <WorkIcon sx={{ color: "#db4a41" }} />
            Recent Projects
          </Typography>
          <Tooltip title="View all projects">
            <IconButton sx={{ color: "white" }} onClick={handleProjectClick}>
              <ArrowForwardIcon />
            </IconButton>
          </Tooltip>
        </Box>
        <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

        <Box sx={{ maxHeight: 350, overflowY: "auto" }}>
          {loading ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Loading projects...
              </Typography>
            </Box>
          ) : projects.length === 0 ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                No recent projects
              </Typography>
            </Box>
          ) : (
            projects.map((project) => (
              <Paper
                key={project._id}
                onClick={handleProjectClick}
                sx={{
                  background: "rgba(255, 255, 255, 0.05)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  borderRadius: "12px",
                  p: 2,
                  mb: 2,
                  cursor: "pointer",
                  transition: "transform 0.2s",
                  "&:hover": {
                    transform: "translateX(5px)",
                    backgroundColor: "rgba(255, 255, 255, 0.08)",
                  },
                }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar
                    sx={{
                      background: `${getStatusColor(project.status)}20`,
                      color: getStatusColor(project.status),
                      width: 40,
                      height: 40,
                    }}
                  >
                    <WorkIcon />
                  </Avatar>
                  <Box flex={1}>
                    <Typography variant="subtitle2" sx={{ color: "white" }}>
                      {project.title}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      {project.clientId?.name || "No client"}
                    </Typography>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        mt: 1,
                      }}
                    >
                      <Chip
                        label={project.status}
                        size="small"
                        sx={{
                          backgroundColor: `${getStatusColor(
                            project.status
                          )}20`,
                          color: getStatusColor(project.status),
                          fontSize: "0.7rem",
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                      >
                        {new Date(project.startDate).toLocaleDateString()} -{" "}
                        {new Date(project.endDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Paper>
            ))
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

const DeadlinesCard = ({ calendarEvents, tasks, loading }) => {
  const { user } = useUser();

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "#d32f2f";
      case "medium":
        return "#ed6c02";
      case "low":
        return "#2e7d32";
      default:
        return "#757575";
    }
  };

  // Check if user is general manager
  const isGeneralManager = () => {
    return user?.role === "general_manager";
  };

  // Calculate deadlines within 5 days
  const getUpcomingDeadlines = () => {
    const now = new Date();
    const fiveDaysFromNow = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000);

    const upcomingDeadlines = [];

    // Add calendar events ending within 5 days
    // Filter events based on user role and assignment
    const filteredEvents = isGeneralManager()
      ? calendarEvents
      : calendarEvents.filter((event) => {
          // Show events where user is assigned or created by user
          return (
            event.assignedTo?.some(
              (assignee) =>
                (typeof assignee === "string" ? assignee : assignee._id) ===
                user?.id
            ) ||
            (typeof event.createdBy === "string"
              ? event.createdBy
              : event.createdBy?._id) === user?.id
          );
        });

    filteredEvents.forEach((event) => {
      if (event.end) {
        const endDate = new Date(event.end);
        if (endDate >= now && endDate <= fiveDaysFromNow) {
          upcomingDeadlines.push({
            id: event._id,
            title: event.title,
            date: endDate,
            type: "event",
            priority: "medium",
            eventType: event.type,
          });
        }
      }
    });

    // Add tasks due within 5 days (tasks are already filtered to user's tasks)
    tasks.forEach((task) => {
      if (task.dueDate && task.status !== "completed") {
        const dueDate = new Date(task.dueDate);
        if (dueDate >= now && dueDate <= fiveDaysFromNow) {
          upcomingDeadlines.push({
            id: task._id,
            title: task.title,
            date: dueDate,
            type: "task",
            priority: task.priority || "medium",
            clientName: task.clientId?.name,
          });
        }
      }
    });

    // Sort by date (earliest first)
    return upcomingDeadlines.sort((a, b) => a.date - b.date);
  };

  const deadlines = getUpcomingDeadlines();

  // const getUrgencyColor = (daysLeft) => {
  //   if (daysLeft <= 1) return "#d32f2f";
  //   if (daysLeft <= 3) return "#ed6c02";
  //   if (daysLeft <= 7) return "#2196F3";
  //   return "#4CAF50";
  // };

  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        height: "100%",
      }}
    >
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
        >
          <Typography
            variant="h6"
            sx={{
              color: "white",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <ScheduleIcon sx={{ color: "#db4a41" }} />
            Deadlines
          </Typography>
          <Chip
            label={`${
              deadlines.filter((d) => {
                const daysLeft = Math.ceil(
                  (d.date - new Date()) / (1000 * 60 * 60 * 24)
                );
                return daysLeft <= 3;
              }).length
            } urgent`}
            size="small"
            sx={{
              backgroundColor: "rgba(211, 47, 47, 0.2)",
              color: "#d32f2f",
              border: "1px solid #d32f2f",
            }}
          />
        </Box>
        <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

        <Box sx={{ maxHeight: 350, overflowY: "auto" }}>
          {loading ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Loading deadlines...
              </Typography>
            </Box>
          ) : deadlines.length === 0 ? (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                No upcoming deadlines
              </Typography>
            </Box>
          ) : (
            deadlines.map((deadline) => {
              const daysLeft = Math.ceil(
                (deadline.date - new Date()) / (1000 * 60 * 60 * 24)
              );
              const urgencyColor =
                daysLeft <= 1
                  ? "#d32f2f"
                  : daysLeft <= 3
                  ? "#ed6c02"
                  : "#2e7d32";

              return (
                <Paper
                  key={deadline.id}
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                    p: 2,
                    mb: 2,
                    transition: "transform 0.2s",
                    "&:hover": {
                      transform: "translateX(5px)",
                    },
                  }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Box
                      sx={{
                        background: `${urgencyColor}20`,
                        borderRadius: "8px",
                        p: 1,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      {deadline.type === "event" ? (
                        <EventIcon sx={{ color: urgencyColor }} />
                      ) : (
                        <AssignmentIcon sx={{ color: urgencyColor }} />
                      )}
                    </Box>
                    <Box flex={1}>
                      <Typography variant="subtitle2" sx={{ color: "white" }}>
                        {deadline.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                      >
                        {deadline.type === "event"
                          ? `Event: ${deadline.eventType}`
                          : deadline.clientName
                          ? `Client: ${deadline.clientName}`
                          : "Task"}
                      </Typography>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          mt: 1,
                        }}
                      >
                        <Chip
                          label={`${daysLeft} days left`}
                          size="small"
                          sx={{
                            backgroundColor: `${urgencyColor}20`,
                            color: urgencyColor,
                            fontSize: "0.7rem",
                          }}
                        />
                        <Chip
                          label={deadline.priority}
                          size="small"
                          sx={{
                            backgroundColor: `${getPriorityColor(
                              deadline.priority
                            )}20`,
                            color: getPriorityColor(deadline.priority),
                            fontSize: "0.7rem",
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Paper>
              );
            })
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

const NotificationsCard = ({ notifications }) => {
  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        height: "100%",
      }}
    >
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
        >
          <Typography
            variant="h6"
            sx={{
              color: "white",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <Badge badgeContent={unreadCount} color="error">
              <NotificationsIcon sx={{ color: "#db4a41" }} />
            </Badge>
            Notifications
          </Typography>
        </Box>
        <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

        <Box sx={{ maxHeight: 350, overflowY: "auto" }}>
          {notifications.map((notification) => (
            <Paper
              key={notification.id}
              sx={{
                background: notification.read
                  ? "rgba(255, 255, 255, 0.02)"
                  : "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(10px)",
                border: `1px solid ${
                  notification.read
                    ? "rgba(255, 255, 255, 0.05)"
                    : "rgba(255, 255, 255, 0.15)"
                }`,
                borderRadius: "12px",
                p: 2,
                mb: 1.5,
                transition: "transform 0.2s",
                "&:hover": {
                  transform: "translateX(5px)",
                },
              }}
            >
              <Box display="flex" alignItems="center" gap={2}>
                {(() => {
                  const config = getNotificationConfig(notification.type);
                  return (
                    <Box
                      sx={{
                        background: config.bgColor,
                        borderRadius: "8px",
                        p: 1,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      {React.cloneElement(config.icon, {
                        sx: { color: config.color },
                      })}
                    </Box>
                  );
                })()}
                <Box flex={1}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: notification.read
                        ? "rgba(255, 255, 255, 0.7)"
                        : "white",
                      fontWeight: notification.read ? "normal" : "bold",
                    }}
                  >
                    {notification.message}
                  </Typography>
                  <Box
                    display="flex"
                    justifyContent="space-between"
                    flexDirection="row"
                  >
                    <Typography
                      variant="caption"
                      sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                    >
                      {!notification.read
                        ? getRelativeTime(notification.createdAt)
                        : new Date(notification.createdAt).toLocaleString()}
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        color: "rgba(255, 255, 255, 0.5)",
                        fontWeight: "bold",
                      }}
                    >
                      {new Date(notification.createdAt).toLocaleDateString()}
                    </Typography>
                  </Box>
                  {notification.relatedData && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: getNotificationConfig(notification.type).color,
                        display: "block",
                        mt: 0.5,
                        fontWeight: "bold",
                      }}
                    >
                      {notification.relatedType}:{" "}
                      {notification.relatedData.title}
                    </Typography>
                  )}
                </Box>
                {!notification.read && (
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: "50%",
                      backgroundColor: "#db4a41",
                    }}
                  />
                )}
              </Box>
            </Paper>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

const NewDashboard = () => {
  const [selectedDay, setSelectedDay] = useState(null);
  const [selectedDate, setSelectedDate] = useState("");
  const [dayEvents, setDayEvents] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Real data state
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [userTasks, setUserTasks] = useState([]);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);

  // Notifications state
  const [notifications, setNotifications] = useState([]);
  const [notificationsLoading, setNotificationsLoading] = useState(false);
  const [notificationAnchorEl, setNotificationAnchorEl] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const { user } = useUser();

  // API Functions
  const fetchCalendarEvents = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      const response = await axios.get(
        `${API_BASE_URL}/system/calendar`,
        config
      );
      setCalendarEvents(response.data);
      console.log("Fetched calendar events:", response.data);
    } catch (error) {
      console.error("Error fetching calendar events:", error);
    }
  }, []);

  const fetchUserTasks = useCallback(async () => {
    try {
      if (!user?.id) {
        console.log("No user ID available for fetching tasks");
        return;
      }

      console.log("Fetching tasks for user ID:", user.id);

      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      console.log(
        "Request URL:",
        `${API_BASE_URL}/tasks/assignedTo/${user.id}`
      );
      console.log("Auth token present:", !!token);

      const response = await axios.get(
        `${API_BASE_URL}/tasks/assignedTo/${user.id}`,
        config
      );

      console.log("Fetched user tasks:", response.data);
      console.log("Tasks count:", response.data?.length || 0);
      setUserTasks(response.data);
    } catch (error) {
      console.error("Error fetching user tasks:", error);
      console.error("Error details:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });
    }
  }, [user?.id]);

  const fetchUserCalendarEvents = useCallback(async () => {
    try {
      if (!user?.id) return;

      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      const response = await axios.get(
        `${API_BASE_URL}/system/calendar/user/${user.id}`,
        config
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching user calendar events:", error);
      return [];
    }
  }, [user?.id]);

  const fetchProjects = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      console.log("Fetching projects...");

      const response = await axios.get(`${API_BASE_URL}/projects`, config);

      // Get last 3 projects (most recent)
      const lastThreeProjects = response.data.slice(-3).reverse();

      console.log("Fetched projects:", lastThreeProjects);
      setProjects(lastThreeProjects);
    } catch (error) {
      console.error("Error fetching projects:", error);
    }
  }, []);

  // Mark task as complete
  const markTaskComplete = useCallback(
    async (taskId) => {
      try {
        const token = localStorage.getItem("token");
        const config = token
          ? { headers: { Authorization: `Bearer ${token}` } }
          : {};

        console.log("Marking task complete:", taskId);

        const response = await axios.patch(
          `${API_BASE_URL}/tasks/${taskId}/complete`,
          {},
          config
        );

        console.log("Task marked complete:", response.data);

        // Refresh tasks after marking complete
        await fetchUserTasks();

        return response.data;
      } catch (error) {
        console.error("Error marking task complete:", error);
        console.error("Error details:", {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
        throw error;
      }
    },
    [fetchUserTasks]
  );
  // Notifications API Functions
  const fetchNotifications = useCallback(async () => {
    if (!user?.id) {
      console.log("No user ID available for fetching notifications");
      return;
    }

    setNotificationsLoading(true);
    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      const response = await axios.get(
        `${API_BASE_URL}/system/notifications/user/${user.id}`,
        config
      );
      setNotifications(response.data);
      console.log("Fetched notifications:", response.data);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      showSnackbar("Failed to fetch notifications", "error");
    } finally {
      setNotificationsLoading(false);
    }
  }, [user?.id]);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchCalendarEvents(),
        fetchUserTasks(),
        fetchProjects(),
        fetchNotifications(),
      ]);
      setLoading(false);
    };

    if (user) {
      loadData();
    }
  }, [
    user,
    fetchCalendarEvents,
    fetchUserTasks,
    fetchProjects,
    fetchNotifications,
  ]);

  // Create calendar event function
  const createCalendarEvent = useCallback(
    async (eventData) => {
      try {
        const token = localStorage.getItem("token");
        const config = token
          ? { headers: { Authorization: `Bearer ${token}` } }
          : {};

        const response = await axios.post(
          `${API_BASE_URL}/system/calendar`,
          eventData,
          config
        );

        // Refresh calendar events after creation
        await fetchCalendarEvents();

        return response.data;
      } catch (error) {
        console.error("Error creating calendar event:", error);
        throw error;
      }
    },
    [fetchCalendarEvents]
  );

  const markNotificationAsRead = useCallback(async (notificationId) => {
    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      await axios.patch(
        `${API_BASE_URL}/system/notifications/${notificationId}/read`,
        {},
        config
      );

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) =>
          notification._id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );

      showSnackbar("Notification marked as read", "success");
    } catch (error) {
      console.error("Error marking notification as read:", error);
      showSnackbar("Failed to mark notification as read", "error");
    }
  }, []);

  const markAllNotificationsAsRead = useCallback(async () => {
    if (!user?.id) return;

    try {
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      await axios.patch(
        `${API_BASE_URL}/system/notifications/user/${user.id}/read-all`,
        {},
        config
      );

      // Update local state
      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true }))
      );

      showSnackbar("All notifications marked as read", "success");
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      showSnackbar("Failed to mark all notifications as read", "error");
    }
  }, [user?.id]);

  // Notification handlers
  const handleNotificationClick = (event) => {
    console.log("Notification bell clicked", event.currentTarget);
    setNotificationAnchorEl(event.currentTarget);
  };

  const handleNotificationClose = () => {
    console.log("Notification popup closed");
    setNotificationAnchorEl(null);
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleDateClick = (day, month, year, events) => {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];

    setSelectedDay(day);
    setSelectedDate(`${monthNames[month]} ${day}, ${year}`);
    setDayEvents(events);
    setDialogOpen(true);
  };

  return (
    <Box
      sx={{
        background: "black",
        minHeight: "100vh",
        p: 4,
      }}
    >
      {/* Header with Notifications */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 4,
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            flexGrow: 1,
            textAlign: "center",
          }}
        >
          Dashboard - Welcome {user?.name || "User"}
        </Typography>

        {/* Notifications Bell Icon */}
        <Tooltip title="Notifications">
          <IconButton
            onClick={handleNotificationClick}
            sx={{
              color: "white",
              "&:hover": {
                backgroundColor: "rgba(219, 74, 65, 0.1)",
              },
            }}
          >
            <Badge
              badgeContent={notifications.filter((n) => !n.read).length}
              color="error"
              max={99}
            >
              <NotificationsIcon />
            </Badge>
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={3}>
        {/* Top Row - Calendar and Tasks */}
        <Grid item xs={12} lg={6}>
          <CalendarCard
            events={calendarEvents}
            tasks={userTasks}
            onDateClick={handleDateClick}
            loading={loading}
            createCalendarEvent={createCalendarEvent}
            fetchUserCalendarEvents={fetchUserCalendarEvents}
            user={user}
          />
        </Grid>
        <Grid item xs={12} lg={6}>
          <TasksCard
            tasks={userTasks}
            loading={loading}
            onMarkComplete={markTaskComplete}
          />
        </Grid>

        {/* Bottom Row - Recent Projects, Deadlines, and Notifications */}
        <Grid item xs={12} lg={4}>
          <RecentProjectsCard projects={projects} loading={loading} />
        </Grid>
        <Grid item xs={12} lg={4}>
          <DeadlinesCard
            calendarEvents={calendarEvents}
            tasks={userTasks}
            loading={loading}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          <NotificationsCard notifications={notifications} />
        </Grid>
      </Grid>

      {/* Day Details Dialog */}
      <DayDetailsDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        selectedDay={selectedDay}
        selectedDate={selectedDate}
        events={dayEvents}
      />

      {/* Notifications Popup */}
      {console.log("Popover state:", {
        notificationAnchorEl,
        open: Boolean(notificationAnchorEl),
        notificationsCount: notifications.length,
      })}
      <Popover
        open={Boolean(notificationAnchorEl)}
        anchorEl={notificationAnchorEl}
        onClose={handleNotificationClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        slotProps={{
          paper: {
            sx: {
              background: "rgba(0, 0, 0, 0.95)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
              width: 400,
              maxHeight: 500,
            },
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          {/* Header */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                color: "white",
                fontFamily: "Formula Bold",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <NotificationsIcon sx={{ color: "#db4a41" }} />
              Notifications
              {notifications.filter((n) => !n.read).length > 0 && (
                <Badge
                  badgeContent={notifications.filter((n) => !n.read).length}
                  color="error"
                  sx={{ ml: 1 }}
                />
              )}
            </Typography>
            <Button
              size="small"
              onClick={markAllNotificationsAsRead}
              disabled={notifications.filter((n) => !n.read).length === 0}
              sx={{
                color: "#db4a41",
                "&:hover": {
                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                },
                "&:disabled": {
                  color: "rgba(255, 255, 255, 0.3)",
                },
              }}
            >
              Mark All Read
            </Button>
          </Box>

          <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />

          {/* Notifications List */}
          <Box sx={{ maxHeight: 400, overflowY: "auto" }}>
            {notificationsLoading ? (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Loading notifications...
                </Typography>
              </Box>
            ) : notifications.length === 0 ? (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  No notifications
                </Typography>
              </Box>
            ) : (
              <List sx={{ p: 0 }}>
                {notifications.map((notification) => (
                  <ListItemButton
                    key={notification._id}
                    onClick={() => markNotificationAsRead(notification._id)}
                    sx={{
                      borderRadius: "8px",
                      mb: 1,
                      backgroundColor: notification.read
                        ? "rgba(255, 255, 255, 0.02)"
                        : "rgba(255, 255, 255, 0.08)",
                      border: `1px solid ${
                        notification.read
                          ? "rgba(255, 255, 255, 0.05)"
                          : "rgba(219, 74, 65, 0.3)"
                      }`,
                      "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                      },
                    }}
                  >
                    <ListItemIcon>
                      {(() => {
                        const config = getNotificationConfig(notification.type);
                        return React.cloneElement(config.icon, {
                          sx: {
                            color: notification.read ? "#757575" : config.color,
                          },
                        });
                      })()}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography
                          variant="body2"
                          sx={{
                            color: notification.read
                              ? "rgba(255, 255, 255, 0.7)"
                              : "white",
                            fontWeight: notification.read ? "normal" : "bold",
                          }}
                        >
                          {notification.message}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography
                            variant="caption"
                            sx={{
                              color: "rgba(255, 255, 255, 0.5)",
                            }}
                          >
                            {notification.createdAt
                              ? !notification.read
                                ? getRelativeTime(notification.createdAt)
                                : new Date(
                                    notification.createdAt
                                  ).toLocaleString()
                              : "Just now"}
                          </Typography>
                          {notification.relatedData && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: getNotificationConfig(notification.type)
                                  .color,
                                display: "block",
                                fontWeight: "bold",
                              }}
                            >
                              {notification.relatedType}:{" "}
                              {notification.relatedData.title}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    {!notification.read && (
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: "50%",
                          backgroundColor: "#db4a41",
                          ml: 1,
                        }}
                      />
                    )}
                  </ListItemButton>
                ))}
              </List>
            )}
          </Box>
        </Box>
      </Popover>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default NewDashboard;
