import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  Tabs,
  Tab,
  InputAdornment,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import MeetingRoomIcon from "@mui/icons-material/MeetingRoom";
import PresentationIcon from "@mui/icons-material/Slideshow";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";

function Meetings() {
  const [meetings, setMeetings] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingMeeting, setEditingMeeting] = useState(null);
  const [viewingMeeting, setViewingMeeting] = useState(null);
  const [newMeeting, setNewMeeting] = useState({
    title: "",
    type: "Meeting",
    start: "",
    end: "",
    allDay: false,
    assignedTo: [],
    location: "",
    notes: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [assignedToFilter, setAssignedToFilter] = useState("");
  const [createdByFilter, setCreatedByFilter] = useState("");
  const { user } = useUser();

  const CALENDAR_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/calendar";
  const EMPLOYEES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees";

  const fetchMeetings = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");

      // Fetch both Meeting and Presentation events using the type endpoint
      const [meetingsResponse, presentationsResponse] = await Promise.all([
        fetch(`${CALENDAR_API_URL}/type/Meeting`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }),
        fetch(`${CALENDAR_API_URL}/type/Presentation`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }),
      ]);

      const meetingsData = await meetingsResponse.json();
      const presentationsData = await presentationsResponse.json();

      // Combine both arrays
      const allMeetings = [...meetingsData, ...presentationsData];
      setMeetings(allMeetings);
    } catch (error) {
      console.error("Error fetching meetings:", error);
      showSnackbar("Failed to fetch meetings", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchEmployees = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(EMPLOYEES_API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error("Error fetching employees:", error);
    }
  }, []);

  useEffect(() => {
    fetchMeetings();
    fetchEmployees();
  }, [fetchMeetings, fetchEmployees]);

  const handleAdd = () => {
    setEditingMeeting(null);
    setNewMeeting({
      title: "",
      type: "Meeting",
      start: "",
      end: "",
      allDay: false,
      assignedTo: [],
      location: "",
      notes: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (meeting) => {
    setEditingMeeting(meeting);
    setNewMeeting({
      title: meeting.title,
      type: meeting.type,
      start: new Date(meeting.start).toISOString().slice(0, 16),
      end: meeting.end ? new Date(meeting.end).toISOString().slice(0, 16) : "",
      allDay: meeting.allDay,
      assignedTo: meeting.assignedTo?.map((user) => user._id) || [],
      location: meeting.location || "",
      notes: meeting.notes || "",
    });
    setOpenModal(true);
  };

  const handleView = (meeting) => {
    setViewingMeeting(meeting);
    setOpenViewModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingMeeting(null);
    setNewMeeting({
      title: "",
      type: "Meeting",
      start: "",
      end: "",
      allDay: false,
      assignedTo: [],
      location: "",
      notes: "",
    });
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingMeeting(null);
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");

      const meetingData = {
        title: newMeeting.title,
        type: newMeeting.type,
        start: new Date(newMeeting.start).toISOString(),
        end: newMeeting.end ? new Date(newMeeting.end).toISOString() : null,
        allDay: newMeeting.allDay,
        assignedTo: newMeeting.assignedTo,
        createdBy: user?.id || user?._id,
        location: newMeeting.location,
        notes: newMeeting.notes,
      };

      const url = editingMeeting
        ? `${CALENDAR_API_URL}/${editingMeeting._id}`
        : CALENDAR_API_URL;

      const method = editingMeeting ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(meetingData),
      });

      if (response.ok) {
        const result = await response.json();
        if (editingMeeting) {
          setMeetings(
            meetings.map((m) => (m._id === editingMeeting._id ? result : m))
          );
          showSnackbar("Meeting updated successfully", "success");
        } else {
          setMeetings([result, ...meetings]);
          showSnackbar("Meeting created successfully", "success");
        }
        handleCloseModal();
        fetchMeetings(); // Refresh to get populated data
      } else {
        throw new Error("Failed to save meeting");
      }
    } catch (error) {
      console.error("Error saving meeting:", error);
      showSnackbar("Failed to save meeting", "error");
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this meeting?")) {
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${CALENDAR_API_URL}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        setMeetings(meetings.filter((meeting) => meeting._id !== id));
        showSnackbar("Meeting deleted successfully", "success");
      } else {
        throw new Error("Failed to delete meeting");
      }
    } catch (error) {
      console.error("Error deleting meeting:", error);
      showSnackbar("Failed to delete meeting", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getTypeColor = (type) => {
    switch (type) {
      case "Meeting":
        return "#2196f3";
      case "Presentation":
        return "#9c27b0";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  // Determine if the logged-in user is the creator of the meeting
  const isMeetingCreator = (meeting) => {
    const creator = meeting?.createdBy;
    const creatorId =
      typeof creator === "object" && creator !== null
        ? creator._id || creator.id
        : creator;
    const loggedInUserId = user?._id || user?.id;

    if (!creatorId || !loggedInUserId) return false;
    return String(creatorId) === String(loggedInUserId);
  };

  // Determine if the logged-in user is assigned to the meeting
  const isMeetingAssignedToUser = (meeting) => {
    const loggedInUserId = user?._id || user?.id;
    if (!loggedInUserId || !meeting?.assignedTo) return false;

    return meeting.assignedTo.some((assignee) => {
      const assigneeId =
        typeof assignee === "object" && assignee !== null
          ? assignee._id || assignee.id
          : assignee;
      return String(assigneeId) === String(loggedInUserId);
    });
  };

  // Check if user is general manager
  const isGeneralManager = () => {
    return user?.role === "general_manager";
  };

  // Check if user can assign all users (general_manager all tiers or account_manager tier 3)
  const canAssignAllUsers = () => {
    return (
      user?.role === "general_manager" ||
      (user?.role === "account_manager" && user?.tier === 3)
    );
  };

  // Filter meetings based on active tab and user role
  const getFilteredMeetings = () => {
    let filteredMeetings = [];

    if (isGeneralManager()) {
      // General manager can see different tabs
      switch (activeTab) {
        case 0: // My Meetings (created by or assigned to)
          filteredMeetings = meetings.filter(
            (meeting) =>
              isMeetingCreator(meeting) || isMeetingAssignedToUser(meeting)
          );
          break;
        case 1: // All Meetings
          filteredMeetings = meetings;
          break;
        default:
          filteredMeetings = meetings.filter(
            (meeting) =>
              isMeetingCreator(meeting) || isMeetingAssignedToUser(meeting)
          );
      }
    } else {
      // Non-general managers can only see their meetings
      filteredMeetings = meetings.filter(
        (meeting) =>
          isMeetingCreator(meeting) || isMeetingAssignedToUser(meeting)
      );
    }

    // Apply search filter
    if (searchTerm.trim()) {
      filteredMeetings = filteredMeetings.filter((meeting) =>
        meeting.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply date filter
    if (dateFilter) {
      const filterDate = new Date(dateFilter).toDateString();
      filteredMeetings = filteredMeetings.filter((meeting) => {
        const meetingDate = new Date(meeting.start).toDateString();
        return meetingDate === filterDate;
      });
    }

    // Apply type filter
    if (typeFilter) {
      filteredMeetings = filteredMeetings.filter(
        (meeting) => meeting.type === typeFilter
      );
    }

    // Apply assigned to filter (only for general managers)
    if (assignedToFilter && isGeneralManager()) {
      filteredMeetings = filteredMeetings.filter((meeting) =>
        meeting.assignedTo?.some((assignee) => {
          const assigneeId =
            typeof assignee === "object" && assignee !== null
              ? assignee._id || assignee.id
              : assignee;
          return String(assigneeId) === String(assignedToFilter);
        })
      );
    }

    // Apply created by filter (only for general managers)
    if (createdByFilter && isGeneralManager()) {
      filteredMeetings = filteredMeetings.filter((meeting) => {
        const creator = meeting?.createdBy;
        const creatorId =
          typeof creator === "object" && creator !== null
            ? creator._id || creator.id
            : creator;
        return String(creatorId) === String(createdByFilter);
      });
    }

    return filteredMeetings;
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setDateFilter("");
    setTypeFilter("");
    setAssignedToFilter("");
    setCreatedByFilter("");
  };

  // Assign all users to meeting
  const assignAllUsers = () => {
    if (canAssignAllUsers()) {
      const allUserIds = employees.map((emp) => emp._id);
      setNewMeeting({ ...newMeeting, assignedTo: allUserIds });
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Meetings & Presentations
          </Typography>
          {(user?.tier === 2 || user?.tier === 3) &&
            (user?.role === "account_manager" ||
              user?.role === "general_manager" ||
              user?.role === "graphic_designer") && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAdd}
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  fontFamily: "Formula Bold",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
              >
                Add Meeting
              </Button>
            )}
        </Box>

        {/* Tabs Section - Only for General Managers */}
        {isGeneralManager() && (
          <Box sx={{ padding: "0 5% 20px" }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              sx={{
                "& .MuiTabs-indicator": {
                  backgroundColor: "#db4a41",
                },
                "& .MuiTab-root": {
                  color: "rgba(255, 255, 255, 0.7)",
                  fontFamily: "Formula Bold",
                  textTransform: "none",
                  fontSize: "1rem",
                  "&.Mui-selected": {
                    color: "#db4a41",
                  },
                  "&:hover": {
                    color: "rgba(219, 74, 65, 0.8)",
                  },
                },
              }}
            >
              <Tab label="My Meetings" />
              <Tab label="All Meetings" />
            </Tabs>
          </Box>
        )}

        {/* Filters Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Grid container spacing={2} alignItems="center">
            {/* Search Bar */}
            <Grid item xs={12} md={isGeneralManager() ? 3 : 4}>
              <TextField
                fullWidth
                placeholder="Search meetings by title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: "rgba(255, 255, 255, 0.7)" }} />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setSearchTerm("")}
                        size="small"
                        sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                      >
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                  },
                  "& .MuiInputBase-input::placeholder": {
                    color: "rgba(255, 255, 255, 0.5)",
                  },
                }}
              />
            </Grid>

            {/* Date Filter */}
            <Grid item xs={12} sm={6} md={isGeneralManager() ? 2 : 3}>
              <TextField
                fullWidth
                label="Filter by Date"
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                InputLabelProps={{ shrink: true }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>

            {/* Type Filter */}
            <Grid item xs={12} sm={6} md={isGeneralManager() ? 2 : 3}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Type
                </InputLabel>
                <Select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  sx={{
                    color: "white",
                    backgroundColor: "rgba(255, 255, 255, 0.05)",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#db4a41",
                    },
                    "& .MuiSelect-icon": { color: "white" },
                  }}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="Meeting">Meeting</MenuItem>
                  <MenuItem value="Presentation">Presentation</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* General Manager Only Filters */}
            {isGeneralManager() && (
              <>
                {/* Assigned To Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Assigned To
                    </InputLabel>
                    <Select
                      value={assignedToFilter}
                      onChange={(e) => setAssignedToFilter(e.target.value)}
                      sx={{
                        color: "white",
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "& .MuiSelect-icon": { color: "white" },
                      }}
                    >
                      <MenuItem value="">All Assignees</MenuItem>
                      {employees.map((employee) => (
                        <MenuItem key={employee._id} value={employee._id}>
                          {employee.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Created By Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Created By
                    </InputLabel>
                    <Select
                      value={createdByFilter}
                      onChange={(e) => setCreatedByFilter(e.target.value)}
                      sx={{
                        color: "white",
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "& .MuiSelect-icon": { color: "white" },
                      }}
                    >
                      <MenuItem value="">All Creators</MenuItem>
                      {employees.map((employee) => (
                        <MenuItem key={employee._id} value={employee._id}>
                          {employee.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </>
            )}

            {/* Clear Filters Button */}
            <Grid item xs={12} md={isGeneralManager() ? 1 : 2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={clearFilters}
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Meeting Count Display */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: "rgba(255, 255, 255, 0.8)",
                    fontFamily: "Formula Bold",
                  }}
                >
                  {isGeneralManager() &&
                    activeTab === 0 &&
                    `My Meetings (${getFilteredMeetings().length})`}
                  {isGeneralManager() &&
                    activeTab === 1 &&
                    `All Meetings (${getFilteredMeetings().length})`}
                  {!isGeneralManager() &&
                    `My Meetings (${getFilteredMeetings().length})`}
                </Typography>
              </Box>

              {getFilteredMeetings().length === 0 ? (
                <Box
                  sx={{
                    textAlign: "center",
                    py: 8,
                    px: 4,
                  }}
                >
                  <Typography
                    variant="h5"
                    sx={{
                      color: "rgba(255, 255, 255, 0.6)",
                      fontFamily: "Formula Bold",
                      mb: 2,
                    }}
                  >
                    No meetings found
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "rgba(255, 255, 255, 0.4)",
                      mb: 3,
                    }}
                  >
                    {searchTerm ||
                    dateFilter ||
                    typeFilter ||
                    assignedToFilter ||
                    createdByFilter
                      ? "No meetings match your current filters."
                      : "You haven't created or been assigned to any meetings yet."}
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={3}>
                  <AnimatePresence>
                    {getFilteredMeetings().map((meeting) => (
                      <Grid item xs={12} sm={6} md={4} key={meeting._id}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card
                            sx={{
                              background: "rgba(255, 255, 255, 0.05)",
                              backdropFilter: "blur(10px)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                              borderRadius: "12px",
                              height: "100%",
                              display: "flex",
                              flexDirection: "column",
                              position: "relative",
                              "&:hover": {
                                background: "rgba(255, 255, 255, 0.08)",
                                borderColor: "rgba(219, 74, 65, 0.3)",
                              },
                            }}
                          >
                            <Box
                              sx={{
                                position: "absolute",
                                top: 8,
                                right: 8,
                                display: "flex",
                                gap: 0.5,
                                zIndex: 10,
                              }}
                            >
                              <Tooltip title="View">
                                <IconButton
                                  size="small"
                                  onClick={() => handleView(meeting)}
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.7)",
                                    "&:hover": { color: "#db4a41" },
                                  }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              {isMeetingCreator(meeting) && (
                                <>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(meeting)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(meeting._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </>
                              )}
                            </Box>

                            <CardContent sx={{ flexGrow: 1, pt: 5 }}>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  mb: 2,
                                }}
                              >
                                {meeting.type === "Meeting" ? (
                                  <MeetingRoomIcon
                                    sx={{
                                      color: getTypeColor(meeting.type),
                                      mr: 1,
                                    }}
                                  />
                                ) : (
                                  <PresentationIcon
                                    sx={{
                                      color: getTypeColor(meeting.type),
                                      mr: 1,
                                    }}
                                  />
                                )}
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontFamily: "Formula Bold",
                                    color: "white",
                                    fontSize: "1.1rem",
                                  }}
                                >
                                  {meeting.title}
                                </Typography>
                              </Box>

                              <Chip
                                label={meeting.type}
                                size="small"
                                sx={{
                                  backgroundColor: `${getTypeColor(
                                    meeting.type
                                  )}20`,
                                  color: getTypeColor(meeting.type),
                                  mb: 2,
                                  fontFamily: "Formula Bold",
                                }}
                              />

                              <Typography
                                variant="body2"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.8)",
                                  mb: 1,
                                  fontFamily: "Anton",
                                }}
                              >
                                📅{" "}
                                {new Date(meeting.start).toLocaleDateString()}{" "}
                                at{" "}
                                {new Date(meeting.start).toLocaleTimeString()}
                              </Typography>

                              {meeting.location && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.7)",
                                    mb: 1,
                                    fontFamily: "Anton",
                                  }}
                                >
                                  📍 {meeting.location}
                                </Typography>
                              )}

                              {meeting.assignedTo &&
                                meeting.assignedTo.length > 0 && (
                                  <Box sx={{ mt: 2 }}>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.6)",
                                        fontFamily: "Anton",
                                        display: "block",
                                        mb: 1,
                                      }}
                                    >
                                      Assigned to:
                                    </Typography>
                                    <Box
                                      sx={{
                                        display: "flex",
                                        flexWrap: "wrap",
                                        gap: 0.5,
                                      }}
                                    >
                                      {meeting.assignedTo
                                        .slice(0, 3)
                                        .map((user) => (
                                          <Tooltip
                                            key={user._id}
                                            title={user.name}
                                          >
                                            <Avatar
                                              sx={{
                                                width: 24,
                                                height: 24,
                                                fontSize: "0.7rem",
                                                bgcolor: stringToColor(
                                                  user.name
                                                ),
                                              }}
                                            >
                                              {user.name
                                                .charAt(0)
                                                .toUpperCase()}
                                            </Avatar>
                                          </Tooltip>
                                        ))}
                                      {meeting.assignedTo.length > 3 && (
                                        <Avatar
                                          sx={{
                                            width: 24,
                                            height: 24,
                                            fontSize: "0.6rem",
                                            bgcolor: "rgba(255, 255, 255, 0.2)",
                                          }}
                                        >
                                          +{meeting.assignedTo.length - 3}
                                        </Avatar>
                                      )}
                                    </Box>
                                  </Box>
                                )}
                            </CardContent>
                          </Card>
                        </motion.div>
                      </Grid>
                    ))}
                  </AnimatePresence>
                </Grid>
              )}
            </>
          )}
        </Box>

        {/* Create/Edit Meeting Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingMeeting ? "Edit Meeting" : "Create New Meeting"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title"
                  value={newMeeting.title}
                  onChange={(e) =>
                    setNewMeeting({ ...newMeeting, title: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Type
                  </InputLabel>
                  <Select
                    value={newMeeting.type}
                    onChange={(e) =>
                      setNewMeeting({ ...newMeeting, type: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="Meeting">Meeting</MenuItem>
                    <MenuItem value="Presentation">Presentation</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Location"
                  value={newMeeting.location}
                  onChange={(e) =>
                    setNewMeeting({ ...newMeeting, location: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Date & Time"
                  type="datetime-local"
                  value={newMeeting.start}
                  onChange={(e) =>
                    setNewMeeting({ ...newMeeting, start: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Date & Time"
                  type="datetime-local"
                  value={newMeeting.end}
                  onChange={(e) =>
                    setNewMeeting({ ...newMeeting, end: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Assign To
                  </InputLabel>
                  <Select
                    multiple
                    value={newMeeting.assignedTo}
                    onChange={(e) =>
                      setNewMeeting({
                        ...newMeeting,
                        assignedTo: e.target.value,
                      })
                    }
                    input={<OutlinedInput label="Assign To" />}
                    renderValue={(selected) =>
                      employees
                        .filter((emp) => selected.includes(emp._id))
                        .map((emp) => emp.name)
                        .join(", ")
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {employees.map((employee) => (
                      <MenuItem key={employee._id} value={employee._id}>
                        <Checkbox
                          checked={
                            newMeeting.assignedTo.indexOf(employee._id) > -1
                          }
                        />
                        <ListItemText primary={employee.name} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                {canAssignAllUsers() && (
                  <Box sx={{ mt: 1 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={assignAllUsers}
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.23)",
                        "&:hover": {
                          borderColor: "#db4a41",
                          color: "#db4a41",
                        },
                      }}
                    >
                      Assign All Users
                    </Button>
                  </Box>
                )}
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={newMeeting.notes}
                  onChange={(e) =>
                    setNewMeeting({ ...newMeeting, notes: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingMeeting ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Meeting Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Meeting Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingMeeting && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography
                    variant="h5"
                    sx={{ color: "white", fontFamily: "Formula Bold", mb: 2 }}
                  >
                    {viewingMeeting.title}
                  </Typography>
                  <Chip
                    label={viewingMeeting.type}
                    sx={{
                      backgroundColor: `${getTypeColor(viewingMeeting.type)}20`,
                      color: getTypeColor(viewingMeeting.type),
                      mb: 2,
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                  >
                    Start Date & Time:
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    {new Date(viewingMeeting.start).toLocaleString()}
                  </Typography>
                </Grid>
                {viewingMeeting.end && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                    >
                      End Date & Time:
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {new Date(viewingMeeting.end).toLocaleString()}
                    </Typography>
                  </Grid>
                )}
                {viewingMeeting.location && (
                  <Grid item xs={12}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                    >
                      Location:
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {viewingMeeting.location}
                    </Typography>
                  </Grid>
                )}
                {viewingMeeting.assignedTo &&
                  viewingMeeting.assignedTo.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                      >
                        Assigned To:
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {viewingMeeting.assignedTo.map((user) => (
                          <Chip
                            key={user._id}
                            label={user.name}
                            avatar={
                              <Avatar
                                sx={{ bgcolor: stringToColor(user.name) }}
                              >
                                {user.name.charAt(0).toUpperCase()}
                              </Avatar>
                            }
                            sx={{
                              backgroundColor: "rgba(255, 255, 255, 0.1)",
                              color: "white",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
                {viewingMeeting.notes && (
                  <Grid item xs={12}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                    >
                      Notes:
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {viewingMeeting.notes}
                    </Typography>
                  </Grid>
                )}
                {viewingMeeting.createdBy && (
                  <Grid item xs={12}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                    >
                      Created By:
                    </Typography>
                    <Chip
                      label={viewingMeeting.createdBy.name}
                      avatar={
                        <Avatar
                          sx={{
                            bgcolor: stringToColor(
                              viewingMeeting.createdBy.name
                            ),
                          }}
                        >
                          {viewingMeeting.createdBy.name
                            .charAt(0)
                            .toUpperCase()}
                        </Avatar>
                      }
                      sx={{
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        color: "white",
                      }}
                    />
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Meetings;
