import React, { useEffect, useMemo, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Chip,
  Divider,
  List,
  ListItemButton,
  ListItemText,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { useUser } from "../../contexts/UserContext";

const API_BASE =
  "https://youngproductions-768ada043db3.herokuapp.com/api/database";

function Database() {
  const { user } = useUser();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [createCategoryOpen, setCreateCategoryOpen] = useState(false);
  const [newCategory, setNewCategory] = useState({
    name: "",
    description: "",
    fields: [],
  });
  const [fieldDraft, setFieldDraft] = useState({
    label: "",
    type: "string",
    required: false,
    options: [],
  });

  const [selectedCategory, setSelectedCategory] = useState(null);
  const [entries, setEntries] = useState([]);
  const [entriesLoading, setEntriesLoading] = useState(false);
  const [entryDialogOpen, setEntryDialogOpen] = useState(false);
  const [entryForm, setEntryForm] = useState({});
  const [editingEntryId, setEditingEntryId] = useState(null);
  const [search, setSearch] = useState("");

  const token = useMemo(() => localStorage.getItem("token"), []);

  const headers = useMemo(
    () => ({
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    }),
    [token]
  );

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const closeSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE}/categories`, { headers });
      if (!res.ok) throw new Error("Failed to fetch categories");
      const data = await res.json();
      setCategories(data);
      if (!selectedCategory && data.length > 0) setSelectedCategory(data[0]);
    } catch (e) {
      showSnackbar("Failed to load categories", "error");
    } finally {
      setLoading(false);
    }
  }, [headers, selectedCategory]);

  const fetchEntries = useCallback(
    async (categoryId) => {
      if (!categoryId) return;
      setEntriesLoading(true);
      try {
        const res = await fetch(`${API_BASE}/entries/${categoryId}`, {
          headers,
        });
        if (!res.ok) throw new Error("Failed to fetch entries");
        const data = await res.json();
        setEntries(data);
      } catch (e) {
        showSnackbar("Failed to load entries", "error");
      } finally {
        setEntriesLoading(false);
      }
    },
    [headers]
  );

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    if (selectedCategory?._id) fetchEntries(selectedCategory._id);
  }, [selectedCategory?._id, fetchEntries]);

  const resetCategoryDraft = () => {
    setNewCategory({ name: "", description: "", fields: [] });
    setFieldDraft({ label: "", type: "string", required: false, options: [] });
  };

  const handleAddFieldToCategory = () => {
    if (!fieldDraft.label || !fieldDraft.type) return;
    const toAdd = {
      label: fieldDraft.label.trim(),
      type: fieldDraft.type,
      required: Boolean(fieldDraft.required),
    };
    if (fieldDraft.type === "select") {
      const opts = Array.isArray(fieldDraft.options)
        ? fieldDraft.options.map((o) => String(o).trim()).filter((o) => o)
        : [];
      toAdd.options = opts;
    }
    setNewCategory((c) => ({ ...c, fields: [...c.fields, toAdd] }));
    setFieldDraft({ label: "", type: "string", required: false, options: [] });
  };

  const handleRemoveField = (idx) => {
    setNewCategory((c) => ({
      ...c,
      fields: c.fields.filter((_, i) => i !== idx),
    }));
  };

  const handleCreateCategory = async () => {
    if (!newCategory.name.trim()) {
      showSnackbar("Category name is required", "error");
      return;
    }
    try {
      const res = await fetch(`${API_BASE}/categories`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          name: newCategory.name.trim(),
          description: newCategory.description || "",
          fields: newCategory.fields,
        }),
      });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.error || "Failed to create category");
      }
      showSnackbar("Category created");
      setCreateCategoryOpen(false);
      resetCategoryDraft();
      await fetchCategories();
    } catch (e) {
      showSnackbar(e.message, "error");
    }
  };

  const openNewEntryDialog = () => {
    if (!selectedCategory) return;
    const initial = {};
    selectedCategory.fields.forEach((f) => (initial[f.label] = ""));
    setEntryForm(initial);
    setEditingEntryId(null);
    setEntryDialogOpen(true);
  };

  const openEditEntryDialog = (entry) => {
    setEditingEntryId(entry._id);
    setEntryForm(entry.data || {});
    setEntryDialogOpen(true);
  };

  const handleSaveEntry = async () => {
    if (!selectedCategory) return;
    const payload = { category: selectedCategory._id, data: entryForm };
    try {
      if (editingEntryId) {
        const res = await fetch(`${API_BASE}/entries/${editingEntryId}`, {
          method: "PUT",
          headers,
          body: JSON.stringify({ data: entryForm }),
        });
        if (!res.ok) {
          const err = await res.json().catch(() => ({}));
          throw new Error(
            err.errors?.join(", ") || err.error || "Failed to update entry"
          );
        }
        showSnackbar("Entry updated");
      } else {
        const res = await fetch(`${API_BASE}/entries`, {
          method: "POST",
          headers,
          body: JSON.stringify(payload),
        });
        if (!res.ok) {
          const err = await res.json().catch(() => ({}));
          throw new Error(
            err.errors?.join(", ") || err.error || "Failed to create entry"
          );
        }
        showSnackbar("Entry created");
      }
      setEntryDialogOpen(false);
      setEditingEntryId(null);
      await fetchEntries(selectedCategory._id);
    } catch (e) {
      showSnackbar(e.message, "error");
    }
  };

  const handleDeleteEntry = async (entryId) => {
    if (!window.confirm("Delete this entry?")) return;
    try {
      const res = await fetch(`${API_BASE}/entries/${entryId}`, {
        method: "DELETE",
        headers,
      });
      if (!res.ok) throw new Error("Failed to delete entry");
      showSnackbar("Entry deleted");
      await fetchEntries(selectedCategory._id);
    } catch (e) {
      showSnackbar(e.message, "error");
    }
  };

  const filteredEntries = useMemo(() => {
    if (!search.trim()) return entries;
    const query = search.toLowerCase();
    return entries.filter((e) => {
      return Object.values(e.data || {}).some((v) =>
        String(v).toLowerCase().includes(query)
      );
    });
  }, [entries, search]);

  const renderInputForField = (field) => {
    const commonProps = {
      fullWidth: true,
      size: "small",
      value: entryForm[field.label] ?? "",
      onChange: (ev) =>
        setEntryForm((f) => ({ ...f, [field.label]: ev.target.value })),
      label: field.label + (field.required ? " *" : ""),
      sx: { mb: 2 },
    };

    switch (field.type) {
      case "number":
        return <TextField type="number" {...commonProps} />;
      case "date":
        return (
          <TextField
            type="date"
            {...commonProps}
            InputLabelProps={{ shrink: true }}
          />
        );
      case "boolean":
        return (
          <TextField select {...commonProps}>
            <MenuItem value="true">True</MenuItem>
            <MenuItem value="false">False</MenuItem>
          </TextField>
        );
      case "select":
        return (
          <TextField select {...commonProps}>
            {(field.options || []).map((opt) => (
              <MenuItem key={opt} value={opt}>
                {opt}
              </MenuItem>
            ))}
          </TextField>
        );
      default:
        return <TextField {...commonProps} />;
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.08) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: "60px 5% 20px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Database
          </Typography>
          {(user?.tier === 3 || user?.tier === 2) && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateCategoryOpen(true)}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                fontFamily: "Formula Bold",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              New Category
            </Button>
          )}
        </Box>

        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "280px 1fr",
            gap: 3,
            p: "0 5% 60px",
          }}
        >
          {/* Categories sidebar */}
          <Box
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              overflow: "hidden",
            }}
          >
            <Box sx={{ p: 2, borderBottom: "1px solid rgba(255,255,255,0.1)" }}>
              <Typography
                sx={{
                  fontFamily: "Formula Bold",
                  color: "rgba(255,255,255,0.8)",
                }}
              >
                Categories
              </Typography>
            </Box>
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                <CircularProgress sx={{ color: "#db4a41" }} size={24} />
              </Box>
            ) : (
              <List dense>
                {categories.map((cat) => (
                  <ListItemButton
                    key={cat._id}
                    selected={selectedCategory?._id === cat._id}
                    onClick={() => setSelectedCategory(cat)}
                    sx={{
                      "&.Mui-selected": {
                        background: "rgba(219, 74, 65, 0.15)",
                      },
                    }}
                  >
                    <ListItemText
                      primary={cat.name}
                      secondary={cat.description}
                      primaryTypographyProps={{ sx: { color: "white" } }}
                      secondaryTypographyProps={{
                        sx: { color: "rgba(255,255,255,0.6)" },
                      }}
                    />
                  </ListItemButton>
                ))}
                {categories.length === 0 && (
                  <Box sx={{ p: 2, color: "rgba(255,255,255,0.7)" }}>
                    No categories yet.
                  </Box>
                )}
              </List>
            )}
          </Box>

          {/* Entries area */}
          <Box>
            {selectedCategory ? (
              <>
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}
                >
                  <Typography variant="h5" sx={{ fontFamily: "Formula Bold" }}>
                    {selectedCategory.name}
                  </Typography>
                  <Chip
                    label={`${selectedCategory.fields?.length || 0} fields`}
                    size="small"
                    sx={{ background: "rgba(255,255,255,0.1)", color: "white" }}
                  />
                  <Box sx={{ flexGrow: 1 }} />
                  <TextField
                    size="small"
                    placeholder="Search entries"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    sx={{ minWidth: 260 }}
                  />
                  {user?.tier === 3 || user?.tier === 2 ? (
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={openNewEntryDialog}
                      sx={{
                        backgroundColor: "#db4a41",
                        color: "white",
                        fontFamily: "Formula Bold",
                        "&:hover": { backgroundColor: "#c62828" },
                      }}
                    >
                      Add Entry
                    </Button>
                  ) : null}
                </Box>

                <TableContainer
                  component={Paper}
                  sx={{
                    background: "rgba(255,255,255,0.05)",
                    border: "1px solid rgba(255,255,255,0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        {(selectedCategory.fields || []).map((f) => (
                          <TableCell
                            key={f.label}
                            sx={{ color: "rgba(255,255,255,0.9)" }}
                          >
                            {f.label}
                          </TableCell>
                        ))}
                        <TableCell
                          sx={{ color: "rgba(255,255,255,0.9)" }}
                          align="right"
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {entriesLoading ? (
                        <TableRow>
                          <TableCell
                            colSpan={(selectedCategory.fields || []).length + 1}
                            align="center"
                          >
                            <CircularProgress
                              sx={{ color: "#db4a41" }}
                              size={24}
                            />
                          </TableCell>
                        </TableRow>
                      ) : filteredEntries.length === 0 ? (
                        <TableRow>
                          <TableCell
                            colSpan={(selectedCategory.fields || []).length + 1}
                            sx={{ color: "rgba(255,255,255,0.7)" }}
                          >
                            No entries found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredEntries.map((entry) => (
                          <TableRow key={entry._id} hover>
                            {(selectedCategory.fields || []).map((f) => (
                              <TableCell
                                key={f.label}
                                sx={{ color: "rgba(255,255,255,0.85)" }}
                              >
                                {String(entry.data?.[f.label] ?? "")}
                              </TableCell>
                            ))}
                            <TableCell align="right">
                              {user?.tier === 3 || user?.tier === 2 ? (
                                <>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      onClick={() => openEditEntryDialog(entry)}
                                      size="small"
                                      sx={{ color: "white" }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      onClick={() =>
                                        handleDeleteEntry(entry._id)
                                      }
                                      size="small"
                                      sx={{ color: "#f44336" }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </>
                              ) : (
                                <span style={{ color: "#db4a41" }}>
                                  Not allowed
                                </span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            ) : (
              <Box sx={{ color: "rgba(255,255,255,0.7)" }}>
                Select a category to view entries.
              </Box>
            )}
          </Box>
        </Box>
      </Box>

      {/* Create Category Dialog */}
      <Dialog
        open={createCategoryOpen}
        onClose={() => setCreateCategoryOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(18,18,18,0.98)",
            border: "1px solid rgba(255,255,255,0.1)",
            color: "white",
            backdropFilter: "blur(6px)",
          },
        }}
      >
        <DialogTitle sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}>
          New Category
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            "& .MuiDivider-root": { borderColor: "rgba(255,255,255,0.12)" },
          }}
        >
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            value={newCategory.name}
            onChange={(e) =>
              setNewCategory((c) => ({ ...c, name: e.target.value }))
            }
            InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
            InputProps={{
              sx: {
                color: "white",
                background: "rgba(255,255,255,0.06)",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(255,255,255,0.18)",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(219,74,65,0.6)",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#db4a41",
                },
              },
            }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            value={newCategory.description}
            onChange={(e) =>
              setNewCategory((c) => ({ ...c, description: e.target.value }))
            }
            InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
            InputProps={{
              sx: {
                color: "white",
                background: "rgba(255,255,255,0.06)",
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(255,255,255,0.18)",
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(219,74,65,0.6)",
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#db4a41",
                },
              },
            }}
          />

          <Divider sx={{ my: 2 }} />
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1,
              color: "rgba(255,255,255,0.9)",
              fontFamily: "Formula Bold",
            }}
          >
            Fields
          </Typography>

          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "1fr 160px 120px",
              gap: 1,
            }}
          >
            <TextField
              label="Label"
              value={fieldDraft.label}
              onChange={(e) =>
                setFieldDraft((f) => ({ ...f, label: e.target.value }))
              }
              InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
              InputProps={{
                sx: {
                  color: "white",
                  background: "rgba(255,255,255,0.06)",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255,255,255,0.18)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(219,74,65,0.6)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                },
              }}
            />
            <TextField
              select
              label="Type"
              value={fieldDraft.type}
              onChange={(e) =>
                setFieldDraft((f) => ({ ...f, type: e.target.value }))
              }
              InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
              InputProps={{
                sx: {
                  color: "white",
                  background: "rgba(255,255,255,0.06)",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255,255,255,0.18)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(219,74,65,0.6)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                },
              }}
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    sx: {
                      background: "#1a1a1a",
                      color: "white",
                      border: "1px solid rgba(255,255,255,0.1)",
                    },
                  },
                },
              }}
            >
              {[
                { v: "string", l: "String" },
                { v: "number", l: "Number" },
                { v: "date", l: "Date" },
                { v: "boolean", l: "Boolean" },
                { v: "select", l: "Select" },
              ].map((t) => (
                <MenuItem key={t.v} value={t.v}>
                  {t.l}
                </MenuItem>
              ))}
            </TextField>
            <TextField
              select
              label="Required"
              value={fieldDraft.required ? "yes" : "no"}
              onChange={(e) =>
                setFieldDraft((f) => ({
                  ...f,
                  required: e.target.value === "yes",
                }))
              }
              InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
              InputProps={{
                sx: {
                  color: "white",
                  background: "rgba(255,255,255,0.06)",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255,255,255,0.18)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(219,74,65,0.6)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                },
              }}
              SelectProps={{
                MenuProps: {
                  PaperProps: {
                    sx: {
                      background: "#1a1a1a",
                      color: "white",
                      border: "1px solid rgba(255,255,255,0.1)",
                    },
                  },
                },
              }}
            >
              <MenuItem value="no">No</MenuItem>
              <MenuItem value="yes">Yes</MenuItem>
            </TextField>
          </Box>

          {fieldDraft.type === "select" && (
            <TextField
              sx={{ mt: 1 }}
              label="Options (comma separated)"
              fullWidth
              value={(fieldDraft.options || []).join(", ")}
              onChange={(e) =>
                setFieldDraft((f) => ({
                  ...f,
                  options: e.target.value.split(",").map((s) => s.trim()),
                }))
              }
              InputLabelProps={{ sx: { color: "rgba(255,255,255,0.8)" } }}
              InputProps={{
                sx: {
                  color: "white",
                  background: "rgba(255,255,255,0.06)",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255,255,255,0.18)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(219,74,65,0.6)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                },
              }}
            />
          )}

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}>
            <Button
              onClick={handleAddFieldToCategory}
              startIcon={<AddIcon />}
              sx={{ color: "#db4a41" }}
            >
              Add field
            </Button>
          </Box>

          <List dense>
            {newCategory.fields.map((f, idx) => (
              <Box
                key={`${f.label}-${idx}`}
                sx={{ display: "flex", alignItems: "center", gap: 1, py: 0.5 }}
              >
                <Chip
                  label={f.label}
                  sx={{ background: "rgba(255,255,255,0.08)", color: "white" }}
                />
                <Chip
                  label={f.type}
                  variant="outlined"
                  sx={{
                    borderColor: "rgba(255,255,255,0.2)",
                    color: "rgba(255,255,255,0.9)",
                  }}
                />
                {f.required && (
                  <Chip label="required" color="error" size="small" />
                )}
                {f.type === "select" && (
                  <Chip
                    label={(f.options || []).join(" | ")}
                    size="small"
                    sx={{
                      background: "rgba(255,255,255,0.06)",
                      color: "white",
                    }}
                  />
                )}
                <Box sx={{ flexGrow: 1 }} />
                <IconButton
                  onClick={() => handleRemoveField(idx)}
                  size="small"
                  sx={{ color: "rgba(255,255,255,0.8)" }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            ))}
            {newCategory.fields.length === 0 && (
              <Typography
                variant="body2"
                sx={{ color: "rgba(255,255,255,0.6)", mt: 1 }}
              >
                No fields yet. Add at least one (optional).
              </Typography>
            )}
          </List>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setCreateCategoryOpen(false)}
            sx={{ color: "rgba(255,255,255,0.85)" }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateCategory}
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            Create Category
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create/Edit Entry Dialog */}
      <Dialog
        open={entryDialogOpen}
        onClose={() => setEntryDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{editingEntryId ? "Edit Entry" : "New Entry"}</DialogTitle>
        <DialogContent dividers>
          {(selectedCategory?.fields || []).map((field) => (
            <Box key={field.label}>{renderInputForField(field)}</Box>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEntryDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSaveEntry}
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            {editingEntryId ? "Save Changes" : "Create Entry"}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={snackbar.severity}
          sx={{
            background: snackbar.severity === "success" ? "#2e7d32" : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Database;
