{"headers": [{"source": "/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/fonts/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/:file*.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/:file*.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/favicon.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/:path*", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}]}], "rewrites": [{"source": "/", "destination": "/index.html"}, {"source": "/:path*", "destination": "/index.html"}]}