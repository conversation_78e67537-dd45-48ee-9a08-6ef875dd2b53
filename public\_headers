# Cache static assets for 1 year (immutable)
/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images and videos for 1 year
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache fonts for 1 year
/fonts/*
  Cache-Control: public, max-age=31536000, immutable

# Cache JS files for 1 year (immutable)
/static/js/*.js
  Cache-Control: public, max-age=31536000, immutable

# Cache CSS files for 1 year (immutable)
/static/css/*.css
  Cache-Control: public, max-age=31536000, immutable

# Cache media files for 1 year
/static/media/*
  Cache-Control: public, max-age=31536000, immutable

# Cache build assets
/build/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache build assets
/build/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache JS and CSS in root
/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Cache-Control: public, max-age=31536000, immutable

# Cache favicon for 1 day
/favicon.ico
  Cache-Control: public, max-age=86400

# Cache HTML for short time (allows updates)
/*.html
  Cache-Control: public, max-age=3600, must-revalidate

# Security headers
/*
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block

