import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    const scroll = () => {
      const lenis = window.lenis;
      if (lenis) {
        lenis.scrollTo(0, { immediate: true }); // Lenis scroll
      } else {
        window.scrollTo(0, 0); // fallback
      }
    };

    // Use requestAnimationFrame for smoother timing after route change
    const raf = requestAnimationFrame(() => scroll());

    return () => cancelAnimationFrame(raf);
  }, [pathname]);

  return null;
};

export default ScrollToTop;
