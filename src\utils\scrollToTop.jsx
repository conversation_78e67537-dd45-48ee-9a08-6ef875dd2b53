import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import Lenis from "@studio-freight/lenis";

const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Get the active Lenis instance
    const lenis = window.lenis;

    // Delay to wait for route transition animation
    setTimeout(() => {
      if (lenis) {
        lenis.scrollTo(0, { immediate: true }); // or smooth by removing immediate
      } else {
        // fallback
        window.scrollTo({ top: 0, behavior: "instant" });
      }
    }, 100);
  }, [pathname]);

  return null;
};

export default ScrollToTop;
