import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  Box,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CheckIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Cancel";
import EditIcon from "@mui/icons-material/Edit";
import PaymentIcon from "@mui/icons-material/Payment";
import { useUser } from "../../contexts/UserContext";

function Quotations() {
  const { user } = useUser();
  const [quotations, setQuotations] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [cycles, setCycles] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectionType, setSelectionType] = useState("cycle"); // "cycle" or "project"
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingQuotation, setEditingQuotation] = useState(null);
  const [viewingQuotation, setViewingQuotation] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [filters, setFilters] = useState({
    status: "",
    client_id: "",
    cycle_id: "",
  });

  const role = useMemo(() => {
    return user?.role || localStorage.getItem("role") || "employee";
  }, [user]);

  const isManager = useMemo(() => {
    const privileged = [
      "finance_manager",
      "general_manager",
      "admin",
      "manager",
    ];
    return privileged.includes(role);
  }, [role]);

  const canEditQuotation = useMemo(() => {
    return (quotation) => {
      const currentUserId = user?._id || user?.id;
      const isCreator =
        quotation.created_by?._id === currentUserId ||
        quotation.created_by === currentUserId;
      const isGeneralManager = role === "general_manager";
      const isTier3 = role === "tier_3";
      // Allow edit when the quotation status is not "approved" AND staging_status is not "approved"
      return (
        (isCreator || isGeneralManager || isTier3) &&
        quotation.status !== "approved" &&
        quotation.staging_status !== "approved"
      );
    };
  }, [role, user]);
  // Create/Edit quotation local state
  const emptyItems = {
    equipment: [],
    props: [],
    models: [],
    day_rates: [],
    others: [],
  };
  const [newQuotation, setNewQuotation] = useState({
    client_id: "",
    cycle_id: "",
    project_id: "",
    items: emptyItems,
    note: "",
  });
  const [decision, setDecision] = useState({
    open: false,
    id: null,
    action: "approve",
    reason: "",
  });
  const [stagingDecision, setStagingDecision] = useState({
    open: false,
    id: null,
    action: "approved",
    reason: "",
  });
  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/quotations"; // placeholders
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";
  const CYCLES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles";
  const PROJECTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/one-time-projects";

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "approved":
        return "#4caf50";
      case "rejected":
        return "#f44336";
      case "requested":
      default:
        return "#ff9800";
    }
  };

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const fetchQuotations = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      setQuotations(result.data || result || []);
    } catch (err) {
      console.error("Error fetching quotations:", err);
      showSnackbar("Failed to fetch quotations", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const list = result?.data || (Array.isArray(result) ? result : []);
      setClientAccounts(list);
    } catch (err) {
      console.error("Error fetching client accounts:", err);
      setClientAccounts([]);
    }
  }, []);

  const fetchCyclesByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setCycles([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${CYCLES_API_URL}/${clientId}/get-client-cycles`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      const result = await response.json();

      // ✅ endpoint returns { success: true, cycles: [...] }
      setCycles(result.cycles || []);
    } catch (err) {
      console.error("Error fetching client cycles:", err);
      setCycles([]);
    }
  }, []);

  const fetchProjectsByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setProjects([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${PROJECTS_API_URL}/client/${clientId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();

      // Handle the response structure based on the provided data format
      if (result.success && result.data) {
        // If single project returned, wrap in array
        const projectsData = Array.isArray(result.data)
          ? result.data
          : [result.data];
        setProjects(projectsData);
      } else {
        setProjects([]);
      }
    } catch (err) {
      console.error("Error fetching client projects:", err);
      setProjects([]);
    }
  }, []);

  // when client changes, fetch cycles and projects
  useEffect(() => {
    if (newQuotation.client_id) {
      fetchCyclesByClient(newQuotation.client_id);
      fetchProjectsByClient(newQuotation.client_id);
    }
  }, [newQuotation.client_id, fetchCyclesByClient, fetchProjectsByClient]);

  useEffect(() => {
    fetchQuotations();
    fetchClientAccounts();
    fetchCyclesByClient();
  }, [fetchQuotations, fetchClientAccounts, fetchCyclesByClient]);

  const filteredQuotations = useMemo(() => {
    return (quotations || []).filter((q) => {
      if (filters.status && q.status !== filters.status) return false;
      if (
        filters.client_id &&
        (q.client_id?._id || q.client_id) !== filters.client_id
      )
        return false;
      if (
        filters.cycle_id &&
        (q.cycle_id?._id || q.cycle_id) !== filters.cycle_id
      )
        return false;
      // role based: if employee, only created_by matches
      // if (!isManager) {
      //   const currentUserId = user?._id || user?.id;
      //   if (
      //     currentUserId &&
      //     q.created_by?._id !== currentUserId &&
      //     q.created_by !== currentUserId
      //   )
      //     return false;
      // }
      return true;
    });
  }, [quotations, filters, isManager, user]);

  // Calculate totals on client side for preview UI only
  const computeTotals = (items) => {
    let totals = {
      equipment: 0,
      props: 0,
      models: 0,
      day_rates: 0,
      others: 0,
      agency_fee: 0,
    };
    items.equipment?.forEach((eq) => {
      totals.equipment +=
        (parseFloat(eq.price) || 0) * (parseFloat(eq.qty) || 0);
    });
    items.props?.forEach((p) => {
      totals.props += (parseFloat(p.price) || 0) * (parseFloat(p.qty) || 0);
    });
    items.models?.forEach((m) => {
      totals.models += parseFloat(m.rate) || 0;
    });
    items.day_rates?.forEach((d) => {
      totals.day_rates += parseFloat(d.rate) || 0;
    });
    items.others?.forEach((o) => {
      totals.others +=
        (parseFloat(o.price) || 0) * (parseFloat(o.qty || 1) || 1);
    });
    totals.agency_fee = totals.models * 0.1;
    const grand =
      totals.equipment +
      totals.props +
      totals.models +
      totals.day_rates +
      totals.others +
      totals.agency_fee;
    return { totals, grand };
  };

  const { grand } = useMemo(
    () => computeTotals(newQuotation.items),
    [newQuotation.items]
  );

  const handleAdd = () => {
    setEditingQuotation(null);
    setNewQuotation({
      client_id: "",
      cycle_id: "",
      items: JSON.parse(JSON.stringify(emptyItems)),
      note: "",
    });
    setOpenModal(true);
  };

  const handleView = async (q) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${q._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const full = result?.data || q;
      setViewingQuotation(full);
      setOpenViewModal(true);
    } catch (e) {
      console.error("Failed to fetch quotation details", e);
      setViewingQuotation(q);
      setOpenViewModal(true);
    }
  };

  const handleEdit = async (q) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${q._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const full = result?.data || q;

      // Determine selection type based on what's present
      const selectionType = full.project_id ? "project" : "cycle";

      // Helper function to extract price values from different formats
      const extractPrice = (price) => {
        if (price === null || price === undefined) return "";
        if (typeof price === "object" && price.$numberDecimal) {
          return price.$numberDecimal;
        }
        return price.toString();
      };

      // Pre-populate the form with existing data
      setNewQuotation({
        client_id: full.client_id?._id || full.client_id || "",
        cycle_id: full.cycle_id?._id || full.cycle_id || "",
        project_id: full.project_id?._id || full.project_id || "",
        items: {
          equipment: (full.items?.equipment || []).map((item) => ({
            tool: item.tool || "",
            price: extractPrice(item.price),
            qty: item.qty || 1,
          })),
          props: (full.items?.props || []).map((item) => ({
            item: item.item || "",
            price: extractPrice(item.price),
            qty: item.qty || 1,
          })),
          models: (full.items?.models || []).map((item) => ({
            name: item.name || "",
            rate: extractPrice(item.rate),
          })),
          day_rates: (full.items?.day_rates || []).map((item) => ({
            name: item.name || "",
            rate: extractPrice(item.rate),
            contact: item.contact || "",
          })),
          others: (full.items?.others || []).map((item) => ({
            description: item.description || "",
            price: extractPrice(item.price),
            qty: item.qty || 1,
          })),
        },
        note: full.note || "",
      });

      setSelectionType(selectionType);
      setEditingQuotation(full);
      setOpenModal(true);
    } catch (e) {
      console.error("Failed to fetch quotation details for editing", e);
      showSnackbar("Failed to load quotation for editing", "error");
    }
  };
  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingQuotation(null);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingQuotation(null);
    setNewQuotation({
      client_id: "",
      cycle_id: "",
      project_id: "",
      items: emptyItems,
      note: "",
    });
    setSelectionType("cycle");
    setCycles([]);
    setProjects([]);
  };

  const handleSelectionTypeChange = (event, newType) => {
    if (newType !== null) {
      setSelectionType(newType);
      // Clear the opposite selection when switching
      setNewQuotation((prev) => ({
        ...prev,
        cycle_id: newType === "cycle" ? prev.cycle_id : "",
        project_id: newType === "project" ? prev.project_id : "",
      }));
    }
  };

  const addItem = (listName) => {
    setNewQuotation((prev) => {
      const copy = {
        ...prev,
        items: { ...prev.items, [listName]: [...(prev.items[listName] || [])] },
      };
      switch (listName) {
        case "equipment":
          copy.items.equipment.push({ tool: "", price: "", qty: 1 });
          break;
        case "props":
          copy.items.props.push({ item: "", price: "", qty: 1 });
          break;
        case "models":
          copy.items.models.push({ name: "", rate: "" });
          break;
        case "day_rates":
          copy.items.day_rates.push({ name: "", rate: "", contact: "" });
          break;
        case "others":
          copy.items.others.push({ description: "", price: "", qty: 1 });
          break;
        default:
          break;
      }
      return copy;
    });
  };

  const updateItem = (listName, index, field, value) => {
    setNewQuotation((prev) => {
      const list = [...(prev.items[listName] || [])];
      list[index] = { ...list[index], [field]: value };
      return { ...prev, items: { ...prev.items, [listName]: list } };
    });
  };

  const removeItem = (listName, index) => {
    setNewQuotation((prev) => {
      const list = [...(prev.items[listName] || [])];
      list.splice(index, 1);
      return { ...prev, items: { ...prev.items, [listName]: list } };
    });
  };

  const handleCreate = async () => {
    try {
      const token = localStorage.getItem("token");
      // Normalize numeric fields and compute total_cost to match backend schema
      const toNumber = (v) =>
        v === "" || v === null || v === undefined ? 0 : parseFloat(v);
      const normalizedItems = {
        equipment: (newQuotation.items.equipment || []).map((it) => ({
          tool: it.tool,
          price: toNumber(it.price),
          qty: parseInt(it.qty || 0),
        })),
        props: (newQuotation.items.props || []).map((it) => ({
          item: it.item,
          price: toNumber(it.price),
          qty: parseInt(it.qty || 0),
        })),
        models: (newQuotation.items.models || []).map((it) => ({
          name: it.name,
          rate: toNumber(it.rate),
        })),
        day_rates: (newQuotation.items.day_rates || []).map((it) => ({
          name: it.name,
          rate: toNumber(it.rate),
          contact: it.contact || "",
        })),
        others: (newQuotation.items.others || []).map((it) => ({
          description: it.description || "",
          price: toNumber(it.price),
          qty: parseInt(it.qty || 1),
        })),
      };
      const totalsCalc = computeTotals(normalizedItems);

      // Validate required fields based on selection type
      const hasValidSelection =
        selectionType === "cycle"
          ? newQuotation.cycle_id
          : newQuotation.project_id;

      if (!newQuotation.client_id || !hasValidSelection) {
        showSnackbar(`Please select a client and ${selectionType}`, "warning");
        return;
      }

      const payload = {
        client_id: newQuotation.client_id,
        items: normalizedItems,
        total_cost: totalsCalc.grand,
        created_by: user?._id || user?.id,
      };

      // Add either cycle_id or project_id based on selection type
      if (selectionType === "cycle") {
        payload.cycle_id = newQuotation.cycle_id;
      } else {
        payload.project_id = newQuotation.project_id;
      }

      // Handle update vs create
      if (editingQuotation) {
        // Update existing quotation
        const response = await fetch(
          `${API_BASE_URL}/${editingQuotation._id}`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          const body = await response.json().catch(() => ({}));
          throw new Error(body.message || "Failed to update quotation");
        }

        await fetchQuotations();
        showSnackbar("Quotation updated successfully", "success");
      } else {
        // Create new quotation
        const response = await fetch(API_BASE_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const body = await response.json().catch(() => ({}));
          throw new Error(body.message || "Failed to create quotation");
        }

        await fetchQuotations();
        showSnackbar("Quotation requested successfully", "success");
      }

      setOpenModal(false);
    } catch (err) {
      console.error(err);
      showSnackbar(err.message || "Failed to process quotation", "error");
    }
  };

  const openDecision = (qId, action) =>
    setDecision({ open: true, id: qId, action, reason: "" });
  const closeDecision = () =>
    setDecision({ open: false, id: null, action: "approve", reason: "" });

  const openStagingDecision = (qId, action) =>
    setStagingDecision({ open: true, id: qId, action, reason: "" });
  const closeStagingDecision = () =>
    setStagingDecision({
      open: false,
      id: null,
      action: "approved",
      reason: "",
    });

  const submitDecision = async () => {
    try {
      const token = localStorage.getItem("token");
      const url = `${API_BASE_URL}/${decision.id}/${decision.action}`; // e.g. /:id/approve or /:id/reject
      const isApprove = decision.action === "approve";
      const body = isApprove
        ? { approved_by: user?._id || user?.id }
        : {
            rejection_reason: decision.reason,
            rejected_by: user?._id || user?.id,
          };
      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) throw new Error("Failed to submit decision");
      await fetchQuotations();
      showSnackbar(`Quotation ${decision.action}d`, "success");
      closeDecision();
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to submit decision", "error");
    }
  };

  const submitStagingDecision = async () => {
    try {
      const token = localStorage.getItem("token");
      const url = `${API_BASE_URL}/${stagingDecision.id}/staging-status`;
      const body = {
        staging_status: stagingDecision.action,
        user_id: user?._id || user?.id,
      };

      // Add rejection reason if rejecting
      if (stagingDecision.action === "rejected") {
        body.rejection_reason = stagingDecision.reason;
      }

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update staging status");
      }

      await fetchQuotations();
      showSnackbar(
        `Quotation staging status updated to ${stagingDecision.action}`,
        "success"
      );
      closeStagingDecision();
    } catch (err) {
      console.error(err);
      showSnackbar(err.message || "Failed to update staging status", "error");
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Quotations
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            New Request
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={filters.status}
                      onChange={(e) =>
                        setFilters({ ...filters, status: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      <MenuItem value="requested">Requested</MenuItem>
                      <MenuItem value="approved">Approved</MenuItem>
                      <MenuItem value="rejected">Rejected</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Client
                    </InputLabel>
                    <Select
                      value={filters.client_id}
                      onChange={(e) =>
                        setFilters({ ...filters, client_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(clientAccounts) &&
                        clientAccounts.map((c) => (
                          <MenuItem key={c._id} value={c._id}>
                            {c.client_name}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Cycle
                    </InputLabel>
                    <Select
                      value={filters.cycle_id}
                      onChange={(e) =>
                        setFilters({ ...filters, cycle_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(cycles) &&
                        cycles.map((cy) => (
                          <MenuItem key={cy._id} value={cy._id}>
                            {cy.cycle_name ||
                              `${cy.client_id?.client_name} - ${cy.month}`}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{ background: "transparent" }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Client
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Cycle
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Total
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Requested
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredQuotations
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((q) => (
                            <TableRow
                              key={q._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                {q.client_id?.client_name || "-"}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {q.cycle_id?.cycle_name ||
                                  `${q.cycle_id?.month || "-"}`}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={q.status}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getStatusColor(
                                      q.status
                                    )}20`,
                                    color: getStatusColor(q.status),
                                    textTransform: "capitalize",
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(q.total_cost)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {q.dates?.requested_at
                                  ? new Date(
                                      q.dates.requested_at
                                    ).toLocaleDateString()
                                  : "-"}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(q)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  {canEditQuotation(q) && (
                                    <Tooltip title="Edit">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleEdit(q)}
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          "&:hover": { color: "#ff9800" },
                                        }}
                                      >
                                        <EditIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                  {isManager &&
                                    q.staging_status === "requested" && (
                                      <>
                                        <Tooltip title="Approve Staging">
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              openStagingDecision(
                                                q._id,
                                                "approved"
                                              )
                                            }
                                            sx={{
                                              color: "rgba(255, 255, 255, 0.7)",
                                              "&:hover": { color: "#4caf50" },
                                            }}
                                          >
                                            <CheckIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Reject Staging">
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              openStagingDecision(
                                                q._id,
                                                "rejected"
                                              )
                                            }
                                            sx={{
                                              color: "rgba(255, 255, 255, 0.7)",
                                              "&:hover": { color: "#f44336" },
                                            }}
                                          >
                                            <CloseIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                      </>
                                    )}
                                  {role === "general_manager" &&
                                    q.staging_status === "approved" && (
                                      <Tooltip title="Pay">
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            openDecision(q._id, "approve")
                                          }
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                            "&:hover": { color: "#2196f3" },
                                          }}
                                        >
                                          <PaymentIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredQuotations.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingQuotation ? "Edit Quotation" : "Create New Quotation"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newQuotation.client_id}
                    onChange={(e) =>
                      setNewQuotation({
                        ...newQuotation,
                        client_id: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((c) => (
                        <MenuItem key={c._id} value={c._id}>
                          {c.client_name}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Toggle Button for Cycle/Project Selection */}
              <Grid item xs={12}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)", mr: 2 }}
                  >
                    Select Type:
                  </Typography>
                  <ToggleButtonGroup
                    value={selectionType}
                    exclusive
                    onChange={handleSelectionTypeChange}
                    sx={{
                      "& .MuiToggleButton-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&.Mui-selected": {
                          backgroundColor: "#db4a41",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#c43a31",
                          },
                        },
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                        },
                      },
                    }}
                  >
                    <ToggleButton value="cycle">
                      Subscription Cycle
                    </ToggleButton>
                    <ToggleButton value="project">
                      One-Time Project
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    {selectionType === "cycle"
                      ? "Subscription Cycle"
                      : "One-Time Project"}
                  </InputLabel>
                  <Select
                    value={
                      selectionType === "cycle"
                        ? newQuotation.cycle_id
                        : newQuotation.project_id
                    }
                    onChange={(e) =>
                      setNewQuotation({
                        ...newQuotation,
                        [selectionType === "cycle" ? "cycle_id" : "project_id"]:
                          e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {selectionType === "cycle"
                      ? // Render Cycles
                        Array.isArray(cycles) &&
                        cycles
                          .filter((cy) => {
                            if (user.role === "general_manager") {
                              return true; // GM sees all
                            }

                            const now = new Date();
                            const currentMonth = String(
                              now.getMonth() + 1
                            ).padStart(2, "0");
                            const currentYear = now.getFullYear();

                            if (cy.year && cy.month) {
                              // case 1: explicit fields
                              return (
                                String(cy.year) === String(currentYear) &&
                                String(cy.month).padStart(2, "0") ===
                                  currentMonth
                              );
                            }

                            if (cy.start_date) {
                              // case 2: start_date exists
                              const cycleDate = new Date(cy.start_date);
                              return (
                                cycleDate.getFullYear() === currentYear &&
                                String(cycleDate.getMonth() + 1).padStart(
                                  2,
                                  "0"
                                ) === currentMonth
                              );
                            }

                            if (cy.cycle_name) {
                              // case 3: fallback check
                              return cy.cycle_name.includes(
                                `CY${currentYear}-${currentMonth}`
                              );
                            }

                            return false;
                          })
                          .map((cy) => (
                            <MenuItem key={cy._id} value={cy._id}>
                              {cy.cycle_name ||
                                `${cy.client_id?.client_name} - ${cy.month}/${cy.year}`}
                            </MenuItem>
                          ))
                      : // Render Projects
                        Array.isArray(projects) &&
                        projects.map((project) => (
                          <MenuItem key={project._id} value={project._id}>
                            {`${project.name} - ${new Date(
                              project.date
                            ).toLocaleDateString()} - ${formatCurrency(
                              project.fees
                            )}`}
                          </MenuItem>
                        ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Quick entry sections per schema */}
              {["equipment", "props", "models", "day_rates", "others"].map(
                (section) => (
                  <Grid item xs={12} key={section}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "8px",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            mb: 2,
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#db4a41",
                            }}
                          >
                            {section.replace("_", " ").toUpperCase()}
                          </Typography>
                          <Button
                            size="small"
                            onClick={() => addItem(section)}
                            sx={{ color: "#db4a41" }}
                          >
                            Add
                          </Button>
                        </Box>
                        <Grid container spacing={1}>
                          {(newQuotation.items[section] || []).map(
                            (row, idx) => (
                              <Grid item xs={12} key={idx}>
                                <Grid container spacing={1}>
                                  {section === "equipment" && (
                                    <>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Tool"
                                          value={row.tool}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "tool",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Price"
                                          type="number"
                                          value={row.price}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "price",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={3}>
                                        <TextField
                                          fullWidth
                                          label="Qty"
                                          type="number"
                                          value={row.qty}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "qty",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                    </>
                                  )}
                                  {section === "props" && (
                                    <>
                                      <Grid item xs={12} sm={5}>
                                        <TextField
                                          fullWidth
                                          label="Item"
                                          value={row.item}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "item",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Price"
                                          type="number"
                                          value={row.price}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "price",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={2}>
                                        <TextField
                                          fullWidth
                                          label="Qty"
                                          type="number"
                                          value={row.qty}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "qty",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                    </>
                                  )}
                                  {section === "models" && (
                                    <>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          label="Name"
                                          value={row.name}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "name",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          label="Rate"
                                          type="number"
                                          value={row.rate}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "rate",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                    </>
                                  )}
                                  {section === "day_rates" && (
                                    <>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Name"
                                          value={row.name}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "name",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Rate"
                                          type="number"
                                          value={row.rate}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "rate",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={4}>
                                        <TextField
                                          fullWidth
                                          label="Contact"
                                          value={row.contact || ""}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "contact",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                    </>
                                  )}
                                  {section === "others" && (
                                    <>
                                      <Grid item xs={12} sm={6}>
                                        <TextField
                                          fullWidth
                                          label="Description"
                                          value={row.description || ""}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "description",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={3}>
                                        <TextField
                                          fullWidth
                                          label="Price"
                                          type="number"
                                          value={row.price}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "price",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                      <Grid item xs={12} sm={2}>
                                        <TextField
                                          fullWidth
                                          label="Qty"
                                          type="number"
                                          value={row.qty}
                                          onChange={(e) =>
                                            updateItem(
                                              section,
                                              idx,
                                              "qty",
                                              e.target.value
                                            )
                                          }
                                          sx={{
                                            "& .MuiOutlinedInput-root": {
                                              color: "white",
                                              "& fieldset": {
                                                borderColor:
                                                  "rgba(255,255,255,0.3)",
                                              },
                                              "&.Mui-focused fieldset": {
                                                borderColor: "#db4a41",
                                              },
                                            },
                                            "& .MuiInputLabel-root": {
                                              color: "rgba(255,255,255,0.7)",
                                            },
                                          }}
                                        />
                                      </Grid>
                                    </>
                                  )}
                                  <Grid item xs={12} sm={1}>
                                    <Button
                                      color="error"
                                      onClick={() => removeItem(section, idx)}
                                    >
                                      Remove
                                    </Button>
                                  </Grid>
                                </Grid>
                              </Grid>
                            )
                          )}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                )
              )}

              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                >
                  Estimated Total: {grand.toLocaleString()} EGP
                </Typography>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingQuotation ? "Update" : "Request"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Quotation Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingQuotation && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Client:</strong>{" "}
                    {viewingQuotation.client_id?.client_name || "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Cycle:</strong>{" "}
                    {viewingQuotation.cycle_id?.cycle_name ||
                      viewingQuotation.cycle_id?.month ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>paid:</strong> {viewingQuotation.status}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Status:</strong> {viewingQuotation.staging_status}
                  </Typography>
                </Grid>
                {viewingQuotation.staging_status === "rejected" && (
                  <Grid item xs={12} sm={4}>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      <strong>rejection reason:</strong>{" "}
                      {viewingQuotation.staging_rejection_reason}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Created By:</strong>{" "}
                    {viewingQuotation?.created_by?.name}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                  >
                    Total: {formatCurrency(viewingQuotation.total_cost)}
                  </Typography>
                </Grid>

                {/* Items breakdown */}
                {viewingQuotation.items && (
                  <Grid item xs={12}>
                    <Grid container spacing={2}>
                      {[
                        {
                          key: "equipment",
                          title: "Equipment",
                          cols: [
                            { field: "tool", label: "Tool" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                        {
                          key: "props",
                          title: "Props",
                          cols: [
                            { field: "item", label: "Item" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                        {
                          key: "models",
                          title: "Models",
                          cols: [
                            { field: "name", label: "Name" },
                            { field: "rate", label: "Rate", isMoney: true },
                          ],
                        },
                        {
                          key: "day_rates",
                          title: "Day Rates",
                          cols: [
                            { field: "name", label: "Name" },
                            { field: "rate", label: "Rate", isMoney: true },
                            { field: "contact", label: "Contact" },
                          ],
                        },
                        {
                          key: "others",
                          title: "Others",
                          cols: [
                            { field: "description", label: "Description" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                      ].map((section) => {
                        const rows = viewingQuotation.items[section.key] || [];
                        if (!rows.length) return null;
                        return (
                          <Grid item xs={12} key={section.key}>
                            <Card
                              sx={{
                                background: "rgba(255, 255, 255, 0.05)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                              }}
                            >
                              <CardContent>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    color: "#db4a41",
                                    fontFamily: "Formula Bold",
                                    mb: 1,
                                  }}
                                >
                                  {section.title}
                                </Typography>
                                <TableContainer
                                  component={Paper}
                                  sx={{ background: "transparent" }}
                                >
                                  <Table size="small">
                                    <TableHead>
                                      <TableRow>
                                        {section.cols.map((c) => (
                                          <TableCell
                                            key={c.field}
                                            sx={{
                                              color: "white",
                                              fontFamily: "Formula Bold",
                                            }}
                                          >
                                            {c.label}
                                          </TableCell>
                                        ))}
                                        <TableCell
                                          sx={{
                                            color: "white",
                                            fontFamily: "Formula Bold",
                                          }}
                                        >
                                          Total
                                        </TableCell>
                                      </TableRow>
                                    </TableHead>
                                    <TableBody>
                                      {rows.map((r, idx) => (
                                        <TableRow key={idx}>
                                          {section.cols.map((c) => {
                                            const raw = r[c.field];
                                            const val =
                                              raw?.$numberDecimal ?? raw;
                                            return (
                                              <TableCell
                                                key={c.field}
                                                sx={{ color: "white" }}
                                              >
                                                {c.isMoney
                                                  ? formatCurrency(val)
                                                  : val || "-"}
                                              </TableCell>
                                            );
                                          })}
                                          <TableCell sx={{ color: "white" }}>
                                            {formatCurrency(
                                              r.total_price ||
                                                (section.key === "equipment" ||
                                                section.key === "props"
                                                  ? parseFloat(
                                                      r.price?.$numberDecimal ||
                                                        r.price ||
                                                        0
                                                    ) * (r.qty || 0)
                                                  : section.key === "others"
                                                  ? parseFloat(
                                                      r.price?.$numberDecimal ||
                                                        r.price ||
                                                        0
                                                    ) * (r.qty || 1)
                                                  : parseFloat(
                                                      r.rate?.$numberDecimal ||
                                                        r.rate ||
                                                        0
                                                    ))
                                            )}
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              </CardContent>
                            </Card>
                          </Grid>
                        );
                      })}

                      {/* Subtotals */}
                      {viewingQuotation.items.totals && (
                        <Grid item xs={12}>
                          <Card
                            sx={{
                              background: "rgba(255, 255, 255, 0.05)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                              borderRadius: "8px",
                            }}
                          >
                            <CardContent>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#db4a41",
                                  fontFamily: "Formula Bold",
                                  mb: 1,
                                }}
                              >
                                Subtotals
                              </Typography>
                              <Grid container spacing={2}>
                                {Object.entries(
                                  viewingQuotation.items.totals
                                ).map(([k, v]) => (
                                  <Grid item key={k} xs={12} sm={6} md={4}>
                                    <Typography sx={{ color: "white" }}>
                                      <strong>{k.replace("_", " ")}</strong>:{" "}
                                      {formatCurrency(v)}
                                    </Typography>
                                  </Grid>
                                ))}
                              </Grid>
                            </CardContent>
                          </Card>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Decision Modal (Approve/Reject) */}
        <Dialog
          open={decision.open}
          onClose={closeDecision}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {decision.action === "approve"
              ? "Pay Quotation"
              : "Reject Quotation"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Comment (optional)"
              value={decision.reason}
              onChange={(e) =>
                setDecision((d) => ({ ...d, reason: e.target.value }))
              }
              multiline
              rows={3}
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                  "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={closeDecision}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitDecision}
              variant="contained"
              sx={{
                backgroundColor:
                  decision.action === "approve" ? "#db4a41" : "#db4a41",
                "&:hover": { opacity: 0.9, backgroundColor: "#c62828" },
              }}
            >
              {decision.action === "approve" ? "Pay" : "Reject"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Staging Decision Modal (Approve/Reject Staging) */}
        <Dialog
          open={stagingDecision.open}
          onClose={closeStagingDecision}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {stagingDecision.action === "approved"
              ? "Approve Staging"
              : "Reject Staging"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {stagingDecision.action === "rejected" && (
              <TextField
                fullWidth
                label="Rejection Reason (required)"
                value={stagingDecision.reason}
                onChange={(e) =>
                  setStagingDecision((d) => ({ ...d, reason: e.target.value }))
                }
                multiline
                rows={3}
                required
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                    "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            )}
            {stagingDecision.action === "approved" && (
              <Typography sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Are you sure you want to approve this quotation for staging?
              </Typography>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={closeStagingDecision}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitStagingDecision}
              variant="contained"
              disabled={
                stagingDecision.action === "rejected" &&
                !stagingDecision.reason.trim()
              }
              sx={{
                backgroundColor:
                  stagingDecision.action === "approved" ? "#db4a41" : "#db4a41",
                "&:hover": { opacity: 0.9, backgroundColor: "#c62828" },
              }}
            >
              {stagingDecision.action === "approved"
                ? "Approve Staging"
                : "Reject Staging"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Quotations;
