// import React, { useState, useEffect } from "react";

// export default function ComingSoon() {
//   const images = [
//     "/assets/comingsoon/Leaked.png",
//     "/assets/comingsoon/YourStorage-21.png",
//     "/assets/comingsoon/YourStorage-25.png",
//     "/assets/comingsoon/YourStorage.png",
//   ];

//   const [bgImage, setBgImage] = useState(images[0]);

//   useEffect(() => {
//     const interval = setInterval(() => {
//       setBgImage((prev) => {
//         let next;
//         do {
//           next = images[Math.floor(Math.random() * images.length)];
//         } while (next === prev);
//         return next;
//       });
//     }, 5000);

//     return () => clearInterval(interval);
//   }, []);

//   return (
//     <div className="wrapper">
//       <div className="center">
//         <img
//           src="/assets/young-logo-white.webp"
//           alt="Young Productions"
//           className="logo"
//         />

//         <p className="description">
//           We’re cooking something cool behind the scenes.
//           <br />
//           Stay young, stay curious —
//           <span className="highlight"> COMING SOON !</span>
//         </p>

//         <div className="socials">
//           <a
//             href="https://instagram.com/youngproductionss"
//             className="social-btn"
//           >
//             Instagram
//           </a>
//           <a
//             href="https://www.tiktok.com/@youngproductions_"
//             className="social-btn"
//           >
//             TikTok
//           </a>
//           <a
//             href="https://www.facebook.com/younggproductions/"
//             className="social-btn"
//           >
//             Facebook
//           </a>
//           <a
//             href="https://eg.linkedin.com/company/young-productionss"
//             className="social-btn"
//           >
//             LinkedIn
//           </a>
//         </div>
//       </div>

//       <div className="bottom-light left" />
//       <div className="bottom-light right" />

//       <div className="reversed">© 2026 - Young Productions</div>
//       <div className="powered-by">Powered By - Young Software House</div>

//       <style jsx>{`
//         .wrapper {
//           height: 100vh;
//           width: 100%;
//           background: #000;
//           background-size: cover;
//           background-repeat: no-repeat;
//           background-position: center;
//           position: relative;
//           overflow: hidden;
//           font-family: Inter, system-ui, sans-serif;
//           transition: background-image 1s ease-in-out;
//         }

//         .center {
//           position: relative;
//           z-index: 10;
//           text-align: center;
//           padding: 20px 40px;
//           margin: auto;
//           display: flex;
//           flex-direction: column;
//           align-items: center;
//           justify-content: center;
//           height: 100%;
//           backdrop-filter: blur(12px);
//         }

//         .logo {
//           width: 740px;
//           max-width: 90vw;
//           margin-bottom: 32px;
//           filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5))
//             drop-shadow(0 0 16px rgba(255, 255, 255, 0.35))
//             drop-shadow(0 0 40px rgba(255, 255, 255, 0.2));
//           animation: bulbPulse 4s ease-in-out infinite;
//         }

//         .description {
//           font-size: 24px;
//           line-height: 1.6;
//           margin-bottom: 40px;
//           color: #fff;
//           font-family: "formula bold";
//         }

//         .highlight {
//           color: #db4a41;
//           font-weight: 600;
//           letter-spacing: 0.1px;
//           font-size: 30px;
//           text-shadow: 0 0 14px rgba(219, 74, 65, 0.6);
//           filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5))
//             drop-shadow(0 0 16px rgba(255, 255, 255, 0.35))
//             drop-shadow(0 0 40px rgba(255, 255, 255, 0.2));
//           animation: bulbPulse 4s ease-in-out infinite;
//         }

//         @keyframes bulbPulse {
//           0% {
//             filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4))
//               drop-shadow(0 0 15px rgba(255, 255, 255, 0.25))
//               drop-shadow(0 0 35px rgba(255, 255, 255, 0.15));
//           }
//           50% {
//             filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6))
//               drop-shadow(0 0 25px rgba(255, 255, 255, 0.45))
//               drop-shadow(0 0 60px rgba(255, 255, 255, 0.3));
//           }
//           100% {
//             filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4))
//               drop-shadow(0 0 15px rgba(255, 255, 255, 0.25))
//               drop-shadow(0 0 35px rgba(255, 255, 255, 0.15));
//           }
//         }

//         .socials {
//           display: flex;
//           gap: 12px;
//           flex-wrap: wrap;
//           justify-content: center;
//         }

//         .social-btn {
//           padding: 10px 18px;
//           border-radius: 8px;
//           background: rgba(255, 255, 255, 0.08);
//           backdrop-filter: blur(14px);
//           color: #fff;
//           font-size: 18px;
//           text-decoration: none;
//           border: 1px solid rgba(255, 255, 255, 0.15);
//           box-shadow: 0 0 20px rgba(255, 255, 255, 0.08);
//           transition: all 0.3s ease;
//           font-family: "formula bold";
//         }

//         .social-btn:hover {
//           background: rgba(219, 74, 65, 0.18);
//           border-color: #db4a41;
//           color: #db4a41;
//           box-shadow: 0 0 30px rgba(219, 74, 65, 0.35);
//         }

//         .bottom-light {
//           position: absolute;
//           bottom: -20%;
//           width: 420px;
//           height: 420px;
//           background: radial-gradient(
//             circle,
//             rgba(255, 255, 255, 0.12) 0%,
//             rgba(255, 255, 255, 0.06) 35%,
//             rgba(255, 255, 255, 0) 70%
//           );
//           filter: blur(40px);
//           z-index: 0;
//           pointer-events: none;
//         }

//         .bottom-light.left {
//           left: -10%;
//         }

//         .bottom-light.right {
//           right: -10%;
//         }

//         .reversed,
//         .powered-by {
//           position: absolute;
//           font-size: 18px;
//           font-family: "formula bold";
//           z-index: 9999;
//           pointer-events: none;
//           color: #fff;
//           opacity: 0.6;
//         }

//         .reversed {
//           bottom: 16px;
//           left: 16px;
//         }

//         .powered-by {
//           bottom: 16px;
//           right: 16px;
//         }

//         /* Responsive adjustments */
//         @media (max-width: 1024px) {
//           .description {
//             font-size: 20px;
//           }
//           .highlight {
//             font-size: 24px;
//           }
//         }

//         @media (max-width: 768px) {
//           .center {
//             padding: 20px 30px;
//           }
//           .logo {
//             width: 100%;
//             max-width: 320px;
//             margin-bottom: 24px;
//           }
//           .description {
//             font-size: 16px;
//             line-height: 1.4;
//             margin-bottom: 32px;
//           }
//           .highlight {
//             font-size: 18px;
//           }
//           .social-btn {
//             font-size: 14px;
//             padding: 8px 14px;
//           }
//           .bottom-light {
//             width: 280px;
//             height: 280px;
//           }
//           .reversed,
//           .powered-by {
//             font-size: 14px;
//           }
//         }

//         @media (max-width: 480px) {
//           .center {
//             padding: 15px 20px;
//           }
//           .logo {
//             max-width: 260px;
//           }
//           .description {
//             font-size: 14px;
//           }
//           .highlight {
//             font-size: 16px;
//           }
//           .social-btn {
//             font-size: 12px;
//             padding: 6px 10px;
//           }
//         }
//       `}</style>
//     </div>
//   );
// }
import React, { useState, useEffect } from "react";

export default function ComingSoonVideo() {
  return (
    <div className="wrapper">
      {/* VIDEO BACKGROUND */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="background-video"
        src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/SAME-YOUNG.webm"
      />

      {/* CONTENT */}
      <div className="center">
        <p className="description">
          We’re cooking something cool behind the scenes.
          <br />
          Stay young, stay curious —
          <span className="highlight"> COMING SOON !</span>
        </p>

        <div className="socials">
          <a
            href="https://instagram.com/youngproductionss"
            className="social-btn"
          >
            Instagram
          </a>
          <a
            href="https://www.tiktok.com/@youngproductions_"
            className="social-btn"
          >
            TikTok
          </a>
          <a
            href="https://www.facebook.com/younggproductions/"
            className="social-btn"
          >
            Facebook
          </a>
          <a
            href="https://eg.linkedin.com/company/young-productionss"
            className="social-btn"
          >
            LinkedIn
          </a>
        </div>
      </div>

      {/* BOTTOM LIGHTS */}
      <div className="bottom-light left" />
      <div className="bottom-light right" />

      {/* REVERSED LOGO TEXT */}
      <div className="reversed">© 2026 - Young Productions</div>
      <div className="powered-by">Powered By - Young Software House</div>

      <style jsx>{`
        .wrapper {
          height: 100vh;
          width: 100%;
          position: relative;
          overflow: hidden;
          font-family: Inter, system-ui, sans-serif;
          background: #fff;
        }

        /* VIDEO BACKGROUND */
        .background-video {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 130%;
          height: 130%;
          object-fit: contain;
          transform: translate(-50%, -50%);
          z-index: 0;
        }

        /* CONTENT */
        .center {
          position: relative;
          z-index: 10;
          text-align: center;
          padding: 20px 40px;
          margin: auto;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        }

        .description {
          font-size: 24px;
          line-height: 1.6;
          margin-top: 700px;
          margin-bottom: 20px;
          color: #000;
          font-family: "formula bold";
          width: 100%;
        }

        .highlight {
          color: #db4a41;
          font-weight: 600;
          letter-spacing: 0.1px;
          font-size: 30px;
          text-shadow: 0 0 14px rgba(219, 74, 65, 0.6);
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5))
            drop-shadow(0 0 16px rgba(255, 255, 255, 0.35))
            drop-shadow(0 0 40px rgba(255, 255, 255, 0.2));
          animation: bulbPulse 4s ease-in-out infinite;
        }

        @keyframes bulbPulse {
          0% {
            filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4))
              drop-shadow(0 0 15px rgba(255, 255, 255, 0.25))
              drop-shadow(0 0 35px rgba(255, 255, 255, 0.15));
          }
          50% {
            filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6))
              drop-shadow(0 0 25px rgba(255, 255, 255, 0.45))
              drop-shadow(0 0 60px rgba(255, 255, 255, 0.3));
          }
          100% {
            filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.4))
              drop-shadow(0 0 15px rgba(255, 255, 255, 0.25))
              drop-shadow(0 0 35px rgba(255, 255, 255, 0.15));
          }
        }

        .socials {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
          justify-content: center;
        }

        .social-btn {
          padding: 10px 18px;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(14px);
          color: #000;
          font-size: 14px;
          text-decoration: none;
          border: 1px solid rgba(7, 7, 7, 0.15);
          box-shadow: 0 0 20px rgba(255, 255, 255, 0.08);
          transition: all 0.3s ease;
          font-family: "formula bold";
        }

        .social-btn:hover {
          background: rgba(219, 74, 65, 0.18);
          border-color: #db4a41;
          color: #db4a41;
          box-shadow: 0 0 30px rgba(219, 74, 65, 0.35);
        }

        .bottom-light {
          position: absolute;
          bottom: -20%;
          width: 420px;
          height: 420px;
          background: radial-gradient(
            circle,
            rgba(255, 255, 255, 0.12) 0%,
            rgba(255, 255, 255, 0.06) 35%,
            rgba(255, 255, 255, 0) 70%
          );
          filter: blur(40px);
          z-index: 5;
          pointer-events: none;
        }

        .bottom-light.left {
          left: -10%;
        }

        .bottom-light.right {
          right: -10%;
        }

        .reversed,
        .powered-by {
          position: absolute;
          font-size: 18px;
          font-family: "formula bold";
          z-index: 10;
          pointer-events: none;
          color: #000;
          opacity: 0.6;
        }

        .reversed {
          bottom: 16px;
          left: 16px;
        }

        .powered-by {
          bottom: 16px;
          right: 16px;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
          .description {
            font-size: 20px;
          }
          .highlight {
            font-size: 24px;
          }
        }

        @media (max-width: 768px) {
          .center {
            padding: 20px 30px;
          }
          .description {
            font-size: 16px;
            line-height: 1.4;
            margin-bottom: 32px;
          }
          .highlight {
            font-size: 18px;
          }
          .social-btn {
            font-size: 14px;
            padding: 8px 14px;
          }
          .bottom-light {
            width: 280px;
            height: 280px;
          }
          .reversed,
          .powered-by {
            font-size: 14px;
          }
        }

        @media (max-width: 480px) {
          .center {
            padding: 15px 20px;
          }
          .description {
            font-size: 14px;
            margin-top: 550px;
            margin-bottom: 24px;
          }
          .highlight {
            font-size: 16px;
          }
          .social-btn {
            font-size: 12px;
            padding: 6px 10px;
          }
        }
      `}</style>
    </div>
  );
}
