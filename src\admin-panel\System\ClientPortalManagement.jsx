import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Avatar,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import { motion } from "framer-motion";
import { useUser } from "../../contexts/UserContext";
function ClientPortalManagement() {
  const [clientUsers, setClientUsers] = useState([]);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    password: "",
    company: "",
    phone: "",
    clientId: "",
    permissions: {
      viewProjects: true,
      viewInvoices: true,
      downloadFiles: true,
      communicate: true,
      viewCalendar: true,
    },
  });
  const { user } = useUser();

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/client-users";
  const CLIENTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement";

  const fetchClientUsers = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL + "/all", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setClientUsers(result.clientUsers || []);
    } catch (error) {
      console.error("Error fetching client users:", error);
      showSnackbar("Failed to fetch client users", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClients = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENTS_API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setClients(result.data || result || []);
    } catch (error) {
      console.error("Error fetching clients:", error);
      showSnackbar("Failed to fetch clients", "error");
    }
  }, []);

  useEffect(() => {
    fetchClientUsers();
    fetchClients();
  }, [fetchClientUsers, fetchClients]);

  const handleAdd = () => {
    setEditingUser(null);
    setNewUser({
      name: "",
      email: "",
      password: "",
      company: "",
      phone: "",
      clientId: "",
      permissions: {
        viewProjects: true,
        viewInvoices: true,
        downloadFiles: true,
        communicate: true,
        viewCalendar: true,
      },
    });
    setOpenModal(true);
  };

  const handleEdit = (user) => {
    setEditingUser(user);
    setNewUser({
      name: user.name,
      email: user.email,
      password: "", // Don't populate password
      company: user.company,
      phone: user.phone,
      clientId: user.clientId._id || user.clientId,
      permissions: user.permissions,
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingUser(null);
    setNewUser({
      name: "",
      email: "",
      password: "",
      company: "",
      phone: "",
      clientId: "",
      permissions: {
        viewProjects: true,
        viewInvoices: true,
        downloadFiles: true,
        communicate: true,
        viewCalendar: true,
      },
    });
  };

  const handleSubmit = async () => {
    if (
      !newUser.name ||
      !newUser.email ||
      !newUser.company ||
      !newUser.phone ||
      !newUser.clientId
    ) {
      showSnackbar("Please fill in all required fields", "error");
      return;
    }

    if (!editingUser && !newUser.password) {
      showSnackbar("Password is required for new users", "error");
      return;
    }

    try {
      const token = localStorage.getItem("token");

      // For new registrations, backend expects multipart/form-data due to multer
      // For edits, continue sending JSON
      let response;
      if (!editingUser) {
        const formData = new FormData();
        formData.append("name", newUser.name);
        formData.append("email", newUser.email);
        formData.append("password", newUser.password);
        formData.append("company", newUser.company);
        formData.append("phone", newUser.phone);
        formData.append("clientId", newUser.clientId);
        // Send permissions as JSON string (backend defaults handle missing props)
        formData.append(
          "permissions",
          JSON.stringify(newUser.permissions || {})
        );

        console.log("Sending FormData to register endpoint:", {
          name: newUser.name,
          email: newUser.email,
          company: newUser.company,
          phone: newUser.phone,
          clientId: newUser.clientId,
          permissions: newUser.permissions,
        });

        response = await fetch(`${API_BASE_URL}/register`, {
          method: "POST",
          // Do NOT set Content-Type; browser sets correct multipart boundary
          // Endpoint does not require Authorization token
          body: formData,
        });
      } else {
        const requestData = { ...newUser };
        if (!requestData.password) delete requestData.password;

        response = await fetch(`${API_BASE_URL}/${editingUser._id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestData),
        });
      }

      if (response.ok) {
        showSnackbar(
          editingUser
            ? "Client user updated successfully"
            : "Client user created successfully",
          "success"
        );
        fetchClientUsers();
        handleCloseModal();
      } else {
        let errorMessage = "Failed to save client user";
        try {
          const error = await response.json();
          errorMessage = error.message || error.error || errorMessage;
          console.error("Backend error details:", error);
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error saving client user:", error);
      showSnackbar(error.message || "Failed to save client user", "error");
    }
  };

  const handleDelete = async (userId) => {
    if (!window.confirm("Are you sure you want to delete this client user?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${userId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        showSnackbar("Client user deleted successfully", "success");
        fetchClientUsers();
      } else {
        throw new Error("Failed to delete client user");
      }
    } catch (error) {
      console.error("Error deleting client user:", error);
      showSnackbar("Failed to delete client user", "error");
    }
  };

  const handleStatusChange = async (userId, newStatus) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${userId}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        showSnackbar("Status updated successfully", "success");
        fetchClientUsers();
      } else {
        throw new Error("Failed to update status");
      }
    } catch (error) {
      console.error("Error updating status:", error);
      showSnackbar("Failed to update status", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "#4caf50";
      case "inactive":
        return "#ff9800";
      case "suspended":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const handlePermissionChange = (permission, value) => {
    setNewUser({
      ...newUser,
      permissions: {
        ...newUser.permissions,
        [permission]: value,
      },
    });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Client Portal Management
          </Typography>
          {(user?.tier === 2 || user?.tier === 3) &&
            (user?.role === "account_manager" ||
              user?.role === "general_manager") && (
              <Button
                variant="contained"
                startIcon={<PersonAddIcon />}
                onClick={handleAdd}
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  fontFamily: "Formula Bold",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
              >
                Add Client User
              </Button>
            )}
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Stats Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <PersonAddIcon sx={{ color: "#4caf50", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Total Users
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {clientUsers.length}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <PersonAddIcon sx={{ color: "#4caf50", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Active Users
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            clientUsers.filter((u) => u.status === "active")
                              .length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <PersonAddIcon sx={{ color: "#ff9800", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#ff9800",
                            }}
                          >
                            Inactive Users
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            clientUsers.filter((u) => u.status === "inactive")
                              .length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <PersonAddIcon sx={{ color: "#f44336", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#f44336",
                            }}
                          >
                            Suspended Users
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            clientUsers.filter((u) => u.status === "suspended")
                              .length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Client Users Table */}
              <Card
                sx={{
                  background: "rgba(255, 255, 255, 0.05)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  borderRadius: "12px",
                }}
              >
                <CardContent>
                  <Typography
                    variant="h6"
                    sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 3 }}
                  >
                    Client Portal Users
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            User
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Company
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Client
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Last Login
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Created
                          </TableCell>

                          <TableCell
                            sx={{ color: "white", fontWeight: "bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {clientUsers
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((user) => (
                            <TableRow key={user._id}>
                              <TableCell>
                                <Box
                                  sx={{ display: "flex", alignItems: "center" }}
                                >
                                  <Avatar sx={{ mr: 2, bgcolor: "#db4a41" }}>
                                    {user.name.charAt(0).toUpperCase()}
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "white",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      {user.name}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                    >
                                      {user.email}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {user.company}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {user.clientId?.name || "N/A"}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={user.status}
                                  sx={{
                                    backgroundColor: getStatusColor(
                                      user.status
                                    ),
                                    color: "white",
                                    textTransform: "capitalize",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {user.lastLogin
                                  ? new Date(
                                      user.lastLogin
                                    ).toLocaleDateString()
                                  : "Never"}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {new Date(user.createdAt).toLocaleDateString()}
                              </TableCell>

                              <TableCell>
                                <Box sx={{ display: "flex", gap: 1 }}>
                                  {(user?.tier === 2 || user?.tier === 3) &&
                                    (user?.role === "account_manager" ||
                                      user?.role === "general_manager") && (
                                      <Tooltip title="Edit User">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleEdit(user)}
                                          sx={{ color: "#2196f3" }}
                                        >
                                          <EditIcon />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                  {(user?.tier === 2 || user?.tier === 3) &&
                                    (user?.role === "account_manager" ||
                                      user?.role === "general_manager") && (
                                      <Tooltip title="Change Status">
                                        <FormControl size="small">
                                          <Select
                                            value={user.status}
                                            onChange={(e) =>
                                              handleStatusChange(
                                                user._id,
                                                e.target.value
                                              )
                                            }
                                            sx={{
                                              color: "white",
                                              "& .MuiOutlinedInput-notchedOutline":
                                                {
                                                  borderColor:
                                                    "rgba(255, 255, 255, 0.3)",
                                                },
                                            }}
                                          >
                                            <MenuItem value="active">
                                              Active
                                            </MenuItem>
                                            <MenuItem value="inactive">
                                              Inactive
                                            </MenuItem>
                                            <MenuItem value="suspended">
                                              Suspended
                                            </MenuItem>
                                          </Select>
                                        </FormControl>
                                      </Tooltip>
                                    )}
                                  :
                                  {
                                    <Tooltip title="Change Status">
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          color: "#db4a41",
                                          cursor: "not-allowed",
                                          fontWeight: "bold",
                                          fontFamily: "Formula Bold",
                                        }}
                                      >
                                        You're not authorized{" "}
                                      </Typography>
                                    </Tooltip>
                                  }
                                  {(user?.tier === 2 || user?.tier === 3) &&
                                    (user?.role === "account_manager" ||
                                      user?.role === "general_manager") && (
                                      <Tooltip title="Delete User">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleDelete(user._id)}
                                          sx={{ color: "#f44336" }}
                                        >
                                          <DeleteIcon />
                                        </IconButton>
                                      </Tooltip>
                                    )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={clientUsers.length}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setRowsPerPage(parseInt(event.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      "& .MuiTablePagination-selectIcon": {
                        color: "white",
                      },
                    }}
                  />
                </CardContent>
              </Card>
            </>
          )}
        </Box>

        {/* Add/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingUser ? "Edit Client User" : "Add New Client User"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Name"
                  value={newUser.name}
                  onChange={(e) =>
                    setNewUser({ ...newUser, name: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) =>
                    setNewUser({ ...newUser, email: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={
                    editingUser
                      ? "New Password (leave empty to keep current)"
                      : "Password"
                  }
                  type="password"
                  value={newUser.password}
                  onChange={(e) =>
                    setNewUser({ ...newUser, password: e.target.value })
                  }
                  required={!editingUser}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Company"
                  value={newUser.company}
                  onChange={(e) =>
                    setNewUser({ ...newUser, company: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  value={newUser.phone}
                  onChange={(e) =>
                    setNewUser({ ...newUser, phone: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client
                  </InputLabel>
                  <Select
                    value={newUser.clientId}
                    onChange={(e) =>
                      setNewUser({ ...newUser, clientId: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {clients.map((client) => (
                      <MenuItem key={client._id} value={client._id}>
                        {client.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ color: "white", mb: 2 }}>
                  Permissions
                </Typography>
                <Grid container spacing={2}>
                  {Object.entries(newUser.permissions).map(([key, value]) => (
                    <Grid item xs={12} sm={6} md={4} key={key}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={value}
                            onChange={(e) =>
                              handlePermissionChange(key, e.target.checked)
                            }
                            sx={{
                              color: "#db4a41",
                              "&.Mui-checked": {
                                color: "#db4a41",
                              },
                            }}
                          />
                        }
                        label={key
                          .replace(/([A-Z])/g, " $1")
                          .replace(/^./, (str) => str.toUpperCase())}
                        sx={{ color: "white" }}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingUser ? "Update User" : "Create User"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ClientPortalManagement;
