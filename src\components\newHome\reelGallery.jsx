// import React, { useState, useRef, useEffect, useCallback, memo } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import {
//   HiOutlineVolumeUp,
//   HiOutlineVolumeOff,
//   HiHome,
//   HiUser,
//   HiOutlineChat,
//   HiOutlineBookmark,
//   HiOutlineShare,
// } from "react-icons/hi";
// import { GoHeartFill } from "react-icons/go";
// import { FaUserFriends } from "react-icons/fa";
// import { AiOutlinePlus } from "react-icons/ai";
// import { useReelGallery } from "../../hooks/useApi";
// import OptimizedVideo from "../OptimizedVideo";
// import { ArrowBack } from "@mui/icons-material";

// const ReelGallery = () => {
//   const { data: allVideos = [], isLoading } = useReelGallery();
//   const videos = allVideos.slice(0, 18);

//   const [currentVideo, setCurrentVideo] = useState(null);
//   const [isMuted, setIsMuted] = useState(true);

//   const mainVideoRef = useRef(null);

//   /* ---------- init first video ---------- */
//   useEffect(() => {
//     if (videos.length && !currentVideo) {
//       setCurrentVideo(videos[0]);
//     }
//   }, [videos, currentVideo]);

//   /* ---------- optimized select ---------- */
//   const handleVideoSelect = useCallback((video) => {
//     setCurrentVideo(video);

//     if (mainVideoRef.current) {
//       const v = mainVideoRef.current;
//       v.pause();
//       v.src = video.src;
//       v.load();
//       v.play().catch(() => {});
//     }
//   }, []);

//   if (isLoading)
//     return (
//       <div style={{ color: "white", textAlign: "center" }}>Loading...</div>
//     );

//   if (!currentVideo)
//     return (
//       <div style={{ color: "white", textAlign: "center" }}>
//         No videos available
//       </div>
//     );

//   return (
//     <motion.div
//       className="reel-container"
//       initial={{ opacity: 0 }}
//       animate={{ opacity: 1 }}
//     >
//       <AnimatePresence mode="wait">
//         <motion.div
//           className="iphone-wrapper"
//           initial={{ opacity: 0, scale: 0.7 }}
//           animate={{ opacity: 1, scale: 1 }}
//           transition={{ duration: 0.6, ease: "easeOut" }}
//         >
//           <motion.div className="notch-style" />

//           {/* ===== MAIN VIDEO (NO REMOUNT) ===== */}
//           <OptimizedVideo
//             ref={mainVideoRef}
//             className="main-video"
//             src={currentVideo.src}
//             autoPlay
//             playsInline
//             preload="metadata"
//             muted={isMuted}
//             onMouseEnter={() => {
//               if (mainVideoRef.current) {
//                 mainVideoRef.current.muted = false;
//                 setIsMuted(false);
//                 mainVideoRef.current.play().catch(() => {});
//               }
//             }}
//             onMouseLeave={() => {
//               if (mainVideoRef.current) {
//                 mainVideoRef.current.muted = true;
//                 setIsMuted(true);
//               }
//             }}
//             onEnded={() => {
//               const i = videos.findIndex((v) => v._id === currentVideo._id);
//               handleVideoSelect(videos[(i + 1) % videos.length]);
//             }}
//           />

//           {/* ===== ACTIONS (UNCHANGED) ===== */}
//           <div className="actions">
//             <div className="action-btn">
//               <AiOutlinePlus size={30} />
//             </div>
//             <div className="action-btn">
//               <GoHeartFill color="red" size={30} />
//               <span>12.3K</span>
//             </div>
//             <div className="action-btn">
//               <HiOutlineChat />
//               <span>250K</span>
//             </div>
//             <div className="action-btn">
//               <HiOutlineBookmark color="Yellow" />
//               <span>Save</span>
//             </div>
//             <div className="action-btn">
//               <HiOutlineShare />
//               <span>Share</span>
//             </div>
//           </div>

//           {/* ===== BOTTOM NAV ===== */}
//           <div className="bottom-nav">
//             <HiHome className="bottom-icon" />
//             <FaUserFriends className="bottom-icon" />
//             <AiOutlinePlus className="bottom-icon" />
//             <HiOutlineChat className="bottom-icon" />
//             <HiUser className="bottom-icon" />
//           </div>

//           {/* ===== MUTE ICON ===== */}
//           <div className="speaker-icon">
//             {isMuted ? (
//               <HiOutlineVolumeOff size={20} color="white" />
//             ) : (
//               <HiOutlineVolumeUp size={20} color="white" />
//             )}
//           </div>

//           <div className="video-title">{currentVideo.title}</div>
//           <div className="video-subtitle">Young Productions</div>
//         </motion.div>
//       </AnimatePresence>

//       {/* ===== THUMBNAILS (VISIBLE + FAST) ===== */}
//       <motion.div
//         className="thumbnail-container"
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//       >
//         {videos.map((video) => (
//           <Thumbnail
//             key={video._id}
//             video={video}
//             active={currentVideo._id === video._id}
//             onClick={handleVideoSelect}
//           />
//         ))}
//       </motion.div>
//       <div className="reel-credit" style={{ color: "white" }}>
//         <h2>
//           {" "}
//           <ArrowBack />
//         </h2>{" "}
//         <h2>Hover is the Key 🔑</h2>
//         Reels Gallery by Young Productions © {new Date().getFullYear()}
//       </div>
//     </motion.div>
//   );
// };

// /* ---------- memoized thumbnail ---------- */
// const Thumbnail = memo(({ video, active, onClick }) => {
//   return (
//     <motion.div
//       className={`thumbnail-item ${active ? "active" : ""}`}
//       onClick={() => onClick(video)}
//       whileHover={{ scale: 1.1 }}
//       transition={{ duration: 0.25 }}
//     >
//       <OptimizedVideo
//         className="thumbnail-video"
//         src={video.src + "#t=0.5"}
//         muted
//         preload="none"
//         playsInline
//       />
//     </motion.div>
//   );
// });

// export default ReelGallery;
import React, {
  useState,
  useRef,
  useEffect,
  useCallback,
  memo,
  startTransition,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  HiOutlineVolumeUp,
  HiOutlineVolumeOff,
  HiHome,
  HiUser,
  HiOutlineChat,
  HiOutlineBookmark,
  HiOutlineShare,
} from "react-icons/hi";
import { GoHeartFill } from "react-icons/go";
import { FaUserFriends } from "react-icons/fa";
import { AiOutlinePlus } from "react-icons/ai";
import { useReelGallery } from "../../hooks/useApi";
import OptimizedVideo from "../OptimizedVideo";

const CDN_BASE = "https://youngproductionss.com/";
const CDN_NEW = "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/";

const normalizeUrl = (url) => {
  if (!url || typeof url !== "string") return "";
  return url.replace(CDN_BASE, CDN_NEW);
};

const ReelGallery = () => {
  const { data: allVideos = [], isLoading } = useReelGallery();
  const videos = allVideos.slice(0, 18);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const mainVideoRef = useRef(null);

  const currentVideo = videos[currentIndex];

  /* ---------- preload next video ---------- */
  useEffect(() => {
    if (!videos.length) return;

    const nextIndex = (currentIndex + 1) % videos.length;
    const nextVideo = videos[nextIndex];

    if (!nextVideo?.src && !nextVideo?.srcWebm) return;

    const link = document.createElement("link");
    link.rel = "preload";
    link.as = "video";
    link.href = normalizeUrl(nextVideo.srcWebm || nextVideo.src);
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, [currentIndex, videos]);

  /* ---------- handlers ---------- */
  const handleVideoSelect = useCallback((index) => {
    startTransition(() => {
      setCurrentIndex(index);
    });
  }, []);

  const handleMouseEnter = useCallback(() => {
    if (!mainVideoRef.current) return;
    mainVideoRef.current.muted = false;
    setIsMuted(false);
    mainVideoRef.current.play().catch(() => {});
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (!mainVideoRef.current) return;
    mainVideoRef.current.muted = true;
    setIsMuted(true);
  }, []);

  if (isLoading)
    return (
      <div style={{ color: "white", textAlign: "center" }}>Loading...</div>
    );

  if (!currentVideo)
    return <div style={{ color: "white", textAlign: "center" }}>No videos</div>;

  return (
    <motion.div
      className="reel-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={currentVideo._id}
          className="iphone-wrapper"
          initial={{ opacity: 0, scale: 0.85 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          <motion.div className="notch-style" />

          {/* MAIN VIDEO */}
          <OptimizedVideo
            ref={mainVideoRef}
            src={normalizeUrl(currentVideo.srcWebm || currentVideo.src)}
            className="main-video"
            autoPlay
            playsInline
            preload="auto"
            muted={isMuted}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onEnded={() =>
              handleVideoSelect((currentIndex + 1) % videos.length)
            }
          />

          {/* ACTIONS */}
          <div className="actions">
            <div className="action-btn">
              <AiOutlinePlus size={30} />
            </div>
            <div className="action-btn">
              <GoHeartFill color="red" size={30} />
              <span>12.3K</span>
            </div>
            <div className="action-btn">
              <HiOutlineChat />
              <span>250K</span>
            </div>
            <div className="action-btn">
              <HiOutlineBookmark color="yellow" />
              <span>Save</span>
            </div>
            <div className="action-btn">
              <HiOutlineShare />
              <span>Share</span>
            </div>
          </div>

          {/* BOTTOM NAV */}
          <div className="bottom-nav">
            <HiHome className="bottom-icon" />
            <FaUserFriends className="bottom-icon" />
            <AiOutlinePlus className="bottom-icon" />
            <HiOutlineChat className="bottom-icon" />
            <HiUser className="bottom-icon" />
          </div>

          {/* MUTE ICON */}
          <div className="speaker-icon">
            {isMuted ? (
              <HiOutlineVolumeOff size={20} color="white" />
            ) : (
              <HiOutlineVolumeUp size={20} color="white" />
            )}
          </div>

          <div className="video-title">{currentVideo.title}</div>
          <div className="video-subtitle">Young Productions</div>
        </motion.div>
      </AnimatePresence>

      {/* THUMBNAILS */}
      <motion.div className="thumbnail-container">
        {videos.map((video, index) => (
          <Thumbnail
            key={video._id}
            video={video}
            active={index === currentIndex}
            onClick={() => handleVideoSelect(index)}
          />
        ))}
      </motion.div>
    </motion.div>
  );
};

/* ---------- Thumbnail ---------- */
const Thumbnail = memo(({ video, active, onClick }) => {
  return (
    <motion.div
      className={`thumbnail-item ${active ? "active" : ""}`}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      <video
        muted
        playsInline
        preload="metadata"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          pointerEvents: "none",
        }}
      >
        {video.srcWebm && (
          <source src={normalizeUrl(video.srcWebm)} type="video/webm" />
        )}
        {video.src && <source src={normalizeUrl(video.src)} type="video/mp4" />}
      </video>
    </motion.div>
  );
});

export default ReelGallery;
