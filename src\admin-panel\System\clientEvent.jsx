import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  ToggleButton,
  ToggleButtonGroup,
  Paper,
  Badge,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import EventIcon from "@mui/icons-material/Event";
import CalendarViewMonthIcon from "@mui/icons-material/CalendarViewMonth";
import ViewListIcon from "@mui/icons-material/ViewList";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import FilterListIcon from "@mui/icons-material/FilterList";
import { motion } from "framer-motion";
import { useUser } from "../../contexts/UserContext";

function ClientEventManagement() {
  const { user } = useUser();
  const [events, setEvents] = useState([]);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState("list"); // "list" or "calendar"
  const [selectedClient, setSelectedClient] = useState(""); // For filtering
  const [currentDate, setCurrentDate] = useState(new Date());
  const [dayDetailsModal, setDayDetailsModal] = useState(false);
  const [selectedDayEvents, setSelectedDayEvents] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [newEvent, setNewEvent] = useState({
    clientId: "",
    title: "",
    type: "",
    description: "",
    startDate: "",
    endDate: "",
  });

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/client-timeline";
  const CLIENTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement";

  const fetchEvents = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/all`);
      const result = await response.json();
      setEvents(result || []);
    } catch (error) {
      console.error("Error fetching events:", error);
      showSnackbar("Failed to fetch events", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(CLIENTS_API_URL);
      const result = await response.json();
      setClients(result.data || result || []);
    } catch (error) {
      console.error("Error fetching clients:", error);
      showSnackbar("Failed to fetch clients", "error");
    }
  }, []);

  useEffect(() => {
    fetchEvents();
    fetchClients();
  }, [fetchEvents, fetchClients]);

  const handleAdd = () => {
    setEditingEvent(null);
    setNewEvent({
      clientId: "",
      title: "",
      type: "",
      description: "",
      startDate: "",
      endDate: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (event) => {
    setEditingEvent(event);
    setNewEvent({
      clientId: event.clientId._id || event.clientId,
      title: event.title,
      type: event.type || "",
      description: event.description || "",
      startDate: event.startDate
        ? new Date(event.startDate).toISOString().split("T")[0]
        : "",
      endDate: event.endDate
        ? new Date(event.endDate).toISOString().split("T")[0]
        : "",
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingEvent(null);
    setNewEvent({
      clientId: "",
      title: "",
      type: "",
      description: "",
      startDate: "",
      endDate: "",
    });
  };

  const handleSubmit = async () => {
    if (
      !newEvent.clientId ||
      !newEvent.title ||
      !newEvent.startDate ||
      !newEvent.endDate
    ) {
      showSnackbar("Please fill in all required fields", "error");
      return;
    }

    try {
      const eventData = {
        ...newEvent,
        createdBy: user?._id,
      };

      let response;
      if (editingEvent) {
        response = await fetch(`${API_BASE_URL}/${editingEvent._id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(eventData),
        });
      } else {
        response = await fetch(`${API_BASE_URL}/add-event`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(eventData),
        });
      }

      if (response.ok) {
        showSnackbar(
          editingEvent
            ? "Event updated successfully"
            : "Event created successfully",
          "success"
        );
        fetchEvents();
        handleCloseModal();
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to save event");
      }
    } catch (error) {
      console.error("Error saving event:", error);
      showSnackbar(error.message || "Failed to save event", "error");
    }
  };

  const handleDelete = async (eventId) => {
    if (!window.confirm("Are you sure you want to delete this event?")) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/${eventId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        showSnackbar("Event deleted successfully", "success");
        fetchEvents();
      } else {
        throw new Error("Failed to delete event");
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      showSnackbar("Failed to delete event", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getEventTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case "Planning":
        return "#2196f3";
      case "Shooting":
        return "#f44336";
      case "Editing":
        return "#4caf50";
      case "other":
        return "#ff9800";
      default:
        return "#9e9e9e";
    }
  };

  // Calendar utility functions
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const getMonthName = (date) => {
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" });
  };

  const navigateMonth = (direction) => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + direction);
      return newDate;
    });
  };

  // Filter events by selected client
  const filteredEvents = selectedClient
    ? events.filter(
        (event) =>
          event.clientId?._id === selectedClient ||
          event.clientId === selectedClient
      )
    : events;

  // Get events for a specific date
  const getEventsForDate = (date) => {
    return filteredEvents.filter((event) => {
      const eventStart = new Date(event.startDate);
      const eventEnd = new Date(event.endDate);
      const checkDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        date
      );

      return checkDate >= eventStart && checkDate <= eventEnd;
    });
  };

  // Format date for input fields (YYYY-MM-DD)
  const formatDateForInput = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Handle calendar day click
  const handleDayClick = (day) => {
    const dayEvents = getEventsForDate(day);
    const clickedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );
    const isAuthorized =
      (user?.tier === 1 || user?.tier === 2 || user?.tier === 3) &&
      (user?.role === "account_manager" || user?.role === "general_manager");

    if (dayEvents.length > 0) {
      // Show events for this day (everyone can see)
      setSelectedDayEvents(dayEvents);
      setSelectedDay(clickedDate);
      setDayDetailsModal(true);
    } else {
      // Only authorized users can add a new event
      if (!isAuthorized) {
        alert("You are not authorized to add events.");
        return;
      }

      // Open form with pre-filled date
      const dateString = formatDateForInput(clickedDate);
      setNewEvent({
        clientId: "",
        title: "",
        type: "",
        description: "",
        startDate: dateString,
        endDate: dateString,
      });
      setEditingEvent(null);
      setOpenModal(true);
    }
  };

  // Close day details modal
  const handleCloseDayDetails = () => {
    setDayDetailsModal(false);
    setSelectedDayEvents([]);
    setSelectedDay(null);
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
            flexWrap: "wrap",
            gap: 2,
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Client Timeline Management
          </Typography>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              flexWrap: "wrap",
            }}
          >
            {/* View Toggle */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(e, newView) => newView && setViewMode(newView)}
              sx={{
                "& .MuiToggleButton-root": {
                  color: "rgba(255, 255, 255, 0.7)",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                  "&.Mui-selected": {
                    backgroundColor: "#db4a41",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "#c62828",
                    },
                  },
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                  },
                },
              }}
            >
              <ToggleButton value="list">
                <ViewListIcon sx={{ mr: 1 }} />
                List
              </ToggleButton>
              <ToggleButton value="calendar">
                <CalendarViewMonthIcon sx={{ mr: 1 }} />
                Calendar
              </ToggleButton>
            </ToggleButtonGroup>

            {/* Client Filter */}
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                <FilterListIcon sx={{ mr: 1, fontSize: "small" }} />
                Filter by Client
              </InputLabel>
              <Select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                }}
              >
                <MenuItem value="">All Clients</MenuItem>
                {clients.map((client) => (
                  <MenuItem key={client._id} value={client._id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {(user?.tier === 2 || user?.tier === 3 || user?.tier === 1) &&
              (user?.role === "account_manager" ||
                user?.role === "general_manager") && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAdd}
                  sx={{
                    backgroundColor: "#db4a41",
                    color: "white",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      backgroundColor: "#c62828",
                    },
                  }}
                >
                  Add Event
                </Button>
              )}
          </Box>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Stats Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#4caf50", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Total Events
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {filteredEvents.length}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#2196f3", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            This Month
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredEvents.filter((event) => {
                              const eventDate = new Date(event.startDate);
                              const now = new Date();
                              return (
                                eventDate.getMonth() === now.getMonth() &&
                                eventDate.getFullYear() === now.getFullYear()
                              );
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#ff9800", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#ff9800",
                            }}
                          >
                            Upcoming
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredEvents.filter((event) => {
                              const eventDate = new Date(event.startDate);
                              const now = new Date();
                              return eventDate > now;
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#f44336", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#f44336",
                            }}
                          >
                            Overdue
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredEvents.filter((event) => {
                              const eventDate = new Date(event.endDate);
                              const now = new Date();
                              return eventDate < now;
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Calendar View */}
              {viewMode === "calendar" && (
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                    mb: 4,
                  }}
                >
                  <CardContent>
                    {/* Calendar Header */}
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mb: 3,
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                      >
                        Calendar View
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <IconButton
                          onClick={() => navigateMonth(-1)}
                          sx={{ color: "white" }}
                        >
                          <ChevronLeftIcon />
                        </IconButton>
                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            minWidth: "200px",
                            textAlign: "center",
                          }}
                        >
                          {getMonthName(currentDate)}
                        </Typography>
                        <IconButton
                          onClick={() => navigateMonth(1)}
                          sx={{ color: "white" }}
                        >
                          <ChevronRightIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Calendar Grid */}
                    <Box
                      sx={{
                        display: "grid",
                        gridTemplateColumns: "repeat(7, 1fr)",
                        gap: 1,
                        mb: 2,
                      }}
                    >
                      {/* Day Headers */}
                      {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                        (day) => (
                          <Box
                            key={day}
                            sx={{
                              p: 1,
                              textAlign: "center",
                              fontWeight: "bold",
                              color: "#db4a41",
                              borderBottom:
                                "1px solid rgba(255, 255, 255, 0.1)",
                            }}
                          >
                            {day}
                          </Box>
                        )
                      )}

                      {/* Empty cells for days before month starts */}
                      {Array.from({
                        length: getFirstDayOfMonth(currentDate),
                      }).map((_, index) => (
                        <Box
                          key={`empty-${index}`}
                          sx={{ p: 1, minHeight: 80 }}
                        />
                      ))}

                      {/* Calendar Days */}
                      {Array.from({ length: getDaysInMonth(currentDate) }).map(
                        (_, index) => {
                          const day = index + 1;
                          const dayEvents = getEventsForDate(day);
                          const isToday =
                            new Date().toDateString() ===
                            new Date(
                              currentDate.getFullYear(),
                              currentDate.getMonth(),
                              day
                            ).toDateString();

                          return (
                            <Paper
                              key={day}
                              onClick={() => handleDayClick(day)}
                              sx={{
                                p: 1,
                                minHeight: 80,
                                backgroundColor: isToday
                                  ? "rgba(219, 74, 65, 0.2)"
                                  : "rgba(255, 255, 255, 0.02)",
                                border: isToday
                                  ? "2px solid #db4a41"
                                  : "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                                cursor: "pointer",
                                transition: "all 0.2s",
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                  transform: "translateY(-2px)",
                                },
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  color: isToday ? "#db4a41" : "white",
                                  fontWeight: isToday ? "bold" : "normal",
                                  mb: 0.5,
                                }}
                              >
                                {day}
                              </Typography>

                              {/* Event indicators */}
                              {dayEvents
                                .slice(0, 3)
                                .map((event, eventIndex) => (
                                  <Badge
                                    key={event._id}
                                    sx={{
                                      display: "block",
                                      mb: 0.5,
                                      "& .MuiBadge-badge": {
                                        position: "static",
                                        transform: "none",
                                        backgroundColor: getEventTypeColor(
                                          event.type
                                        ),
                                        color: "white",
                                        fontSize: "10px",
                                        height: "16px",
                                        minWidth: "16px",
                                        borderRadius: "8px",
                                        padding: "0 4px",
                                      },
                                    }}
                                    badgeContent={
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          fontSize: "9px",
                                          fontWeight: "bold",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          whiteSpace: "nowrap",
                                          maxWidth: "60px",
                                        }}
                                      >
                                        {event.title.length > 8
                                          ? `${event.title.substring(0, 8)}...`
                                          : event.title}
                                      </Typography>
                                    }
                                  />
                                ))}

                              {/* Show "+X more" if there are more events */}
                              {dayEvents.length > 3 && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "#db4a41",
                                    fontSize: "9px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  +{dayEvents.length - 3} more
                                </Typography>
                              )}
                            </Paper>
                          );
                        }
                      )}
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Events Table */}
              {viewMode === "list" && (
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 3,
                      }}
                    >
                      Client Events
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Title
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Client
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Type
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Start Date
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              End Date
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Status
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {filteredEvents
                            .slice(
                              page * rowsPerPage,
                              page * rowsPerPage + rowsPerPage
                            )
                            .map((event) => (
                              <TableRow key={event._id}>
                                <TableCell>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "white",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      {event.title}
                                    </Typography>
                                    {event.description && (
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          display: "block",
                                        }}
                                      >
                                        {event.description.length > 50
                                          ? `${event.description.substring(
                                              0,
                                              50
                                            )}...`
                                          : event.description}
                                      </Typography>
                                    )}
                                  </Box>
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {event.clientId?.name || "N/A"}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={event.type || "General"}
                                    sx={{
                                      backgroundColor: getEventTypeColor(
                                        event.type
                                      ),
                                      color: "white",
                                      textTransform: "capitalize",
                                    }}
                                  />
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {new Date(
                                    event.startDate
                                  ).toLocaleDateString()}
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {new Date(event.endDate).toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={
                                      new Date(event.endDate) < new Date()
                                        ? "Completed"
                                        : new Date(event.startDate) <=
                                          new Date()
                                        ? "Active"
                                        : "Upcoming"
                                    }
                                    sx={{
                                      backgroundColor:
                                        new Date(event.endDate) < new Date()
                                          ? "#4caf50"
                                          : new Date(event.startDate) <=
                                            new Date()
                                          ? "#2196f3"
                                          : "#ff9800",
                                      color: "white",
                                    }}
                                  />
                                </TableCell>

                                <TableCell>
                                  {(user?.tier === 2 ||
                                    user?.tier === 3 ||
                                    user?.tier === 1) &&
                                    (user?.role === "account_manager" ||
                                      user?.role === "general_manager") && (
                                      <Box sx={{ display: "flex", gap: 1 }}>
                                        <Tooltip title="Edit Event">
                                          <IconButton
                                            size="small"
                                            onClick={() => handleEdit(event)}
                                            sx={{ color: "#2196f3" }}
                                          >
                                            <EditIcon />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Delete Event">
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              handleDelete(event._id)
                                            }
                                            sx={{ color: "#f44336" }}
                                          >
                                            <DeleteIcon />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    )}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    <TablePagination
                      component="div"
                      count={filteredEvents.length}
                      page={page}
                      onPageChange={(_, newPage) => setPage(newPage)}
                      rowsPerPage={rowsPerPage}
                      onRowsPerPageChange={(event) => {
                        setRowsPerPage(parseInt(event.target.value, 10));
                        setPage(0);
                      }}
                      sx={{
                        color: "white",
                        "& .MuiTablePagination-selectIcon": {
                          color: "white",
                        },
                      }}
                    />
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </Box>

        {/* Add/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingEvent ? "Edit Event" : "Add New Event"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Title"
                  value={newEvent.title}
                  onChange={(e) =>
                    setNewEvent({ ...newEvent, title: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client
                  </InputLabel>
                  <Select
                    value={newEvent.clientId}
                    onChange={(e) =>
                      setNewEvent({ ...newEvent, clientId: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {clients.map((client) => (
                      <MenuItem key={client._id} value={client._id}>
                        {client.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Type
                  </InputLabel>
                  <Select
                    value={newEvent.type}
                    onChange={(e) =>
                      setNewEvent({ ...newEvent, type: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="Planning">Planning</MenuItem>
                    <MenuItem value="Shooting">Shooting</MenuItem>
                    <MenuItem value="Editing">Editing</MenuItem>
                    <MenuItem value="other">other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="date"
                  value={newEvent.startDate}
                  onChange={(e) =>
                    setNewEvent({ ...newEvent, startDate: e.target.value })
                  }
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="date"
                  value={newEvent.endDate}
                  onChange={(e) =>
                    setNewEvent({ ...newEvent, endDate: e.target.value })
                  }
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={newEvent.description}
                  onChange={(e) =>
                    setNewEvent({ ...newEvent, description: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingEvent ? "Update Event" : "Create Event"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Day Details Modal */}
        <Dialog
          open={dayDetailsModal}
          onClose={handleCloseDayDetails}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box>
              Events for{" "}
              {selectedDay?.toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </Box>
            {(user?.tier === 2 || user?.tier === 3) &&
              (user?.role === "account_manager" ||
                user?.role === "general_manager") && (
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const dateString = formatDateForInput(selectedDay);
                    setNewEvent({
                      clientId: "",
                      title: "",
                      type: "",
                      description: "",
                      startDate: dateString,
                      endDate: dateString,
                    });
                    setEditingEvent(null);
                    handleCloseDayDetails();
                    setOpenModal(true);
                  }}
                  sx={{
                    backgroundColor: "#db4a41",
                    "&:hover": { backgroundColor: "#c62828" },
                  }}
                >
                  Add Event
                </Button>
              )}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {selectedDayEvents.length > 0 ? (
              <Grid container spacing={2}>
                {selectedDayEvents.map((event) => (
                  <Grid item xs={12} key={event._id}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        cursor: "pointer",
                        transition: "all 0.2s",
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          transform: "translateY(-2px)",
                        },
                      }}
                      onClick={() => {
                        handleEdit(event);
                        handleCloseDayDetails();
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            mb: 2,
                          }}
                        >
                          <Box>
                            <Typography
                              variant="h6"
                              sx={{
                                color: "white",
                                fontFamily: "Formula Bold",
                                mb: 1,
                              }}
                            >
                              {event.title}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                            >
                              Client: {event.clientId?.name || "N/A"}
                            </Typography>
                            {event.description && (
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                              >
                                {event.description}
                              </Typography>
                            )}
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "flex-end",
                              gap: 1,
                            }}
                          >
                            <Chip
                              label={event.type || "General"}
                              sx={{
                                backgroundColor: getEventTypeColor(event.type),
                                color: "white",
                                textTransform: "capitalize",
                              }}
                            />
                            <Typography
                              variant="caption"
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            >
                              {new Date(event.startDate).toLocaleDateString()} -{" "}
                              {new Date(event.endDate).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Box>
                        {(user?.tier === 2 || user?.tier === 3) &&
                          (user?.role === "account_manager" ||
                            user?.role === "general_manager") && (
                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                              }}
                            >
                              <Tooltip title="Edit Event">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEdit(event);
                                    handleCloseDayDetails();
                                  }}
                                  sx={{ color: "#2196f3" }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Event">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDelete(event._id);
                                    handleCloseDayDetails();
                                  }}
                                  sx={{ color: "#f44336" }}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Typography
                variant="body1"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  textAlign: "center",
                  py: 4,
                }}
              >
                No events scheduled for this day
              </Typography>
            )}
          </DialogContent>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ClientEventManagement;
