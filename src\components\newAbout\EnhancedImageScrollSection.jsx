import React, { useRef } from "react";
import { Box } from "@mui/material";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";
import ScrollRevealText from "../animations/scrollRevealText";

/* ---------------- IMAGE + POSITION CONFIG ---------------- */

const images = [
  "/assets/about/about-1.jpeg",
  "/assets/about/about-2.jpeg",
  "/assets/about/about-3.jpeg",
  "/assets/about/about-4.jpeg",
];

const positions = [
  { top: "20%", left: "15%", width: 220, height: 300 },
  { top: "25%", left: "65%", width: 200, height: 250 },
  { top: "60%", left: "20%", width: 240, height: 350 },
  { top: "65%", left: "70%", width: 180, height: 280 },
];

/* ---------------- COMPONENT ---------------- */

export default function EnhancedImageScrollSection() {
  const containerRef = useRef(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 25,
    damping: 20,
  });

  // Vertical movement
  const imageTransformsY = [
    useTransform(
      smoothProgress,
      [0, 0.3, 0.7, 1],
      ["-200px", "0px", "0px", "200px"]
    ),
    useTransform(
      smoothProgress,
      [0, 0.4, 0.6, 1],
      ["-250px", "50px", "50px", "150px"]
    ),
    useTransform(
      smoothProgress,
      [0, 0.2, 0.8, 1],
      ["200px", "0px", "0px", "-250px"]
    ),
    useTransform(
      smoothProgress,
      [0, 0.5, 0.5, 1],
      ["300px", "-100px", "-100px", "-200px"]
    ),
  ];

  // Horizontal movement
  const imageTransformsX = [
    useTransform(smoothProgress, [0, 1], ["-30px", "30px"]),
    useTransform(smoothProgress, [0, 1], ["40px", "-20px"]),
    useTransform(smoothProgress, [0, 1], ["-50px", "50px"]),
    useTransform(smoothProgress, [0, 1], ["25px", "-40px"]),
  ];

  // Scaling + rotation
  const scales = [
    useTransform(smoothProgress, [0, 0.5, 1], [0.7, 1.1, 0.8]),
    useTransform(smoothProgress, [0, 0.5, 1], [0.8, 1.2, 0.7]),
    useTransform(smoothProgress, [0, 0.5, 1], [0.6, 1.0, 0.9]),
    useTransform(smoothProgress, [0, 0.5, 1], [0.9, 1.1, 0.6]),
  ];

  const rotations = [
    useTransform(smoothProgress, [0, 1], [-8, 12]),
    useTransform(smoothProgress, [0, 1], [10, -15]),
    useTransform(smoothProgress, [0, 1], [-12, 8]),
    useTransform(smoothProgress, [0, 1], [15, -10]),
  ];

  const opacities = [
    useTransform(smoothProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]),
    useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]),
    useTransform(smoothProgress, [0, 0.1, 0.9, 1], [0, 1, 1, 0]),
    useTransform(smoothProgress, [0, 0.4, 0.6, 1], [0, 1, 1, 0]),
  ];

  return (
    <Box
      ref={containerRef}
      sx={{
        height: "300vh",
        position: "relative",
        overflow: "hidden",
        backgroundColor: "#000",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {/* Text */}
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          zIndex: 15,
          width: "70%",
          maxWidth: "1200px",
          color: "white",
          textAlign: "center",
          fontFamily: "formula bold",
          fontSize: "clamp(1.8rem, 3.5vw, 4rem)",
          lineHeight: 1.4,
          textShadow: "2px 2px 12px rgba(0,0,0,.9)",
          pointerEvents: "none",
        }}
      >
        <ScrollRevealText enableBlur baseOpacity={0.2}>
          Welcome to YOUNG HOUSE, a creative hub where we bring innovation and a
          touch of madness to life. We're all about turning ideas into stunning
          solutions that leave a lasting impression.
        </ScrollRevealText>
      </Box>

      {/* Images */}
      {images.map((src, i) => (
        <motion.img
          key={i}
          src={process.env.PUBLIC_URL + src}
          style={{
            position: "absolute",
            width: positions[i].width,
            height: positions[i].height,
            objectFit: "cover",
            borderRadius: "8px",
            top: positions[i].top,
            left: positions[i].left,
            zIndex: 5 + i,
            y: imageTransformsY[i],
            x: imageTransformsX[i],
            scale: scales[i],
            rotate: rotations[i],
            opacity: opacities[i],
            filter: "brightness(.85) contrast(1.1) saturate(1.1)",
            boxShadow: "0 25px 80px rgba(0,0,0,.7)",
          }}
          whileHover={{
            scale: 1.15,
            filter: "brightness(1.1) contrast(1.3) saturate(1.3)",
            zIndex: 20,
            transition: { duration: 0.4 },
          }}
        />
      ))}
    </Box>
  );
}
