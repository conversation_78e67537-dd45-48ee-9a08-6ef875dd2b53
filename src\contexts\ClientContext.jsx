import React, { createContext, useContext, useState, useEffect } from "react";

const ClientContext = createContext();

export const useClient = () => {
  const context = useContext(ClientContext);
  if (!context) {
    throw new Error("useClient must be used within a ClientProvider");
  }
  return context;
};

export const ClientProvider = ({ children }) => {
  const [clientUser, setClientUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing client session
    const token = localStorage.getItem("clientToken");
    const userData = localStorage.getItem("clientUser");

    if (token && userData) {
      try {
        setClientUser(JSON.parse(userData));
      } catch (error) {
        console.error("Error parsing client user data:", error);
        localStorage.removeItem("clientToken");
        localStorage.removeItem("clientUser");
      }
    }
    setLoading(false);
  }, []);

  const login = (token, userData) => {
    localStorage.setItem("clientToken", token);
    localStorage.setItem("clientUser", JSON.stringify(userData));
    setClientUser(userData);
  };

  const logout = () => {
    localStorage.removeItem("clientToken");
    localStorage.removeItem("clientUser");
    setClientUser(null);
  };

  const isAuthenticated = () => {
    const token = localStorage.getItem("clientToken");
    return !!token && !!clientUser;
  };

  const updateUser = (userData) => {
    localStorage.setItem("clientUser", JSON.stringify(userData));
    setClientUser(userData);
  };

  const value = {
    clientUser,
    loading,
    login,
    logout,
    isAuthenticated,
    updateUser,
  };

  return (
    <ClientContext.Provider value={value}>{children}</ClientContext.Provider>
  );
};
