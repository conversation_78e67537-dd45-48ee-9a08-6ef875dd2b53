import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Button,
  Modal,
  TextField,
  Box,
  Paper,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

function ClientsLogos() {
  const [clients, setClients] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [newClientName, setNewClientName] = useState("");
  const [newClientAlt, setNewClientAlt] = useState("");
  const [logoFile, setLogoFile] = useState(null);

  const handleDelete = async (clientId) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this client? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await axios.delete(
        `https://youngproductions-768ada043db3.herokuapp.com/api/clientsLogos/${clientId}`
      );

      // Remove the deleted client from the state
      setClients(clients.filter((client) => client._id !== clientId));

      // Close modal if it's open
      setOpenModal(false);
      setNewClientName("");
      setNewClientAlt("");
      setLogoFile(null);
      setEditingClient(null);

      console.log("Client deleted successfully");
    } catch (error) {
      console.error("Error deleting client:", error);
      console.error("Error details:", error.response?.data);
      alert(
        `Error deleting client: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  };

  // Fetch clients
  const fetchData = async () => {
    try {
      const response = await axios.get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/clientsLogos"
      );
      console.log("Fetched clients:", response.data);

      // Ensure we have a valid array
      if (Array.isArray(response.data)) {
        setClients(response.data);
      } else {
        console.error("Invalid data format received:", response.data);
        setClients([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      console.error("Error details:", error.response?.data);
      setClients([]); // Set empty array on error
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleAdd = () => {
    setEditingClient(null);
    setNewClientName("");
    setNewClientAlt("");
    setLogoFile(null);
    setOpenModal(true);
  };

  const handleEdit = (client) => {
    setEditingClient(client);
    setNewClientName(client.name);
    setNewClientAlt(client.alt || "");
    setLogoFile(null); // Clear file input for editing
    setOpenModal(true);
  };

  const handleSubmit = async () => {
    try {
      // Validation
      if (!newClientName.trim()) {
        alert("Please enter a client name");
        return;
      }

      if (!newClientAlt.trim()) {
        alert("Please enter alt text for the logo");
        return;
      }

      if (!editingClient && !logoFile) {
        alert("Please select a logo file");
        return;
      }

      const formData = new FormData();
      formData.append("name", newClientName.trim());
      formData.append("alt", newClientAlt.trim());

      // Only append logo if a new file is selected
      if (logoFile) {
        formData.append("logoUrl", logoFile);
      }

      // Debug logging
      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      let response;
      if (editingClient) {
        // Update existing client
        console.log("Updating client:", editingClient._id);
        response = await axios.put(
          `https://youngproductions-768ada043db3.herokuapp.com/api/clientsLogos/${editingClient._id}`,
          formData
        );
        console.log("Update response:", response.data);
        setClients(
          clients.map((client) =>
            client._id === editingClient._id ? response.data : client
          )
        );
      } else {
        // Add new client
        console.log("Adding new client");
        response = await axios.post(
          "https://youngproductions-768ada043db3.herokuapp.com/api/clientsLogos",
          formData
        );
        console.log("Add response:", response.data);
        setClients([...clients, response.data]);
      }

      // Close modal and reset form
      setOpenModal(false);
      setNewClientName("");
      setNewClientAlt("");
      setLogoFile(null);
      setEditingClient(null);
    } catch (error) {
      console.error("Error saving client:", error);
      console.error("Error response:", error.response);
      console.error("Error status:", error.response?.status);
      console.error("Error data:", error.response?.data);
      console.error("Error headers:", error.response?.headers);

      // Try to get more detailed error message
      let errorMessage = "Unknown error occurred";
      if (error.response?.data) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      } else {
        errorMessage = error.message;
      }

      alert(`Error saving client: ${errorMessage}`);
    }
  };

  return (
    <div
      style={{
        backgroundColor: "black",
        padding: "20px 5vw",
        minHeight: "100vh",
      }}
    >
      {/* Responsive header with button */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          alignItems: { xs: "stretch", sm: "center" },
          justifyContent: "space-between",
          mb: 3,
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            textAlign: { xs: "left", sm: "center" },
            marginTop: "5px",
            marginBottom: { xs: 2, sm: 0 },
            fontSize: { xs: "2rem", sm: "2.5rem", md: "3rem" },
          }}
        >
          Clients
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAdd}
          sx={{
            mt: { xs: 2, sm: 0 },
            alignSelf: { xs: "flex-start", sm: "center" },
            background: "#db4a41",
            "&:hover": { background: "#c43a31" },
            minWidth: 180,
          }}
        >
          Add New Client
        </Button>
      </Box>
      <Grid container spacing={3} sx={{ mt: 2 }}>
        {clients && clients.length > 0 ? (
          clients.map((client) => (
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={3}
              key={client._id || client.id}
            >
              <Card
                onClick={() => handleEdit(client)}
                sx={{
                  background: "rgba(255,255,255,0.05)",
                  border: "1px solid rgba(255,255,255,0.1)",
                  borderRadius: "12px",
                  boxShadow: 3,
                  cursor: "pointer",
                  transition: "transform 0.2s",
                  "&:hover": { transform: "scale(1.03)", boxShadow: 6 },
                  color: "white",
                  minHeight: 320,
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  width: "100%",
                  maxWidth: { xs: "100%", sm: 360 },
                  mx: "auto",
                }}
              >
                {client.logoUrl && (
                  <CardMedia
                    component="img"
                    height="120"
                    image={client.logoUrl.replace(
                      "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                      "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                    )}
                    alt={client.alt || client.name}
                    sx={{ objectFit: "contain", background: "#222", p: 2 }}
                  />
                )}
                <CardContent>
                  <Typography
                    variant="h6"
                    sx={{
                      color: "#db4a41",
                      fontWeight: 700,
                      fontSize: { xs: "1.1rem", sm: "1.25rem" },
                    }}
                  >
                    {client.name}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))
        ) : (
          <Grid item xs={12}>
            <Typography
              variant="h6"
              sx={{ color: "white", textAlign: "center" }}
            >
              No clients found
            </Typography>
          </Grid>
        )}
      </Grid>
      {/* Modal for adding/editing client */}
      <Modal
        open={openModal}
        onClose={() => setOpenModal(false)}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 400,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            {editingClient ? "Edit Client" : "Add New Client"}
          </Typography>

          {/* Current Logo Preview */}
          {editingClient && editingClient.logoUrl && (
            <Box
              sx={{
                width: 120,
                height: 120,
                borderRadius: "8px",
                overflow: "hidden",
                margin: "0 auto 20px auto",
                border: "3px solid #db4a41",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(255, 255, 255, 0.1)",
              }}
            >
              <img
                src={editingClient.logoUrl}
                alt={editingClient.alt || editingClient.name}
                style={{
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                }}
              />
            </Box>
          )}

          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
          >
            {/* File Upload */}
            <Box sx={{ mb: 2 }}>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => setLogoFile(e.target.files[0])}
                style={{
                  width: "100%",
                  padding: "10px",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  border: "1px solid rgba(255, 255, 255, 0.23)",
                  borderRadius: "4px",
                  color: "white",
                }}
              />
            </Box>

            <TextField
              fullWidth
              label="Client Name"
              value={newClientName}
              onChange={(e) => setNewClientName(e.target.value)}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Alt Text"
              value={newClientAlt}
              onChange={(e) => setNewClientAlt(e.target.value)}
              required
              placeholder="Describe the logo for accessibility"
              sx={{
                mb: 3,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                gap: 2,
              }}
            >
              {/* Delete button - only show when editing */}
              {editingClient && (
                <Button
                  onClick={() => handleDelete(editingClient._id)}
                  sx={{
                    backgroundColor: "#d32f2f",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "#b71c1c",
                    },
                  }}
                  variant="contained"
                >
                  Delete
                </Button>
              )}

              {/* Spacer when not editing */}
              {!editingClient && <Box />}

              {/* Cancel and Submit buttons */}
              <Box sx={{ display: "flex", gap: 2 }}>
                <Button
                  onClick={() => setOpenModal(false)}
                  sx={{
                    color: "white",
                    borderColor: "white",
                    "&:hover": {
                      borderColor: "#db4a41",
                      color: "#db4a41",
                    },
                  }}
                  variant="outlined"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={
                    !newClientName ||
                    !newClientAlt ||
                    (!logoFile && !editingClient)
                  }
                  sx={{
                    backgroundColor: "#db4a41",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "#c62828",
                    },
                  }}
                  variant="contained"
                >
                  {editingClient ? "Update Client" : "Add Client"}
                </Button>
              </Box>
            </Box>
          </form>
        </Paper>
      </Modal>
    </div>
  );
}

export default ClientsLogos;
