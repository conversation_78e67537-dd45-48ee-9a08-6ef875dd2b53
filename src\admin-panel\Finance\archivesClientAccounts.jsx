import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from "@mui/material";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RestoreIcon from "@mui/icons-material/Restore";
import { motion } from "framer-motion";

// Utility function to parse MongoDB Decimal128 values
const parseDecimal = (value) => {
  if (value && typeof value === "object" && value.$numberDecimal) {
    return parseFloat(value.$numberDecimal);
  }
  return value;
};

function ArchivesClientAccounts() {
  const navigate = useNavigate();
  const [archivedAccounts, setArchivedAccounts] = useState([]);
  const [deletedAccounts, setDeletedAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [unarchiveDialog, setUnarchiveDialog] = useState({
    open: false,
    account: null,
    end_date: "",
  });

  const ARCHIVED_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-Account/archived/list";
  const DELETED_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account/deleted/list";

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      const fetchArchivedAccounts = async () => {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(ARCHIVED_API_URL, {
            headers: { Authorization: `Bearer ${token}` },
          });
          const result = await response.json();
          if (Array.isArray(result)) {
            setArchivedAccounts(
              result.map((account) => ({
                ...account,
                subscription_fee: parseDecimal(account.subscription_fee),
              }))
            );
          } else if (result.data && Array.isArray(result.data)) {
            setArchivedAccounts(
              result.data.map((account) => ({
                ...account,
                subscription_fee: parseDecimal(account.subscription_fee),
              }))
            );
          } else {
            console.error("Unexpected archived API response:", result);
            showSnackbar(
              "Unexpected data format for archived accounts",
              "error"
            );
          }
        } catch (error) {
          console.error("Error fetching archived accounts:", error);
          showSnackbar("Failed to fetch archived accounts", "error");
        }
      };

      const fetchDeletedAccounts = async () => {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(DELETED_API_URL, {
            headers: { Authorization: `Bearer ${token}` },
          });
          const result = await response.json();
          if (Array.isArray(result)) {
            setDeletedAccounts(
              result.map((account) => ({
                ...account,
                subscription_fee: parseDecimal(account.subscription_fee),
              }))
            );
          } else if (result.data && Array.isArray(result.data)) {
            setDeletedAccounts(
              result.data.map((account) => ({
                ...account,
                subscription_fee: parseDecimal(account.subscription_fee),
              }))
            );
          } else {
            console.error("Unexpected deleted API response:", result);
            showSnackbar(
              "Unexpected data format for deleted accounts",
              "error"
            );
          }
        } catch (error) {
          console.error("Error fetching deleted accounts:", error);
          showSnackbar("Failed to fetch deleted accounts", "error");
        }
      };

      await Promise.all([fetchArchivedAccounts(), fetchDeletedAccounts()]);
      setLoading(false);
    };

    fetchData();
  }, [ARCHIVED_API_URL, DELETED_API_URL]);

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleUnarchive = async () => {
    if (!unarchiveDialog.account || !unarchiveDialog.end_date) return;
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account/archived/unarchive/${unarchiveDialog.account._id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ end_date: unarchiveDialog.end_date }),
        }
      );

      if (response.ok) {
        setArchivedAccounts(
          archivedAccounts.filter(
            (acc) => acc._id !== unarchiveDialog.account._id
          )
        );
        showSnackbar("Account unarchived successfully", "success");
        setUnarchiveDialog({ open: false, account: null, end_date: "" });
      } else {
        throw new Error("Failed to unarchive account");
      }
    } catch (error) {
      console.error("Error unarchiving account:", error);
      showSnackbar("Failed to unarchive account", "error");
    }
  };

  const handleOpenUnarchiveDialog = (account) => {
    setUnarchiveDialog({ open: true, account });
  };

  const handleCloseUnarchiveDialog = () => {
    setUnarchiveDialog({ open: false, account: null, end_date: "" });
  };

  const handleEndDateChange = (event) => {
    setUnarchiveDialog((prev) => ({
      ...prev,
      end_date: event.target.value,
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "archived":
        return "#ff9800";
      case "deleted":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }
    let color = "#";
    for (let i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    return color;
  };

  const AccountCard = ({ account, status }) => (
    <Grid item xs={12} sm={6} md={4}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card
          sx={{
            background: "rgba(255, 255, 255, 0.05)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
            cursor: "pointer",
            "&:hover": {
              background: "rgba(255, 255, 255, 0.08)",
              borderColor: "rgba(219, 74, 65, 0.3)",
            },
          }}
          onClick={() =>
            navigate(
              `/admin/financial/archives/subscription-cycles/${account._id}`
            )
          }
        >
          <CardContent sx={{ flexGrow: 1, pt: 3 }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                mb: 2,
              }}
            >
              <AccountBalanceIcon
                sx={{
                  bgcolor: stringToColor(account.client_name),
                  borderRadius: "50%",
                  p: 1,
                  mr: 2,
                  color: "white",
                }}
              />
              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "Formula Bold",
                    color: "white",
                    fontSize: "1.1rem",
                  }}
                >
                  {account.client_name}
                </Typography>
                <Chip
                  label={status}
                  size="small"
                  sx={{
                    backgroundColor: `${getStatusColor(status)}20`,
                    color: getStatusColor(status),
                    textTransform: "capitalize",
                    fontFamily: "Formula Bold",
                  }}
                />
              </Box>
            </Box>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                fontFamily: "Anton",
              }}
            >
              Subscription Fee: {account.subscription_fee || "N/A"} EGP
            </Typography>
            {status === "archived" && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<RestoreIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenUnarchiveDialog(account);
                  }}
                  sx={{
                    backgroundColor: "#4caf50",
                    color: "white",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      backgroundColor: "#388e3c",
                    },
                  }}
                >
                  Unarchive
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </Grid>
  );

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate("/admin/financial/archives")}
            sx={{
              color: "#db4a41",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "rgba(219, 74, 65, 0.1)",
              },
            }}
          >
            Back to Archieves
          </Button>
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Archieved Client Accounts
          </Typography>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Archived Accounts */}
              <Typography
                variant="h4"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "#ff9800",
                  mb: 3,
                }}
              >
                Archived Accounts
              </Typography>
              <Grid container spacing={3} sx={{ mb: 5 }}>
                {archivedAccounts.length > 0 ? (
                  archivedAccounts.map((account) => (
                    <AccountCard
                      key={account._id}
                      account={account}
                      status="archived"
                    />
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        textAlign: "center",
                      }}
                    >
                      No archived accounts found
                    </Typography>
                  </Grid>
                )}
              </Grid>

              {/* Deleted Accounts */}
              <Typography
                variant="h4"
                sx={{
                  fontFamily: "Formula Bold",
                  color: "#f44336",
                  mb: 3,
                }}
              >
                Deleted Accounts
              </Typography>
              <Grid container spacing={3}>
                {deletedAccounts.length > 0 ? (
                  deletedAccounts.map((account) => (
                    <AccountCard
                      key={account._id}
                      account={account}
                      status="deleted"
                    />
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        textAlign: "center",
                      }}
                    >
                      No deleted accounts found
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </>
          )}
        </Box>

        {/* Unarchive Confirmation Dialog */}
        <Dialog
          open={unarchiveDialog.open}
          onClose={handleCloseUnarchiveDialog}
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Confirm Unarchive
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Typography sx={{ color: "white", mb: 3 }}>
              Unarchive the account for{" "}
              <strong>{unarchiveDialog.account?.client_name}</strong>? This will
              restore the account to active status.
            </Typography>
            <TextField
              label="End Date"
              type="date"
              value={unarchiveDialog.end_date}
              onChange={handleEndDateChange}
              fullWidth
              required
              InputLabelProps={{
                shrink: true,
                sx: { color: "rgba(255, 255, 255, 0.7)" },
              }}
              InputProps={{
                sx: {
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#4caf50",
                  },
                },
              }}
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseUnarchiveDialog}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUnarchive}
              variant="contained"
              sx={{
                backgroundColor: "#4caf50",
                "&:hover": { backgroundColor: "#388e3c" },
              }}
            >
              Unarchive
            </Button>
          </DialogActions>
        </Dialog>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ArchivesClientAccounts;
