import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Button,
} from "@mui/material";
import LoopIcon from "@mui/icons-material/Loop";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { motion } from "framer-motion";

function ArchivesSubscriptionCycles() {
  const { client_id } = useParams();
  const navigate = useNavigate();
  const [cycles, setCycles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const API_URL = `https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles/${client_id}/get-client-cycles`;

  useEffect(() => {
    const fetchCycles = async () => {
      setLoading(true);
      try {
        const token = localStorage.getItem("token");
        const response = await fetch(API_URL, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        const result = await response.json();
        if (Array.isArray(result)) {
          setCycles(result);
        } else if (result.data && Array.isArray(result.data)) {
          setCycles(result.data);
        } else {
          console.error("Unexpected API response:", result);
          showSnackbar("Unexpected data format for cycles", "error");
        }
      } catch (error) {
        console.error("Error fetching cycles:", error);
        showSnackbar("Failed to fetch subscription cycles", "error");
      } finally {
        setLoading(false);
      }
    };

    if (client_id) {
      fetchCycles();
    }
  }, [client_id, API_URL]); // API_URL depends on client_id

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "#4caf50";
      case "inactive":
        return "#ff9800";
      case "completed":
        return "#2196f3";
      default:
        return "#9e9e9e";
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() =>
              navigate("/admin/financial/archives/client-accounts")
            }
            sx={{
              color: "#db4a41",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "rgba(219, 74, 65, 0.1)",
              },
            }}
          >
            Back to Accounts
          </Button>
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Subscription Cycles
          </Typography>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {cycles.length > 0 ? (
                cycles.map((cycle) => (
                  <Grid item xs={12} sm={6} md={4} key={cycle._id}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          height: "100%",
                          display: "flex",
                          flexDirection: "column",
                        }}
                      >
                        <CardContent sx={{ flexGrow: 1, pt: 3 }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            <LoopIcon
                              sx={{
                                color: "#db4a41",
                                mr: 2,
                                fontSize: 40,
                              }}
                            />
                            <Box>
                              <Typography
                                variant="h6"
                                sx={{
                                  fontFamily: "Formula Bold",
                                  color: "white",
                                  fontSize: "1.1rem",
                                }}
                              >
                                Cycle{" "}
                                {cycle.cycle_number || cycle._id.slice(-4)}
                              </Typography>
                              <Chip
                                label={cycle.status || "unknown"}
                                size="small"
                                sx={{
                                  backgroundColor: `${getStatusColor(
                                    cycle.status
                                  )}20`,
                                  color: getStatusColor(cycle.status),
                                  textTransform: "capitalize",
                                  fontFamily: "Formula Bold",
                                }}
                              />
                            </Box>
                          </Box>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255, 255, 255, 0.7)",
                              mb: 1,
                              fontFamily: "Anton",
                            }}
                          >
                            Start:{" "}
                            {cycle.start_date
                              ? new Date(cycle.start_date).toLocaleDateString()
                              : "N/A"}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255, 255, 255, 0.7)",
                              mb: 1,
                              fontFamily: "Anton",
                            }}
                          >
                            End:{" "}
                            {cycle.end_date
                              ? new Date(cycle.end_date).toLocaleDateString()
                              : "N/A"}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255, 255, 255, 0.7)",
                              fontFamily: "Anton",
                            }}
                          >
                            Amount: {cycle.amount || "N/A"} EGP
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{
                      color: "rgba(255, 255, 255, 0.7)",
                      textAlign: "center",
                      mt: 4,
                    }}
                  >
                    No subscription cycles found for this client
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </Box>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ArchivesSubscriptionCycles;
