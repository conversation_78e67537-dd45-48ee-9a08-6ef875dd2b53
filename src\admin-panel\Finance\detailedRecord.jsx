import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  TextField,
  LinearProgress,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RefreshIcon from "@mui/icons-material/Refresh";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ReceiptIcon from "@mui/icons-material/Receipt";
import CalculateIcon from "@mui/icons-material/Calculate";
import { motion } from "framer-motion";
import {
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Tooltip as RechartsTooltip,
} from "recharts";

function DetailedRecord() {
  const { period } = useParams();
  const navigate = useNavigate();
  const [record, setRecord] = useState(null);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [clientRevenue, setClientRevenue] = useState("");
  const [updating, setUpdating] = useState(false);

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/agency-finance";

  const showSnackbar = (message, severity) =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const fetchRecord = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${period}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await res.json();
      setRecord(result?.data || null);
    } catch (err) {
      console.error("Error fetching record:", err);
      setRecord(null);
      showSnackbar("Failed to fetch record", "error");
    } finally {
      setLoading(false);
    }
  }, [period]);

  useEffect(() => {
    fetchRecord();
  }, [fetchRecord]);

  const handleRecalculate = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/${period}/recalculate`, {
        method: "PUT",
      });
      if (response.ok) {
        showSnackbar("Finance recalculated", "success");
        fetchRecord();
      } else {
        throw new Error("Failed to recalculate");
      }
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to recalculate", "error");
    }
  };

  const handleUpdateRevenue = async () => {
    if (!clientRevenue) {
      showSnackbar("Enter revenue amount", "warning");
      return;
    }
    setUpdating(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${period}/client-revenue`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ client_revenue: parseFloat(clientRevenue) }),
      });
      if (response.ok) {
        showSnackbar("Revenue updated", "success");
        setClientRevenue("");
        fetchRecord();
      } else {
        throw new Error("Failed to update revenue");
      }
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to update revenue", "error");
    } finally {
      setUpdating(false);
    }
  };

  const COLORS = ["#2196f3", "#ff9800", "#4caf50", "#f44336", "#9c27b0"];
  const expenseBreakdown = useMemo(() => {
    if (!record) return [];
    return [
      {
        name: "Salaries",
        value: parseFloat(record.salaries_expense?.$numberDecimal || 0),
      },
      {
        name: "Agency Expenses",
        value: parseFloat(record.agency_expenses?.$numberDecimal || 0),
      },
    ].filter((i) => i.value > 0);
  }, [record]);

  const exportCsv = () => {
    if (!record) return;
    const rows = [];
    rows.push(["Period", record.period]);
    rows.push([
      "Client Revenue",
      record.client_revenue?.$numberDecimal || record.client_revenue || 0,
    ]);
    rows.push([
      "Total Inflow",
      record.total_inflow?.$numberDecimal || record.total_inflow || 0,
    ]);
    rows.push([
      "Total Outflow",
      record.total_outflow?.$numberDecimal || record.total_outflow || 0,
    ]);
    rows.push([
      "Net Profit",
      record.net_profit?.$numberDecimal || record.net_profit || 0,
    ]);
    rows.push([]);
    rows.push([
      "Salaries Expense",
      record.salaries_expense?.$numberDecimal || record.salaries_expense || 0,
    ]);
    rows.push([
      "Agency Expenses",
      record.agency_expenses?.$numberDecimal || record.agency_expenses || 0,
    ]);
    rows.push([]);
    rows.push(["Salary Payments"]);
    rows.push([
      "Employee",
      "Role",
      "Month",
      "Year",
      "Net Amount",
      "Paid",
      "Paid At",
    ]);
    (record.salary_payments || []).forEach((p) => {
      rows.push([
        p?.userId?.name || "",
        p?.userId?.role || "",
        p?.month || "",
        p?.year || "",
        p?.net_amount?.$numberDecimal || p?.net_amount || 0,
        p?.paid ? "Yes" : "No",
        p?.paid_at ? new Date(p.paid_at).toLocaleDateString() : "",
      ]);
    });
    rows.push([]);
    rows.push(["Agency Expenses (Items)"]);
    rows.push(["Title", "Category", "Date", "Amount", "Description"]);
    (record.expenses || []).forEach((e) => {
      rows.push([
        e?.title || "",
        e?.category || "",
        e?.date ? new Date(e.date).toLocaleDateString() : "",
        e?.amount?.$numberDecimal || e?.amount || 0,
        (e?.description || "").replace(/\n|\r/g, " "),
      ]);
    });

    const csv = rows
      .map((r) => r.map((v) => `"${String(v).replace(/"/g, '""')}"`).join(","))
      .join("\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `finance-record-${period}.csv`;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(url);
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "60px 5% 40px",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Button
              variant="text"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate(-1)}
              sx={{ color: "#db4a41" }}
            >
              Back
            </Button>
            <Typography
              variant="h4"
              sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
            >
              Finance Record — {period}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              onClick={exportCsv}
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
              }}
            >
              Export CSV
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRecalculate}
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
              }}
            >
              Recalculate
            </Button>
          </Box>
        </Box>

        <Box sx={{ px: "5%", pb: 6 }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", py: 6 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : record ? (
            <>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#4caf50", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Revenue
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(record.client_revenue)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <ReceiptIcon sx={{ color: "#f44336", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#f44336",
                            }}
                          >
                            Expenses
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(record.total_outflow)}
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                          <Chip
                            label={`Salaries: ${formatCurrency(
                              record.salaries_expense
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#2196f3",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                          <Chip
                            label={`Agency: ${formatCurrency(
                              record.agency_expenses
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#ff9800",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          {parseFloat(record.net_profit?.$numberDecimal || 0) >=
                          0 ? (
                            <TrendingUpIcon sx={{ color: "#4caf50", mr: 1 }} />
                          ) : (
                            <TrendingDownIcon
                              sx={{ color: "#f44336", mr: 1 }}
                            />
                          )}
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color:
                                parseFloat(
                                  record.net_profit?.$numberDecimal || 0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            }}
                          >
                            Net Profit
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color:
                              parseFloat(
                                record.net_profit?.$numberDecimal || 0
                              ) >= 0
                                ? "#4caf50"
                                : "#f44336",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(record.net_profit)}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(
                            (Math.abs(
                              parseFloat(record.net_profit?.$numberDecimal || 0)
                            ) /
                              Math.max(
                                parseFloat(
                                  record.total_inflow?.$numberDecimal || 1
                                ),
                                1
                              )) *
                              100,
                            100
                          )}
                          sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            "& .MuiLinearProgress-bar": {
                              backgroundColor:
                                parseFloat(
                                  record.net_profit?.$numberDecimal || 0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            },
                          }}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <AccountBalanceIcon
                            sx={{ color: "#2196f3", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            Cash Flow
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(record.total_inflow)}
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 1,
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{ color: "#4caf50" }}
                          >
                            In: {formatCurrency(record.total_inflow)}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "#f44336" }}
                          >
                            Out: {formatCurrency(record.total_outflow)}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                        >
                          Expense Breakdown
                        </Typography>
                      </Box>
                      {expenseBreakdown.length > 0 ? (
                        <Box sx={{ height: 300 }}>
                          <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                              <Pie
                                data={expenseBreakdown}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                dataKey="value"
                              >
                                {expenseBreakdown.map((entry, index) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={COLORS[index % COLORS.length]}
                                    name={entry.name}
                                  />
                                ))}
                              </Pie>
                              <Legend
                                layout="horizontal"
                                verticalAlign="top"
                                align="center"
                                formatter={(value) => (
                                  <span style={{ color: "#fff" }}>{value}</span>
                                )}
                              />
                              <RechartsTooltip
                                contentStyle={{
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "8px",
                                  color: "white",
                                }}
                                formatter={(value) => [
                                  `${parseFloat(value).toLocaleString()} EGP`,
                                  "Amount",
                                ]}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No expenses recorded for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#db4a41",
                          mb: 1,
                        }}
                      >
                        Update Revenue
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <TextField
                          label="Client Revenue"
                          type="number"
                          value={clientRevenue}
                          onChange={(e) => setClientRevenue(e.target.value)}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              color: "white",
                              "& fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&:hover fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "#db4a41",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              color: "rgba(255, 255, 255, 0.7)",
                            },
                          }}
                        />
                        <Button
                          onClick={handleUpdateRevenue}
                          variant="contained"
                          disabled={updating}
                          sx={{
                            backgroundColor: "#db4a41",
                            "&:hover": { backgroundColor: "#c62828" },
                          }}
                        >
                          {updating ? "Updating..." : "Update"}
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#2196f3",
                          mb: 2,
                        }}
                      >
                        Salary Payments ({record.salary_payments?.length || 0})
                      </Typography>
                      {record.salary_payments?.length ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {(record.salary_payments || []).map(
                            (payment, idx) => (
                              <Box
                                key={payment._id || idx}
                                sx={{
                                  p: 2,
                                  mb: 2,
                                  background: "rgba(33, 150, 243, 0.1)",
                                  borderRadius: "8px",
                                  border: "1px solid rgba(33, 150, 243, 0.2)",
                                }}
                              >
                                <Typography
                                  variant="body1"
                                  sx={{
                                    color: "white",
                                    fontFamily: "Formula Bold",
                                  }}
                                >
                                  {formatCurrency(payment.net_amount)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                >
                                  Employee Name: {payment.userId?.name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                >
                                  Employee Role: {payment.userId?.role}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                >
                                  Month: {payment.month}/{payment.year}
                                </Typography>
                                <Chip
                                  label={payment.paid ? "PAID" : "PENDING"}
                                  size="small"
                                  sx={{
                                    mt: 1,
                                    backgroundColor: payment.paid
                                      ? "#4caf50"
                                      : "#ff9800",
                                    color: "white",
                                  }}
                                />
                              </Box>
                            )
                          )}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No salary payments for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#ff9800",
                          mb: 2,
                        }}
                      >
                        Agency Expenses ({record.expenses?.length || 0})
                      </Typography>
                      {record.expenses?.length ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {(record.expenses || []).map((expense, idx) => (
                            <Box
                              key={expense._id || idx}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(255, 152, 0, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(255, 152, 0, 0.2)",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {expense.title}
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#ff9800",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(expense.amount)}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Category: {expense.category}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Date:{" "}
                                {expense.date
                                  ? new Date(expense.date).toLocaleDateString()
                                  : ""}
                              </Typography>
                              {expense.description && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.6)",
                                    fontStyle: "italic",
                                  }}
                                >
                                  {expense.description}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No agency expenses for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </>
          ) : (
            <Box sx={{ textAlign: "center", py: 6 }}>
              <Typography
                variant="body1"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                No data for this period
              </Typography>
            </Box>
          )}
        </Box>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default DetailedRecord;
