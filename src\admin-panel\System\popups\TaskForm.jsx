import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Box,
  Typography,
} from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { useUser } from "../../../contexts/UserContext";

function TaskForm({ open, onClose, task, refreshTasks, showSnackbar }) {
  const { user } = useUser();
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    type: "custom",
    customType: "",
    priority: "medium",
    status: "pending",
    clientId: "",
    assignedTo: [],
    projectId: "",
    dueDate: "",
    briefDocument: [],
  });
  const [clients, setClients] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const API_URL = "https://youngproductions-768ada043db3.herokuapp.com/api";

  useEffect(() => {
    const fetchTaskDetails = async () => {
      if (task?._id) {
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(`${API_URL}/tasks/${task._id}`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          if (!response.ok) {
            throw new Error("Failed to fetch task details");
          }
          const detailedTask = await response.json();
          setFormData({
            title: detailedTask.title || "",
            description: detailedTask.description || "",
            type: detailedTask.type || "custom",
            customType: detailedTask.customType || "",
            priority: detailedTask.priority || "medium",
            status: detailedTask.status || "pending",
            clientId: detailedTask.clientId?._id || "",
            assignedTo: detailedTask.assignedTo
              ? detailedTask.assignedTo.map((user) => user._id)
              : [],
            projectId: detailedTask.projectId?._id || "",
            dueDate: detailedTask.dueDate
              ? new Date(detailedTask.dueDate).toISOString().split("T")[0]
              : "",
            briefDocument: [],
          });
        } catch (error) {
          console.error("Error fetching task details:", error);
          showSnackbar("Could not load task details.", "error");
        }
      } else {
        setFormData({
          title: "",
          description: "",
          type: "custom",
          customType: "",
          priority: "medium",
          status: "pending",
          clientId: "",
          assignedTo: [],
          projectId: "",
          dueDate: "",
          briefDocument: [],
        });
      }
      // Clear errors when form is opened
      setErrors({});
    };

    if (open) {
      fetchTaskDetails();
    }
  }, [task, open]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("token");
        const headers = { Authorization: `Bearer ${token}` };
        const [clientsRes, employeesRes, projectsRes] = await Promise.all([
          fetch(`${API_URL}/clientsManagement`, { headers }),
          fetch(`${API_URL}/system/employees`, { headers }),
          fetch(`${API_URL}/projects`, { headers }),
        ]);
        setClients(await clientsRes.json());
        setEmployees(await employeesRes.json());
        setProjects(await projectsRes.json());
      } catch (error) {
        console.error("Failed to fetch data for task form", error);
      }
    };
    if (open) {
      fetchData();
    }
  }, [open]);

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.assignedTo || formData.assignedTo.length === 0) {
      newErrors.assignedTo = "At least one person must be assigned";
    }

    // Custom type validation when type is "custom"
    if (formData.type === "custom" && !formData.customType.trim()) {
      newErrors.customType =
        "Custom type is required when type is set to Custom";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: "" });
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    console.log("Selected files:", files);
    setFormData({ ...formData, briefDocument: files });
  };

  const handleSubmit = async () => {
    if (!user?.id) {
      showSnackbar(
        "User data is not available. Please try again shortly.",
        "error"
      );
      return;
    }

    // Validate form before submission
    if (!validateForm()) {
      const errorMessages = Object.values(errors).filter((msg) => msg);
      showSnackbar(
        `Please fix the following errors: ${errorMessages.join(", ")}`,
        "error"
      );
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const url = task ? `${API_URL}/tasks/${task._id}` : `${API_URL}/tasks`;
      const method = task ? "PUT" : "POST";

      const formPayload = new FormData();
      Object.keys(formData).forEach((key) => {
        if (key === "briefDocument") {
          console.log("Brief document files:", formData.briefDocument);
          if (formData.briefDocument && formData.briefDocument.length > 0) {
            formData.briefDocument.forEach((file) => {
              formPayload.append("briefDocument", file);
            });
          }
        } else if (key === "assignedTo") {
          // Handle array fields by appending each element individually
          if (Array.isArray(formData[key]) && formData[key].length > 0) {
            formData[key].forEach((value) => {
              formPayload.append(key, value);
            });
          }
        } else if (key === "clientId" || key === "projectId") {
          // Only append clientId and projectId if they have valid values
          if (formData[key] && formData[key] !== "" && formData[key] !== null) {
            formPayload.append(key, formData[key]);
          }
          console.log(
            `${key} value:`,
            formData[key],
            "- Will be sent:",
            !!(formData[key] && formData[key] !== "" && formData[key] !== null)
          );
        } else if (formData[key] !== null && formData[key] !== "") {
          formPayload.append(key, formData[key]);
        }
      });

      // Debug: Log what's being sent to the server
      console.log("Form data being sent:", formData);
      for (let [key, value] of formPayload.entries()) {
        console.log(`${key}:`, value);
      }

      if (!task) {
        if (!user?.id) {
          throw new Error(
            "User information is missing. Please try logging in again."
          );
        }
        formPayload.append("createdBy", user.id);
      }

      console.log(`Sending ${method} request to: ${url}`);
      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formPayload,
      });

      // Log response details for debugging
      console.log("Response status:", response.status);
      const responseText = await response.text();
      console.log("Response text:", responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
        console.log("Response JSON:", responseData);
      } catch (e) {
        console.log("Response is not JSON:", responseText);
        responseData = { message: responseText };
      }

      if (!response.ok) {
        throw new Error(responseData.message || "Failed to save task");
      }

      showSnackbar(
        `Task ${task ? "updated" : "created"} successfully`,
        "success"
      );
      refreshTasks();
      onClose();
    } catch (error) {
      console.error("Error saving task:", error);
      showSnackbar(error.message || "Failed to save task", "error");
    } finally {
      setLoading(false);
    }
  };

  const MuiStyled = {
    "& .MuiOutlinedInput-root": {
      color: "white",
      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
      "&:hover fieldset": {
        borderColor: "rgba(255, 255, 255, 0.5)",
      },
    },
    "& .MuiInputLabel-root": {
      color: "rgba(255, 255, 255, 0.7)",
    },
    "& .MuiSelect-icon": {
      color: "white",
    },
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          background: "rgba(0, 0, 0, 0.9)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "12px",
          color: "white",
        },
      }}
    >
      <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
        {task ? "Edit Task" : "Add New Task"}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              required
              error={!!errors.title}
              helperText={errors.title}
              sx={{
                ...MuiStyled,
                "& .MuiFormHelperText-root": {
                  color: errors.title ? "#f44336" : "rgba(255, 255, 255, 0.7)",
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              multiline
              rows={4}
              sx={MuiStyled}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={MuiStyled}>
              <InputLabel>Type</InputLabel>
              <Select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                <MenuItem value="creative_strategy">Creative Strategy</MenuItem>
                <MenuItem value="content_schedule">Content Schedule</MenuItem>
                <MenuItem value="shoot">Shoot</MenuItem>
                <MenuItem value="video_editing">Video Editing</MenuItem>
                <MenuItem value="photo_editing">Photo Editing</MenuItem>
                <MenuItem value="coloring">Coloring</MenuItem>
                <MenuItem value="proposal">Proposal</MenuItem>
                <MenuItem value="custom">Custom</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          {formData.type === "custom" && (
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Custom Type"
                name="customType"
                value={formData.customType}
                onChange={handleInputChange}
                required
                error={!!errors.customType}
                helperText={errors.customType}
                sx={{
                  ...MuiStyled,
                  "& .MuiFormHelperText-root": {
                    color: errors.customType
                      ? "#f44336"
                      : "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
          )}
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={MuiStyled}>
              <InputLabel>Priority</InputLabel>
              <Select
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="high">High</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={MuiStyled}>
              <InputLabel>Status</InputLabel>
              <Select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="in_progress">In Progress</MenuItem>
                <MenuItem value="needs_info">Needs Info</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="on_hold">On Hold</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={MuiStyled}>
              <InputLabel>Client (Optional)</InputLabel>
              <Select
                name="clientId"
                value={formData.clientId}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {clients.map((client) => (
                  <MenuItem key={client._id} value={client._id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={MuiStyled}>
              <InputLabel>Project (Optional)</InputLabel>
              <Select
                name="projectId"
                value={formData.projectId}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {projects.map((project) => (
                  <MenuItem key={project._id} value={project._id}>
                    {project.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl
              fullWidth
              required
              error={!!errors.assignedTo}
              sx={MuiStyled}
            >
              <InputLabel>Assigned To</InputLabel>
              <Select
                multiple
                name="assignedTo"
                value={formData.assignedTo}
                onChange={handleInputChange}
                sx={{ color: "white" }}
              >
                {employees.map((emp) => (
                  <MenuItem key={emp._id} value={emp._id}>
                    {emp.name}
                  </MenuItem>
                ))}
              </Select>
              {errors.assignedTo && (
                <Box sx={{ mt: 0.5, ml: 1.75 }}>
                  <Typography
                    variant="caption"
                    sx={{ color: "#f44336", fontSize: "0.75rem" }}
                  >
                    {errors.assignedTo}
                  </Typography>
                </Box>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Due Date"
              name="dueDate"
              type="date"
              value={formData.dueDate}
              onChange={handleInputChange}
              InputLabelProps={{ shrink: true }}
              sx={MuiStyled}
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<CloudUploadIcon />}
              sx={{ color: "white", borderColor: "rgba(255,255,255,0.23)" }}
            >
              Upload Brief Document
              <input type="file" hidden multiple onChange={handleFileChange} />
            </Button>
            {formData.briefDocument.length > 0 && (
              <Typography variant="body2" sx={{ ml: 2, display: "inline" }}>
                {formData.briefDocument.map((f) => f.name).join(", ")}
              </Typography>
            )}
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} sx={{ color: "white" }}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          sx={{
            backgroundColor: "#db4a41",
            "&:hover": { backgroundColor: "#c62828" },
          }}
        >
          {loading ? (
            <CircularProgress size={24} sx={{ color: "white" }} />
          ) : task ? (
            "Update Task"
          ) : (
            "Create Task"
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default TaskForm;
