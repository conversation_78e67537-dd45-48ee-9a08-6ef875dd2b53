import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Modal,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  Avatar,
  Snackbar,
  Alert,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import LockIcon from "@mui/icons-material/Lock";
import axios from "axios";
import Autocomplete from "@mui/material/Autocomplete";
import { useUser } from "../../contexts/UserContext";

function Employees() {
  const { user } = useUser();
  const [employees, setEmployees] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [openPasswordModal, setOpenPasswordModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [passwordChangeEmployee, setPasswordChangeEmployee] = useState(null);
  const [newPassword, setNewPassword] = useState("");
  const [newEmployee, setNewEmployee] = useState({
    name: "",
    email: "",
    password: "",
    role: "",
    tier: "",
    phone: "",
    profilePicture: null,
  });
  const [editEmployee, setEditEmployee] = useState({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [previewImage, setPreviewImage] = useState(null);

  // const [orderEdits, setOrderEdits] = useState({});

  const roleOptions = [
    "general_manager",
    "account_manager",
    "videographer",
    "video_editor",
    "graphic_designer",
    "financial_manager",
  ];

  // Fetch employees from backend
  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = () => {
    axios
      .get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees"
      )
      .then((response) => setEmployees(response.data))
      .catch((error) => console.error("Error fetching employees:", error));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewEmployee((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditEmployee((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    setNewEmployee((prev) => ({
      ...prev,
      profilePicture: e.target.files[0],
    }));
  };

  const handleEditFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Set preview URL
      setPreviewImage(URL.createObjectURL(file));

      // Keep file for upload
      setEditEmployee((prev) => ({
        ...prev,
        profilePicture: file,
      }));
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check required fields
    const { name, email, password, role, tier } = newEmployee;
    if (!name || !email || !password || !role || !tier) {
      setSnackbar({
        open: true,
        message: "Please fill in all required fields.",
        severity: "error",
      });
      return;
    }

    const formData = new FormData();
    Object.keys(newEmployee).forEach((key) => {
      formData.append(key, newEmployee[key]);
    });

    // Debug: log form data
    console.log("FormData being sent:", [...formData]);

    const token = localStorage.getItem("token");

    try {
      const response = await axios.post(
        "https://youngproductions-768ada043db3.herokuapp.com/api/auth/signup",
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200 || response.status === 201) {
        setSnackbar({
          open: true,
          message: "Employee added!",
          severity: "success",
        });
        fetchEmployees();
        setOpenModal(false);
        setNewEmployee({
          name: "",
          email: "",
          password: "",
          role: "",
          tier: "",
          phone: "",
          profilePicture: null,
        });
      }
    } catch (error) {
      const backendMsg =
        error.response?.data?.message || "Error adding employee";
      setSnackbar({ open: true, message: backendMsg, severity: "error" });
      console.error("Error adding employee:", error);
    }
  };

  const handleEdit = (employee) => {
    setSelectedEmployee(employee);
    setEditEmployee({ ...employee, profilePicture: null });
    setOpenEditModal(true);
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    Object.keys(editEmployee).forEach((key) => {
      if (
        editEmployee[key] !== undefined &&
        key !== "_id" &&
        (key !== "password" ||
          (editEmployee[key] && editEmployee[key].trim() !== ""))
      ) {
        formData.append(key, editEmployee[key]);
      }
    });
    const token = localStorage.getItem("token");
    try {
      const response = await axios.put(
        `https://youngproductions-768ada043db3.herokuapp.com/api/system/employees/${selectedEmployee._id}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setSnackbar({
          open: true,
          message: "Employee updated!",
          severity: "success",
        });
        fetchEmployees();
        setOpenEditModal(false);
      }
    } catch (error) {
      setSnackbar({
        open: true,
        message: "Error updating employee",
        severity: "error",
      });
      console.error("Error updating employee:", error);
    }
  };

  const handleDelete = async (id) => {
    const token = localStorage.getItem("token");
    try {
      await axios.delete(
        `https://youngproductions-768ada043db3.herokuapp.com/api/system/employees/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setSnackbar({
        open: true,
        message: "Employee deleted!",
        severity: "success",
      });
      setEmployees((prev) => prev.filter((emp) => emp._id !== id));
    } catch (error) {
      setSnackbar({
        open: true,
        message: "Error deleting employee",
        severity: "error",
      });
      console.error("Error deleting employee:", error);
    }
  };

  // Check if user is general manager
  const isGeneralManager = () => {
    return user?.role === "general_manager";
  };

  // Handle opening password change modal
  const handleOpenPasswordModal = (employee) => {
    setPasswordChangeEmployee(employee);
    setNewPassword("");
    setOpenPasswordModal(true);
  };

  // Handle closing password change modal
  const handleClosePasswordModal = () => {
    setOpenPasswordModal(false);
    setPasswordChangeEmployee(null);
    setNewPassword("");
  };

  // Handle password change submission
  const handlePasswordChange = async () => {
    if (!newPassword.trim()) {
      setSnackbar({
        open: true,
        message: "Please enter a new password",
        severity: "error",
      });
      return;
    }

    if (newPassword.length < 6) {
      setSnackbar({
        open: true,
        message: "Password must be at least 6 characters long",
        severity: "error",
      });
      return;
    }

    try {
      await axios.put(
        `https://youngproductions-768ada043db3.herokuapp.com/api/system/employees/admin-users/${passwordChangeEmployee._id}/password`,
        { newPassword }
      );

      setSnackbar({
        open: true,
        message: "Password updated successfully",
        severity: "success",
      });
      handleClosePasswordModal();
    } catch (error) {
      setSnackbar({
        open: true,
        message: error.response?.data?.message || "Failed to update password",
        severity: "error",
      });
      console.error("Error updating password:", error);
    }
  };

  const handleToggleStatus = async (employee) => {
    const token = localStorage.getItem("token");

    // Proper toggle
    const newStatus = employee.status === "active" ? "inactive" : "active";

    try {
      await axios.patch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/system/employees/${employee._id}/toggle-status`,
        {}, // <--- no body needed
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setSnackbar({
        open: true,
        message: `Employee ${
          newStatus === "active" ? "activated" : "deactivated"
        }!`,
        severity: "success",
      });

      fetchEmployees(); // refresh list
    } catch (error) {
      setSnackbar({
        open: true,
        message: "Error updating status",
        severity: "error",
      });
      console.error("Error updating status:", error);
    }
  };

  return (
    <Box sx={{ p: 3, backgroundColor: "black", minHeight: "100vh" }}>
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert severity={snackbar.severity} sx={{ width: "100%" }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 3 }}>
        <Typography
          variant="h4"
          sx={{ fontFamily: "Formula Bold", color: "white" }}
        >
          Employees
        </Typography>
        {user?.role === "general_manager" && (
          <Button
            variant="contained"
            onClick={() => setOpenModal(true)}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Employee
          </Button>
        )}
      </Box>

      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: "black",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          "& .MuiTableCell-root": {
            color: "white",
            fontFamily: "Formula Bold",
            fontSize: "1rem",
            letterSpacing: "0.5px",
            textTransform: "capitalize",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          },
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ color: "white" }}>Picture</TableCell>
              <TableCell sx={{ color: "white" }}>Name</TableCell>
              <TableCell sx={{ color: "white" }}>Email</TableCell>
              <TableCell sx={{ color: "white" }}>Role</TableCell>
              <TableCell sx={{ color: "white" }}>Tier</TableCell>
              <TableCell sx={{ color: "white" }}>Phone</TableCell>
              <TableCell sx={{ color: "white" }}>Status</TableCell>
              {isGeneralManager() && (
                <TableCell sx={{ color: "white" }}>Actions</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {employees.map((employee) => (
              <TableRow key={employee._id}>
                <TableCell>
                  <Avatar
                    src={
                      employee.profilePicture
                        ? `https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/profiles/${employee.profilePicture}`
                        : "/assets/default-avatar.webp"
                    }
                    alt={employee.name}
                  />
                </TableCell>
                <TableCell sx={{ color: "white" }}>{employee.name}</TableCell>
                <TableCell
                  sx={{ color: "white", textTransform: "lowercase !important" }}
                >
                  {employee.email}
                </TableCell>
                <TableCell sx={{ color: "white", textTransform: "capitalize" }}>
                  {employee.role}
                </TableCell>
                <TableCell sx={{ color: "white" }}>{employee.tier}</TableCell>
                <TableCell sx={{ color: "white" }}>{employee.phone}</TableCell>
                {/* <TableCell sx={{ color: "white" }}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <TextField
                      type="number"
                      value={
                        orderEdits[employee._id] !== undefined
                          ? orderEdits[employee._id]
                          : employee.order || 0
                      }
                      onChange={(e) =>
                        handleOrderChange(employee._id, e.target.value)
                      }
                      size="small"
                      sx={{
                        width: 70,
                        input: {
                          color: "white",
                          background: "#222",
                          borderRadius: 1,
                        },
                        "& .MuiOutlinedInput-root": {
                          "& fieldset": {
                            borderColor: "rgba(255,255,255,0.23)",
                          },
                          "&:hover fieldset": { borderColor: "white" },
                        },
                      }}
                    />
                    {orderEdits[employee._id] !== undefined &&
                      orderEdits[employee._id] !==
                        String(employee.order || 0) && (
                        <Button
                          size="small"
                          variant="contained"
                          sx={{
                            backgroundColor: "#db4a41",
                            color: "white",
                            minWidth: 0,
                            px: 1,
                          }}
                          onClick={() => handleOrderSave(employee)}
                        >
                          Save
                        </Button>
                      )}
                  </Box>
                </TableCell> */}
                <TableCell>
                  {employee.status === "active" ? (
                    <span
                      style={{
                        color: "#1A3B09FF",
                        fontWeight: "bold",
                        textTransform: "uppercase",
                        backgroundColor: "#74C774FF",
                        padding: "5px 7px",
                        borderRadius: "4px",
                      }}
                    >
                      Active
                    </span>
                  ) : (
                    <span
                      style={{
                        color: "#db4a41",
                        fontWeight: "bold",
                        textTransform: "uppercase",
                        padding: "5px 7px",
                        borderRadius: "4px",
                        backgroundColor: "#F8D7DA",
                      }}
                    >
                      Inactive
                    </span>
                  )}
                </TableCell>
                {isGeneralManager() && (
                  <TableCell>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleToggleStatus(employee)}
                      sx={{
                        backgroundColor:
                          employee.status === "active" ? "#db4a41" : "#43a047",
                        color: "white",
                        "&:hover": {
                          backgroundColor:
                            employee.status === "active"
                              ? "#c62828"
                              : "#388e3c",
                        },
                        minWidth: 10,
                        fontFamily: "Formula Bold",
                        textTransform: "capitalize",
                        padding: "5px 10px",
                        borderRadius: "4px",
                        fontSize: "1rem",
                        letterSpacing: "0.5px",
                      }}
                    >
                      {employee.status === "active" ? "Deactivate" : "Activate"}
                    </Button>
                    {isGeneralManager() && (
                      <Tooltip title="Edit">
                        <IconButton
                          color="primary"
                          onClick={() => handleEdit(employee)}
                          sx={{ color: "#db4a41" }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {isGeneralManager() && (
                      <Tooltip title="Change Password">
                        <IconButton
                          color="secondary"
                          onClick={() => handleOpenPasswordModal(employee)}
                          sx={{ color: "#ff9800" }}
                        >
                          <LockIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {isGeneralManager() && (
                      <Tooltip title="Delete">
                        <IconButton
                          color="error"
                          onClick={() => handleDelete(employee._id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                )}{" "}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Employee Modal */}
      <Modal
        open={openModal}
        onClose={() => setOpenModal(false)}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 400,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            Add New Employee
          </Typography>
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={newEmployee.name}
              onChange={handleInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={newEmployee.email}
              onChange={handleInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={newEmployee.password}
              onChange={handleInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            <TextField
              fullWidth
              label="Phone"
              name="phone"
              type="text"
              value={newEmployee.phone}
              onChange={handleInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            {/* Role Autocomplete for Add */}
            <Autocomplete
              freeSolo
              options={roleOptions}
              value={newEmployee.role}
              onChange={(_, newValue) =>
                setNewEmployee((prev) => ({ ...prev, role: newValue || "" }))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Role"
                  name="role"
                  sx={{
                    mb: 2,
                    "& .MuiInputLabel-root": { color: "white" },
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.23)",
                      },
                      "&:hover fieldset": { borderColor: "white" },
                    },
                  }}
                  onChange={(e) =>
                    setNewEmployee((prev) => ({
                      ...prev,
                      role: e.target.value,
                    }))
                  }
                />
              )}
            />
            <FormControl
              fullWidth
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <InputLabel>Tier</InputLabel>
              <Select
                name="tier"
                value={newEmployee.tier}
                onChange={handleInputChange}
                label="Tier"
              >
                <MenuItem value="1">Tier 1</MenuItem>
                <MenuItem value="2">Tier 2</MenuItem>
                <MenuItem value="3">Tier 3</MenuItem>
              </Select>
            </FormControl>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              style={{
                width: "100%",
                marginBottom: "16px",
                color: "white",
              }}
            />
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
              <Button
                onClick={() => setOpenModal(false)}
                sx={{
                  color: "white",
                  borderColor: "white",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
                variant="contained"
              >
                Add Employee
              </Button>
            </Box>
          </form>
        </Paper>
      </Modal>

      {/* Edit Employee Modal */}
      <Modal
        open={openEditModal}
        onClose={() => setOpenEditModal(false)}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 400,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            Edit Employee
          </Typography>
          {/* Show current profile picture if exists and no new preview */}

          <img
            src={
              editEmployee?.profilePicture
                ? `https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/profiles/${editEmployee?.profilePicture}`
                : "/assets/default-avatar.webp"
            }
            alt="Profile"
            style={{
              width: "120px",
              height: "120px",
              borderRadius: "50%",
              objectFit: "cover",
              marginBottom: "25px",
              display: "block",
              margin: "auto",
              border: "2px solid white",
            }}
          />

          <form onSubmit={handleEditSubmit}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={editEmployee.name || ""}
              onChange={handleEditInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={editEmployee.email || ""}
              onChange={handleEditInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            <TextField
              fullWidth
              label="Phone"
              name="phone"
              type="text"
              value={editEmployee.phone}
              onChange={handleInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
            {/* Role Autocomplete for Edit */}
            <Autocomplete
              freeSolo
              options={roleOptions}
              value={editEmployee.role || ""}
              onChange={(_, newValue) =>
                setEditEmployee((prev) => ({ ...prev, role: newValue || "" }))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Role"
                  name="role"
                  sx={{
                    mb: 2,
                    "& .MuiInputLabel-root": { color: "white" },
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.23)",
                      },
                      "&:hover fieldset": { borderColor: "white" },
                    },
                  }}
                  onChange={(e) =>
                    setEditEmployee((prev) => ({
                      ...prev,
                      role: e.target.value,
                    }))
                  }
                />
              )}
            />
            <FormControl
              fullWidth
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <InputLabel>Tier</InputLabel>
              <Select
                name="tier"
                value={editEmployee.tier || ""}
                onChange={handleEditInputChange}
                label="Tier"
              >
                <MenuItem value="1">Tier 1</MenuItem>
                <MenuItem value="2">Tier 2</MenuItem>
                <MenuItem value="3">Tier 3</MenuItem>
              </Select>
            </FormControl>
            {/* <TextField
              fullWidth
              label="Order"
              name="order"
              type="number"
              value={editEmployee.order || ""}
              onChange={handleEditInputChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            /> */}
            <div style={{ marginBottom: "16px" }}>
              {/* Show current profile picture if exists and no new preview */}
              {editEmployee?.profilePicture &&
                !previewImage &&
                typeof editEmployee.profilePicture === "string" && (
                  <img
                    src={editEmployee.profilePicture}
                    alt="Profile"
                    style={{
                      width: "120px",
                      height: "120px",
                      borderRadius: "50%",
                      objectFit: "cover",
                      marginBottom: "10px",
                    }}
                  />
                )}

              {/* Show preview if user selected a new file */}
              {previewImage && (
                <img
                  src={previewImage}
                  alt="Preview"
                  style={{
                    width: "120px",
                    height: "120px",
                    borderRadius: "50%",
                    objectFit: "cover",
                    marginBottom: "10px",
                  }}
                />
              )}

              <input
                type="file"
                accept="image/*"
                onChange={handleEditFileChange}
                style={{
                  width: "100%",
                  marginBottom: "16px",
                  color: "white",
                }}
              />
            </div>

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
              <Button
                onClick={() => setOpenEditModal(false)}
                sx={{
                  color: "white",
                  borderColor: "white",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
                variant="contained"
              >
                Save Changes
              </Button>
            </Box>
          </form>
        </Paper>
      </Modal>

      {/* Change Password Modal */}
      <Modal
        open={openPasswordModal}
        onClose={handleClosePasswordModal}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 400,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            Change Password
          </Typography>

          {passwordChangeEmployee && (
            <Typography
              variant="body1"
              sx={{
                color: "rgba(255, 255, 255, 0.8)",
                mb: 3,
                textAlign: "center",
              }}
            >
              Changing password for:{" "}
              <strong>{passwordChangeEmployee.name}</strong>
            </Typography>
          )}

          <TextField
            fullWidth
            type="password"
            label="New Password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter new password"
            sx={{
              mb: 3,
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.3)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(255, 255, 255, 0.5)",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#db4a41",
                },
              },
              "& .MuiInputLabel-root": {
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          />

          <Typography
            variant="caption"
            sx={{
              color: "rgba(255, 255, 255, 0.6)",
              display: "block",
              mb: 3,
            }}
          >
            Password must be at least 6 characters long
          </Typography>

          <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
            <Button
              onClick={handleClosePasswordModal}
              sx={{
                color: "white",
                borderColor: "white",
                "&:hover": {
                  borderColor: "#db4a41",
                  color: "#db4a41",
                },
              }}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePasswordChange}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
              variant="contained"
              disabled={!newPassword.trim()}
            >
              Change Password
            </Button>
          </Box>
        </Paper>
      </Modal>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Employees;
