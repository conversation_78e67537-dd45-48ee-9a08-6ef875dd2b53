import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Chip,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Divider,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DownloadIcon from "@mui/icons-material/Download";
import { IoLogoFacebook } from "react-icons/io";
import { FiInstagram } from "react-icons/fi";
import { AiFillTikTok } from "react-icons/ai";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";
function ClientManagement() {
  const { user } = useUser();
  const [clients, setClients] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [viewingClient, setViewingClient] = useState(null);
  const [editingClient, setEditingClient] = useState(null);
  const [newClient, setNewClient] = useState({
    client_account: "",
    email: "",
    address: "",
    description: "",
    instagram: "",
    facebook: "",
    tiktok: "",
    website: "",
    status: "pending",
    type: "retainer",
    accountManager: "",
    contacts: [],
    package: {
      reels: 0,
      posts: 0,
      tiktoks: 0,
      stories: 0,
    },
    logoImage: null,
    brandingFiles: [],
    agreementDocument: null,
    logoImageUrl: "",
    brandingFilesUrls: [],
    agreementDocumentUrl: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({
    logoImage: { loading: false, progress: 0 },
    brandingFiles: { loading: false, progress: 0 },
    agreementDocument: { loading: false, progress: 0 },
  });

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  const fetchClients = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setClients(data);
    } catch (error) {
      console.error("Error fetching clients:", error);
      showSnackbar("Failed to fetch clients", "error");
    }
  }, []);

  const fetchEmployees = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error("Error fetching employees:", error);
      showSnackbar("Failed to fetch employees", "error");
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setClientAccounts(data.data || data || []);
    } catch (error) {
      console.error("Error fetching client accounts:", error);
      showSnackbar("Failed to fetch client accounts", "error");
    }
  }, []);

  useEffect(() => {
    fetchClients();
    fetchEmployees();
    fetchClientAccounts();
  }, [fetchClients, fetchEmployees, fetchClientAccounts]);

  const handleView = (client) => {
    setViewingClient(client);
    setOpenViewModal(true);
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingClient(null);
  };

  const handleDownload = (url, filename) => {
    if (!url) return;

    const link = document.createElement("a");
    link.href = url;
    link.download = filename || "download";
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleAdd = () => {
    setEditingClient(null);
    setNewClient({
      client_account: "",
      email: "",
      address: "",
      description: "",
      instagram: "",
      facebook: "",
      tiktok: "",
      website: "",
      status: "pending",
      accountManager: "",
      contacts: [],
      package: {
        reels: 0,
        posts: 0,
        tiktoks: 0,
        stories: 0,
      },
      logoImage: null,
      brandingFiles: [],
      agreementDocument: null,
      logoImageUrl: "",
      brandingFilesUrls: [],
      agreementDocumentUrl: "",
      type: "retainer",
    });
    setOpenModal(true);
  };

  const handleEdit = (client) => {
    setEditingClient(client);
    setNewClient({
      client_account: client.client_account || "",
      email: client.email || "",
      address: client.address || "",
      description: client.description || "",
      instagram: client.instagram || "",
      facebook: client.facebook || "",
      tiktok: client.tiktok || "",
      website: client.website || "",
      status: client.status || "pending",
      type: client.type || "retainer",
      accountManager: client.accountManager || "",
      contacts: client.contacts || [],
      package: {
        reels: client.package?.reels || 0,
        posts: client.package?.posts || 0,
        tiktoks: client.package?.tiktoks || 0,
        stories: client.package?.stories || 0,
      },
      logoImage: null,
      brandingFiles: [],
      agreementDocument: null,
      logoImageUrl: client.logoImage || "",
      brandingFilesUrls: client.brandingFiles || [],
      agreementDocumentUrl: client.agreementDocument || "",
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingClient(null);
    setNewClient({
      client_account: "",
      email: "",
      address: "",
      description: "",
      instagram: "",
      facebook: "",
      tiktok: "",
      website: "",
      status: "pending",
      type: "retainer",
      accountManager: "",
      contacts: [],
      package: {
        reels: 0,
        posts: 0,
        tiktoks: 0,
        stories: 0,
      },
      logoImage: null,
      brandingFiles: [],
      agreementDocument: null,
      logoImageUrl: "",
      brandingFilesUrls: [],
      agreementDocumentUrl: "",
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewClient({
      ...newClient,
      [name]: value,
    });
  };

  const handleContactChange = (index, field, value) => {
    const updatedContacts = [...newClient.contacts];
    if (!updatedContacts[index]) {
      updatedContacts[index] = { name: "", phone: "", position: "", email: "" };
    }
    updatedContacts[index][field] = value;
    setNewClient({
      ...newClient,
      contacts: updatedContacts,
    });
  };

  const addContact = () => {
    setNewClient({
      ...newClient,
      contacts: [
        ...newClient.contacts,
        { name: "", phone: "", position: "", email: "" },
      ],
    });
  };

  const removeContact = (index) => {
    const updatedContacts = newClient.contacts.filter((_, i) => i !== index);
    setNewClient({
      ...newClient,
      contacts: updatedContacts,
    });
  };

  const handlePackageChange = (e) => {
    const { name, value } = e.target;
    setNewClient({
      ...newClient,
      package: {
        ...newClient.package,
        [name]: parseInt(value) || 0,
      },
    });
  };

  const handleFileChange = async (e, fileType) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const progressKey = fileType;
    setUploadProgress((prev) => ({
      ...prev,
      [progressKey]: { loading: true, progress: 0 },
    }));

    const totalSteps = 10;
    for (let i = 1; i <= totalSteps; i++) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const progress = (i / totalSteps) * 100;
      setUploadProgress((prev) => ({
        ...prev,
        [progressKey]: { loading: true, progress },
      }));
    }

    if (fileType === "brandingFiles") {
      setNewClient({
        ...newClient,
        brandingFiles: Array.from(files),
      });
    } else {
      const file = files[0];
      setNewClient({
        ...newClient,
        [fileType]: file,
      });
    }

    setUploadProgress((prev) => ({
      ...prev,
      [progressKey]: { loading: false, progress: 100 },
    }));

    setTimeout(() => {
      setUploadProgress((prev) => ({
        ...prev,
        [progressKey]: { loading: false, progress: 0 },
      }));
    }, 1000);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validate required fields according to schema
      if (!newClient.client_account || !newClient.email || !newClient.address) {
        showSnackbar(
          "Please fill in all required fields (client account, email, address)",
          "error"
        );
        setLoading(false);
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newClient.email)) {
        showSnackbar("Please enter a valid email address", "error");
        setLoading(false);
        return;
      }
      // Validate contacts - ensure all required fields are filled
      for (let i = 0; i < newClient.contacts.length; i++) {
        const contact = newClient.contacts[i];
        if (
          !contact.name ||
          !contact.phone ||
          !contact.position ||
          !contact.email
        ) {
          showSnackbar(
            `Please fill in all fields for contact ${i + 1}`,
            "error"
          );
          setLoading(false);
          return;
        }
      }
      const token = localStorage.getItem("token");
      if (!token) {
        showSnackbar("Authentication token not found", "error");
        setLoading(false);
        return;
      }

      const formData = new FormData();

      formData.append("client_account", newClient.client_account);
      formData.append("email", newClient.email.trim());
      formData.append("address", newClient.address.trim());
      formData.append("description", newClient.description.trim());
      formData.append("instagram", newClient.instagram.trim());
      formData.append("facebook", newClient.facebook.trim());
      formData.append("tiktok", newClient.tiktok.trim());
      formData.append("website", newClient.website.trim());
      formData.append("status", newClient.status);
      formData.append("type", newClient.type);
      formData.append("accountManager", newClient.accountManager);

      // Add contacts as JSON string (backend expects JSON.parse)
      if (newClient.contacts.length > 0) {
        formData.append("contacts", JSON.stringify(newClient.contacts));
      }

      // Add package as JSON string (backend expects JSON.parse)
      formData.append("package", JSON.stringify(newClient.package));

      if (newClient.logoImage && newClient.logoImage instanceof File) {
        formData.append("logoImage", newClient.logoImage);
      }
      if (
        newClient.agreementDocument &&
        newClient.agreementDocument instanceof File
      ) {
        formData.append("agreementDocument", newClient.agreementDocument);
      }
      if (newClient.brandingFiles && newClient.brandingFiles.length > 0) {
        newClient.brandingFiles.forEach((file) => {
          if (file instanceof File) {
            formData.append("brandingFiles", file);
          }
        });
      }

      const url = editingClient
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement/${editingClient._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement";

      const method = editingClient ? "PUT" : "POST";

      // Debug logging
      console.log("Submitting client data:", {
        client_account: newClient.client_account,
        email: newClient.email,
        address: newClient.address,
        contacts: newClient.contacts,
        package: newClient.package,
      });

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error("Server response:", response.status, errorData);
        throw new Error(
          `Failed to save client: ${response.status} - ${errorData}`
        );
      }

      const result = await response.json();
      const data = result.data || result;

      if (editingClient) {
        setClients(
          clients.map((c) => (c._id === editingClient._id ? data : c))
        );
        showSnackbar("Client updated successfully", "success");
      } else {
        setClients([data, ...clients]);
        showSnackbar("Client added successfully", "success");
      }

      handleCloseModal();
      fetchClients();
    } catch (error) {
      console.error("Error saving client:", error);
      showSnackbar("Failed to save client", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this client?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setClients(clients.filter((client) => client._id !== id));
        showSnackbar("Client deleted successfully", "success");
      } else {
        throw new Error("Failed to delete client");
      }
    } catch (error) {
      console.error("Error deleting client:", error);
      showSnackbar("Failed to delete client", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "onboarded":
        return "#4caf50";
      case "pending":
        return "#ff9800";
      case "terminated":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case "retainer":
        return "#4caf50";
      case "campaign":
        return "#ff9800";
      case "one_time_project":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  // Filter and search logic
  const filteredClients = React.useMemo(() => {
    let filtered = [...clients];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (client) =>
          client.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          client.client_account
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase())
      );
    }

    // Type filter
    if (typeFilter) {
      filtered = filtered.filter((client) => client.type === typeFilter);
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter((client) => client.status === statusFilter);
    }

    return filtered;
  }, [clients, searchTerm, typeFilter, statusFilter]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Client Management
          </Typography>
          {(user?.role === "general_manager" ||
            (user?.role === "account_manager" && user?.tier === 3) ||
            user?.tier === 2) && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                fontFamily: "Formula Bold",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
            >
              Add Client
            </Button>
          )}
        </Box>

        {/* Filter Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                {/* Search Bar */}
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="Search by name, email, or account..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputBase-input::placeholder": {
                        color: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  />
                </Grid>

                {/* Type Filter */}
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Filter by Type
                    </InputLabel>
                    <Select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Types</MenuItem>
                      <MenuItem value="retainer">Retainer</MenuItem>
                      <MenuItem value="campaign">Campaign</MenuItem>
                      <MenuItem value="one_time_project">
                        One Time Project
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Status Filter */}
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Filter by Status
                    </InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Statuses</MenuItem>
                      <MenuItem value="pending">Pending</MenuItem>
                      <MenuItem value="onboarded">On-Boarded</MenuItem>
                      <MenuItem value="terminated">Terminated</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Clear Filters */}
                <Grid item xs={12} md={2}>
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={() => {
                      setSearchTerm("");
                      setTypeFilter("");
                      setStatusFilter("");
                    }}
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Clear Filters
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* Client Cards Grid */}
        <Box sx={{ padding: "0 5% 40px" }}>
          <Grid container spacing={3}>
            <AnimatePresence>
              {filteredClients.map((client) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={client._id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        position: "relative",
                        "&:hover": {
                          background: "rgba(255, 255, 255, 0.08)",
                          borderColor: "rgba(219, 74, 65, 0.3)",
                        },
                      }}
                    >
                      {/* Admin Controls */}
                      <Box
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          display: "flex",
                          gap: 0.5,
                          zIndex: 10,
                        }}
                      >
                        <Tooltip title="View">
                          <IconButton
                            onClick={() => handleView(client)}
                            size="small"
                            sx={{
                              backgroundColor: "rgba(0, 0, 0, 0.7)",
                              color: "white",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.8)",
                              },
                            }}
                          >
                            <VisibilityIcon sx={{ fontSize: "1rem" }} />
                          </IconButton>
                        </Tooltip>
                        {user?.role === "general_manager" && (
                          <>
                            <Tooltip title="Edit">
                              <IconButton
                                onClick={() => handleEdit(client)}
                                size="small"
                                sx={{
                                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                                  color: "white",
                                  "&:hover": {
                                    backgroundColor: "rgba(219, 74, 65, 0.8)",
                                  },
                                }}
                              >
                                <EditIcon sx={{ fontSize: "1rem" }} />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton
                                onClick={() => handleDelete(client._id)}
                                size="small"
                                sx={{
                                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                                  color: "white",
                                  "&:hover": {
                                    backgroundColor: "rgba(244, 67, 54, 0.8)",
                                  },
                                }}
                              >
                                <DeleteIcon sx={{ fontSize: "1rem" }} />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                      </Box>

                      {/* Logo */}
                      {client.logoImage && (
                        <CardMedia
                          component="img"
                          height="100px"
                          width="90%"
                          image={client.logoImage.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          alt={client.name}
                          sx={{
                            objectFit: "contain",
                            padding: "10px 0px",
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        />
                      )}

                      <CardContent sx={{ flexGrow: 1, padding: "20px" }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 1 }}
                        >
                          <Typography
                            variant="h5"
                            component="h5"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "white",
                              flexGrow: 1,
                            }}
                          >
                            {client.name}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              gap: 1,
                              flexWrap: "wrap",
                              flexDirection: "column",
                            }}
                          >
                            <Chip
                              label={client.status}
                              size="small"
                              sx={{
                                backgroundColor: getStatusColor(client.status),
                                color: "white",
                                fontSize: "0.8rem",
                                textTransform: "capitalize",
                                height: "30px",
                                padding: "15px 15px",
                              }}
                            />
                            <Chip
                              label={client.type}
                              size="small"
                              sx={{
                                backgroundColor: getTypeColor(client.type),
                                color: "white",
                                fontSize: "0.8rem",
                                textTransform: "capitalize",
                                height: "30px",
                                padding: "15px 15px",
                              }}
                            />
                          </Box>
                        </Box>

                        {/* <Typography
                          variant="body2"
                          color="rgba(255, 255, 255, 0.7)"
                          sx={{
                            mb: 1,
                            fontFamily: "Anton",
                            letterSpacing: "0.05em",
                          }}
                        >
                          {client.description.split(" ").slice(0, 6).join(" ") +
                            (client.description.split(" ").length > 6
                              ? "..."
                              : "")}
                        </Typography> */}

                        {client.accountManager && (
                          <Typography
                            variant="body2"
                            color="rgba(255, 255, 255, 0.5)"
                            sx={{ mb: 1, fontSize: "0.8rem" }}
                          >
                            👤 Account Manager: {client.accountManager.name}
                          </Typography>
                        )}

                        {/* {client.contacts && client.contacts.length > 0 && (
                          <Box sx={{ mb: 2 }}>
                            <Typography
                              variant="body2"
                              color="#db4a41"
                              sx={{ fontFamily: "Formula Bold", mb: 1 }}
                            >
                              Contacts:
                            </Typography>
                            {client.contacts.map((contact, index) => (
                              <Typography
                                key={index}
                                variant="body2"
                                color="rgba(255, 255, 255, 0.5)"
                                sx={{ fontSize: "0.7rem" }}
                              >
                                {contact.name} - {contact.position}
                              </Typography>
                            ))}
                          </Box>
                        )} */}

                        {/* Package Details */}
                        <Box sx={{ mb: 2 }}>
                          <Typography
                            variant="body2"
                            color="#db4a41"
                            sx={{ fontFamily: "Formula Bold", mb: 1 }}
                          >
                            Package Details:
                          </Typography>
                          <Box
                            sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}
                          >
                            <Chip
                              label={`Reels: ${client.package?.reels || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`Posts: ${client.package?.posts || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`TikToks: ${client.package?.tiktoks || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`Stories: ${client.package?.stories || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                          </Box>
                        </Box>

                        {/* Social Links */}
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-start",
                            gap: 1,
                          }}
                        >
                          {client.instagram && (
                            <Typography
                              variant="body2"
                              color="rgba(255, 255, 255, 0.5)"
                              sx={{ fontSize: "0.7rem" }}
                            >
                              <FiInstagram /> @ {client.instagram}
                            </Typography>
                          )}
                          {client.facebook && (
                            <Typography
                              variant="body2"
                              color="rgba(255, 255, 255, 0.5)"
                              sx={{ fontSize: "0.7rem" }}
                            >
                              <IoLogoFacebook /> @ {client.facebook}
                            </Typography>
                          )}
                          {client.tiktok && (
                            <Typography
                              variant="body2"
                              color="rgba(255, 255, 255, 0.5)"
                              sx={{ fontSize: "0.7rem" }}
                            >
                              <AiFillTikTok />: @{client.tiktok}
                            </Typography>
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </AnimatePresence>
          </Grid>
        </Box>
      </Box>

      {/* View Modal */}
      <Dialog
        open={openViewModal}
        onClose={handleCloseViewModal}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
            color: "white",
          },
        }}
      >
        <DialogTitle
          sx={{
            color: "white",
            fontFamily: "Formula Bold",
            textAlign: "center",
          }}
        >
          Client Details
        </DialogTitle>
        <DialogContent sx={{ maxHeight: "70vh", overflowY: "auto" }}>
          {viewingClient && (
            <Box>
              {/* Logo - Centered at top */}
              {viewingClient.logoImage && (
                <Box sx={{ display: "flex", justifyContent: "center", mb: 3 }}>
                  <img
                    src={viewingClient.logoImage.replace(
                      "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                      "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                    )}
                    alt={viewingClient.name}
                    style={{
                      maxWidth: "200px",
                      maxHeight: "150px",
                      objectFit: "contain",
                      backgroundColor: "rgba(255, 255, 255, 0.05)",
                      padding: "20px",
                      borderRadius: "8px",
                    }}
                  />
                </Box>
              )}

              {/* Basic Information */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                >
                  Basic Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Name:</strong> {viewingClient.name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Email:</strong>{" "}
                      {viewingClient.email || "Not provided"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Address:</strong>{" "}
                      {viewingClient.address || "Not provided"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Account Manager:</strong>{" "}
                      {viewingClient.accountManager.name || "Not assigned"}-
                      {viewingClient.accountManager.role || "N/A "}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <strong>Status:</strong>
                      <Chip
                        label={viewingClient.status}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(viewingClient.status),
                          color: "white",
                          textTransform: "capitalize",
                        }}
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <strong>Type:</strong>
                      <Chip
                        label={viewingClient.type}
                        size="small"
                        sx={{
                          backgroundColor: getTypeColor(viewingClient.type),
                          color: "white",
                          textTransform: "capitalize",
                        }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              <Divider
                sx={{ borderColor: "rgba(255, 255, 255, 0.1)", mb: 3 }}
              />

              {/* Description */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                >
                  Description
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                  {viewingClient.description}
                </Typography>
              </Box>

              <Divider
                sx={{ borderColor: "rgba(255, 255, 255, 0.1)", mb: 3 }}
              />

              {/* Contacts */}
              {viewingClient.contacts && viewingClient.contacts.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                  >
                    Contacts
                  </Typography>
                  <Grid container spacing={2}>
                    {viewingClient.contacts.map((contact, index) => (
                      <Grid item xs={12} md={6} key={index}>
                        <Box
                          sx={{
                            p: 2,
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            borderRadius: "8px",
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                          }}
                        >
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            <strong>Name:</strong> {contact.name || "N/A"}
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            <strong>Position:</strong>{" "}
                            {contact.position || "N/A"}
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            <strong>Phone:</strong> {contact.phone || "N/A"}
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            <strong>Email:</strong> {contact.email || "N/A"}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              <Divider
                sx={{ borderColor: "rgba(255, 255, 255, 0.1)", mb: 3 }}
              />

              {/* Package Details */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                >
                  Package Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body1">
                      <strong>Reels:</strong>{" "}
                      {viewingClient.package?.reels || 0}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body1">
                      <strong>Posts:</strong>{" "}
                      {viewingClient.package?.posts || 0}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body1">
                      <strong>TikToks:</strong>{" "}
                      {viewingClient.package?.tiktoks || 0}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body1">
                      <strong>Stories:</strong>{" "}
                      {viewingClient.package?.stories || 0}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider
                sx={{ borderColor: "rgba(255, 255, 255, 0.1)", mb: 3 }}
              />

              {/* Social Media */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                >
                  Social Media
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Instagram:</strong>{" "}
                      {viewingClient.instagram
                        ? `@${viewingClient.instagram}`
                        : "Not provided"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Facebook:</strong>{" "}
                      {viewingClient.facebook
                        ? `@${viewingClient.facebook}`
                        : "Not provided"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>TikTok:</strong>{" "}
                      {viewingClient.tiktok
                        ? `@${viewingClient.tiktok}`
                        : "Not provided"}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>Website:</strong>{" "}
                      {viewingClient.website || "Not provided"}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider
                sx={{ borderColor: "rgba(255, 255, 255, 0.1)", mb: 3 }}
              />

              {/* Documents */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, fontFamily: "Formula Bold" }}
                >
                  Documents
                </Typography>
                <Grid container spacing={2}>
                  {viewingClient.agreementDocument && (
                    <Grid item xs={12} md={6}>
                      <Box
                        sx={{
                          p: 2,
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "8px",
                          backgroundColor: "rgba(255, 255, 255, 0.05)",
                        }}
                      >
                        <Typography variant="body1" sx={{ mb: 1 }}>
                          <strong>Agreement Document</strong>
                        </Typography>
                        <Button
                          variant="outlined"
                          startIcon={<DownloadIcon />}
                          onClick={() =>
                            handleDownload(
                              viewingClient.agreementDocument.replace(
                                "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                                "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                              ),
                              `${viewingClient.name}_agreement.pdf`
                            )
                          }
                          sx={{
                            color: "white",
                            borderColor: "rgba(255, 255, 255, 0.23)",
                            "&:hover": {
                              borderColor: "#db4a41",
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          }}
                        >
                          Download
                        </Button>
                      </Box>
                    </Grid>
                  )}
                  {viewingClient.brandingFiles &&
                    viewingClient.brandingFiles.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Box
                          sx={{
                            p: 2,
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            borderRadius: "8px",
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                          }}
                        >
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            <strong>
                              Branding Files (
                              {viewingClient.brandingFiles.length})
                            </strong>
                          </Typography>
                          {viewingClient.brandingFiles.map((file, index) => (
                            <Button
                              key={index}
                              variant="outlined"
                              startIcon={<DownloadIcon />}
                              onClick={() =>
                                handleDownload(
                                  file.replace(
                                    "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                                    "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                                  ),
                                  `${viewingClient.name}_branding_${index + 1}`
                                )
                              }
                              sx={{
                                color: "white",
                                borderColor: "rgba(255, 255, 255, 0.23)",
                                mr: 1,
                                mb: 1,
                                "&:hover": {
                                  borderColor: "#db4a41",
                                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                                },
                              }}
                            >
                              File {index + 1}
                            </Button>
                          ))}
                        </Box>
                      </Grid>
                    )}
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseViewModal} sx={{ color: "white" }}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Modal */}
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          {editingClient ? "Edit Client" : "Add New Client"}
        </DialogTitle>
        <DialogContent sx={{ maxHeight: "70vh", overflowY: "auto" }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal" required>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Client Account
                </InputLabel>
                <Select
                  name="client_account"
                  value={newClient.client_account}
                  onChange={handleInputChange}
                  displayEmpty
                  sx={{
                    color: "white",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "& .MuiSelect-icon": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    <em style={{ color: "rgba(255, 255, 255, 0.5)" }}>
                      Select Client Account
                    </em>
                  </MenuItem>
                  {clientAccounts.map((account) => (
                    <MenuItem key={account._id} value={account._id}>
                      {account.client_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={newClient.email}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Account Manager
                </InputLabel>
                <Select
                  name="accountManager"
                  value={newClient.accountManager}
                  onChange={handleInputChange}
                  sx={{
                    color: "white",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.23)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  }}
                >
                  <MenuItem value="">None</MenuItem>
                  {employees.map((employee) => (
                    <MenuItem key={employee._id} value={employee._id}>
                      {employee.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Status"
                name="status"
                select
                value={newClient.status}
                onChange={handleInputChange}
                margin="normal"
                SelectProps={{
                  native: true,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              >
                <option value="pending">Pending</option>
                <option value="onboarded">On-Boarded</option>
                <option value="terminated">Terminated</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Type"
                name="type"
                select
                value={newClient.type}
                onChange={handleInputChange}
                margin="normal"
                SelectProps={{
                  native: true,
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              >
                <option value="retainer">Retainer</option>
                <option value="campaign">Campaign</option>
                <option value="one_time_project">One Time Project</option>
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={newClient.address}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={newClient.description}
                onChange={handleInputChange}
                margin="normal"
                multiline
                rows={3}
                required
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Instagram"
                name="instagram"
                value={newClient.instagram}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Facebook"
                name="facebook"
                value={newClient.facebook}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="TikTok"
                name="tiktok"
                value={newClient.tiktok}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website"
                name="website"
                value={newClient.website}
                onChange={handleInputChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>

            {/* Contacts Section */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 1 }}>
                Contacts
              </Typography>
              {newClient.contacts.map((contact, index) => (
                <Box
                  key={index}
                  sx={{
                    mb: 2,
                    p: 2,
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "8px",
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Name *"
                        value={contact.name}
                        onChange={(e) =>
                          handleContactChange(index, "name", e.target.value)
                        }
                        margin="normal"
                        required
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            color: "white",
                            "& fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.23)",
                            },
                            "&:hover fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.5)",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Position *"
                        value={contact.position}
                        onChange={(e) =>
                          handleContactChange(index, "position", e.target.value)
                        }
                        margin="normal"
                        required
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            color: "white",
                            "& fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.23)",
                            },
                            "&:hover fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.5)",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Phone *"
                        value={contact.phone}
                        onChange={(e) =>
                          handleContactChange(index, "phone", e.target.value)
                        }
                        margin="normal"
                        required
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            color: "white",
                            "& fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.23)",
                            },
                            "&:hover fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.5)",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        fullWidth
                        label="Email *"
                        type="email"
                        value={contact.email}
                        onChange={(e) =>
                          handleContactChange(index, "email", e.target.value)
                        }
                        margin="normal"
                        required
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            color: "white",
                            "& fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.23)",
                            },
                            "&:hover fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.5)",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                          },
                        }}
                      />
                    </Grid>
                  </Grid>
                  <Button
                    onClick={() => removeContact(index)}
                    variant="outlined"
                    color="error"
                    sx={{
                      mt: 1,
                      color: "#f44336",
                      borderColor: "#f44336",
                      "&:hover": {
                        backgroundColor: "rgba(244, 67, 54, 0.1)",
                      },
                    }}
                  >
                    Remove Contact
                  </Button>
                </Box>
              ))}
              <Button
                onClick={addContact}
                variant="outlined"
                startIcon={<AddIcon />}
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                Add Contact
              </Button>
            </Grid>
            {/* Package Details */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 1 }}>
                Package Details
              </Typography>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Reels"
                name="reels"
                type="number"
                value={newClient.package.reels}
                onChange={handlePackageChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Posts"
                name="posts"
                type="number"
                value={newClient.package.posts}
                onChange={handlePackageChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="TikToks"
                name="tiktoks"
                type="number"
                value={newClient.package.tiktoks}
                onChange={handlePackageChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Stories"
                name="stories"
                type="number"
                value={newClient.package.stories}
                onChange={handlePackageChange}
                margin="normal"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </Grid>

            {/* File Uploads */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 1 }}>
                Files
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <input
                accept="image/*"
                style={{ display: "none" }}
                id="logoImage-upload"
                type="file"
                onChange={(e) => handleFileChange(e, "logoImage")}
              />
              <label htmlFor="logoImage-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  fullWidth
                  sx={{
                    color: "white",
                    borderColor: "rgba(255, 255, 255, 0.23)",
                    "&:hover": {
                      borderColor: "#db4a41",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Logo Image
                </Button>
              </label>
            </Grid>
            <Grid item xs={12} md={4}>
              <input
                accept="*/*"
                style={{ display: "none" }}
                id="brandingFiles-upload"
                type="file"
                multiple
                onChange={(e) => handleFileChange(e, "brandingFiles")}
              />
              <label htmlFor="brandingFiles-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  fullWidth
                  sx={{
                    color: "white",
                    borderColor: "rgba(255, 255, 255, 0.23)",
                    "&:hover": {
                      borderColor: "#db4a41",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Branding Files
                </Button>
              </label>
            </Grid>
            <Grid item xs={12} md={4}>
              <input
                accept="*/*"
                style={{ display: "none" }}
                id="agreementDocument-upload"
                type="file"
                onChange={(e) => handleFileChange(e, "agreementDocument")}
              />

              <label htmlFor="agreementDocument-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  fullWidth
                  sx={{
                    color: "white",
                    borderColor: "rgba(255, 255, 255, 0.23)",
                    "&:hover": {
                      borderColor: "#db4a41",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Agreement Document
                </Button>
              </label>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} sx={{ color: "white" }} />
              ) : null
            }
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
              "&:disabled": {
                backgroundColor: "rgba(219, 74, 65, 0.5)",
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          >
            {loading ? "Saving..." : editingClient ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background: snackbar.severity === "success" ? "#2e7d32" : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ClientManagement;
