# Apache cache configuration for static assets
<IfModule mod_expires.c>
  ExpiresActive On
  
  # Images and videos - 1 year
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType video/webm "access plus 1 year"
  ExpiresByType video/mp4 "access plus 1 year"
  
  # Fonts - 1 year
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/ttf "access plus 1 year"
  
  # CSS and JavaScript - 1 year
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType text/javascript "access plus 1 year"
  
  # HTML - 1 hour (allows updates)
  ExpiresByType text/html "access plus 1 hour"
  
  # Favicon - 1 day
  ExpiresByType image/x-icon "access plus 1 day"
</IfModule>

# Cache-Control headers
<IfModule mod_headers.c>
  # Static assets - 1 year immutable
  <FilesMatch "\.(js|css|webp|jpg|jpeg|png|gif|svg|woff2|woff|ttf|webm|mp4)$">
    Header set Cache-Control "public, max-age=31536000, immutable"
  </FilesMatch>
  
  # HTML - 1 hour
  <FilesMatch "\.html$">
    Header set Cache-Control "public, max-age=3600, must-revalidate"
  </FilesMatch>
  
  # Favicon - 1 day
  <FilesMatch "favicon\.ico$">
    Header set Cache-Control "public, max-age=86400"
  </FilesMatch>
  
  # Security headers
  Header always set X-Content-Type-Options "nosniff"
  Header always set X-Frame-Options "DENY"
  Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Enable compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json
</IfModule>

