import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Avatar,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";

function Salaries() {
  const [salaries, setSalaries] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editing, setEditing] = useState(null);
  const [viewing, setViewing] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({ status: "", userId: "" });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/salaries";
  const USERS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees"; // adjust if different

  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return "0 EGP";
    const value = amount?.$numberDecimal ?? amount;
    return `${parseFloat(value || 0).toLocaleString()} EGP`;
  };

  const parseDecimal = (v) => parseFloat(v?.$numberDecimal ?? v ?? 0) || 0;

  const netAmount = (s) =>
    parseDecimal(s.base_amount) +
    parseDecimal(s.bonuses) -
    parseDecimal(s.deductions);

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const fetchSalaries = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(API_BASE_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      setSalaries(json.data || []);
    } catch (e) {
      console.error("Error fetching salaries:", e);
      showSnackbar("Failed to fetch salaries", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchUsers = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(USERS_API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      const list = json?.data || json || [];
      setUsers(Array.isArray(list) ? list : []);
    } catch (e) {
      console.error("Error fetching users:", e);
      setUsers([]);
    }
  }, []);

  useEffect(() => {
    fetchSalaries();
    fetchUsers();
  }, [fetchSalaries, fetchUsers]);

  const [form, setForm] = useState({
    title: "",
    userId: "",
    base_amount: "",
    bonuses: "0",
    deductions: "0",
    effective_from: "",
    effective_to: "",
    status: "active",
    notes: "",
  });

  const filtered = useMemo(() => {
    return (salaries || []).filter((s) => {
      if (filters.status && s.status !== filters.status) return false;
      if (filters.userId && (s.userId?._id || s.userId) !== filters.userId)
        return false;
      return true;
    });
  }, [salaries, filters]);

  const handleAdd = () => {
    setEditing(null);
    setForm({
      title: "",
      userId: "",
      base_amount: "",
      bonuses: "0",
      deductions: "0",
      effective_from: "",
      effective_to: "",
      status: "active",
      notes: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (s) => {
    setEditing(s);
    setForm({
      title: s.title || "",
      userId: s.userId?._id || s.userId,
      base_amount: s.base_amount?.$numberDecimal || s.base_amount || "",
      bonuses: s.bonuses?.$numberDecimal || s.bonuses || "0",
      deductions: s.deductions?.$numberDecimal || s.deductions || "0",
      effective_from: s.effective_from
        ? new Date(s.effective_from).toISOString().slice(0, 10)
        : "",
      effective_to: s.effective_to
        ? new Date(s.effective_to).toISOString().slice(0, 10)
        : "",
      status: s.status || "active",
      notes: s.notes || "",
    });
    setOpenModal(true);
  };

  const handleView = async (s) => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${s._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      setViewing(json.data || s);
      setOpenViewModal(true);
    } catch (e) {
      setViewing(s);
      setOpenViewModal(true);
    }
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditing(null);
  };
  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewing(null);
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");
      const payload = {
        title: form.title,
        userId: form.userId,
        base_amount: parseFloat(form.base_amount),
        bonuses: parseFloat(form.bonuses || 0),
        deductions: parseFloat(form.deductions || 0),
        effective_from: form.effective_from,
        effective_to: form.effective_to || undefined,
        status: form.status,
        notes: form.notes,
      };
      const url = editing ? `${API_BASE_URL}/${editing._id}` : API_BASE_URL;
      const method = editing ? "PUT" : "POST";
      const res = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body.message || "Failed to save salary");
      }
      await fetchSalaries();
      setOpenModal(false);
      showSnackbar(editing ? "Salary updated" : "Salary created", "success");
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to save salary", "error");
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this salary?")) return;
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!res.ok) throw new Error("Failed to delete salary");
      setSalaries((prev) => prev.filter((s) => s._id !== id));
      showSnackbar("Salary deleted", "success");
    } catch (e) {
      console.error(e);
      showSnackbar("Failed to delete salary", "error");
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "#4caf50";
      case "inactive":
        return "#ff9800";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    if (!string) return "#666";
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }
    let color = "#";
    for (let i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    return color;
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Salaries
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            Add Salary
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Filters */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={filters.status}
                      onChange={(e) =>
                        setFilters({ ...filters, status: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      User
                    </InputLabel>
                    <Select
                      value={filters.userId}
                      onChange={(e) =>
                        setFilters({ ...filters, userId: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(users) &&
                        users.map((u) => (
                          <MenuItem key={u._id} value={u._id}>
                            {u.name || u.email || u.username}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Table */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{ background: "transparent" }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            User
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Base
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Bonuses
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Deductions
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Net
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Effective
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filtered
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((s) => (
                            <TableRow
                              key={s._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: stringToColor(
                                        s.userId?.name || s.userId?.email || "U"
                                      ),
                                      width: 32,
                                      height: 32,
                                    }}
                                  >
                                    <MonetizationOnIcon fontSize="small" />
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontFamily: "Formula Bold" }}
                                    >
                                      {s.userId?.name ||
                                        s.userId?.email ||
                                        "User"}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                    >
                                      {s.title || "Salary"}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(s.base_amount)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(s.bonuses)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(s.deductions)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(netAmount(s))}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography variant="caption">
                                  From:{" "}
                                  {s.effective_from
                                    ? new Date(
                                        s.effective_from
                                      ).toLocaleDateString()
                                    : "-"}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{ display: "block" }}
                                >
                                  To:{" "}
                                  {s.effective_to
                                    ? new Date(
                                        s.effective_to
                                      ).toLocaleDateString()
                                    : "—"}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={s.status}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getStatusColor(
                                      s.status
                                    )}20`,
                                    color: getStatusColor(s.status),
                                    textTransform: "capitalize",
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(s)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(s)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(s._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filtered.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editing ? "Edit Salary" : "Create Salary"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title (optional)"
                  value={form.title}
                  onChange={(e) => setForm({ ...form, title: e.target.value })}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    User
                  </InputLabel>
                  <Select
                    value={form.userId}
                    onChange={(e) =>
                      setForm({ ...form, userId: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {Array.isArray(users) &&
                      users.map((u) => (
                        <MenuItem key={u._id} value={u._id}>
                          {u.name || u.email || u.username}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Base Amount"
                  type="number"
                  value={form.base_amount}
                  onChange={(e) =>
                    setForm({ ...form, base_amount: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Bonuses"
                  type="number"
                  value={form.bonuses}
                  onChange={(e) =>
                    setForm({ ...form, bonuses: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Deductions"
                  type="number"
                  value={form.deductions}
                  onChange={(e) =>
                    setForm({ ...form, deductions: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Effective From"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  value={form.effective_from}
                  onChange={(e) =>
                    setForm({ ...form, effective_from: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Effective To"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  value={form.effective_to}
                  onChange={(e) =>
                    setForm({ ...form, effective_to: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Status
                  </InputLabel>
                  <Select
                    value={form.status}
                    onChange={(e) =>
                      setForm({ ...form, status: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={form.notes}
                  onChange={(e) => setForm({ ...form, notes: e.target.value })}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editing ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Salary Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewing && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          viewing.userId?.name || viewing.userId?.email || "U"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <MonetizationOnIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h5"
                        sx={{ color: "white", fontFamily: "Formula Bold" }}
                      >
                        {viewing.userId?.name ||
                          viewing.userId?.email ||
                          "User"}
                      </Typography>
                      {viewing.title && (
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255,255,255,0.8)" }}
                        >
                          {viewing.title}
                        </Typography>
                      )}
                      <Chip
                        label={viewing.status}
                        size="small"
                        sx={{
                          ml: 1,
                          backgroundColor: getStatusColor(viewing.status),
                          color: "white",
                          textTransform: "capitalize",
                        }}
                      />
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography sx={{ color: "white" }}>
                    <strong>Base:</strong> {formatCurrency(viewing.base_amount)}
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    <strong>Bonuses:</strong> {formatCurrency(viewing.bonuses)}
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    <strong>Deductions:</strong>{" "}
                    {formatCurrency(viewing.deductions)}
                  </Typography>
                  <Typography
                    sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                  >
                    <strong>Net:</strong> {formatCurrency(netAmount(viewing))}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography sx={{ color: "white" }}>
                    <strong>Effective From:</strong>{" "}
                    {viewing.effective_from
                      ? new Date(viewing.effective_from).toLocaleDateString()
                      : "-"}
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    <strong>Effective To:</strong>{" "}
                    {viewing.effective_to
                      ? new Date(viewing.effective_to).toLocaleDateString()
                      : "—"}
                  </Typography>
                  {viewing.notes && (
                    <Typography sx={{ color: "rgba(255,255,255,0.8)", mt: 1 }}>
                      <strong>Notes:</strong> {viewing.notes}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Salaries;
