import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Grid,
  Paper,
  Divider,
  CircularProgress,
} from "@mui/material";
import { useUser } from "../contexts/UserContext";
import axios from "axios";

const API_URL =
  "https://youngproductions-768ada043db3.herokuapp.com/api/clients";

function ClientDetails() {
  const { id } = useParams();
  const { user } = useUser();
  const token = user?.token;
  const [client, setClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchClient = async () => {
      setLoading(true);
      try {
        const res = await axios.get(
          `https://youngproductions-768ada043db3.herokuapp.com/api/clients/${id}`,
          {
            headers: token ? { Authorization: `Bearer ${token}` } : {},
          }
        );
        let data = res.data;

        // Transform API response to match expected fields
        data.logoUrl = data.logoImage
          ? `https://youngproductions-768ada043db3.herokuapp.com/uploads/${data.logoImage}`
          : undefined;

        data.brandingFiles = (data.brandingFiles || []).map((file) => ({
          name: file,
          url: `https://youngproductions-768ada043db3.herokuapp.com/uploads/${file}`,
        }));

        // If you want to show agreementDocument as a document
        data.documents = data.agreementDocument
          ? [
              {
                name: data.agreementDocument,
                url: `https://youngproductions-768ada043db3.herokuapp.com/uploads/${data.agreementDocument}`,
              },
            ]
          : [];

        setClient(data);
      } catch (err) {
        if (err.response) {
          setError(`Error: ${err.response.status} - ${err.response.data}`);
        } else {
          setError(err.message);
        }
      } finally {
        setLoading(false);
      }
    };
    fetchClient();
  }, [id, token]);

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "80vh",
          background: "black",
        }}
      >
        <CircularProgress sx={{ color: "#db4a41" }} />
      </Box>
    );
  }
  if (error) {
    return (
      <Box sx={{ color: "white", textAlign: "center", mt: 8 }}>{error}</Box>
    );
  }
  if (!client) return null;

  // Example: Only show financials for admin or finance roles/tiers
  const canViewFinancials =
    user && (user.role === "admin" || user.tier === "finance");

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
      }}
    >
      <Paper
        sx={{
          maxWidth: 900,
          margin: "40px auto",
          background: "rgba(255,255,255,0.05)",
          border: "1px solid rgba(255,255,255,0.1)",
          borderRadius: "16px",
          boxShadow: 4,
          p: { xs: 2, sm: 4 },
        }}
      >
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            {client.logoUrl && (
              <CardMedia
                component="img"
                image={client.logoUrl}
                alt={client.name}
                sx={{
                  objectFit: "contain",
                  background: "#222",
                  borderRadius: "12px",
                  p: 2,
                  width: "100%",
                  maxHeight: 180,
                }}
              />
            )}
            <Box sx={{ mt: 2 }}>
              <Chip
                label={client.status || "Unknown"}
                sx={{
                  background:
                    client.status === "onboarded" ? "#2e7d32" : "#ed6c02",
                  color: "white",
                  fontWeight: 700,
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <Typography
              variant="h4"
              sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
            >
              {client.name}
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{ color: "rgba(255,255,255,0.8)", mt: 1 }}
            >
              {client.description || "No description provided."}
            </Typography>
            <Divider sx={{ my: 2, background: "rgba(255,255,255,0.1)" }} />
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                Contract Type:
              </Typography>
              <Typography variant="body2" sx={{ color: "white" }}>
                {client.contractType || "N/A"}
              </Typography>
            </Box>
            {client.links && client.links.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                  Links:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                  {client.links.map((link, idx) => (
                    <Chip
                      key={idx}
                      label={link.name || link.url}
                      component="a"
                      href={link.url}
                      target="_blank"
                      clickable
                      sx={{ background: "#222", color: "#db4a41" }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            {client.brandingFiles && client.brandingFiles.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                  Branding Files:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                  {client.brandingFiles.map((file, idx) => (
                    <Chip
                      key={idx}
                      label={
                        file.name || file.url || `Branding File ${idx + 1}`
                      }
                      component="a"
                      href={file.url}
                      target="_blank"
                      clickable
                      sx={{ background: "#222", color: "#db4a41" }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            {client.documents && client.documents.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                  Documents:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                  {client.documents.map((doc, idx) => (
                    <Chip
                      key={idx}
                      label={doc.name || doc.url || `Document ${idx + 1}`}
                      component="a"
                      href={doc.url}
                      target="_blank"
                      clickable
                      sx={{ background: "#222", color: "#db4a41" }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            {client.tasks && client.tasks.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                  Tasks:
                </Typography>
                <ul style={{ color: "white", margin: 0, paddingLeft: 18 }}>
                  {client.tasks.map((task, idx) => (
                    <li key={idx}>
                      {task.title || task.name || `Task ${idx + 1}`}
                    </li>
                  ))}
                </ul>
              </Box>
            )}
            {canViewFinancials && client.financials && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ color: "#db4a41" }}>
                  Financials:
                </Typography>
                <Typography variant="body2" sx={{ color: "white" }}>
                  {client.financials.summary || "Confidential"}
                </Typography>
              </Box>
            )}
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
}

export default ClientDetails;
