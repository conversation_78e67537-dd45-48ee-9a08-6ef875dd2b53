import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Avatar,
  FormControlLabel,
  Switch,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import FilterListIcon from "@mui/icons-material/FilterList";
import CalculateIcon from "@mui/icons-material/Calculate";
import PaymentIcon from "@mui/icons-material/Payment";
import ClearIcon from "@mui/icons-material/Clear";

function Payments() {
  const [payments, setPayments] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingPayment, setEditingPayment] = useState(null);
  const [viewingPayment, setViewingPayment] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    client_id: "",
    method: "",
    date_from: "",
    date_to: "",
    amount_min: "",
    amount_max: "",
  });
  const [newPayment, setNewPayment] = useState({
    client_id: "",
    amount: "",
    date: "",
    method: "cash",
    allocations: [],
    project_id: "",
    is_bulk: false,
    notes: "",
  });
  const [invoiceFile, setInvoiceFile] = useState(null);
  const [bankTransferFile, setBankTransferFile] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [clientCycles, setClientCycles] = useState([]);
  const [allocations, setAllocations] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectionType, setSelectionType] = useState("cycle"); // "cycle" or "project"
  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/payments";
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";
  const PROJECTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/one-time-projects";

  const normalizeFileUrl = (url) => {
    if (!url) return url;
    try {
      const oldBase = "https://youngproductionss.com/";
      const altOldBase = "https://youngproductionss.com";
      const newBase = "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/";
      if (url.startsWith(oldBase)) {
        return url.replace(oldBase, newBase);
      }
      if (url.startsWith(altOldBase)) {
        // ensure we don't double add slash
        const path = url.slice(altOldBase.length).replace(/^\//, "");
        return newBase + path;
      }
      return url;
    } catch {
      return url;
    }
  };

  const handleDownloadFile = async (fileUrl, suggestedName) => {
    try {
      if (!fileUrl) {
        showSnackbar("No file to download", "warning");
        return;
      }
      const response = await fetch(normalizeFileUrl(fileUrl));
      if (!response.ok) {
        throw new Error("Failed to fetch file");
      }
      const blob = await response.blob();
      const objectUrl = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = objectUrl;
      const fallbackName = (fileUrl.split("/").pop() || "file").split("?")[0];
      anchor.download = suggestedName || fallbackName;
      document.body.appendChild(anchor);
      anchor.click();
      anchor.remove();
      window.URL.revokeObjectURL(objectUrl);
    } catch (err) {
      console.error("Download error:", err);
      showSnackbar("Failed to download file", "error");
    }
  };

  const fetchPayments = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setPayments(result.data || []);
    } catch (error) {
      console.error("Error fetching payments:", error);
      showSnackbar("Failed to fetch payments", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();

      if (result.success && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else if (Array.isArray(result)) {
        setClientAccounts(result);
      } else if (result.data && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else {
        setClientAccounts([]);
      }
    } catch (error) {
      console.error("Error fetching client accounts:", error);
      setClientAccounts([]);
    }
  }, []);

  useEffect(() => {
    fetchPayments();
    fetchClientAccounts();
  }, [fetchPayments, fetchClientAccounts]);

  // Fetch cycles for selected client
  const fetchCycles = useCallback(async (clientId, isEditing = false) => {
    if (!clientId) {
      setClientCycles([]);
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles/${clientId}/get-client-cycles`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      const result = await response.json();
      if (result.success) {
        // When editing, show all cycles. When creating, filter out paid cycles
        const cycles = isEditing
          ? result.cycles
          : result.cycles.filter((c) => c.status !== "paid");
        console.log("Fetched cycles:", {
          isEditing,
          totalCycles: result.cycles.length,
          filteredCycles: cycles.length,
          cycles,
        });
        setClientCycles(cycles);
      }
    } catch (err) {
      console.error("Error fetching cycles", err);
      setClientCycles([]);
    }
  }, []);

  const fetchProjects = useCallback(async (clientId) => {
    if (!clientId) {
      setProjects([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${PROJECTS_API_URL}/client/${clientId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();

      // Handle the response structure based on the provided data format
      if (result.success && result.data) {
        // If single project returned, wrap in array
        const projectsData = Array.isArray(result.data)
          ? result.data
          : [result.data];
        setProjects(projectsData);
      } else {
        setProjects([]);
      }
    } catch (err) {
      console.error("Error fetching projects:", err);
      setProjects([]);
    }
  }, []);

  useEffect(() => {
    fetchCycles(newPayment.client_id, !!editingPayment);
    fetchProjects(newPayment.client_id);
  }, [newPayment.client_id, editingPayment, fetchCycles, fetchProjects]);
  // Add allocation row
  const addAllocation = () => {
    setAllocations([...allocations, { cycle_id: "", amount: "" }]);
  };

  // Update allocation
  const updateAllocation = (index, field, value) => {
    const updated = [...allocations];
    updated[index][field] = value;
    setAllocations(updated);
  };

  // Remove allocation row
  const removeAllocation = (index) => {
    setAllocations(allocations.filter((_, i) => i !== index));
  };
  const handleAdd = () => {
    setEditingPayment(null);
    setNewPayment({
      client_id: "",
      amount: "",
      date: "",
      method: "cash",
      is_bulk: false,
      allocations: [],
      notes: "",
    });
    setAllocations([]);
    setInvoiceFile(null);
    setBankTransferFile(null);
    setOpenModal(true);
  };

  const handleEdit = (payment) => {
    setEditingPayment(payment);

    // Process allocations to extract proper values
    const processedAllocations = (payment.allocations || []).map(
      (allocation) => ({
        cycle_id: allocation.cycle_id?._id || allocation.cycle_id,
        amount: allocation.amount?.$numberDecimal || allocation.amount || "",
      })
    );

    const clientId = payment.client_id?._id || payment.client_id;

    console.log("Edit Payment Debug:", {
      originalPayment: payment,
      processedAllocations,
      clientId,
    });

    // Determine selection type based on payment data
    const hasProject = payment.project_id;
    const hasAllocations = processedAllocations.length > 0;

    setSelectionType(hasProject ? "project" : "cycle");

    setNewPayment({
      client_id: clientId,
      amount: payment.amount?.$numberDecimal || payment.amount,
      date: payment.date
        ? new Date(payment.date).toISOString().slice(0, 10)
        : "",
      method: payment.method,
      is_bulk: !!payment.is_bulk,
      allocations: processedAllocations,
      project_id: payment.project_id?._id || payment.project_id || "",
      notes: payment.notes || "",
    });
    setAllocations(processedAllocations);
    setInvoiceFile(null);
    setBankTransferFile(null);

    // Fetch cycles and projects for the client immediately
    fetchCycles(clientId, true);
    fetchProjects(clientId);

    setOpenModal(true);
  };

  const handleView = (payment) => {
    setViewingPayment(payment);
    setOpenViewModal(true);
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingPayment(null);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingPayment(null);
    setNewPayment({
      client_id: "",
      amount: "",
      date: "",
      method: "cash",
      is_bulk: false,
      allocations: [],
      project_id: "",
      notes: "",
    });
    setAllocations([]);
    setInvoiceFile(null);
    setBankTransferFile(null);
    setSelectionType("cycle");
    setClientCycles([]);
    setProjects([]);
  };

  const handleSelectionTypeChange = (event, newType) => {
    if (newType !== null) {
      setSelectionType(newType);
      // Clear allocations when switching to project mode
      if (newType === "project") {
        setAllocations([]);
        setNewPayment((prev) => ({
          ...prev,
          allocations: [],
          project_id: "",
        }));
      } else {
        setNewPayment((prev) => ({
          ...prev,
          project_id: "",
        }));
      }
    }
  };

  // Modified handleSubmit
  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");
      const amountNumber = parseFloat(newPayment.amount || "");
      // Validate based on selection type
      let sanitizedAllocations = [];
      let hasValidSelection = false;

      if (selectionType === "project") {
        // Project payment validation
        hasValidSelection =
          newPayment.project_id && String(newPayment.project_id).trim() !== "";
        if (!hasValidSelection) {
          showSnackbar("Please select a project for payment.", "warning");
          return;
        }
      } else {
        // Cycle payment validation (existing logic)
        sanitizedAllocations = (allocations || [])
          .filter(
            (a) =>
              a &&
              a.cycle_id &&
              String(a.cycle_id).trim() !== "" &&
              a.amount !== "" &&
              Number.isFinite(parseFloat(a.amount)) &&
              parseFloat(a.amount) > 0
          )
          .map((a) => ({ cycle_id: a.cycle_id, amount: parseFloat(a.amount) }));

        if (sanitizedAllocations.length > 0) {
          const totalAllocated = sanitizedAllocations.reduce(
            (sum, a) => sum + (parseFloat(a.amount) || 0),
            0
          );
          if (!Number.isFinite(amountNumber) || amountNumber <= 0) {
            showSnackbar("Amount must be a number greater than 0.", "warning");
            return;
          }
          if (Math.abs(totalAllocated - amountNumber) > 0.0001) {
            showSnackbar(
              `Total allocations (${totalAllocated}) must equal payment amount (${amountNumber}).`,
              "error"
            );
            return;
          }
        }
        hasValidSelection = sanitizedAllocations.length > 0;
      }

      // Client-side validation
      const requiredMissing =
        !newPayment.client_id || !newPayment.date || !newPayment.method;
      const validAmount = Number.isFinite(amountNumber) && amountNumber > 0;
      const allowedMethods = ["cash", "bank_transfer", "Check", "instapay"];
      const validMethod = allowedMethods.includes(newPayment.method);
      if (requiredMissing || !validAmount || !validMethod) {
        const reason = requiredMissing
          ? "Please fill client, date and method."
          : !validAmount
          ? "Amount must be a number greater than 0."
          : "Invalid method selected.";
        showSnackbar(reason, "warning");
        return;
      }

      const url = editingPayment
        ? `${API_BASE_URL}/${editingPayment._id}`
        : API_BASE_URL;

      const method = editingPayment ? "PUT" : "POST";

      const hasFiles = !!invoiceFile || !!bankTransferFile;
      let response;

      if (!hasFiles) {
        // Send as JSON so backend sees allocations as array
        const payload = {
          client_id: newPayment.client_id,
          amount: amountNumber,
          date: newPayment.date,
          method: newPayment.method,
          is_bulk: !!newPayment.is_bulk,
          notes: newPayment.notes || undefined,
          ...(selectionType === "project" && newPayment.project_id
            ? { project_id: newPayment.project_id }
            : {}),
          ...(sanitizedAllocations.length > 0
            ? { allocations: sanitizedAllocations }
            : {}),
        };
        response = await fetch(url, {
          method,
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });
      } else {
        // Multipart with files. Encode allocations as indexed fields
        const formData = new FormData();
        formData.append("client_id", newPayment.client_id);
        formData.append("amount", String(amountNumber));
        formData.append("date", newPayment.date);
        formData.append("method", newPayment.method);
        formData.append("is_bulk", newPayment.is_bulk ? "true" : "false");
        if (newPayment.notes) formData.append("notes", newPayment.notes);
        if (selectionType === "project" && newPayment.project_id) {
          formData.append("project_id", newPayment.project_id);
        }
        if (sanitizedAllocations.length > 0) {
          sanitizedAllocations.forEach((a, i) => {
            formData.append(`allocations[${i}][cycle_id]`, a.cycle_id);
            formData.append(`allocations[${i}][amount]`, String(a.amount));
          });
        }
        if (invoiceFile) formData.append("invoice", invoiceFile);
        if (bankTransferFile)
          formData.append("bank_transfer_statement", bankTransferFile);

        response = await fetch(url, {
          method,
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });
      }

      let resultJson = null;
      try {
        resultJson = await response.json();
      } catch (_) {
        // ignore json parse error (e.g., empty body)
      }

      if (response.ok) {
        const result = resultJson || {};
        if (editingPayment) {
          setPayments(
            payments.map((p) =>
              p._id === editingPayment._id ? result.data : p
            )
          );
          showSnackbar("Payment updated successfully", "success");
        } else {
          setPayments([result.data, ...payments]);
          showSnackbar("Payment created successfully", "success");
        }
        handleCloseModal();
        fetchPayments();
      } else {
        const errorMessage = resultJson?.message || "Failed to save payment";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error saving payment:", error);
      showSnackbar(error?.message || "Failed to save payment", "error");
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this payment?")) {
      return;
    }
    const token = localStorage.getItem("token");
    const paymentId = typeof id === "object" && id?._id ? id._id : id;
    const primaryUrl = `${API_BASE_URL}/${paymentId}`;
    try {
      // Try primary RESTful route: DELETE /payments/:paymentId
      console.log("Deleting payment", { paymentId, url: primaryUrl });
      const response = await fetch(primaryUrl, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setPayments(payments.filter((payment) => payment._id !== paymentId));
        showSnackbar("Payment deleted successfully", "success");
        // Ensure UI is in sync with server
        fetchPayments();
      } else {
        let message = `Failed to delete payment`;
        try {
          const err = await response.json();
          if (err?.message) {
            message = `${message}: ${err.message}`;
          } else if (err) {
            message = `${message}: ${JSON.stringify(err)}`;
          }
        } catch (_) {
          try {
            const text = await response.text();
            if (text) message = `${message}: ${text}`;
          } catch (__) {}
        }
        message = `${message} (status ${response.status}${
          response.statusText ? " - " + response.statusText : ""
        }) for URL: ${primaryUrl}`;
        throw new Error(message);
      }
    } catch (error) {
      console.error("Error deleting payment:", error);
      showSnackbar(error?.message || "Failed to delete payment", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getMethodColor = (method) => {
    switch (method) {
      case "cash":
        return "#4caf50";
      case "bank_transfer":
        return "#2196f3";
      case "Check":
        return "#ff9800";
      case "instapay":
        return "#9c27b0";
      default:
        return "#9e9e9e";
    }
  };

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  // Filter payments based on current filters
  const filteredPayments = payments.filter((payment) => {
    if (filters.client_id && payment.client_id?._id !== filters.client_id)
      return false;
    if (filters.method && payment.method !== filters.method) return false;
    if (
      filters.date_from &&
      new Date(payment.date) < new Date(filters.date_from)
    )
      return false;
    if (filters.date_to && new Date(payment.date) > new Date(filters.date_to))
      return false;
    if (
      filters.amount_min &&
      parseFloat(payment.amount?.$numberDecimal || payment.amount) <
        parseFloat(filters.amount_min)
    )
      return false;
    if (
      filters.amount_max &&
      parseFloat(payment.amount?.$numberDecimal || payment.amount) >
        parseFloat(filters.amount_max)
    )
      return false;
    return true;
  });

  // Calculate totals for filtered payments
  const calculateTotals = () => {
    const total = filteredPayments.reduce((sum, payment) => {
      return (
        sum + parseFloat(payment.amount?.$numberDecimal || payment.amount || 0)
      );
    }, 0);

    const byMethod = filteredPayments.reduce((acc, payment) => {
      const method = payment.method;
      const amount = parseFloat(
        payment.amount?.$numberDecimal || payment.amount || 0
      );
      acc[method] = (acc[method] || 0) + amount;
      return acc;
    }, {});

    return { total, byMethod };
  };

  const totals = calculateTotals();

  const clearFilters = () => {
    setFilters({
      client_id: "",
      method: "",
      date_from: "",
      date_to: "",
      amount_min: "",
      amount_max: "",
    });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Payments
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Payment
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Calculator Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  Payment Calculator
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(219, 74, 65, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h4"
                      sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
                    >
                      {totals.total.toLocaleString()} EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Total Payments
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(76, 175, 80, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      {(totals.byMethod.cash || 0).toLocaleString()} EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Cash Payments
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(33, 150, 243, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                    >
                      {(totals.byMethod.bank_transfer || 0).toLocaleString()}{" "}
                      EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Bank Transfers
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(255, 152, 0, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                    >
                      {filteredPayments.length}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Total Records
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Filters Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <FilterListIcon sx={{ color: "#db4a41", mr: 1 }} />
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  Filters
                </Typography>
                <Button
                  startIcon={<ClearIcon />}
                  onClick={clearFilters}
                  sx={{ ml: "auto", color: "rgba(255, 255, 255, 0.7)" }}
                >
                  Clear All
                </Button>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Client
                    </InputLabel>
                    <Select
                      value={filters.client_id}
                      onChange={(e) =>
                        setFilters({ ...filters, client_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            backgroundColor: "rgba(26, 26, 26, 0.95)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            "& .MuiMenuItem-root": {
                              color: "white",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.1)",
                              },
                            },
                          },
                        },
                      }}
                    >
                      <MenuItem value="">All Clients</MenuItem>
                      {Array.isArray(clientAccounts) &&
                        clientAccounts.map((account) => (
                          <MenuItem key={account._id} value={account._id}>
                            {account.client_name}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Method
                    </InputLabel>
                    <Select
                      value={filters.method}
                      onChange={(e) =>
                        setFilters({ ...filters, method: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            backgroundColor: "rgba(26, 26, 26, 0.95)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            "& .MuiMenuItem-root": {
                              color: "white",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.1)",
                              },
                            },
                          },
                        },
                      }}
                    >
                      <MenuItem value="">All Methods</MenuItem>
                      <MenuItem value="cash">Cash</MenuItem>
                      <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
                      <MenuItem value="Check">Check</MenuItem>
                      <MenuItem value="instapay">Instapay</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Date From"
                    type="date"
                    value={filters.date_from}
                    onChange={(e) =>
                      setFilters({ ...filters, date_from: e.target.value })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Date To"
                    type="date"
                    value={filters.date_to}
                    onChange={(e) =>
                      setFilters({ ...filters, date_to: e.target.value })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Min Amount"
                    type="number"
                    value={filters.amount_min}
                    onChange={(e) =>
                      setFilters({ ...filters, amount_min: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Max Amount"
                    type="number"
                    value={filters.amount_max}
                    onChange={(e) =>
                      setFilters({ ...filters, amount_max: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Table Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Client
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Amount
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Date
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Method
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Allocations
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "white",
                              fontFamily: "Formula Bold",
                            }}
                          >
                            Project
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredPayments
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((payment) => (
                            <TableRow
                              key={payment._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: stringToColor(
                                        payment.client_id?.client_name ||
                                          "Unknown"
                                      ),
                                      width: 32,
                                      height: 32,
                                    }}
                                  >
                                    <PaymentIcon fontSize="small" />
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontFamily: "Formula Bold" }}
                                    >
                                      {payment.client_id?.client_name ||
                                        "Unknown Client"}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                    >
                                      ID:{" "}
                                      {payment.client_id?._id?.slice(-6) ||
                                        "N/A"}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontFamily: "Formula Bold",
                                    color: "#4caf50",
                                  }}
                                >
                                  {formatCurrency(payment.amount)}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography variant="body2">
                                  {new Date(payment.date).toLocaleDateString()}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                >
                                  {new Date(payment.date).toLocaleTimeString()}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={payment.method
                                    .replace("_", " ")
                                    .toUpperCase()}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getMethodColor(
                                      payment.method
                                    )}20`,
                                    color: getMethodColor(payment.method),
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography variant="body2">
                                  {payment.allocations?.length || 0} cycle(s)
                                </Typography>
                                {payment.allocations
                                  ?.slice(0, 2)
                                  .map((allocation, index) => (
                                    <Typography
                                      key={index}
                                      variant="caption"
                                      sx={{
                                        display: "block",
                                        color: "rgba(255, 255, 255, 0.6)",
                                        fontSize: "0.7rem",
                                      }}
                                    >
                                      {allocation.cycle_id?.cycle_name ||
                                        `Cycle ${index + 1}`}{" "}
                                      || allocation.project_id :{" "}
                                      {formatCurrency(allocation.amount)}
                                    </Typography>
                                  ))}
                                {payment.allocations?.length > 2 && (
                                  <Typography
                                    variant="caption"
                                    sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                                  >
                                    +{payment.allocations.length - 2} more...
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {payment.project_id ? (
                                  <Typography variant="body2">
                                    Proj-{payment.project_id?.name}
                                  </Typography>
                                ) : (
                                  <Typography variant="body2">N/A</Typography>
                                )}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(payment)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(payment)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(payment._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredPayments.length}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setRowsPerPage(parseInt(event.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                      "& .MuiTablePagination-selectIcon": {
                        color: "white",
                      },
                      "& .MuiTablePagination-select": {
                        color: "white",
                      },
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Payment Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingPayment && (
              <Grid container spacing={3}>
                {/* Payment Header */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          viewingPayment.client_id?.client_name || "Unknown"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <PaymentIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold", color: "white" }}
                      >
                        {formatCurrency(viewingPayment.amount)}
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{ color: "rgba(255, 255, 255, 0.8)" }}
                      >
                        {viewingPayment.client_id?.client_name ||
                          "Unknown Client"}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        <Chip
                          label={viewingPayment.method
                            .replace("_", " ")
                            .toUpperCase()}
                          sx={{
                            backgroundColor: getMethodColor(
                              viewingPayment.method
                            ),
                            color: "white",
                            textTransform: "capitalize",
                          }}
                        />
                        {viewingPayment.is_bulk && (
                          <Chip
                            label="BULK PAYMENT"
                            sx={{
                              backgroundColor: "#9c27b0",
                              color: "white",
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Payment Information */}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Payment Information
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Amount:</strong>{" "}
                    {formatCurrency(viewingPayment.amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Date:</strong>{" "}
                    {new Date(viewingPayment.date).toLocaleDateString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Time:</strong>{" "}
                    {new Date(viewingPayment.date).toLocaleTimeString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Method:</strong>{" "}
                    {viewingPayment.method.replace("_", " ").toUpperCase()}
                  </Typography>
                  {viewingPayment.notes && (
                    <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                      <strong>Notes:</strong> {viewingPayment.notes}
                    </Typography>
                  )}
                  {(viewingPayment.invoice ||
                    viewingPayment.bank_transfer_stament) && (
                    <Box sx={{ mt: 1 }}>
                      {viewingPayment.invoice && (
                        <Typography variant="body2" sx={{ color: "white" }}>
                          <strong>Invoice:</strong>{" "}
                          <Button
                            size="small"
                            onClick={() =>
                              handleDownloadFile(
                                viewingPayment.invoice,
                                `invoice-${viewingPayment._id || "payment"}`
                              )
                            }
                            sx={{
                              color: "#4caf50",
                              textTransform: "none",
                              p: 0,
                              minWidth: 0,
                            }}
                          >
                            Download file
                          </Button>
                        </Typography>
                      )}
                      {viewingPayment.bank_transfer_stament && (
                        <Typography variant="body2" sx={{ color: "white" }}>
                          <strong>Bank Transfer Statement:</strong>{" "}
                          <Button
                            size="small"
                            onClick={() =>
                              handleDownloadFile(
                                viewingPayment.bank_transfer_stament,
                                `bank-transfer-${
                                  viewingPayment._id || "payment"
                                }`
                              )
                            }
                            sx={{
                              color: "#2196f3",
                              textTransform: "none",
                              p: 0,
                              minWidth: 0,
                            }}
                          >
                            Download file
                          </Button>
                        </Typography>
                      )}
                    </Box>
                  )}
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Bulk Payment:</strong>{" "}
                    {viewingPayment.is_bulk ? "Yes" : "No"}
                  </Typography>
                </Grid>

                {/* Client Information */}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Client Information
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Client Name:</strong>{" "}
                    {viewingPayment.client_id?.client_name || "Unknown"}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Subscription Fee:</strong>{" "}
                    {formatCurrency(viewingPayment.client_id?.subscription_fee)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Profit Goal:</strong>{" "}
                    {viewingPayment.client_id?.profit_goal_percent}%
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Status:</strong>
                    <Chip
                      label={viewingPayment.client_id?.status}
                      size="small"
                      sx={{
                        ml: 1,
                        backgroundColor:
                          viewingPayment.client_id?.status === "active"
                            ? "#4caf50"
                            : "#ff9800",
                        color: "white",
                        textTransform: "capitalize",
                      }}
                    />
                  </Typography>
                  {viewingPayment.client_id?.notes && (
                    <Typography
                      variant="body1"
                      sx={{ color: "white", fontStyle: "italic" }}
                    >
                      <strong>Notes:</strong> {viewingPayment.client_id.notes}
                    </Typography>
                  )}
                </Grid>

                {/* Payment Allocations */}
                {viewingPayment.allocations &&
                  viewingPayment.allocations.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#db4a41",
                          mb: 2,
                          fontFamily: "Formula Bold",
                        }}
                      >
                        Payment Allocations ({viewingPayment.allocations.length}
                        )
                      </Typography>
                      <Grid container spacing={2}>
                        {viewingPayment.allocations.map((allocation, index) => (
                          <Grid
                            item
                            xs={12}
                            sm={6}
                            md={4}
                            key={allocation._id || index}
                          >
                            <Card
                              sx={{
                                background: "rgba(255, 255, 255, 0.05)",
                                backdropFilter: "blur(10px)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                              }}
                            >
                              <CardContent>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    color: "#4caf50",
                                    mb: 1,
                                    fontFamily: "Formula Bold",
                                  }}
                                >
                                  {formatCurrency(allocation.amount)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "white", mb: 1 }}
                                >
                                  <strong>Cycle:</strong>{" "}
                                  {allocation.cycle_id?.cycle_name ||
                                    `Cycle ${index + 1}`}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "white", mb: 1 }}
                                >
                                  <strong>Month:</strong>{" "}
                                  {allocation.cycle_id?.month || "N/A"}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "white", mb: 1 }}
                                >
                                  <strong>Due Amount:</strong>{" "}
                                  {formatCurrency(
                                    allocation.cycle_id?.due_amount
                                  )}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "white", mb: 1 }}
                                >
                                  <strong>Paid Amount:</strong>{" "}
                                  {formatCurrency(
                                    allocation.cycle_id?.paid_amount
                                  )}
                                </Typography>
                                {allocation.cycle_id?.status && (
                                  <Chip
                                    label={allocation.cycle_id.status}
                                    size="small"
                                    sx={{
                                      backgroundColor:
                                        allocation.cycle_id.status === "paid"
                                          ? "#4caf50"
                                          : "#ff9800",
                                      color: "white",
                                      textTransform: "capitalize",
                                    }}
                                  />
                                )}
                                {allocation.cycle_id?.profit_status && (
                                  <Chip
                                    label={allocation.cycle_id.profit_status}
                                    size="small"
                                    sx={{
                                      ml: 1,
                                      backgroundColor:
                                        allocation.cycle_id.profit_status ===
                                        "exceeded"
                                          ? "#4caf50"
                                          : "#ff9800",
                                      color: "white",
                                      textTransform: "capitalize",
                                    }}
                                  />
                                )}
                                {allocation.cycle_id?.actual_profit && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      display: "block",
                                      color: "rgba(255, 255, 255, 0.7)",
                                      mt: 1,
                                    }}
                                  >
                                    Profit:{" "}
                                    {formatCurrency(
                                      allocation.cycle_id.actual_profit
                                    )}
                                  </Typography>
                                )}
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    </Grid>
                  )}

                {/* Timestamps */}
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Timestamps
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Created:</strong>{" "}
                    {new Date(viewingPayment.createdAt).toLocaleString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Last Updated:</strong>{" "}
                    {new Date(viewingPayment.updatedAt).toLocaleString()}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingPayment ? "Edit Payment" : "Create New Payment"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newPayment.client_id}
                    onChange={(e) =>
                      setNewPayment({
                        ...newPayment,
                        client_id: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          backgroundColor: "rgba(26, 26, 26, 0.95)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          "& .MuiMenuItem-root": {
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          },
                        },
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((account) => (
                        <MenuItem key={account._id} value={account._id}>
                          {account.client_name} -{" "}
                          {formatCurrency(account.subscription_fee)}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={newPayment.amount}
                  onChange={(e) =>
                    setNewPayment({ ...newPayment, amount: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date"
                  type="date"
                  value={newPayment.date}
                  onChange={(e) =>
                    setNewPayment({ ...newPayment, date: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Payment Method
                  </InputLabel>
                  <Select
                    value={newPayment.method}
                    onChange={(e) =>
                      setNewPayment({ ...newPayment, method: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          backgroundColor: "rgba(26, 26, 26, 0.95)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          "& .MuiMenuItem-root": {
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          },
                        },
                      },
                    }}
                  >
                    <MenuItem value="cash">Cash</MenuItem>
                    <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
                    <MenuItem value="Check">Check</MenuItem>
                    <MenuItem value="instapay">Instapay</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Toggle Button for Payment Type Selection */}
              <Grid item xs={12}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)", mr: 2 }}
                  >
                    Payment Type:
                  </Typography>
                  <ToggleButtonGroup
                    value={selectionType}
                    exclusive
                    onChange={handleSelectionTypeChange}
                    sx={{
                      "& .MuiToggleButton-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&.Mui-selected": {
                          backgroundColor: "#db4a41",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#c43a31",
                          },
                        },
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                        },
                      },
                    }}
                  >
                    <ToggleButton value="cycle">
                      Subscription Cycles
                    </ToggleButton>
                    <ToggleButton value="project">
                      One-Time Project
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>

              {/* Conditional Rendering Based on Selection Type */}
              {selectionType === "cycle" && (
                <Grid item xs={12}>
                  <Typography
                    variant="subtitle1"
                    sx={{ color: "white", mb: 1 }}
                  >
                    Allocations
                  </Typography>
                  {allocations.map((alloc, index) => (
                    <Grid container spacing={2} key={index} sx={{ mb: 1 }}>
                      <Grid item xs={6}>
                        <FormControl fullWidth>
                          <InputLabel
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            Cycle
                          </InputLabel>
                          <Select
                            value={alloc.cycle_id || ""}
                            onChange={(e) =>
                              updateAllocation(
                                index,
                                "cycle_id",
                                e.target.value
                              )
                            }
                            displayEmpty
                            sx={{
                              color: "white",
                              "& .MuiOutlinedInput-notchedOutline": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&.Mui-focused .MuiOutlinedInput-notchedOutline":
                                {
                                  borderColor: "#db4a41",
                                },
                            }}
                          >
                            <MenuItem value="" disabled>
                              <em>Select a cycle</em>
                            </MenuItem>
                            {clientCycles.map((cycle) => (
                              <MenuItem key={cycle._id} value={cycle._id}>
                                {cycle.cycle_name || `${cycle.month}`} — Due:{" "}
                                {parseFloat(
                                  cycle.due_amount?.$numberDecimal ||
                                    cycle.due_amount ||
                                    0
                                ).toLocaleString()}{" "}
                                EGP / Paid:{" "}
                                {parseFloat(
                                  cycle.paid_amount?.$numberDecimal ||
                                    cycle.paid_amount ||
                                    0
                                ).toLocaleString()}{" "}
                                EGP
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={4}>
                        <TextField
                          fullWidth
                          label="Amount"
                          type="number"
                          value={alloc.amount}
                          onChange={(e) =>
                            updateAllocation(index, "amount", e.target.value)
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              color: "white",
                              "& fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              color: "rgba(255, 255, 255, 0.7)",
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={2}>
                        <IconButton onClick={() => removeAllocation(index)}>
                          <ClearIcon sx={{ color: "white" }} />
                        </IconButton>
                      </Grid>
                    </Grid>
                  ))}
                  <Button
                    onClick={addAllocation}
                    startIcon={<AddIcon />}
                    sx={{ color: "white", mt: 1 }}
                  >
                    Add Allocation
                  </Button>
                </Grid>
              )}

              {/* Project Selection Section */}
              {selectionType === "project" && (
                <Grid item xs={12}>
                  <Typography
                    variant="subtitle1"
                    sx={{ color: "white", mb: 1 }}
                  >
                    Project Selection
                  </Typography>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Select Project
                    </InputLabel>
                    <Select
                      value={newPayment.project_id || ""}
                      onChange={(e) =>
                        setNewPayment({
                          ...newPayment,
                          project_id: e.target.value,
                        })
                      }
                      displayEmpty
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            backgroundColor: "rgba(26, 26, 26, 0.95)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            "& .MuiMenuItem-root": {
                              color: "white",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.1)",
                              },
                            },
                          },
                        },
                      }}
                    >
                      <MenuItem value="" disabled>
                        <em>Select a project</em>
                      </MenuItem>
                      {Array.isArray(projects) &&
                        projects.map((project) => (
                          <MenuItem key={project._id} value={project._id}>
                            {`${project.name} - ${new Date(
                              project.date
                            ).toLocaleDateString()} - ${formatCurrency(
                              project.fees
                            )}`}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newPayment.is_bulk}
                      onChange={(e) =>
                        setNewPayment({
                          ...newPayment,
                          is_bulk: e.target.checked,
                        })
                      }
                    />
                  }
                  label="Bulk Payment"
                  sx={{ color: "rgba(255, 255, 255, 0.85)" }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Notes"
                  value={newPayment.notes}
                  onChange={(e) =>
                    setNewPayment({ ...newPayment, notes: e.target.value })
                  }
                  multiline
                  minRows={1}
                  maxRows={4}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  variant="outlined"
                  component="label"
                  sx={{
                    borderColor: "#db4a41",
                    color: "#db4a41",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      borderColor: "#c62828",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Upload Invoice
                  <input
                    type="file"
                    hidden
                    onChange={(e) =>
                      setInvoiceFile(e.target.files?.[0] || null)
                    }
                    accept="image/*,application/pdf"
                  />
                </Button>
                {invoiceFile && (
                  <Typography
                    variant="caption"
                    sx={{ ml: 1, color: "rgba(255, 255, 255, 0.8)" }}
                  >
                    {invoiceFile.name}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <Button
                  variant="outlined"
                  component="label"
                  sx={{
                    borderColor: "#db4a41",
                    color: "#db4a41",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      borderColor: "#c62828",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  Upload Bank Transfer Statement
                  <input
                    type="file"
                    hidden
                    onChange={(e) =>
                      setBankTransferFile(e.target.files?.[0] || null)
                    }
                    accept="image/*,application/pdf"
                  />
                </Button>
                {bankTransferFile && (
                  <Typography
                    variant="caption"
                    sx={{ ml: 1, color: "rgba(255, 255, 255, 0.8)" }}
                  >
                    {bankTransferFile.name}
                  </Typography>
                )}
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingPayment ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Payments;
