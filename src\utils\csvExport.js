/**
 * CSV Export Utilities
 * Reusable functions for exporting data to CSV format
 */

/**
 * Converts a decimal value to a number, handling MongoDB Decimal128 format
 * @param {*} value - The value to parse (can be Decimal128 or regular number)
 * @returns {number} - Parsed decimal value
 */
export const parseDecimal = (value) => {
  return parseFloat(value?.$numberDecimal ?? value ?? 0) || 0;
};

/**
 * Escapes CSV field values and wraps them in quotes
 * @param {*} field - The field value to escape
 * @returns {string} - Escaped and quoted field value
 */
export const escapeCsvField = (field) => {
  return `"${String(field).replace(/"/g, '""')}"`;
};

/**
 * Converts array of arrays to CSV string with formatting support
 * @param {Array<Array>} data - 2D array of data
 * @param {Object} options - Formatting options
 * @returns {string} - CSV formatted string
 */
export const arrayToCsv = (data, options = {}) => {
  return data
    .map((row, rowIndex) => {
      return row
        .map((field, colIndex) => {
          let formattedField = String(field);

          // Add formatting markers for Excel/Google Sheets
          if (options.boldRows && options.boldRows.includes(rowIndex)) {
            formattedField = `**${formattedField}**`; // Bold marker
          } else if (
            options.semiBoldRows &&
            options.semiBoldRows.includes(rowIndex)
          ) {
            formattedField = `*${formattedField}*`; // Semi-bold marker
          }

          return escapeCsvField(formattedField);
        })
        .join(",");
    })
    .join("\n");
};

/**
 * Downloads a CSV file with the given content
 * @param {string} csvContent - The CSV content as string
 * @param {string} filename - The filename for the download
 */
export const downloadCsv = (csvContent, filename) => {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);

  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Exports payroll data to CSV format
 * @param {Array} payments - Array of payment objects
 * @param {Object} filters - Filter object containing month and year
 * @param {Function} showSnackbar - Function to show notifications
 * @returns {boolean} - Success status
 */
export const exportPayrollToCSV = (payments, filters, showSnackbar) => {
  if (!payments || payments.length === 0) {
    showSnackbar("No payroll data available to export", "error");
    return false;
  }

  // Validate month and year filters
  if (!filters.month || !filters.year) {
    showSnackbar("Please select both month and year to export", "warning");
    return false;
  }

  // Filter payments by month and year
  const filteredPayments = payments.filter(
    (p) =>
      p.month === parseInt(filters.month) && p.year === parseInt(filters.year)
  );

  if (filteredPayments.length === 0) {
    showSnackbar(
      `No payroll data found for ${String(filters.month).padStart(2, "0")}/${
        filters.year
      }`,
      "warning"
    );
    return false;
  }

  // Prepare CSV data
  const csvData = [];

  // Add header row
  csvData.push([
    "Employee Name",
    "Employee Email",
    "Month/Year",
    "Gross Amount (EGP)",
    "Deductions (EGP)",
    "Net Amount (EGP)",
    "Status",
    "Paid Date",
    "Processed By",
    "Salary ID",
  ]);

  // Add data rows and calculate totals
  let totalGross = 0;
  let totalDeductions = 0;
  let totalNet = 0;

  filteredPayments.forEach((payment) => {
    const grossAmount = parseDecimal(payment.gross_amount);
    const deductionsAmount = parseDecimal(payment.deductions);
    const netAmount = parseDecimal(payment.net_amount);

    totalGross += grossAmount;
    totalDeductions += deductionsAmount;
    totalNet += netAmount;

    csvData.push([
      payment.userId?.name || payment.userId?.email || "Unknown",
      payment.userId?.email || "",
      `${String(payment.month).padStart(2, "0")}/${payment.year}`,
      grossAmount.toFixed(2),
      deductionsAmount.toFixed(2),
      netAmount.toFixed(2),
      payment.paid ? "Paid" : "Unpaid",
      payment.paid_at ? new Date(payment.paid_at).toLocaleDateString() : "",
      payment.processed_by?.name || payment.processed_by || "",
      payment.salary_id?._id?.slice(-6) || "",
    ]);
  });

  // Add empty row
  csvData.push([]);

  // Add total row
  csvData.push([
    "TOTAL PAYROLL",
    "",
    `${String(filters.month).padStart(2, "0")}/${filters.year}`,
    totalGross.toFixed(2),
    totalDeductions.toFixed(2),
    totalNet.toFixed(2),
    "",
    "",
    "",
    "",
  ]);

  // Convert to CSV and download
  const csvContent = arrayToCsv(csvData);
  const filename = `payroll-${String(filters.month).padStart(2, "0")}-${
    filters.year
  }-${new Date().toISOString().split("T")[0]}.csv`;

  downloadCsv(csvContent, filename);

  showSnackbar(
    `Payroll data for ${String(filters.month).padStart(2, "0")}/${
      filters.year
    } exported successfully`,
    "success"
  );

  return true;
};

/**
 * Exports agency finance data to CSV format
 * @param {Object} currentData - Aggregated finance data for the selected period
 * @param {Array} cycles - Array of subscription cycles
 * @param {string} selectedStartPeriod - Start period for the export
 * @param {string} selectedEndPeriod - End period for the export
 * @param {Function} showSnackbar - Function to show notifications
 * @returns {boolean} - Success status
 */
export const exportAgencyFinanceToCSV = (
  currentData,
  cycles,
  selectedStartPeriod,
  selectedEndPeriod,
  showSnackbar,
  paymentsData = null
) => {
  if (!currentData) {
    showSnackbar("No finance data available to export", "error");
    return false;
  }

  if (!selectedStartPeriod || !selectedEndPeriod) {
    showSnackbar(
      "Please select both start and end periods to export",
      "warning"
    );
    return false;
  }

  const csvData = [];
  const boldRows = []; // Track rows that should be bold
  const semiBoldRows = []; // Track rows that should be semi-bold
  let currentRow = 0;

  // Add header information
  csvData.push(["**AGENCY FINANCE REPORT**"]);
  boldRows.push(currentRow++);
  csvData.push(["Generated on:", new Date().toLocaleDateString()]);
  currentRow++;
  csvData.push([
    "Period:",
    selectedStartPeriod === selectedEndPeriod
      ? selectedStartPeriod
      : `${selectedStartPeriod} to ${selectedEndPeriod}`,
  ]);
  currentRow++;
  csvData.push([]);
  currentRow++;

  // Add summary section
  csvData.push(["**FINANCIAL SUMMARY**"]);
  boldRows.push(currentRow++);
  csvData.push(["*Metric*", "*Amount (EGP)*"]);
  semiBoldRows.push(currentRow++);

  // Add total payments sum above total inflow
  let totalPayments = 0;
  if (paymentsData && paymentsData.length > 0) {
    totalPayments = paymentsData.reduce((sum, payment) => {
      return sum + parseDecimal(payment.amount);
    }, 0);
    csvData.push(["Total Payments", totalPayments.toFixed(2)]);
    currentRow++;
  }

  csvData.push([
    "Total Inflow",
    parseDecimal(currentData.total_inflow).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Total Outflow",
    parseDecimal(currentData.total_outflow).toFixed(2),
  ]);
  currentRow++;

  // Add breakdown under Total Outflow
  csvData.push([
    "  - Salaries Expense",
    parseDecimal(currentData.salaries_expense).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "  - Agency Expenses",
    parseDecimal(currentData.agency_expenses).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "  - Client Outflows",
    parseDecimal(currentData.client_outflows).toFixed(2),
  ]);
  currentRow++;

  csvData.push(["Net Profit", parseDecimal(currentData.net_profit).toFixed(2)]);
  currentRow++;
  csvData.push([
    "Client Revenue",
    parseDecimal(currentData.client_revenue).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Total Cycles Fees",
    parseDecimal(currentData.total_cycles_fees).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Client Paid Amount",
    parseDecimal(currentData.client_paid).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Client Due Amount",
    parseDecimal(currentData.client_due_amount).toFixed(2),
  ]);
  currentRow++;
  csvData.push([]);
  currentRow++;

  // Add project overview section
  csvData.push(["**ONE-TIME PROJECT OVERVIEW**"]);
  boldRows.push(currentRow++);
  csvData.push(["*Metric*", "*Amount (EGP)*"]);
  semiBoldRows.push(currentRow++);
  csvData.push([
    "Total Project Fees",
    parseDecimal(currentData.total_project_fees).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Project Paid",
    parseDecimal(currentData.project_paid).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Project Due Amount",
    parseDecimal(currentData.project_due_amount).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Project Inflows",
    parseDecimal(currentData.project_inflows).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Project Outflows",
    parseDecimal(currentData.project_outflows).toFixed(2),
  ]);
  currentRow++;
  csvData.push([]);
  currentRow++;

  // Add payments section
  if (paymentsData && paymentsData.length > 0) {
    csvData.push([`**PAYMENTS (${paymentsData.length})**`]);
    boldRows.push(currentRow++);
    csvData.push([
      "*Payment ID*",
      "*Amount (EGP)*",
      "*Payment Method*",
      "*Client Name*",
      "*Project Name*",
      "*Project Fees*",
      "*Project Paid Amount*",
      "*Project Status*",
      "*Project Date*",
      "*Cycle Name*",
      "*Cycle Month*",
      "*Date*",
      "*Payment Type*",
      "*Allocations Count*",
    ]);
    semiBoldRows.push(currentRow++);

    paymentsData.forEach((payment) => {
      // Extract allocation information from the first allocation (most payments have one allocation)
      const firstAllocation =
        payment.allocations && payment.allocations.length > 0
          ? payment.allocations[0]
          : null;

      // Extract cycle information
      const cycleInfo = firstAllocation?.cycle_id || null;
      const cycleName = cycleInfo?.cycle_name || "";
      const cycleMonth = cycleInfo?.month || "";

      // Extract project information if payment is for a project
      const projectInfo = firstAllocation?.project_id || null;
      const isProjectPayment = !!projectInfo;
      // Determine payment for type
      const paymentFor = isProjectPayment ? "Project" : "Cycle";

      csvData.push([
        payment._id || "",
        parseDecimal(payment.amount).toFixed(2),
        payment.method || "",
        payment.client_id?.client_name || "",
        payment.project_id?.name,
        parseDecimal(payment.project_id?.fees).toFixed(2),
        parseDecimal(payment.project_id?.paid_amount).toFixed(2),
        payment.project_id?.status,
        payment.project_id?.date,
        cycleName,
        cycleMonth,
        payment.date ? new Date(payment.date).toLocaleDateString() : "",
        payment.is_bulk ? "Bulk Payment" : "Single Payment",
        payment.allocations?.length || 0,
      ]);
      currentRow++;
    });
    csvData.push([]);
    currentRow++;
  }

  // Add salary payments section
  if (currentData.salary_payments && currentData.salary_payments.length > 0) {
    csvData.push([
      `**SALARY PAYMENTS (${currentData.salary_payments.length})**`,
    ]);
    boldRows.push(currentRow++);
    csvData.push([
      "*Employee Name*",
      "*Employee Role*",
      "*Month/Year*",
      "*Gross Amount (EGP)*",
      "*Deductions (EGP)*",
      "*Net Amount (EGP)*",
      "*Status*",
      "*Paid Date*",
    ]);
    semiBoldRows.push(currentRow++);

    currentData.salary_payments.forEach((payment) => {
      csvData.push([
        payment.userId?.name || "Unknown",
        payment.userId?.role || "N/A",
        `${payment.month}/${payment.year}`,
        parseDecimal(payment.gross_amount).toFixed(2),
        parseDecimal(payment.deductions).toFixed(2),
        parseDecimal(payment.net_amount).toFixed(2),
        payment.paid ? "PAID" : "PENDING",
        payment.paid_at ? new Date(payment.paid_at).toLocaleDateString() : "",
      ]);
      currentRow++;
    });
    csvData.push([]);
    currentRow++;
  }

  // Add agency expenses section
  if (currentData.expenses && currentData.expenses.length > 0) {
    csvData.push([`**AGENCY EXPENSES (${currentData.expenses.length})**`]);
    boldRows.push(currentRow++);
    csvData.push([
      "*Title*",
      "*Category*",
      "*Amount (EGP)*",
      "*Date*",
      "*Description*",
    ]);
    semiBoldRows.push(currentRow++);

    currentData.expenses.forEach((expense) => {
      csvData.push([
        expense.title || "",
        expense.category || "",
        parseDecimal(expense.amount).toFixed(2),
        expense.date ? new Date(expense.date).toLocaleDateString() : "",
        (expense.description || "").replace(/\n|\r/g, " "),
      ]);
      currentRow++;
    });
    csvData.push([]);
    currentRow++;
  }

  // Add debugging information to understand the cycles data
  console.log("CSV Export Debug Info:");
  console.log("Selected Period:", selectedStartPeriod, "to", selectedEndPeriod);
  console.log("Total cycles received:", cycles?.length || 0);
  console.log("Cycles data:", cycles);

  // Use the cycles array directly as the dashboard does
  // The API should already be filtering by period
  const cyclesToExport = cycles || [];

  // Add subscription cycles section
  if (cyclesToExport && cyclesToExport.length > 0) {
    csvData.push([`**SUBSCRIPTION CYCLES (${cyclesToExport.length})**`]);
    boldRows.push(currentRow++);
    csvData.push([
      "*Cycle Name*",
      "*Client Name*",
      "*Status*",
      "*Due Amount (EGP)*",
      "*Paid Amount (EGP)*",
      "*Inflow (EGP)*",
      "*Outflow (EGP)*",
      "*Goal Profit (EGP)*",
      "*Actual Profit (EGP)*",
      "*Profit Status*",
      "*Penalties (EGP)*",
      "*Quotations Count*",
      "*Expenses Count*",
      "*Adjustments Count*",
    ]);
    semiBoldRows.push(currentRow++);

    cyclesToExport.forEach((cycle) => {
      csvData.push([
        cycle.cycle_name || "",
        cycle.client_id?.client_name || "",
        cycle.status || "",
        parseDecimal(cycle.due_amount).toFixed(2),
        parseDecimal(cycle.paid_amount).toFixed(2),
        parseDecimal(cycle.inflow).toFixed(2),
        parseDecimal(cycle.outflow).toFixed(2),
        parseDecimal(cycle.goal_profit).toFixed(2),
        parseDecimal(cycle.actual_profit).toFixed(2),
        cycle.profit_status || "",
        parseDecimal(cycle.penalties).toFixed(2),
        cycle.quotations?.length || 0,
        cycle.expenses?.length || 0,
        cycle.adjustments?.length || 0,
      ]);
      currentRow++;
    });
    csvData.push([]);
    currentRow++;
  } else {
    // Add a note if no cycles found
    csvData.push(["**SUBSCRIPTION CYCLES (0)**"]);
    boldRows.push(currentRow++);
    csvData.push(["No subscription cycles found for the selected period"]);
    currentRow++;
    csvData.push([]);
    currentRow++;
  }

  // Add footer
  csvData.push([]);
  currentRow++;
  csvData.push(["Report generated by Young Productions CMS"]);
  currentRow++;
  csvData.push(["Export Date:", new Date().toISOString()]);
  currentRow++;

  // Convert to CSV and download with formatting
  const csvContent = arrayToCsv(csvData, { boldRows, semiBoldRows });
  const filename = `agency-finance-${selectedStartPeriod}-to-${selectedEndPeriod}-${
    new Date().toISOString().split("T")[0]
  }.csv`;

  downloadCsv(csvContent, filename);

  showSnackbar(
    `Agency finance data for ${
      selectedStartPeriod === selectedEndPeriod
        ? selectedStartPeriod
        : `${selectedStartPeriod} to ${selectedEndPeriod}`
    } exported successfully`,
    "success"
  );

  return true;
};
export const exportAgencyFinanceSummaryToCSV = (
  currentData,
  selectedStartPeriod,
  selectedEndPeriod,
  showSnackbar
) => {
  if (!currentData) {
    showSnackbar("No finance data available to export", "error");
    return false;
  }

  const csvData = [];
  const boldRows = [];
  const semiBoldRows = [];
  let currentRow = 0;

  csvData.push(["**AGENCY FINANCE SUMMARY REPORT**"]);
  boldRows.push(currentRow++);
  csvData.push(["Generated on:", new Date().toLocaleDateString()]);
  currentRow++;
  csvData.push([
    "Period:",
    selectedStartPeriod === selectedEndPeriod
      ? selectedStartPeriod
      : `${selectedStartPeriod} to ${selectedEndPeriod}`,
  ]);
  currentRow++;
  csvData.push([]);
  currentRow++;

  csvData.push(["**FINANCIAL SUMMARY**"]);
  boldRows.push(currentRow++);
  csvData.push(["*Metric*", "*Amount (EGP)*"]);
  semiBoldRows.push(currentRow++);
  csvData.push([
    "Total Inflow",
    parseDecimal(currentData.total_inflow).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Total Outflow",
    parseDecimal(currentData.total_outflow).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "  - Salaries Expense",
    parseDecimal(currentData.salaries_expense).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "  - Agency Expenses",
    parseDecimal(currentData.agency_expenses).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "  - Client Outflows",
    parseDecimal(currentData.client_outflows).toFixed(2),
  ]);
  currentRow++;
  csvData.push(["Net Profit", parseDecimal(currentData.net_profit).toFixed(2)]);
  currentRow++;
  csvData.push([
    "Client Revenue",
    parseDecimal(currentData.client_revenue).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Total Cycles Fees",
    parseDecimal(currentData.total_cycles_fees).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Client Paid Amount",
    parseDecimal(currentData.client_paid).toFixed(2),
  ]);
  currentRow++;
  csvData.push([
    "Client Due Amount",
    parseDecimal(currentData.client_due_amount).toFixed(2),
  ]);
  currentRow++;

  csvData.push([]);
  csvData.push(["Report generated by Young Productions CMS"]);
  currentRow++;
  csvData.push(["Export Date:", new Date().toISOString()]);
  currentRow++;

  const csvContent = arrayToCsv(csvData, { boldRows, semiBoldRows });
  const filename = `agency-finance-summary-${selectedStartPeriod}-to-${selectedEndPeriod}-${
    new Date().toISOString().split("T")[0]
  }.csv`;

  downloadCsv(csvContent, filename);

  showSnackbar(
    `Agency finance summary for ${
      selectedStartPeriod === selectedEndPeriod
        ? selectedStartPeriod
        : `${selectedStartPeriod} to ${selectedEndPeriod}`
    } exported successfully`,
    "success"
  );

  return true;
};

/**
 * Generic CSV export function for any tabular data
 * @param {Array} data - Array of objects to export
 * @param {Array} columns - Array of column definitions {key, label}
 * @param {string} filename - Filename for the export
 * @param {Function} showSnackbar - Function to show notifications
 * @returns {boolean} - Success status
 */
export const exportGenericCSV = (data, columns, filename, showSnackbar) => {
  if (!data || data.length === 0) {
    showSnackbar("No data available to export", "error");
    return false;
  }

  const csvData = [];

  // Add header row
  csvData.push(columns.map((col) => col.label));

  // Add data rows
  data.forEach((item) => {
    csvData.push(
      columns.map((col) => {
        const value = col.key.split(".").reduce((obj, key) => obj?.[key], item);
        return col.formatter ? col.formatter(value) : value ?? "";
      })
    );
  });

  // Convert to CSV and download
  const csvContent = arrayToCsv(csvData);
  downloadCsv(csvContent, filename);

  showSnackbar("Data exported successfully", "success");
  return true;
};
