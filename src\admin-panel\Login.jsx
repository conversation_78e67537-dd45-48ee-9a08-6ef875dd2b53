import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  TextField,
  Typography,
  Paper,
  IconButton,
  InputAdornment,
} from "@mui/material";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useUser } from "../contexts/UserContext";

function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useUser();
  const from = location.state?.from?.pathname || "/admin";

  const handleLogin = async (e) => {
    e.preventDefault();
    setError("");

    const result = await login(email, password);

    if (result.success) {
      navigate(from, { replace: true });
    } else {
      setError(result.message);
    }
  };
  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <Box
      sx={{
        position: "relative",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        color: "white",
        backgroundColor: "black",
      }}
    >
      <img
        src="/assets/young-logo-white.webp"
        alt="logo"
        style={{
          width: "200px",
          zIndex: 1,
          position: "absolute",
          top: "5%",
          left: "5%",
        }}
      />

      <Paper
        elevation={24}
        sx={{
          background: "rgba(255, 255, 255, 0.1)",
          backdropFilter: "blur(10px)",
          padding: "40px",
          borderRadius: "15px",
          width: "400px",
          maxWidth: "90%",
        }}
      >
        <Typography
          sx={{
            fontFamily: "Formula Bold",
            fontSize: "2.5rem",
            color: "white",
            textAlign: "center",
          }}
          variant="h4"
          gutterBottom
        >
          Admin Login
        </Typography>

        {error && (
          <Typography
            sx={{
              color: "#ff6b6b",
              textAlign: "center",
              marginBottom: "15px",
              fontSize: "0.9rem",
            }}
          >
            {error}
          </Typography>
        )}

        <form
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "20px",
          }}
          onSubmit={handleLogin}
        >
          <TextField
            label="Email"
            variant="outlined"
            value={email}
            onChange={(e) => setEmail(e.target.value.toLowerCase())} // 👈 Force lowercase
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
                textTransform: "lowercase", // 👈 optional: ensures visual lowercase styling
              },
            }}
          />

          <TextField
            label="Password"
            type={showPassword ? "text" : "password"}
            variant="outlined"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            InputLabelProps={{ style: { color: "white" } }}
            InputProps={{
              style: { color: "white" },
              sx: {
                "&.Mui-focused": {
                  backgroundColor: "transparent",
                },
                "& input": {
                  color: "white",
                },
              },
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    onMouseDown={handleMouseDownPassword}
                  >
                    {showPassword ? (
                      <Visibility sx={{ color: "white" }} />
                    ) : (
                      <VisibilityOff sx={{ color: "white" }} />
                    )}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                "&:hover fieldset": { borderColor: "white" },
                "&.Mui-focused fieldset": { borderColor: "white" },
                backgroundColor: "transparent",
              },
              "& .MuiInputBase-input": {
                color: "white",
              },
            }}
          />
          <button
            style={{
              padding: "12px",
              backgroundColor: "#db4a41",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontWeight: "bold",
              fontSize: "16px",
            }}
            type="submit"
          >
            Log In
          </button>
          <Typography sx={{ color: "white", textAlign: "center", mt: 2 }}>
            Don't have an account?{" "}
            <a
              href="mailto:<EMAIL>"
              style={{ color: "#db4a41", textDecoration: "none" }}
            >
              contact the site administrator
            </a>
          </Typography>
        </form>
      </Paper>
    </Box>
  );
}

export default Login;
