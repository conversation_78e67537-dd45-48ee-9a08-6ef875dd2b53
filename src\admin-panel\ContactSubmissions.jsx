import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  MenuItem,
  Select,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Snackbar,
  Alert,
  Divider,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";
import DeleteIcon from "@mui/icons-material/Delete";
import axios from "axios";
import { useUndo } from "./UndoContext";

const ContactSubmissions = () => {
  const [formData, setFormData] = useState([]);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const navigate = useNavigate();
  const { triggerUndo } = useUndo();

  // Create axios instance with default config
  const api = axios.create({
    baseURL: "https://youngproductions-768ada043db3.herokuapp.com/api",
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add request interceptor to add token to all requests
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add response interceptor to handle 401 errors
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.status === 401) {
        // Token is invalid or expired
        localStorage.removeItem("token");
        navigate("/admin/login");
      }
      return Promise.reject(error);
    }
  );

  useEffect(() => {
    fetchFormData();
  }, []);

  const fetchFormData = async () => {
    try {
      setLoading(true);
      const response = await api.get("/contactform");
      const formDataWithStatus = response.data.map((item) => ({
        ...item,
        status: item.status || "pending",
      }));
      setFormData(formDataWithStatus);
      setError(null);
    } catch (error) {
      console.error("Error fetching contact form submissions:", error);
      if (error.response && error.response.status === 401) {
        setError("Please log in to view submissions");
      } else {
        setError("Failed to load submissions. Please try again later.");
      }
      setSnackbar({
        open: true,
        message: "Failed to load submissions",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const doDelete = async (id) => {
    try {
      await api.delete(`/contactform/${id}`);
      setFormData(formData.filter((item) => item._id !== id));
      // No success snackbar, undo snackbar handles feedback
    } catch (error) {
      console.error("Error deleting entry:", error);
      setSnackbar({
        open: true,
        message: "Failed to delete submission",
        severity: "error",
      });
    }
  };

  const handleDelete = (id) => {
    triggerUndo("Submission deleted. Undo?", () => doDelete(id));
  };

  const handleChangeStatus = async (id, newStatus) => {
    try {
      await api.patch(`/contactform/${id}`, { status: newStatus });

      if (newStatus === "archived") {
        navigate(`/archived/${id}`);
        setFormData(formData.filter((item) => item._id !== id));
      } else {
        const updatedFormData = formData.map((item) => {
          if (item._id === id) {
            return { ...item, status: newStatus };
          }
          return item;
        });
        setFormData(updatedFormData);
      }
      setSnackbar({
        open: true,
        message: "Status updated successfully",
        severity: "success",
      });
    } catch (error) {
      console.error("Error updating status:", error);
      setSnackbar({
        open: true,
        message: "Failed to update status",
        severity: "error",
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "closed deal":
        return "#4CAF50";
      case "negotiating":
        return "#FFA726";
      case "archived":
        return "#9E9E9E";
      default:
        return "#2196F3";
    }
  };

  const handleOpenDialog = (submission) => {
    setSelectedSubmission(submission);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedSubmission(null);
  };

  if (loading) {
    return (
      <Box
        sx={{
          background: "black",
          minHeight: "100vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Typography variant="h6" sx={{ color: "white" }}>
          Loading submissions...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          background: "black",
          minHeight: "100vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Typography variant="h6" sx={{ color: "#db4a41" }}>
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        background: "black",
        minHeight: "100vh",
        p: 4,
      }}
    >
      <Typography
        variant="h3"
        sx={{
          fontFamily: "Formula Bold",
          color: "#db4a41",
          textAlign: "center",
          mb: 4,
        }}
      >
        Contact Form Submissions
      </Typography>

      <Grid container spacing={3}>
        {formData.length > 0 ? (
          formData.map((formItem) => (
            <Grid item xs={12} md={6} lg={4} key={formItem._id}>
              <Card
                sx={{
                  background: "rgba(255, 255, 255, 0.05)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  borderRadius: "15px",
                  height: "100%",
                  transition: "transform 0.2s, box-shadow 0.2s",
                  "&:hover": {
                    transform: "translateY(-5px)",
                    boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
                  },
                }}
              >
                <CardContent>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      mb: 2,
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                      }}
                    >
                      {formItem.companyName}
                    </Typography>
                    <Chip
                      label={formItem.status}
                      sx={{
                        backgroundColor: `${getStatusColor(formItem.status)}20`,
                        color: getStatusColor(formItem.status),
                        fontWeight: "bold",
                        border: `1px solid ${getStatusColor(formItem.status)}`,
                      }}
                    />
                  </Box>

                  <Divider
                    sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }}
                  />

                  <Typography
                    variant="subtitle1"
                    sx={{ color: "white", mb: 1 }}
                  >
                    {formItem.selectedType}
                  </Typography>
                  <Typography variant="h6" sx={{ color: "#db4a41", mb: 2 }}>
                    ${formItem.selectedBudget}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Deadline: {formItem.selectedDate}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Meeting: {formItem.meetingDateTime}
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mt: 2,
                    }}
                  >
                    <Button
                      variant="outlined"
                      startIcon={<InfoIcon />}
                      onClick={() => handleOpenDialog(formItem)}
                      sx={{
                        color: "#db4a41",
                        borderColor: "#db4a41",
                        "&:hover": {
                          borderColor: "#db4a41",
                          backgroundColor: "rgba(219, 74, 65, 0.1)",
                        },
                      }}
                    >
                      View Details
                    </Button>
                    <IconButton
                      onClick={() => handleDelete(formItem._id)}
                      sx={{
                        color: "#db4a41",
                        "&:hover": {
                          backgroundColor: "rgba(219, 74, 65, 0.1)",
                        },
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))
        ) : (
          <Grid item xs={12}>
            <Typography
              variant="body1"
              sx={{ color: "white", textAlign: "center" }}
            >
              No submissions yet.
            </Typography>
          </Grid>
        )}
      </Grid>

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          },
        }}
      >
        {selectedSubmission && (
          <>
            <DialogTitle>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="h5"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  {selectedSubmission.companyName}
                </Typography>
                <IconButton onClick={handleCloseDialog} sx={{ color: "white" }}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers sx={{ color: "white" }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ mb: 2, color: "#db4a41" }}>
                    Project Details
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Type:</strong> {selectedSubmission.selectedType}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Budget:</strong> $
                    {selectedSubmission.selectedBudget}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Deadline:</strong> {selectedSubmission.selectedDate}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Meeting:</strong>{" "}
                    {selectedSubmission.meetingDateTime}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ mb: 2, color: "#db4a41" }}>
                    Contact Information
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Name:</strong> {selectedSubmission.firstName}{" "}
                    {selectedSubmission.lastName}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Phone:</strong> {selectedSubmission.phoneNumber}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Email:</strong> {selectedSubmission.email}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1, color: "white" }}>
                    <strong>Source:</strong> {selectedSubmission.hearing}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mb: 2, color: "#db4a41" }}>
                    Additional Notes
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    {selectedSubmission.description}
                  </Typography>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions sx={{ background: "rgba(255, 255, 255, 0.05)" }}>
              <Select
                value={selectedSubmission.status}
                onChange={(e) =>
                  handleChangeStatus(selectedSubmission._id, e.target.value)
                }
                sx={{
                  minWidth: 150,
                  mr: 2,
                  color: "white",
                  "& .MuiSelect-icon": {
                    color: "white",
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                }}
              >
                <MenuItem value="closed deal">Closed Deal</MenuItem>
                <MenuItem value="archived">Archived</MenuItem>
                <MenuItem value="negotiating">Negotiating</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
              </Select>
              <Button
                onClick={handleCloseDialog}
                sx={{
                  color: "#db4a41",
                  "&:hover": {
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            width: "100%",
            backgroundColor:
              snackbar.severity === "error" ? "#db4a41" : "#4CAF50",
            color: "white",
            "& .MuiAlert-icon": {
              color: "white",
            },
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ContactSubmissions;
