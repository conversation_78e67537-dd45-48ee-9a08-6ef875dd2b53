/**
 * Global Video Load Manager
 * Centralized controller that allows only one active video download at a time
 * Implements queuing system with priority-based loading
 */

class VideoLoadManager {
  constructor() {
    this.activeDownload = null;
    this.downloadQueue = [];
    this.loadedVideos = new Set();
    this.networkInfo = null;
    this.isIdle = false;
    
    // Initialize network monitoring
    this.initNetworkMonitoring();
    this.initIdleDetection();
  }

  initNetworkMonitoring() {
    if ('connection' in navigator) {
      this.networkInfo = navigator.connection;
      this.networkInfo.addEventListener('change', () => {
        this.handleNetworkChange();
      });
    }
  }

  initIdleDetection() {
    // Use requestIdleCallback to detect main thread idle state
    const checkIdle = () => {
      if (window.requestIdleCallback) {
        window.requestIdleCallback(() => {
          this.isIdle = true;
          this.processQueue();
        }, { timeout: 1000 });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(() => {
          this.isIdle = true;
          this.processQueue();
        }, 100);
      }
    };
    
    checkIdle();
    setInterval(checkIdle, 2000); // Check every 2 seconds
  }

  /**
   * Request video download with priority
   * @param {string} videoId - Unique identifier for the video
   * @param {string} src - Video source URL
   * @param {number} priority - Priority level (0 = highest, 2 = lowest)
   * @param {HTMLVideoElement} videoElement - Video element to attach src
   * @param {Object} options - Additional options
   */
  requestVideoLoad(videoId, src, priority = 1, videoElement, options = {}) {
    // Skip if already loaded
    if (this.loadedVideos.has(videoId)) {
      this.attachVideoSrc(videoElement, src);
      return Promise.resolve();
    }

    // Skip if already in queue
    if (this.downloadQueue.find(item => item.videoId === videoId)) {
      return Promise.resolve();
    }

    const downloadRequest = {
      videoId,
      src,
      priority,
      videoElement,
      options,
      timestamp: Date.now(),
      resolve: null,
      reject: null
    };

    return new Promise((resolve, reject) => {
      downloadRequest.resolve = resolve;
      downloadRequest.reject = reject;
      
      // Insert into queue based on priority
      this.insertByPriority(downloadRequest);
      this.processQueue();
    });
  }

  insertByPriority(request) {
    const insertIndex = this.downloadQueue.findIndex(
      item => item.priority > request.priority
    );
    
    if (insertIndex === -1) {
      this.downloadQueue.push(request);
    } else {
      this.downloadQueue.splice(insertIndex, 0, request);
    }
  }

  async processQueue() {
    // Don't process if there's an active download or queue is empty
    if (this.activeDownload || this.downloadQueue.length === 0) {
      return;
    }

    // Check network conditions
    if (!this.shouldAllowDownload()) {
      return;
    }

    const request = this.downloadQueue.shift();
    this.activeDownload = request;

    try {
      await this.downloadVideo(request);
      this.loadedVideos.add(request.videoId);
      request.resolve();
    } catch (error) {
      console.error(`Failed to load video ${request.videoId}:`, error);
      request.reject(error);
    } finally {
      this.activeDownload = null;
      this.isIdle = false;
      
      // Process next item in queue after a brief delay
      setTimeout(() => this.processQueue(), 100);
    }
  }

  shouldAllowDownload() {
    // Check network conditions
    if (this.networkInfo) {
      const effectiveType = this.networkInfo.effectiveType;
      
      // Be more conservative on slow networks
      if (effectiveType === 'slow-2g' || effectiveType === '2g') {
        return false;
      }
      
      // Check if we're on a metered connection
      if (this.networkInfo.saveData) {
        return false;
      }
    }

    // Only allow downloads when main thread is idle
    return this.isIdle;
  }

  async downloadVideo(request) {
    const { videoElement, src, options } = request;
    
    // Attach src to video element
    this.attachVideoSrc(videoElement, src);
    
    // Wait for video to have enough data to play
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Video load timeout'));
      }, options.timeout || 10000);

      const handleCanPlay = () => {
        clearTimeout(timeout);
        videoElement.removeEventListener('canplay', handleCanPlay);
        videoElement.removeEventListener('error', handleError);
        resolve();
      };

      const handleError = (error) => {
        clearTimeout(timeout);
        videoElement.removeEventListener('canplay', handleCanPlay);
        videoElement.removeEventListener('error', handleError);
        reject(error);
      };

      videoElement.addEventListener('canplay', handleCanPlay, { once: true });
      videoElement.addEventListener('error', handleError, { once: true });
    });
  }

  attachVideoSrc(videoElement, src) {
    if (videoElement && src) {
      videoElement.src = src;
      videoElement.load();
    }
  }

  handleNetworkChange() {
    // Pause queue processing on network changes
    this.isIdle = false;
    
    // Resume after network stabilizes
    setTimeout(() => {
      this.isIdle = true;
      this.processQueue();
    }, 1000);
  }

  // Clear queue and reset state
  reset() {
    this.downloadQueue.forEach(request => {
      request.reject(new Error('Video load manager reset'));
    });
    
    this.downloadQueue = [];
    this.activeDownload = null;
    this.loadedVideos.clear();
  }

  // Get current queue status for debugging
  getStatus() {
    return {
      activeDownload: this.activeDownload?.videoId || null,
      queueLength: this.downloadQueue.length,
      loadedCount: this.loadedVideos.size,
      networkType: this.networkInfo?.effectiveType || 'unknown',
      isIdle: this.isIdle
    };
  }
}

// Create singleton instance
const videoLoadManager = new VideoLoadManager();

export default videoLoadManager;
