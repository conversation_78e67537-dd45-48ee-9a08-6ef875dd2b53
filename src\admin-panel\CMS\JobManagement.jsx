import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  Paper,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Pagination,
  FormControlLabel,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import axios from "axios";
import { useUser } from "../../contexts/UserContext";

const JobManagement = () => {
  const { user } = useUser();
  // State Management
  const [state, setState] = useState({
    // Data
    jobs: [],
    currentJob: null,
    loading: false,
    error: null,

    // Filters
    searchTerm: "",
    selectedDepartment: "",
    selectedJobType: "",
    selectedWorkMode: "",
    selectedExperienceLevel: "",
    showActiveOnly: false,

    // Pagination
    currentPage: 1,
    pageSize: 10,
    totalJobs: 0,

    // Modals
    showCreateModal: false,
    showEditModal: false,
    showViewModal: false,
    showDeleteModal: false,

    // Form
    formData: {
      title: "",
      description: "",
      department: "",
      jobType: "",
      workMode: "",
      experienceLevel: "",
      location: "",
      country: "",
      address: "",
      applicationDeadline: "",
      recruiterEmail: "",
      language: {
        language: "",
        level: "",
      },
      requirements: [],
      responsibilities: [],
      benefits: [],
    },
    formErrors: {},
    isSubmitting: false,
  });

  // Form field options
  const jobTypeOptions = [
    { value: "full-time", label: "Full Time" },
    { value: "part-time", label: "Part Time" },
    { value: "contract", label: "Contract" },
    { value: "internship", label: "Internship" },
    { value: "freelance", label: "Freelance" },
  ];

  const workModeOptions = [
    { value: "remote", label: "Remote" },
    { value: "on-site", label: "On-site" },
    { value: "hybrid", label: "Hybrid" },
  ];

  const experienceLevelOptions = [
    { value: "entry-level", label: "Entry Level" },
    { value: "mid-level", label: "Mid Level" },
    { value: "senior-level", label: "Senior Level" },
    { value: "executive", label: "Executive" },
  ];

  const languageLevelOptions = [
    { value: "basic", label: "Basic" },
    { value: "intermediate", label: "Intermediate" },
    { value: "advanced", label: "Advanced" },
    { value: "native", label: "Native" },
  ];

  // API Functions
  const fetchJobs = async (filters = {}) => {
    try {
      setState((prev) => ({ ...prev, loading: true }));
      const response = await axios.get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/jobs",
        {
          params: filters,
        }
      );
      setState((prev) => ({
        ...prev,
        jobs: response.data,
        totalJobs: response.data.length,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching jobs:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to fetch jobs",
        loading: false,
      }));
    }
  };

  const createJob = async (jobData) => {
    try {
      setState((prev) => ({ ...prev, isSubmitting: true }));
      const response = await axios.post(
        "https://youngproductions-768ada043db3.herokuapp.com/api/jobs",
        jobData
      );
      setState((prev) => ({
        ...prev,
        jobs: [...prev.jobs, response.data],
        isSubmitting: false,
        showCreateModal: false,
      }));
      resetForm();
    } catch (error) {
      console.error("Error creating job:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to create job",
        isSubmitting: false,
      }));
    }
  };

  const updateJob = async (id, jobData) => {
    try {
      setState((prev) => ({ ...prev, isSubmitting: true }));
      const response = await axios.put(
        `https://youngproductions-768ada043db3.herokuapp.com/api/jobs/${id}`,
        jobData
      );
      setState((prev) => ({
        ...prev,
        jobs: prev.jobs.map((job) => (job._id === id ? response.data : job)),
        isSubmitting: false,
        showEditModal: false,
      }));
      resetForm();
    } catch (error) {
      console.error("Error updating job:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to update job",
        isSubmitting: false,
      }));
    }
  };

  const deleteJob = async (id) => {
    try {
      await axios.delete(
        `https://youngproductions-768ada043db3.herokuapp.com/api/jobs/${id}`
      );
      setState((prev) => ({
        ...prev,
        jobs: prev.jobs.filter((job) => job._id !== id),
        showDeleteModal: false,
        currentJob: null,
      }));
    } catch (error) {
      console.error("Error deleting job:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to delete job",
      }));
    }
  };

  // UI Functions
  const handleSearch = (searchTerm) => {
    setState((prev) => ({ ...prev, searchTerm }));
  };

  const handleFilterChange = (filterType, value) => {
    setState((prev) => ({ ...prev, [filterType]: value }));
  };

  const clearFilters = () => {
    setState((prev) => ({
      ...prev,
      searchTerm: "",
      selectedDepartment: "",
      selectedJobType: "",
      selectedWorkMode: "",
      selectedExperienceLevel: "",
      showActiveOnly: false,
    }));
    fetchJobs();
  };

  // Modal Functions
  const openCreateModal = () => {
    resetForm();
    setState((prev) => ({ ...prev, showCreateModal: true }));
  };

  const openEditModal = (job) => {
    setState((prev) => ({
      ...prev,
      currentJob: job,
      formData: { ...job },
      showEditModal: true,
    }));
  };

  const openViewModal = (job) => {
    setState((prev) => ({
      ...prev,
      currentJob: job,
      showViewModal: true,
    }));
  };

  const openDeleteModal = (job) => {
    setState((prev) => ({
      ...prev,
      currentJob: job,
      showDeleteModal: true,
    }));
  };

  const closeAllModals = () => {
    setState((prev) => ({
      ...prev,
      showCreateModal: false,
      showEditModal: false,
      showViewModal: false,
      showDeleteModal: false,
      currentJob: null,
    }));
    resetForm();
  };

  // Form Functions
  const handleFormChange = (field, value) => {
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      setState((prev) => ({
        ...prev,
        formData: {
          ...prev.formData,
          [parent]: {
            ...prev.formData[parent],
            [child]: value,
          },
        },
      }));
    } else {
      setState((prev) => ({
        ...prev,
        formData: {
          ...prev.formData,
          [field]: value,
        },
      }));
    }
  };

  const resetForm = () => {
    setState((prev) => ({
      ...prev,
      formData: {
        title: "",
        description: "",
        department: "",
        jobType: "",
        workMode: "",
        experienceLevel: "",
        location: "",
        country: "",
        address: "",
        applicationDeadline: "",
        recruiterEmail: "",
        language: {
          language: "",
          level: "",
        },
        requirements: [],
        responsibilities: [],
        benefits: [],
      },
      formErrors: {},
    }));
  };

  const validateForm = () => {
    const errors = {};
    const { formData } = state;

    if (!formData.title) errors.title = "Title is required";
    if (!formData.description) errors.description = "Description is required";
    if (!formData.department) errors.department = "Department is required";
    if (!formData.jobType) errors.jobType = "Job type is required";
    if (!formData.workMode) errors.workMode = "Work mode is required";
    if (!formData.experienceLevel)
      errors.experienceLevel = "Experience level is required";
    if (!formData.location) errors.location = "Location is required";
    if (!formData.country) errors.country = "Country is required";
    if (!formData.applicationDeadline)
      errors.applicationDeadline = "Deadline is required";
    if (!formData.recruiterEmail)
      errors.recruiterEmail = "Recruiter email is required";
    if (!formData.language.language) errors.language = "Language is required";
    if (!formData.language.level)
      errors.languageLevel = "Language level is required";

    setState((prev) => ({ ...prev, formErrors: errors }));
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    if (state.currentJob) {
      await updateJob(state.currentJob._id, state.formData);
    } else {
      await createJob(state.formData);
    }
  };

  // Array Functions
  const addArrayItem = (field) => {
    setState((prev) => ({
      ...prev,
      formData: {
        ...prev.formData,
        [field]: [...prev.formData[field], ""],
      },
    }));
  };

  const removeArrayItem = (field, index) => {
    setState((prev) => ({
      ...prev,
      formData: {
        ...prev.formData,
        [field]: prev.formData[field].filter((_, i) => i !== index),
      },
    }));
  };

  const updateArrayItem = (field, index, value) => {
    setState((prev) => ({
      ...prev,
      formData: {
        ...prev.formData,
        [field]: prev.formData[field].map((item, i) =>
          i === index ? value : item
        ),
      },
    }));
  };

  // Effects
  useEffect(() => {
    fetchJobs();
  }, []);

  // Get filtered jobs
  const getFilteredJobs = () => {
    let filtered = [...state.jobs];

    if (state.searchTerm) {
      filtered = filtered.filter(
        (job) =>
          job.title?.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
          job.description
            ?.toLowerCase()
            .includes(state.searchTerm.toLowerCase()) ||
          job.department?.toLowerCase().includes(state.searchTerm.toLowerCase())
      );
    }

    if (state.selectedDepartment) {
      filtered = filtered.filter(
        (job) => job.department === state.selectedDepartment
      );
    }

    if (state.selectedJobType) {
      filtered = filtered.filter(
        (job) => job.jobType === state.selectedJobType
      );
    }

    if (state.selectedWorkMode) {
      filtered = filtered.filter(
        (job) => job.workMode === state.selectedWorkMode
      );
    }

    if (state.selectedExperienceLevel) {
      filtered = filtered.filter(
        (job) => job.experienceLevel === state.selectedExperienceLevel
      );
    }

    if (state.showActiveOnly) {
      const now = new Date();
      filtered = filtered.filter(
        (job) => new Date(job.applicationDeadline) > now
      );
    }

    return filtered;
  };

  const filteredJobs = getFilteredJobs();
  const paginatedJobs = filteredJobs.slice(
    (state.currentPage - 1) * state.pageSize,
    state.currentPage * state.pageSize
  );

  // Get unique departments for filter
  const departments = [
    ...new Set(state.jobs.map((job) => job.department).filter(Boolean)),
  ];

  // Statistics
  const totalJobs = state.jobs.length;
  const activeJobs = state.jobs.filter(
    (job) => new Date(job.applicationDeadline) > new Date()
  ).length;
  const expiredJobs = totalJobs - activeJobs;

  return (
    <div
      style={{
        backgroundColor: "black",
        padding: "20px 5vw",
        minHeight: "100vh",
        color: "white",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontFamily: "Formula Bold",
            color: "white",
          }}
        >
          Jobs Management
        </Typography>
        {(user?.tier === 2 || user?.tier === 3) &&
          (user?.role === "account_manager" ||
            user?.role === "general_manager" ||
            user?.role === "graphic_designer") && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={openCreateModal}
              sx={{
                background: "#db4a41",
                "&:hover": {
                  background: "#c43a31",
                },
              }}
            >
              Create Job
            </Button>
          )}
      </Box>

      {/* Search and Filters */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="Search jobs..."
              value={state.searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: "white", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Department"
              value={state.selectedDepartment}
              onChange={(e) =>
                handleFilterChange("selectedDepartment", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {departments.map((dept) => (
                <MenuItem key={dept} value={dept}>
                  {dept}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Job Type"
              value={state.selectedJobType}
              onChange={(e) =>
                handleFilterChange("selectedJobType", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {jobTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Work Mode"
              value={state.selectedWorkMode}
              onChange={(e) =>
                handleFilterChange("selectedWorkMode", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {workModeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Experience"
              value={state.selectedExperienceLevel}
              onChange={(e) =>
                handleFilterChange("selectedExperienceLevel", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {experienceLevelOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={1}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              startIcon={<ClearIcon />}
              sx={{
                color: "white",
                borderColor: "white",
                "&:hover": {
                  borderColor: "#db4a41",
                  color: "#db4a41",
                },
              }}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
        <Box sx={{ mt: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={state.showActiveOnly}
                onChange={(e) =>
                  handleFilterChange("showActiveOnly", e.target.checked)
                }
                sx={{
                  "& .MuiSwitch-switchBase.Mui-checked": {
                    color: "#db4a41",
                  },
                  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#db4a41",
                  },
                }}
              />
            }
            label="Show Active Jobs Only"
            sx={{ color: "white" }}
          />
        </Box>
      </Paper>

      {/* Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
              >
                Total Jobs
              </Typography>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Anton" }}
              >
                {totalJobs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
              >
                Active Jobs
              </Typography>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Anton" }}
              >
                {activeJobs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                sx={{ color: "#f44336", fontFamily: "Formula Bold" }}
              >
                Expired Jobs
              </Typography>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Anton" }}
              >
                {expiredJobs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Jobs Table */}
      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          mb: 3,
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Title
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Department
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Type
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Mode
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Experience
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Status
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Applications
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.loading ? (
              <TableRow>
                <TableCell colSpan={7} sx={{ textAlign: "center", py: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </TableCell>
              </TableRow>
            ) : paginatedJobs.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={7}
                  sx={{ textAlign: "center", py: 4, color: "white" }}
                >
                  No jobs found
                </TableCell>
              </TableRow>
            ) : (
              paginatedJobs.map((job) => (
                <TableRow
                  key={job._id}
                  sx={{
                    "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.05)" },
                  }}
                >
                  <TableCell sx={{ color: "white" }}>{job.title}</TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {job.department}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    <Chip
                      label={
                        jobTypeOptions.find((opt) => opt.value === job.jobType)
                          ?.label || job.jobType
                      }
                      size="small"
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                        border: "1px solid #db4a41",
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>{job.workMode}</TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {job.experienceLevel}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={
                        new Date(job.applicationDeadline) > new Date()
                          ? "Active"
                          : "Expired"
                      }
                      size="small"
                      sx={{
                        backgroundColor:
                          new Date(job.applicationDeadline) > new Date()
                            ? "rgba(76, 175, 80, 0.2)"
                            : "rgba(244, 67, 54, 0.2)",
                        color:
                          new Date(job.applicationDeadline) > new Date()
                            ? "#4caf50"
                            : "#f44336",
                        border: `1px solid ${
                          new Date(job.applicationDeadline) > new Date()
                            ? "#4caf50"
                            : "#f44336"
                        }`,
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {job.numberOfApplications}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Tooltip title="View">
                        <IconButton
                          onClick={() => openViewModal(job)}
                          sx={{
                            color: "white",
                            "&:hover": { color: "#db4a41" },
                          }}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={() => openEditModal(job)}
                          sx={{
                            color: "white",
                            "&:hover": { color: "#db4a41" },
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => openDeleteModal(job)}
                          sx={{
                            color: "white",
                            "&:hover": { color: "#f44336" },
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: "flex", justifyContent: "center", mb: 3 }}>
        <Pagination
          count={Math.ceil(filteredJobs.length / state.pageSize)}
          page={state.currentPage}
          onChange={(e, page) =>
            setState((prev) => ({ ...prev, currentPage: page }))
          }
          sx={{
            "& .MuiPaginationItem-root": {
              color: "white",
              "&:hover": {
                backgroundColor: "rgba(219, 74, 65, 0.2)",
              },
            },
            "& .Mui-selected": {
              backgroundColor: "#db4a41 !important",
              color: "white",
            },
          }}
        />
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={state.showDeleteModal}
        onClose={closeAllModals}
        PaperProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ color: "white" }}>
            Are you sure you want to delete the job "{state.currentJob?.title}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeAllModals} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={() => deleteJob(state.currentJob?._id)}
            sx={{
              backgroundColor: "#f44336",
              color: "white",
              "&:hover": { backgroundColor: "#d32f2f" },
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Job Modal */}
      <Dialog
        open={state.showCreateModal}
        onClose={closeAllModals}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          Create New Job
        </DialogTitle>
        <DialogContent sx={{ overflow: "auto" }}>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2 }}>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Job Title *"
                value={state.formData.title}
                onChange={(e) => handleFormChange("title", e.target.value)}
                error={!!state.formErrors.title}
                helperText={state.formErrors.title}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Department *"
                value={state.formData.department}
                onChange={(e) => handleFormChange("department", e.target.value)}
                error={!!state.formErrors.department}
                helperText={state.formErrors.department}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Job Type *"
                value={state.formData.jobType}
                onChange={(e) => handleFormChange("jobType", e.target.value)}
                error={!!state.formErrors.jobType}
                helperText={state.formErrors.jobType}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {jobTypeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Work Mode *"
                value={state.formData.workMode}
                onChange={(e) => handleFormChange("workMode", e.target.value)}
                error={!!state.formErrors.workMode}
                helperText={state.formErrors.workMode}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {workModeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Experience Level *"
                value={state.formData.experienceLevel}
                onChange={(e) =>
                  handleFormChange("experienceLevel", e.target.value)
                }
                error={!!state.formErrors.experienceLevel}
                helperText={state.formErrors.experienceLevel}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {experienceLevelOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Location Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Location Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Location *"
                value={state.formData.location}
                onChange={(e) => handleFormChange("location", e.target.value)}
                error={!!state.formErrors.location}
                helperText={state.formErrors.location}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Country *"
                value={state.formData.country}
                onChange={(e) => handleFormChange("country", e.target.value)}
                error={!!state.formErrors.country}
                helperText={state.formErrors.country}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address (Optional)"
                value={state.formData.address}
                onChange={(e) => handleFormChange("address", e.target.value)}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                }}
              />
            </Grid>

            {/* Language Requirements */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Language Requirements
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Required Language *"
                value={state.formData.language.language}
                onChange={(e) =>
                  handleFormChange("language.language", e.target.value)
                }
                error={!!state.formErrors.language}
                helperText={state.formErrors.language}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                select
                fullWidth
                label="Language Level *"
                value={state.formData.language.level}
                onChange={(e) =>
                  handleFormChange("language.level", e.target.value)
                }
                error={!!state.formErrors.languageLevel}
                helperText={state.formErrors.languageLevel}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {languageLevelOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Application Details */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Application Details
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label="Application Deadline *"
                value={state.formData.applicationDeadline}
                onChange={(e) =>
                  handleFormChange("applicationDeadline", e.target.value)
                }
                error={!!state.formErrors.applicationDeadline}
                helperText={state.formErrors.applicationDeadline}
                InputLabelProps={{ shrink: true }}
                inputProps={{ min: new Date().toISOString().split("T")[0] }}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="email"
                label="Recruiter Email *"
                value={state.formData.recruiterEmail}
                onChange={(e) =>
                  handleFormChange("recruiterEmail", e.target.value)
                }
                error={!!state.formErrors.recruiterEmail}
                helperText={state.formErrors.recruiterEmail}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            {/* Job Description */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Job Description
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={6}
                label="Job Description *"
                value={state.formData.description}
                onChange={(e) =>
                  handleFormChange("description", e.target.value)
                }
                error={!!state.formErrors.description}
                helperText={state.formErrors.description}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            {/* Requirements */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Requirements
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("requirements")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Requirement
                </Button>
              </Box>
              {state.formData.requirements.map((requirement, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={requirement}
                    onChange={(e) =>
                      updateArrayItem("requirements", index, e.target.value)
                    }
                    placeholder="Enter requirement..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("requirements", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>

            {/* Responsibilities */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Responsibilities
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("responsibilities")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Responsibility
                </Button>
              </Box>
              {state.formData.responsibilities.map((responsibility, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={responsibility}
                    onChange={(e) =>
                      updateArrayItem("responsibilities", index, e.target.value)
                    }
                    placeholder="Enter responsibility..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("responsibilities", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>

            {/* Benefits */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Benefits
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("benefits")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Benefit
                </Button>
              </Box>
              {state.formData.benefits.map((benefit, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={benefit}
                    onChange={(e) =>
                      updateArrayItem("benefits", index, e.target.value)
                    }
                    placeholder="Enter benefit..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("benefits", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeAllModals} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={state.isSubmitting}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": { backgroundColor: "#c43a31" },
              "&:disabled": { backgroundColor: "rgba(219, 74, 65, 0.5)" },
            }}
          >
            {state.isSubmitting ? (
              <CircularProgress size={20} sx={{ color: "white" }} />
            ) : (
              "Create Job"
            )}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Job Modal */}
      <Dialog
        open={state.showViewModal}
        onClose={closeAllModals}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          Job Details: {state.currentJob?.title}
        </DialogTitle>
        <DialogContent sx={{ overflow: "auto" }}>
          {state.currentJob && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ color: "#db4a41", mb: 2 }}>
                  Basic Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Job Title
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.title}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Department
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.department}
                </Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Job Type
                </Typography>
                <Chip
                  label={
                    jobTypeOptions.find(
                      (opt) => opt.value === state.currentJob.jobType
                    )?.label || state.currentJob.jobType
                  }
                  sx={{
                    backgroundColor: "rgba(219, 74, 65, 0.2)",
                    color: "#db4a41",
                    border: "1px solid #db4a41",
                  }}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Work Mode
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.workMode}
                </Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Experience Level
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.experienceLevel}
                </Typography>
              </Grid>

              {/* Location Information */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                >
                  Location Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Location
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.location}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Country
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.country}
                </Typography>
              </Grid>

              {state.currentJob.address && (
                <Grid item xs={12}>
                  <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                    Address
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    {state.currentJob.address}
                  </Typography>
                </Grid>
              )}

              {/* Application Details */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                >
                  Application Details
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Application Deadline
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {new Date(
                    state.currentJob.applicationDeadline
                  ).toLocaleDateString()}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Recruiter Email
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentJob.recruiterEmail}
                </Typography>
              </Grid>

              {/* Job Description */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                >
                  Job Description
                </Typography>
                <Typography sx={{ color: "white", whiteSpace: "pre-wrap" }}>
                  {state.currentJob.description}
                </Typography>
              </Grid>

              {/* Requirements */}
              {state.currentJob.requirements &&
                state.currentJob.requirements.length > 0 && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                    >
                      Requirements
                    </Typography>
                    {state.currentJob.requirements.map((requirement, index) => (
                      <Typography key={index} sx={{ color: "white", mb: 1 }}>
                        {index + 1}. {requirement}
                      </Typography>
                    ))}
                  </Grid>
                )}

              {/* Responsibilities */}
              {state.currentJob.responsibilities &&
                state.currentJob.responsibilities.length > 0 && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                    >
                      Responsibilities
                    </Typography>
                    {state.currentJob.responsibilities.map(
                      (responsibility, index) => (
                        <Typography key={index} sx={{ color: "white", mb: 1 }}>
                          {index + 1}. {responsibility}
                        </Typography>
                      )
                    )}
                  </Grid>
                )}

              {/* Benefits */}
              {state.currentJob.benefits &&
                state.currentJob.benefits.length > 0 && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                    >
                      Benefits
                    </Typography>
                    {state.currentJob.benefits.map((benefit, index) => (
                      <Typography key={index} sx={{ color: "white", mb: 1 }}>
                        {index + 1}. {benefit}
                      </Typography>
                    ))}
                  </Grid>
                )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeAllModals} sx={{ color: "white" }}>
            Close
          </Button>
          <Button
            onClick={() => {
              closeAllModals();
              openEditModal(state.currentJob);
            }}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": { backgroundColor: "#c43a31" },
            }}
          >
            Edit Job
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Job Modal */}
      <Dialog
        open={state.showEditModal}
        onClose={closeAllModals}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          Edit Job: {state.currentJob?.title}
        </DialogTitle>
        <DialogContent sx={{ overflow: "auto" }}>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2 }}>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Job Title *"
                value={state.formData.title}
                onChange={(e) => handleFormChange("title", e.target.value)}
                error={!!state.formErrors.title}
                helperText={state.formErrors.title}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Department *"
                value={state.formData.department}
                onChange={(e) => handleFormChange("department", e.target.value)}
                error={!!state.formErrors.department}
                helperText={state.formErrors.department}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Job Type *"
                value={state.formData.jobType}
                onChange={(e) => handleFormChange("jobType", e.target.value)}
                error={!!state.formErrors.jobType}
                helperText={state.formErrors.jobType}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {jobTypeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Work Mode *"
                value={state.formData.workMode}
                onChange={(e) => handleFormChange("workMode", e.target.value)}
                error={!!state.formErrors.workMode}
                helperText={state.formErrors.workMode}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {workModeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                select
                fullWidth
                label="Experience Level *"
                value={state.formData.experienceLevel}
                onChange={(e) =>
                  handleFormChange("experienceLevel", e.target.value)
                }
                error={!!state.formErrors.experienceLevel}
                helperText={state.formErrors.experienceLevel}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {experienceLevelOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Location Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Location Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Location *"
                value={state.formData.location}
                onChange={(e) => handleFormChange("location", e.target.value)}
                error={!!state.formErrors.location}
                helperText={state.formErrors.location}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Country *"
                value={state.formData.country}
                onChange={(e) => handleFormChange("country", e.target.value)}
                error={!!state.formErrors.country}
                helperText={state.formErrors.country}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address (Optional)"
                value={state.formData.address}
                onChange={(e) => handleFormChange("address", e.target.value)}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                }}
              />
            </Grid>

            {/* Language Requirements */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Language Requirements
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Required Language *"
                value={state.formData.language.language}
                onChange={(e) =>
                  handleFormChange("language.language", e.target.value)
                }
                error={!!state.formErrors.language}
                helperText={state.formErrors.language}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                select
                fullWidth
                label="Language Level *"
                value={state.formData.language.level}
                onChange={(e) =>
                  handleFormChange("language.level", e.target.value)
                }
                error={!!state.formErrors.languageLevel}
                helperText={state.formErrors.languageLevel}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              >
                {languageLevelOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Application Details */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Application Details
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label="Application Deadline *"
                value={state.formData.applicationDeadline}
                onChange={(e) =>
                  handleFormChange("applicationDeadline", e.target.value)
                }
                error={!!state.formErrors.applicationDeadline}
                helperText={state.formErrors.applicationDeadline}
                InputLabelProps={{ shrink: true }}
                inputProps={{ min: new Date().toISOString().split("T")[0] }}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="email"
                label="Recruiter Email *"
                value={state.formData.recruiterEmail}
                onChange={(e) =>
                  handleFormChange("recruiterEmail", e.target.value)
                }
                error={!!state.formErrors.recruiterEmail}
                helperText={state.formErrors.recruiterEmail}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            {/* Job Description */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Job Description
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={6}
                label="Job Description *"
                value={state.formData.description}
                onChange={(e) =>
                  handleFormChange("description", e.target.value)
                }
                error={!!state.formErrors.description}
                helperText={state.formErrors.description}
                sx={{
                  "& .MuiInputLabel-root": { color: "white" },
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                    "&:hover fieldset": { borderColor: "white" },
                  },
                  "& .MuiFormHelperText-root": { color: "#f44336" },
                }}
              />
            </Grid>

            {/* Requirements */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Requirements
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("requirements")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Requirement
                </Button>
              </Box>
              {state.formData.requirements.map((requirement, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={requirement}
                    onChange={(e) =>
                      updateArrayItem("requirements", index, e.target.value)
                    }
                    placeholder="Enter requirement..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("requirements", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>

            {/* Responsibilities */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Responsibilities
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("responsibilities")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Responsibility
                </Button>
              </Box>
              {state.formData.responsibilities.map((responsibility, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={responsibility}
                    onChange={(e) =>
                      updateArrayItem("responsibilities", index, e.target.value)
                    }
                    placeholder="Enter responsibility..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("responsibilities", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>

            {/* Benefits */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ color: "#db4a41", mb: 2, mt: 2 }}>
                Benefits
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => addArrayItem("benefits")}
                  sx={{
                    color: "#db4a41",
                    borderColor: "#db4a41",
                    "&:hover": {
                      borderColor: "#c43a31",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                    },
                  }}
                >
                  + Add Benefit
                </Button>
              </Box>
              {state.formData.benefits.map((benefit, index) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 2 }}
                >
                  <Typography sx={{ color: "white", mr: 2, minWidth: "30px" }}>
                    {index + 1}.
                  </Typography>
                  <TextField
                    fullWidth
                    value={benefit}
                    onChange={(e) =>
                      updateArrayItem("benefits", index, e.target.value)
                    }
                    placeholder="Enter benefit..."
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.23)",
                        },
                        "&:hover fieldset": { borderColor: "white" },
                      },
                    }}
                  />
                  <IconButton
                    onClick={() => removeArrayItem("benefits", index)}
                    sx={{ color: "#f44336", ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeAllModals} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={state.isSubmitting}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": { backgroundColor: "#c43a31" },
              "&:disabled": { backgroundColor: "rgba(219, 74, 65, 0.5)" },
            }}
          >
            {state.isSubmitting ? (
              <CircularProgress size={20} sx={{ color: "white" }} />
            ) : (
              "Update Job"
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default JobManagement;
