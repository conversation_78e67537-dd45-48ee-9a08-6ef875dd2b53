import React, { useRef, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useTheme, useMediaQuery, Box } from "@mui/material";

const LazyMotionLogo = ({
  src,
  alt,
  index = 0,
  size = { xs: 50, sm: 70, md: 150 },
}) => {
  const ref = useRef();
  const [visible, setVisible] = useState(false);

  // MUI theme breakpoints for responsive sizes
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.only("xs"));
  const isSm = useMediaQuery(theme.breakpoints.only("sm"));
  // const isMd = useMediaQuery(theme.breakpoints.up("md"));

  let logoSize = size.md;
  if (isXs) logoSize = size.xs;
  else if (isSm) logoSize = size.sm;

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) setVisible(true);
      },
      { rootMargin: "200px" }
    );
    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, []);

  return (
    <motion.div
      ref={ref}
      style={{
        width: logoSize,
        height: logoSize,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        overflow: "hidden",
        borderRadius: "6px",
        cursor: "pointer",
      }}
    >
      <Box
        component="img"
        src={
          visible
            ? src
            : "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///ywAAAAAAQABAAACAUwAOw==" // tiny placeholder
        }
        alt={alt}
        loading="lazy"
        decoding="async"
        sx={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
          display: "block",
          position: "relative",
        }}
      />
    </motion.div>
  );
};

export default LazyMotionLogo;
