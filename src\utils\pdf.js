// Utility to export a shoot as a print-ready Call Sheet PDF using the browser print dialog
// This avoids extra dependencies and sticks closely to the provided design

// const formatTime = (dateLike) => {
//   if (!dateLike) return "";
//   const d = new Date(dateLike);
//   return d.toLocaleTimeString([], { hour: "numeric", minute: "2-digit" });
// };

const formatDate = (dateLike) => {
  if (!dateLike) return "";
  const d = new Date(dateLike);
  return d.toLocaleDateString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const getCrewName = (member, employees = []) => {
  if (!member) return "";
  if (member.user && typeof member.user === "object" && member.user.name)
    return member.user.name;
  if (member.user && typeof member.user === "string") {
    const found = employees.find(
      (e) => e && (e._id === member.user || e.id === member.user)
    );
    if (found && found.name) return found.name;
    return member.name || member.user; // fallback to name or id if missing
  }
  if (member.name) return member.name;
  return "Unknown";
};

const findCrewByRole = (crew = [], roleQuery, employees = []) => {
  const item = crew.find((m) =>
    (m.role || "").toLowerCase().includes(roleQuery.toLowerCase())
  );
  return item ? getCrewName(item, employees) : "";
};

export function exportShootAsPDF(shoot, employees = []) {
  if (!shoot) return;
  const title = shoot.title || "Call Sheet";
  const dateStr = formatDate(shoot.date);
  const generalCrewCall = shoot.startTime ? shoot.startTime : "";

  // Get key roles from the shoot object directly
  const creativeDirector = shoot.creativeDirector
    ? getCrewName({ user: shoot.creativeDirector }, employees)
    : findCrewByRole(shoot.crew, "Creative Director", employees);
  const producer = shoot.producer
    ? getCrewName({ user: shoot.producer }, employees)
    : findCrewByRole(shoot.crew, "Producer", employees);
  const director = shoot.Director
    ? getCrewName({ user: shoot.Director }, employees)
    : findCrewByRole(shoot.crew, "Director", employees);
  const dop = findCrewByRole(shoot.crew, "DOP", employees);

  const castRows = (shoot.cast || [])
    .map((c) => {
      const timeRange =
        c.startTime && c.endTime
          ? `${c.startTime} - ${c.endTime}`
          : c.startTime || c.endTime || c.time || "";
      return `<tr><td>${c.name || ""}</td><td>${timeRange}</td></tr>`;
    })
    .join("");

  const crewRows = (shoot.crew || [])
    .map(
      (m) =>
        `<tr><td>${getCrewName(m, employees)}</td><td>${m.role || ""}</td></tr>`
    )
    .join("");

  const timingRows = (shoot.timing || [])
    .map((t) => {
      // Format notes with bullet points if there are multiple lines
      let formattedNotes = "";
      if (t.notes && t.notes.trim()) {
        const noteLines = t.notes.split("\n").filter((line) => line.trim());
        if (noteLines.length > 1) {
          formattedNotes = `<ul>${noteLines
            .map((line) => `<li>${line.trim()}</li>`)
            .join("")}</ul>`;
        } else {
          formattedNotes = noteLines[0] || "";
        }
      }

      return `<tr><td>${t.title || ""}</td><td>${
        t.time || ""
      }</td><td>${formattedNotes}</td></tr>`;
    })
    .join("");

  const timeRange =
    shoot.startTime && shoot.endTime
      ? `${shoot.startTime} - ${shoot.endTime}`
      : "";

  const locationAddress = shoot.location || "";
  const locationLink =
    shoot.addressLink || shoot.locationLink || shoot.locationUrl || "";

  const notesList = (shoot.notes || "")
    .split(/\n|•/)
    .map((s) => s.trim())
    .filter(Boolean)
    .map((n) => `<li>${n}</li>`) // bullet list
    .join("");

  const win = window.open("", "_blank");
  if (!win) return;

  const styles = `
    @page { size: A4; margin: 18mm; }
    * { box-sizing: border-box; }
    @font-face {
        font-family: 'Formula';
        src: url('/fonts/FormulaFont.woff2') format('truetype');
        font-weight: 800; font-style: normal; font-display: swap;
    }
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; color: #111; }
    .header { display:flex; justify-content: space-between; align-items:flex-end; }
    .brand { text-align:right; }
    .brand img { height: 28px; }
    h1 { font-size: 42px; margin: 18px 0; letter-spacing: 1px; font-family: Formula, Arial, sans-serif; font-weight: 800; }
    .table { width:100%; border-collapse: collapse; margin: 10px 0; }
		.table th, .table td { border: 1px solid #d9d9d9; padding: 10px; vertical-align: top; }
		.table th { background:#f6f6f6; text-align:left; width: 35%; }
		.small { font-size: 12px; color:#333; }
    .section-title { margin: 24px 0 8px; font-size: 14px; font-weight: 800; letter-spacing: .5px; font-family: Formula, Arial, sans-serif; }
		.grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 14px; }
		.call-times th:first-child, .crew-table th:first-child { width: 65%; }
		ul { margin: 0; padding-left: 18px; }
		.footer-note { margin-top: 16px; font-size: 11px; color:#666; }
		.page-break { page-break-after: always; }
		.timeline th:first-child { width: 25%; }
    .chip { display:inline-block; padding:4px 8px; background:#f0f0f0; border:1px solid #e5e5e5; border-radius: 14px; margin: 0 6px 6px 0; font-size: 12px; }
    .script-card { border:1px solid #e5e5e5; background:#fafafa; padding:10px; margin-bottom:8px; border-radius:6px; }
    .script-title { color:#db4a41; font-family: Formula, Arial, sans-serif; font-weight: 800; margin-bottom: 6px; }
    .shotlist { border:1px solid #e5e5e5; padding:10px; border-radius:6px; margin-bottom:10px; background:#fafafa; }
    .shotlist-title { color:#db4a41; font-family: Formula, Arial, sans-serif; font-weight: 800; margin-bottom:6px; }
    .checkitem { display:flex; align-items:center; gap:8px; margin:4px 0; }
    .checkbox { width:12px; height:12px; border:1px solid #999; display:inline-block; }
    .checkbox.checked { background:#db4a41; border-color:#db4a41; }
    .check-cell { width: 60px; text-align: center; }
    .disclaimer { font-size: 10px; color:#555; line-height: 1.5; text-align: center; }
	`;

  const html = `
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="utf-8" />
			<title>${title} – Call Sheet</title>
			<style>${styles}</style>
		</head>
		<body>
			<div class="header">
				<div></div>
        <div class="brand">
          <img src="/assets/young-logo-black.webp" alt="Young Productions" />
        </div>
			</div>
			<h1>CALL SHEET</h1>
			<table class="table">
				<tr>
					<th>PRODUCTION TITLE</th>
					<td>
						<div style="font-weight:800">${title}</div>
						<div class="small">Creative Director: ${creativeDirector || "—"}</div>
						<div class="small">Producer: ${producer || "—"}</div>
						<div class="small">DOP/Director: ${dop || director || "—"}</div>
					</td>
				</tr>
				<tr>
					<th>GENERAL CREW CALL</th>
					<td>${generalCrewCall || "—"}</td>
				</tr>
				<tr>
					<th>DATE</th>
					<td>${dateStr || "—"}</td>
				</tr>
			</table>

			<div class="section-title">CALL TIMES</div>
			<table class="table call-times">
				<tr><th>CAST MEMBER</th><th>Start Time - End Time</th></tr>
				${castRows || `<tr><td colspan="2">—</td></tr>`}
			</table>

			<div class="section-title">LOCATION</div>
			<table class="table">
				<tr>
					<th>Location</th>
					<td>${locationLink ? `<a href="${locationLink}">${locationLink}</a>` : "—"}</td>
				</tr>
				<tr>
					<th>ADDRESS</th>
					<td>${locationAddress || "—"}</td>
				</tr>
			</table>

			<div class="section-title">IMPORTANT NOTES</div>
			<ul>${notesList || "<li>Being on time is crucial</li>"}</ul>

			<div class="section-title">CREW</div>
			<table class="table crew-table">
				<tr><th>Crew Member</th><th>Role</th></tr>
				${crewRows || `<tr><td colspan="2">—</td></tr>`}
			</table>

			<div class="footer-note">Time window: ${timeRange || "—"}</div>

			<div class="page-break"></div>

			<h1>Timeline</h1>
			<table class="table timeline">
				<tr><th>Title</th><th>Time</th><th>Notes</th></tr>
				${timingRows || `<tr><td colspan="3">—</td></tr>`}
			</table>

        <div class="section-title">Props</div>
        ${
          shoot.props && shoot.props.length
            ? `
              <table class="table">
                <tr><th>Item</th><th class="check-cell">Done</th></tr>
                ${shoot.props
                  .map(
                    (p) => `
                      <tr>
                        <td>${p}</td>
                        <td class="check-cell"><span class="checkbox"></span></td>
                      </tr>
                    `
                  )
                  .join("")}
              </table>
            `
            : `<div class="small">—</div>`
        }

        <div class="section-title">Equipment</div>
        ${
          shoot.equipment && shoot.equipment.length
            ? `
              <table class="table">
                <tr><th>Item</th><th class="check-cell">Done</th></tr>
                ${shoot.equipment
                  .map(
                    (e) => `
                      <tr>
                        <td>${e}</td>
                        <td class="check-cell"><span class="checkbox"></span></td>
                      </tr>
                    `
                  )
                  .join("")}
              </table>
            `
            : `<div class="small">—</div>`
        }


        <div class="page-break"></div>
        <div class="disclaimer">
          <p>Any changes to the call sheet will be communicated</p>
          <p>Times in the sheet might slightly change due to on location circumstances</p>
          <p>Thank you for your cooperation and we look forward to a successful shoot!</p>
        </div>
		</body>
		</html>
	`;

  win.document.open();
  win.document.write(html);
  win.document.close();
  win.focus();
  // Ensure fonts/styles load before print
  setTimeout(() => {
    win.print();
  }, 350);
}

// Separate function to export shot lists as PDF
export function exportShotListsAsPDF(shoot) {
  if (!shoot || !shoot.shotLists || !shoot.shotLists.length) {
    alert("No shot lists available for this shoot");
    return;
  }

  const title = shoot.title || "Shot Lists";
  const dateStr = formatDate(shoot.date);

  const win = window.open("", "_blank");
  if (!win) return;

  const styles = `
    @page { size: A4; margin: 18mm; }
    * { box-sizing: border-box; }
    @font-face {
        font-family: 'Formula';
        src: url('/fonts/FormulaFont.woff2') format('truetype');
        font-weight: 800; font-style: normal; font-display: swap;
    }
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; color: #111; }
    .header { display:flex; justify-content: space-between; align-items:flex-end; }
    .brand { text-align:right; }
    .brand img { height: 28px; }
    h1 { font-size: 42px; margin: 18px 0; letter-spacing: 1px; font-family: Formula, Arial, sans-serif; font-weight: 800; }
    .table { width:100%; border-collapse: collapse; margin: 10px 0; }
    .table th, .table td { border: 1px solid #d9d9d9; padding: 10px; vertical-align: top; }
    .table th { background:#f6f6f6; text-align:left; width: 35%; }
    .small { font-size: 12px; color:#333; }
    .section-title { margin: 24px 0 8px; font-size: 14px; font-weight: 800; letter-spacing: .5px; font-family: Formula, Arial, sans-serif; }
    .shotlist { border:1px solid #e5e5e5; padding:10px; border-radius:6px; margin-bottom:10px; background:#fafafa; }
    .shotlist-title { color:#db4a41; font-family: Formula, Arial, sans-serif; font-weight: 800; margin-bottom:6px; }
    .checkitem { display:flex; align-items:center; gap:8px; margin:4px 0; }
    .checkbox { width:12px; height:12px; border:1px solid #999; display:inline-block; }
    .checkbox.checked { background:#db4a41; border-color:#db4a41; }
    .disclaimer { font-size: 10px; color:#555; line-height: 1.5; text-align: center; }
  `;

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8" />
      <title>${title} – Shot Lists</title>
      <style>${styles}</style>
    </head>
    <body>
      <div class="header">
        <div></div>
        <div class="brand">
          <img src="/assets/young-logo-black.webp" alt="Young Productions" />
        </div>
      </div>
      <h1>SHOT LISTS</h1>
      
      <table class="table">
        <tr>
          <th>PRODUCTION TITLE</th>
          <td>${title}</td>
        </tr>
        <tr>
          <th>DATE</th>
          <td>${dateStr || "—"}</td>
        </tr>
      </table>

      <div class="section-title">SHOT LISTS</div>
      <div>
        ${shoot.shotLists
          .map(
            (sl) => `
              <div class="shotlist">
                <div class="shotlist-title">${sl.title || "Untitled"}</div>
                ${(sl.checklist || [])
                  .map(
                    (it) => `
                      <div class="checkitem">
                        <span class="checkbox"></span>
                        <span>${it.text || ""}</span>
                      </div>
                    `
                  )
                  .join("")}
              </div>
            `
          )
          .join("")}
      </div>

      <div class="disclaimer">
        <p>Check off items as they are completed during the shoot</p>
        <p>Any changes to the shot list will be communicated on location</p>
      </div>
    </body>
    </html>
  `;

  win.document.open();
  win.document.write(html);
  win.document.close();
  win.focus();
  // Ensure fonts/styles load before print
  setTimeout(() => {
    win.print();
  }, 350);
}

export default exportShootAsPDF;
