import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useClient } from "../contexts/ClientContext";
import {
  Box,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Snackbar,
  Alert,
  CircularProgress,
  Container,
  InputAdornment,
  IconButton,
  Divider,
} from "@mui/material";
import { motion } from "framer-motion";
import {
  Login as LoginIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Visibility,
  VisibilityOff,
  Security as SecurityIcon,
} from "@mui/icons-material";

function ClientLogin() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "error",
  });
  const navigate = useNavigate();
  const { login } = useClient();

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/client-users/login";

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    setFormData({
      ...formData,
      [name]: name === "email" ? value.toLowerCase() : value, // 👈 lowercase only for email
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(API_BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        // Store client token and user data using context
        login(result.token, result.clientUser);

        setSnackbar({
          open: true,
          message: "Login successful! Redirecting to dashboard...",
          severity: "success",
        });

        // Redirect to client dashboard
        setTimeout(() => {
          navigate("/client-portal/dashboard");
        }, 1500);
      } else {
        setSnackbar({
          open: true,
          message:
            result.message || "Login failed. Please check your credentials.",
          severity: "error",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      setSnackbar({
        open: true,
        message: "Network error. Please try again.",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background:
          "linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at 30% 20%, rgba(219, 74, 65, 0.15) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%)",
          zIndex: 0,
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: "-50%",
          left: "-50%",
          width: "200%",
          height: "200%",
          background:
            'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>\') repeat',
          animation: "grain 8s steps(10) infinite",
          zIndex: 1,
        },
        "@keyframes grain": {
          "0%, 100%": { transform: "translate(0, 0)" },
          "10%": { transform: "translate(-5%, -10%)" },
          "20%": { transform: "translate(-15%, 5%)" },
          "30%": { transform: "translate(7%, -25%)" },
          "40%": { transform: "translate(-5%, 25%)" },
          "50%": { transform: "translate(-15%, 10%)" },
          "60%": { transform: "translate(15%, 0%)" },
          "70%": { transform: "translate(0%, 15%)" },
          "80%": { transform: "translate(3%, -10%)" },
          "90%": { transform: "translate(-10%, 5%)" },
        },
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "3%",
          left: "3%",
          zIndex: 3,
          height: "80vh",
        }}
      >
        <motion.img
          src="/assets/young-logo-white.webp"
          alt="Young Productions Logo"
          width={window.innerWidth < 768 ? 120 : 280}
          style={{
            filter: "brightness(100%) drop-shadow(0 4px 8px rgba(0,0,0,0.3))",
            objectFit: "contain",
          }}
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        />
      </Box>
      <Container
        maxWidth="sm"
        sx={{
          position: "relative",
          zIndex: 2,
          px: { xs: 2, sm: 3 },
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.98)",
              backdropFilter: "blur(25px)",
              borderRadius: { xs: "16px", sm: "24px" },
              boxShadow:
                "0 25px 50px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.1)",
              border: "1px solid rgba(255, 255, 255, 0.3)",
              overflow: "hidden",
              position: "relative",
              mx: { xs: 1, sm: 0 },
              "&::before": {
                content: '""',
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                height: "4px",
                background: "linear-gradient(90deg, #db4a41, #ff6b5b, #db4a41)",
                zIndex: 1,
              },
            }}
          >
            <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
              {/* Header */}
              <Box
                sx={{
                  textAlign: "center",
                  mb: { xs: 4, sm: 5 },
                  position: "relative",
                  zIndex: 2,
                }}
              >
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{
                    delay: 0.4,
                    type: "spring",
                    stiffness: 150,
                    damping: 10,
                  }}
                >
                  <Box
                    sx={{
                      width: { xs: 70, sm: 80, md: 90 },
                      height: { xs: 70, sm: 80, md: 90 },
                      borderRadius: "50%",
                      background:
                        "linear-gradient(135deg, #db4a41, #ff6b5b, #c62828)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mx: "auto",
                      mb: { xs: 2, sm: 3 },
                      boxShadow:
                        "0 15px 30px rgba(219, 74, 65, 0.4), 0 5px 15px rgba(0,0,0,0.1)",
                      position: "relative",
                      "&::before": {
                        content: '""',
                        position: "absolute",
                        top: -3,
                        left: -3,
                        right: -3,
                        bottom: -3,
                        borderRadius: "50%",
                        background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                        opacity: 0.3,
                        zIndex: -1,
                      },
                    }}
                  >
                    <SecurityIcon
                      sx={{
                        color: "white",
                        fontSize: { xs: 35, sm: 40, md: 45 },
                      }}
                    />
                  </Box>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                >
                  <Typography
                    variant="h3"
                    sx={{
                      fontFamily: "Formula Bold",
                      color: "#2c2c2c",
                      mb: 1,
                      fontWeight: "bold",
                      letterSpacing: "-0.5px",
                      fontSize: { xs: "1.75rem", sm: "2.25rem", md: "3rem" },
                    }}
                  >
                    Welcome Back
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      color: "#db4a41",
                      fontSize: { xs: "1rem", sm: "1.1rem" },
                      fontWeight: 600,
                      mb: 1,
                    }}
                  >
                    Client Portal
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: "#666",
                      fontSize: { xs: "0.9rem", sm: "1rem" },
                      lineHeight: 1.6,
                    }}
                  >
                    Access your project timeline, posts schedule, and exclusive
                    content
                  </Typography>
                </motion.div>
              </Box>

              <Divider sx={{ mb: 4, opacity: 0.1 }} />

              {/* Login Form */}
              <motion.form
                onSubmit={handleSubmit}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.6 }}
              >
                <Box sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <EmailIcon
                            sx={{
                              color: "#db4a41",
                              opacity: 0.7,
                              fontSize: { xs: 20, sm: 24 },
                            }}
                          />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: { xs: "12px", sm: "16px" },
                        backgroundColor: "rgba(248, 249, 250, 0.8)",
                        transition: "all 0.3s ease",
                        fontSize: { xs: "0.9rem", sm: "1rem" },
                        "& fieldset": {
                          borderColor: "rgba(0,0,0,0.1)",
                          borderWidth: "2px",
                        },
                        "&:hover": {
                          backgroundColor: "rgba(248, 249, 250, 1)",
                          "& fieldset": {
                            borderColor: "#db4a41",
                          },
                        },
                        "&.Mui-focused": {
                          backgroundColor: "rgba(255, 255, 255, 1)",
                          boxShadow: "0 0 0 3px rgba(219, 74, 65, 0.1)",
                          "& fieldset": {
                            borderColor: "#db4a41",
                          },
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "#666",
                        fontWeight: 500,
                        fontSize: { xs: "0.9rem", sm: "1rem" },
                        "&.Mui-focused": {
                          color: "#db4a41",
                        },
                      },
                    }}
                  />
                </Box>

                <Box sx={{ mb: 4 }}>
                  <TextField
                    fullWidth
                    label="Password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon sx={{ color: "#db4a41", opacity: 0.7 }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={togglePasswordVisibility}
                            edge="end"
                            sx={{ color: "#666" }}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "16px",
                        backgroundColor: "rgba(248, 249, 250, 0.8)",
                        transition: "all 0.3s ease",
                        "& fieldset": {
                          borderColor: "rgba(0,0,0,0.1)",
                          borderWidth: "2px",
                        },
                        "&:hover": {
                          backgroundColor: "rgba(248, 249, 250, 1)",
                          "& fieldset": {
                            borderColor: "#db4a41",
                          },
                        },
                        "&.Mui-focused": {
                          backgroundColor: "rgba(255, 255, 255, 1)",
                          boxShadow: "0 0 0 3px rgba(219, 74, 65, 0.1)",
                          "& fieldset": {
                            borderColor: "#db4a41",
                          },
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "#666",
                        fontWeight: 500,
                        "&.Mui-focused": {
                          color: "#db4a41",
                        },
                      },
                    }}
                  />
                </Box>

                <motion.div
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading}
                    sx={{
                      background:
                        "linear-gradient(135deg, #db4a41 0%, #ff6b5b 50%, #c62828 100%)",
                      color: "white",
                      fontFamily: "Formula Bold",
                      fontSize: { xs: "1rem", sm: "1.2rem" },
                      py: { xs: 1.5, sm: 2 },
                      borderRadius: { xs: "12px", sm: "16px" },
                      textTransform: "none",
                      boxShadow:
                        "0 12px 24px rgba(219, 74, 65, 0.4), 0 4px 8px rgba(0,0,0,0.1)",
                      position: "relative",
                      overflow: "hidden",
                      "&::before": {
                        content: '""',
                        position: "absolute",
                        top: 0,
                        left: "-100%",
                        width: "100%",
                        height: "100%",
                        background:
                          "linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",
                        transition: "left 0.5s",
                      },
                      "&:hover": {
                        background:
                          "linear-gradient(135deg, #c62828 0%, #db4a41 50%, #ff6b5b 100%)",
                        boxShadow:
                          "0 16px 32px rgba(219, 74, 65, 0.5), 0 6px 12px rgba(0,0,0,0.15)",
                        "&::before": {
                          left: "100%",
                        },
                      },
                      "&:disabled": {
                        background: "rgba(219, 74, 65, 0.5)",
                        color: "rgba(255, 255, 255, 0.7)",
                        boxShadow: "none",
                      },
                    }}
                  >
                    {loading ? (
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <CircularProgress size={24} sx={{ color: "white" }} />
                        <Typography
                          sx={{ color: "white", fontFamily: "Formula Bold" }}
                        >
                          Signing In...
                        </Typography>
                      </Box>
                    ) : (
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                      >
                        <LoginIcon />
                        Sign In to Dashboard
                      </Box>
                    )}
                  </Button>
                </motion.div>
              </motion.form>

              {/* Footer */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.2, duration: 0.6 }}
              >
                <Divider sx={{ my: 3, opacity: 0.1 }} />
                <Box sx={{ textAlign: "center" }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#999",
                      fontSize: { xs: "0.85rem", sm: "0.95rem" },
                      mb: 1,
                      lineHeight: 1.5,
                    }}
                  >
                    Need help accessing your account?
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#db4a41",
                      fontSize: { xs: "0.8rem", sm: "0.9rem" },
                      fontWeight: 600,
                      cursor: "pointer",
                      "&:hover": {
                        textDecoration: "underline",
                      },
                    }}
                  >
                    Contact your account manager
                  </Typography>
                </Box>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </Container>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ClientLogin;
