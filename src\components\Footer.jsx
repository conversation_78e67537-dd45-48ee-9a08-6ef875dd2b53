import React, { useState, useEffect } from "react";
import { Box, Typography, Link } from "@mui/material";
import InstagramIcon from "@mui/icons-material/Instagram";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import YouTubeIcon from "@mui/icons-material/YouTube";

const footerLogo = process.env.PUBLIC_URL + "/assets/footer-logo.webp";
// const gdLogo = process.env.PUBLIC_URL + "/assets/gd-icon.png";

const Footer = () => {
  const [randomMessage, setRandomMessage] = useState("");

  const messages = [
    "<PERSON><PERSON><PERSON> and <PERSON><PERSON> made this site happen. Any bugs are 100% on them.",
    "Handcrafted by <PERSON><PERSON><PERSON> & <PERSON>. If something's off, it's their fault.",
    "This site was coded by <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Break it, and they'll explain why.",
    "Built by <PERSON><PERSON><PERSON> & <PERSON><PERSON>. Blame them if it's messy.",
    "<PERSON><PERSON><PERSON> & <PERSON> made this. They're also to blame if it's broken.",
    "Created by <PERSON><PERSON><PERSON> & <PERSON>. All errors = their responsibility.",
    "<PERSON><PERSON><PERSON> and <PERSON><PERSON> wrote this. Bugs are their signature touch.",
    "This site exists thanks to <PERSON><PERSON><PERSON> & Ka<PERSON>. Handle complaints accordingly.",
    "Crafted by <PERSON>haled & Karim. Perfection not guaranteed.",
  ];

  useEffect(() => {
    // Set initial random message
    const randomIndex = Math.floor(Math.random() * messages.length);
    setRandomMessage(messages[randomIndex]);

    // Change message every 5 seconds
    const interval = setInterval(() => {
      const newRandomIndex = Math.floor(Math.random() * messages.length);
      setRandomMessage(messages[newRandomIndex]);
    }, 5000);

    // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, []);
  return (
    <Box
      className="footer"
      sx={{
        backgroundColor: "#000",
        color: "white",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-around",
          alignContent: "flex-start",
          maxWidth: "1400px",
          margin: "0 auto",
          flexDirection: { xs: "column", md: "row" },
          padding: "50px 20px",
          gap: " 80px",
        }}
      >
        {/* Section 1 */}
        <Box
          sx={{
            marginBottom: { xs: "20px", md: "0", textAlign: "left", flex: "1" },
          }}
        >
          <Typography
            variant="h3"
            sx={{
              marginBottom: "15px",
              fontFamily: "Formula Bold",
            }}
          >
            Well, Okay then.
          </Typography>
          <Typography
            variant="body1"
            sx={{
              marginBottom: "15px",
              color: "",
              fontFamily: "Formula Bold",
            }}
          >
            Contact us to create work that matters, whether you're a global
            brand or an emerging start-up.
            <br />
            <span style={{ color: "#DB4A41" }}>Let's work together.</span>
          </Typography>
          <Link
            className="btn btn-primary link-btn"
            sx={{
              color: "white",
              textDecoration: "none",
              borderRadius: "10px",
              fontFamily: "Formula Bold",
            }}
            href="/contact"
          >
            GET IN TOUCH
          </Link>
        </Box>

        {/* Section 2 */}
        <Box
          sx={{
            marginBottom: { xs: "20px", md: "0", flex: "1", textAlign: "left" },
            marginLeft: "15px",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              marginBottom: "15px",
              color: "#fff",
              fontFamily: "Formula Bold",
            }}
          >
            Explore
          </Typography>
          <ul
            style={{
              listStyle: "none",
              padding: 0,
              display: "flex",
              flexDirection: "column",
              gap: "3.5px",
              fontFamily: "Formula Bold",
              fontSize: "20px",
            }}
          >
            <li>
              <Link href="/about">About</Link>
            </li>
            <li>
              <Link href="/insights ">Work</Link>
            </li>
            <li>
              <Link href="/insights">Insights</Link>
            </li>
            <li>
              <Link href="/reels">Reels</Link>
            </li>
            <li>
              <Link href="/client-portal">Client Portal</Link>
            </li>
            <li>
              <Link href="/joinUs">Join Us</Link>
            </li>
          </ul>
        </Box>

        {/* Section 3 */}
        <Box
          sx={{
            marginBottom: { xs: "20px", md: "0", flex: "1", textAlign: "left" },
            marginLeft: "15px",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              marginBottom: "15px",
              color: "#fff",
              fontFamily: "Formula Bold",
            }}
          >
            Social
          </Typography>
          <ul className="footer-social-navigation">
            <li>
              <InstagramIcon />
              <Link href="https://www.instagram.com/youngproductionss/">
                Instagram
              </Link>
            </li>
            <li>
              <LinkedInIcon />
              <Link href="https://www.linkedin.com/company/young-prroductions">
                LinkedIn
              </Link>
            </li>
            <li>
              <YouTubeIcon />
              <Link href="https://www.youtube.com/channel/UCuXflYNaaJTcxIpaLQFcteg">
                Youtube
              </Link>
            </li>
          </ul>
        </Box>

        {/* Section 4 */}
        <Box sx={{ flex: "1", textAlign: "left" }}>
          <Typography
            variant="h3"
            sx={{
              marginTop: "20px",
              color: "#Db4a41",
              fontFamily: "Formula Bold",
            }}
          >
            Notice
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: "#fff",
              fontFamily: "Formula Bold",
              minHeight: "60px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {randomMessage}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              marginBottom: "15px",
              color: "#fff",
              fontFamily: "Formula Bold",
            }}
          >
            Copyright © 2025 Young Software House. All rights reserved.
          </Typography>
        </Box>
      </Box>
      <Box
        className="footer-bottom"
        sx={{ padding: "50px 10px 0px 10px", textAlign: "center" }}
      >
        <img
          src={footerLogo}
          alt="Logo"
          style={{
            width: "100%",
            position: "relative",
            bottom: 0,
          }}
        />
      </Box>
    </Box>
  );
};

export default Footer;
