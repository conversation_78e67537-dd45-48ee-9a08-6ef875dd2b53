import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  IconButton,
  Tooltip,
  Switch,
  Snackbar,
  Alert,
  FormControlLabel,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { motion, AnimatePresence } from "framer-motion";

function HeroVideos() {
  const [heroVideos, setHeroVideos] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingVideo, setEditingVideo] = useState(null);
  const [newVideo, setNewVideo] = useState({
    name: "",
    alt: "",
    visibility: false,
    videoFile: null,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("token");
        const response = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/heroVideos",
          { headers: { Authorization: `Bearer ${token}` } }
        );
        const data = await response.json();
        console.log("Fetched hero videos:", data);
        setHeroVideos(data);
      } catch (error) {
        console.error("Error fetching hero videos:", error);
        showSnackbar("Failed to fetch hero videos", "error");
      }
    };
    fetchData();
  }, []);

  const handleAdd = () => {
    setEditingVideo(null);
    setNewVideo({ name: "", alt: "", visibility: false, videoFile: null });
    setOpenModal(true);
  };

  const handleEdit = (video) => {
    setEditingVideo(video);
    setNewVideo({
      name: video.name,
      alt: video.alt,
      visibility: video.visibility,
      videoFile: null,
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingVideo(null);
    setNewVideo({ name: "", alt: "", visibility: false, videoFile: null });
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewVideo({
      ...newVideo,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setNewVideo({
      ...newVideo,
      videoFile: file,
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");

      // Check visibility limit before submitting
      const currentVisibleCount = heroVideos.filter(
        (video) => video.visibility
      ).length;
      let finalVisibility = newVideo.visibility;
      let visibilityWarning = false;

      // If trying to set visibility to true
      if (newVideo.visibility) {
        if (editingVideo) {
          // For editing: check if we're changing from false to true
          const wasVisible = editingVideo.visibility;
          if (!wasVisible && currentVisibleCount >= 6) {
            finalVisibility = false;
            visibilityWarning = true;
          }
        } else {
          // For new video: check if we already have 6 visible videos
          if (currentVisibleCount >= 6) {
            finalVisibility = false;
            visibilityWarning = true;
          }
        }
      }

      const formData = new FormData();
      formData.append("name", newVideo.name);
      formData.append("alt", newVideo.alt);
      formData.append("visibility", finalVisibility);

      if (newVideo.videoFile) {
        formData.append("video", newVideo.videoFile);
      }

      const url = editingVideo
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/heroVideos/${editingVideo._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/heroVideos";

      const method = editingVideo ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to save hero video");
      }

      const data = await response.json();

      if (editingVideo) {
        setHeroVideos(
          heroVideos.map((v) => (v._id === editingVideo._id ? data : v))
        );
        if (visibilityWarning) {
          showSnackbar(
            "Hero video updated successfully. Visibility set to false - maximum 6 videos can be visible.",
            "warning"
          );
        } else {
          showSnackbar("Hero video updated successfully", "success");
        }
      } else {
        setHeroVideos([data, ...heroVideos]);
        if (visibilityWarning) {
          showSnackbar(
            "Hero video added successfully. Visibility set to false - maximum 6 videos can be visible.",
            "warning"
          );
        } else {
          showSnackbar("Hero video added successfully", "success");
        }
      }

      handleCloseModal();
    } catch (error) {
      console.error("Error saving hero video:", error);
      showSnackbar("Failed to save hero video", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this hero video?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/heroVideos/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setHeroVideos(heroVideos.filter((video) => video._id !== id));
        showSnackbar("Hero video deleted successfully", "success");
      } else {
        throw new Error("Failed to delete hero video");
      }
    } catch (error) {
      console.error("Error deleting hero video:", error);
      showSnackbar("Failed to delete hero video", "error");
    }
  };

  const toggleVisibility = async (id, currentVisibility) => {
    try {
      const token = localStorage.getItem("token");

      // Check if trying to make visible when already at limit
      if (!currentVisibility) {
        const currentVisibleCount = heroVideos.filter(
          (video) => video.visibility
        ).length;
        if (currentVisibleCount >= 6) {
          showSnackbar(
            "Cannot make video visible - maximum 6 videos can be visible at once",
            "error"
          );
          return;
        }
      }

      const formData = new FormData();
      formData.append("visibility", !currentVisibility);

      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/heroVideos/${id}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (response.ok) {
        const updatedVideo = await response.json();
        setHeroVideos(heroVideos.map((v) => (v._id === id ? updatedVideo : v)));
        showSnackbar(
          `Video ${updatedVideo.visibility ? "shown" : "hidden"}`,
          "success"
        );
      }
    } catch (error) {
      console.error("Error toggling visibility:", error);
      showSnackbar("Failed to update visibility", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 4,
          }}
        >
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontFamily: "Formula Bold",
                color: "#db4a41",
                fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
                textShadow: "0 2px 4px rgba(0,0,0,0.3)",
              }}
            >
              Hero Videos
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                fontSize: { xs: "0.8rem", sm: "0.9rem" },
                mt: 0.5,
              }}
            >
              {heroVideos.filter((video) => video.visibility).length} of 6
              videos visible
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Hero Video
          </Button>
        </Box>

        {/* Videos Grid */}
        <Grid container spacing={3}>
          <AnimatePresence>
            {heroVideos.map((video) => (
              <Grid item xs={12} sm={6} md={4} key={video._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      borderRadius: "12px",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      transition: "transform 0.3s ease, box-shadow 0.3s ease",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      "&:hover": {
                        transform: "translateY(-5px)",
                        boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
                      },
                    }}
                  >
                    <Box sx={{ position: "relative" }}>
                      {video.videoUrl ? (
                        <video
                          style={{
                            width: "100%",
                            height: "200px",
                            objectFit: "cover",
                            borderRadius: "12px 12px 0 0",
                          }}
                          muted
                          loop
                          autoPlay
                          preload="metadata"
                          // onMouseEnter={(e) => {
                          //   e.target
                          //     .play()
                          //     .catch((err) => console.log("Play failed:", err));
                          // }}
                          // onMouseLeave={(e) => {
                          //   e.target.pause();
                          // }}
                          onError={(e) => {
                            console.error("Video load error:", e.target.error);
                            console.log("Video URL:", video.videoUrl);
                            console.log("Video element:", e.target);
                          }}
                          onLoadStart={() =>
                            console.log(
                              "Video loading started:",
                              video.videoUrl
                            )
                          }
                          onCanPlay={() =>
                            console.log("Video can play:", video.videoUrl)
                          }
                        >
                          <source
                            src={video.videoUrl.replace(
                              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/mp4"
                          />
                          <source
                            src={video.videoUrl.replace(
                              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/webm"
                          />
                          <source
                            src={video.videoUrl.replace(
                              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/ogg"
                          />
                          Your browser does not support the video tag.
                        </video>
                      ) : (
                        <Box
                          sx={{
                            width: "100%",
                            height: "200px",
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            borderRadius: "12px 12px 0 0",
                          }}
                        >
                          <Typography
                            sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                          >
                            No video available
                          </Typography>
                        </Box>
                      )}
                      <Box
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          display: "flex",
                          gap: 1,
                        }}
                      >
                        <Tooltip
                          title={video.visibility ? "Visible" : "Hidden"}
                        >
                          <IconButton
                            onClick={() =>
                              toggleVisibility(video._id, video.visibility)
                            }
                            sx={{
                              backgroundColor: video.visibility
                                ? "rgba(76, 175, 80, 0.8)"
                                : "rgba(158, 158, 158, 0.8)",
                              color: "white",
                              "&:hover": {
                                backgroundColor: video.visibility
                                  ? "rgba(76, 175, 80, 1)"
                                  : "rgba(158, 158, 158, 1)",
                              },
                            }}
                          >
                            {video.visibility ? (
                              <VisibilityIcon />
                            ) : (
                              <VisibilityOffIcon />
                            )}
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          mb: 2,
                        }}
                      >
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              color: "white",
                              fontWeight: "bold",
                              mb: 0.5,
                              fontSize: {
                                xs: "0.9rem",
                                sm: "1rem",
                                md: "1.1rem",
                              },
                            }}
                          >
                            {video.name}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255, 255, 255, 0.7)",
                              fontSize: {
                                xs: "0.75rem",
                                sm: "0.8rem",
                                md: "0.85rem",
                              },
                            }}
                          >
                            {video.alt}
                          </Typography>
                        </Box>
                        <Box sx={{ display: "flex", gap: 0.5 }}>
                          <Tooltip title="Edit">
                            <IconButton
                              onClick={() => handleEdit(video)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "white" },
                              }}
                            >
                              <EditIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              onClick={() => handleDelete(video._id)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "#db4a41" },
                              }}
                            >
                              <DeleteIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </AnimatePresence>
        </Grid>
      </Box>

      {/* Add/Edit Modal */}
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle sx={{ color: "white" }}>
          {editingVideo ? "Edit Hero Video" : "Add New Hero Video"}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Video Name"
            name="name"
            value={newVideo.name}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <TextField
            fullWidth
            label="Alt Text"
            name="alt"
            value={newVideo.alt}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <FormControlLabel
            control={
              <Switch
                checked={newVideo.visibility}
                onChange={handleInputChange}
                name="visibility"
                sx={{
                  "& .MuiSwitch-switchBase.Mui-checked": {
                    color: "#db4a41",
                  },
                  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#db4a41",
                  },
                }}
              />
            }
            label="Visible"
            sx={{ color: "white", mt: 2 }}
          />
          <Box sx={{ mt: 2 }}>
            <input
              accept="video/*"
              style={{ display: "none" }}
              id="video-upload"
              type="file"
              onChange={handleFileChange}
            />
            <label htmlFor="video-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                {newVideo.videoFile ? newVideo.videoFile.name : "Upload Video"}
              </Button>
            </label>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            {loading ? "Saving..." : editingVideo ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background:
              snackbar.severity === "success"
                ? "#2e7d32"
                : snackbar.severity === "warning"
                ? "#f57c00"
                : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default HeroVideos;
