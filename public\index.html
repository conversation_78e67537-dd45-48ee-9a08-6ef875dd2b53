<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="favicon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Young Productions is a dynamic media production company providing services in film making, creative solutions, website development, and more."
    />
    <meta
      name="keywords"
      content="Young Productions, media production, film making, creative solutions, website development , media production service , event videography, video filming, production company egypt"
    />
    <meta name="author" content="Young Productions" />
    <meta name="robots" content="index, follow" />

    <!-- Preload CDN connection for faster video loading -->
    <link
      rel="preconnect"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
    />
    <link
      rel="dns-prefetch"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
    />
    <meta property="og:title" content="Young Productions" />
    <meta
      property="og:description"
      content="Young Productions is a dynamic media production company providing services in film making, creative solutions, website development, and more."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.youngproductionss.com" />
    <meta
      property="og:image"
      content="https://www.youngproductionss.com/assets/young-logo-black.webp"
    />
    <meta property="og:site_name" content="Young Productions" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@youngproductions" />
    <meta
      name="twitter:title"
      content="Young Productions - Media Production Company"
    />
    <meta
      name="twitter:description"
      content="Young Productions is a dynamic media production company providing services in film making, creative solutions, website development, and more."
    />
    <meta
      name="twitter:image"
      content="https://www.yourdomain.com/path/to/your/image.jpg"
    />
    <title>Young Productions</title>

    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Anton&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Permanent+Marker&family=Playwrite+HU:wght@100..400&display=swap"
      rel="stylesheet"
    />
    <!-- <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    /> -->

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.

      
    -->

    <link rel="stylesheet" href="%PUBLIC_URL%/styles.css" />
    <link rel="stylesheet" href="%PUBLIC_URL%/responsive.css" />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
