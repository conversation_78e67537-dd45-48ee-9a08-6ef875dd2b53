<!DOCTYPE html>
<html lang="en">
  <link
    rel="preload"
    href="%PUBLIC_URL%/fonts/FormulaFont.woff2"
    as="font"
    type="font/woff2"
    crossorigin
  />

  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="favicon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="description"
      content="Young Productions offers top-notch media production services, including film making, advertising, and website development for impactful storytelling."
    />
    <meta
      name="keywords"
      content="media production services, film making, advertising, website development, Young Productions, social media, event videography"
    />
    <meta name="author" content="Young Productions" />
    <meta name="robots" content="index, follow" />
    <!-- Preload CDN connection for faster video loading -->
    <link
      rel="preconnect"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
    />
    <link
      rel="dns-prefetch"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
    />
    <!-- Open Graph / Social Sharing -->
    <meta
      property="og:title"
      content="Young Productions - Expert Media Production Services"
    />
    <meta
      property="og:description"
      content="Young Productions offers top-notch media production services, including film making, advertising, and website development for impactful storytelling."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.youngproductionss.com" />
    <meta
      property="og:image"
      content="https://www.youngproductionss.com/assets/young-logo-black.webp"
    />
    <meta property="og:site_name" content="Young Productions" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@youngproductions" />
    <meta
      name="twitter:title"
      content="Young Productions - Expert Media Production Services"
    />
    <meta
      name="twitter:description"
      content="Young Productions offers top-notch media production services, including film making, advertising, and website development for impactful storytelling."
    />
    <meta
      name="twitter:image"
      content="https://www.youngproductionss.com/assets/young-logo-black.webp"
    />

    <title>Media Production Services | Young Productions</title>

    <!-- Favicon -->
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Fonts -->
    <link
      rel="preload"
      href="%PUBLIC_URL%/fonts/FormulaFont.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Anton&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Permanent+Marker&family=Playwrite+HU:wght@100..400&display=swap"
      rel="stylesheet"
    />

    <!-- Preconnect for external video/CDN -->
    <link
      rel="preconnect"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
      crossorigin
    />
    <link
      rel="dns-prefetch"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
    />
    <!-- Preconnect to API for faster data fetching -->
    <link
      rel="preconnect"
      href="https://youngproductions-768ada043db3.herokuapp.com"
      crossorigin
    />
    <link
      rel="dns-prefetch"
      href="https://youngproductions-768ada043db3.herokuapp.com"
    />
    <!-- Preload critical hero logo for LCP -->
    <link
      rel="preload"
      as="image"
      href="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/young-logo-white.webp"
      fetchpriority="high"
      crossorigin="anonymous"
    />

    <!-- <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    /> -->

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.

      
    -->

    <link rel="stylesheet" href="%PUBLIC_URL%/styles.css" />
    <link rel="stylesheet" href="%PUBLIC_URL%/responsive.css" />
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
