import { useRef, useEffect, useState, forwardRef, useMemo } from "react";

/**
 * LazyVideo - Renders <video> immediately with placeholder, delays src assignment until intersection.
 * Prevents media from blocking LCP while maintaining React reconciliation and preventing CLS.
 */
const LazyVideo = forwardRef(
  (
    {
      src,
      lazy = true,
      rootMargin = "100px",
      threshold = 0,
      poster,
      placeholder,
      style = {},
      delay = 0,
      ...props
    },
    ref
  ) => {
    const videoRef = useRef(null);
    const [videoSrc, setVideoSrc] = useState(lazy ? undefined : src);
    const [shouldLoad, setShouldLoad] = useState(!lazy);
    const observerRef = useRef(null);

    // Generate placeholder from video URL if not provided
    const placeholderSrc = useMemo(() => {
      if (poster) return poster;
      if (placeholder) return placeholder;
      // Use first frame from video as placeholder
      if (src && typeof src === "string") {
        return src.includes("#t=") ? src : `${src}#t=0.1`;
      }
      return undefined;
    }, [poster, placeholder, src]);

    // Combine refs properly for GSAP
    useEffect(() => {
      const node = videoRef.current;
      if (!node) return;

      if (typeof ref === "function") {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref]);

    // IntersectionObserver for lazy loading with staggered delay
    useEffect(() => {
      if (!lazy || shouldLoad) return;

      const node = videoRef.current;
      if (!node) return;

      observerRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              // Stagger loading by delay
              if (delay > 0) {
                setTimeout(() => {
                  setShouldLoad(true);
                  setVideoSrc(src);
                  observerRef.current?.disconnect();
                }, delay);
              } else {
                setShouldLoad(true);
                setVideoSrc(src);
                observerRef.current?.disconnect();
              }
            }
          });
        },
        { rootMargin, threshold }
      );

      observerRef.current.observe(node);

      return () => {
        observerRef.current?.disconnect();
      };
    }, [lazy, src, shouldLoad, rootMargin, threshold, delay]);

    const finalSrc = shouldLoad ? videoSrc : undefined;
    const hasPlaceholder = placeholderSrc && !finalSrc;

    return (
      <div style={{ position: "relative", width: "100%", height: "100%", ...style }}>
        {hasPlaceholder && (
          <img
            src={placeholderSrc}
            alt=""
            loading="lazy"
            decoding="async"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              objectFit: "cover",
              zIndex: 1,
            }}
          />
        )}
        <video
          ref={videoRef}
          src={finalSrc}
          poster={hasPlaceholder ? undefined : poster}
          preload={lazy && !finalSrc ? "none" : props.preload || "metadata"}
          style={{
            position: hasPlaceholder ? "absolute" : "relative",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex: hasPlaceholder ? 2 : "auto",
            ...style,
          }}
          {...props}
        />
      </div>
    );
  }
);

LazyVideo.displayName = "LazyVideo";

export default LazyVideo;
