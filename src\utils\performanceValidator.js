/**\n * Performance Validation Utility\n * Tests homepage performance against strict budgets\n */\n\nclass PerformanceValidator {\n  constructor() {\n    this.metrics = {\n      lcp: null,\n      fcp: null,\n      cls: null,\n      fid: null,\n      ttfb: null,\n      videoStartTime: null,\n      totalRequests: 0,\n      totalBytes: 0,\n      videoRequests: 0,\n      offscreenVideoRequests: 0\n    };\n    \n    this.budgets = {\n      lcp: 1500, // 1.5s\n      fcp: 1000, // 1s\n      videoStart: 2000, // 2s\n      totalRequests: 15,\n      totalBytes: 2 * 1024 * 1024, // 2MB\n      videoRequests: 1\n    };\n    \n    this.violations = [];\n    this.startTime = performance.now();\n    \n    this.initValidation();\n  }\n\n  initValidation() {\n    // Core Web Vitals monitoring\n    this.observeWebVitals();\n    \n    // Resource monitoring\n    this.observeResources();\n    \n    // Video-specific monitoring\n    this.observeVideoLoading();\n    \n    // Generate report after 5 seconds\n    setTimeout(() => {\n      this.generateValidationReport();\n    }, 5000);\n  }\n\n  observeWebVitals() {\n    // LCP (Largest Contentful Paint)\n    new PerformanceObserver((list) => {\n      const entries = list.getEntries();\n      const lastEntry = entries[entries.length - 1];\n      this.metrics.lcp = lastEntry.startTime;\n      \n      if (this.metrics.lcp > this.budgets.lcp) {\n        this.addViolation('LCP_EXCEEDED', {\n          current: this.metrics.lcp,\n          budget: this.budgets.lcp\n        });\n      }\n    }).observe({ entryTypes: ['largest-contentful-paint'] });\n\n    // FCP (First Contentful Paint)\n    new PerformanceObserver((list) => {\n      const entries = list.getEntries();\n      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n      if (fcpEntry) {\n        this.metrics.fcp = fcpEntry.startTime;\n        \n        if (this.metrics.fcp > this.budgets.fcp) {\n          this.addViolation('FCP_EXCEEDED', {\n            current: this.metrics.fcp,\n            budget: this.budgets.fcp\n          });\n        }\n      }\n    }).observe({ entryTypes: ['paint'] });\n\n    // CLS (Cumulative Layout Shift)\n    new PerformanceObserver((list) => {\n      let clsValue = 0;\n      for (const entry of list.getEntries()) {\n        if (!entry.hadRecentInput) {\n          clsValue += entry.value;\n        }\n      }\n      this.metrics.cls = clsValue;\n    }).observe({ entryTypes: ['layout-shift'] });\n  }\n\n  observeResources() {\n    new PerformanceObserver((list) => {\n      for (const entry of list.getEntries()) {\n        this.metrics.totalRequests++;\n        this.metrics.totalBytes += entry.transferSize || entry.encodedBodySize || 0;\n        \n        // Check if it's a video request\n        if (this.isVideoRequest(entry)) {\n          this.metrics.videoRequests++;\n          \n          // Check if video is offscreen\n          if (this.isOffscreenVideo(entry)) {\n            this.metrics.offscreenVideoRequests++;\n            this.addViolation('OFFSCREEN_VIDEO_DOWNLOAD', {\n              resource: entry.name,\n              bytes: entry.transferSize || entry.encodedBodySize || 0\n            });\n          }\n        }\n        \n        // Check budgets\n        if (this.metrics.totalRequests > this.budgets.totalRequests) {\n          this.addViolation('REQUEST_BUDGET_EXCEEDED', {\n            current: this.metrics.totalRequests,\n            budget: this.budgets.totalRequests\n          });\n        }\n        \n        if (this.metrics.totalBytes > this.budgets.totalBytes) {\n          this.addViolation('BYTES_BUDGET_EXCEEDED', {\n            current: this.formatBytes(this.metrics.totalBytes),\n            budget: this.formatBytes(this.budgets.totalBytes)\n          });\n        }\n        \n        if (this.metrics.videoRequests > this.budgets.videoRequests) {\n          this.addViolation('VIDEO_COUNT_EXCEEDED', {\n            current: this.metrics.videoRequests,\n            budget: this.budgets.videoRequests\n          });\n        }\n      }\n    }).observe({ entryTypes: ['resource'] });\n  }\n\n  observeVideoLoading() {\n    // Monitor when first video starts playing\n    const videos = document.querySelectorAll('video');\n    videos.forEach((video, index) => {\n      const startTime = performance.now();\n      \n      const handlePlay = () => {\n        if (index === 0 && !this.metrics.videoStartTime) { // First video only\n          this.metrics.videoStartTime = performance.now() - this.startTime;\n          \n          if (this.metrics.videoStartTime > this.budgets.videoStart) {\n            this.addViolation('VIDEO_START_EXCEEDED', {\n              current: this.metrics.videoStartTime,\n              budget: this.budgets.videoStart\n            });\n          }\n        }\n        \n        video.removeEventListener('play', handlePlay);\n      };\n      \n      video.addEventListener('play', handlePlay);\n    });\n  }\n\n  isVideoRequest(entry) {\n    const url = entry.name.toLowerCase();\n    return url.includes('.mp4') || \n           url.includes('.webm') || \n           url.includes('.mov') ||\n           entry.initiatorType === 'video';\n  }\n\n  isOffscreenVideo(entry) {\n    // Simple heuristic: videos not in hero section are considered offscreen\n    const url = entry.name;\n    const heroVideoPatterns = [\n      'hero', 'banner', 'main'\n    ];\n    \n    return !heroVideoPatterns.some(pattern => \n      url.toLowerCase().includes(pattern.toLowerCase())\n    );\n  }\n\n  addViolation(type, data) {\n    const violation = {\n      type,\n      timestamp: Date.now(),\n      timeSinceStart: performance.now() - this.startTime,\n      ...data\n    };\n    \n    this.violations.push(violation);\n    console.error(`🚨 PERFORMANCE VIOLATION: ${type}`, violation);\n  }\n\n  formatBytes(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  generateValidationReport() {\n    const report = {\n      timestamp: new Date().toISOString(),\n      metrics: this.metrics,\n      budgets: this.budgets,\n      violations: this.violations,\n      passed: this.violations.length === 0,\n      summary: {\n        lcp: this.metrics.lcp ? `${Math.round(this.metrics.lcp)}ms` : 'N/A',\n        fcp: this.metrics.fcp ? `${Math.round(this.metrics.fcp)}ms` : 'N/A',\n        videoStart: this.metrics.videoStartTime ? `${Math.round(this.metrics.videoStartTime)}ms` : 'N/A',\n        requests: `${this.metrics.totalRequests}/${this.budgets.totalRequests}`,\n        bytes: `${this.formatBytes(this.metrics.totalBytes)}/${this.formatBytes(this.budgets.totalBytes)}`,\n        videos: `${this.metrics.videoRequests}/${this.budgets.videoRequests}`\n      }\n    };\n\n    console.log('📊 PERFORMANCE VALIDATION REPORT:', report);\n    \n    if (report.passed) {\n      console.log('✅ All performance budgets passed!');\n    } else {\n      console.error('❌ Performance validation failed');\n      console.error('Violations:', this.violations);\n    }\n\n    return report;\n  }\n\n  // Manual validation trigger\n  validate() {\n    return this.generateValidationReport();\n  }\n}\n\n// Export singleton\nconst performanceValidator = new PerformanceValidator();\n\n// Expose to window for manual testing\nif (typeof window !== 'undefined') {\n  window.performanceValidator = performanceValidator;\n}\n\nexport default performanceValidator;"
