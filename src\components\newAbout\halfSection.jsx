// import React, { useEffect, useRef, useState } from "react";
// const video1 =
//   process.env.PUBLIC_URL + "/assets/header-videos/about_header.mp4";
// const video2 =
//   process.env.PUBLIC_URL + "/assets/header-videos/careers-video.MP4";
// const video3 =
//   process.env.PUBLIC_URL + "/assets/header-videos/contact-video.MP4";
// const video4 = process.env.PUBLIC_URL + "/assets/header-videos/work-video.MP4";

// // Individual project card component
// function ProjectCard({ project, index, total }) {
//   // Split title to get last 2-3 letters for overlay
//   const titleLength = project.title.length;
//   const overlapChars = Math.min(3, Math.ceil(titleLength * 0.15));
//   const titleBeforeOverlap = project.title.slice(0, -overlapChars);
//   const titleOverlap = project.title.slice(-overlapChars);

//   return (
//     <div
//       style={{
//         minWidth: "100vw",
//         height: "100vh",
//         display: "flex",
//         flexDirection: "column",
//         alignItems: "center",
//         justifyContent: "center",
//         flexShrink: 0,
//         backgroundColor: "#000",
//         position: "relative",
//         padding: "0 8rem",
//       }}
//     >
//       {/* Label - Top Left */}
//       <div
//         style={{
//           position: "absolute",
//           top: "12%",
//           left: "8rem",
//           padding: "0.5rem 1rem",
//           fontSize: "0.8rem",
//           letterSpacing: "0.12em",
//           textTransform: "uppercase",
//           backgroundColor: "rgba(255,255,255,0.08)",
//           borderRadius: "999px",
//           border: "1px solid rgba(255,255,255,0.15)",
//           color: "#f5f5f5",
//           zIndex: 10,
//         }}
//       >
//         {project.label}
//       </div>

//       {/* Main Content Area */}
//       <div
//         style={{
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "center",
//           width: "100%",
//           maxWidth: "1400px",
//           position: "relative",
//         }}
//       >
//         {/* Title - Left of Video with Overlap */}
//         <div
//           style={{
//             position: "relative",
//             zIndex: 5,
//             marginLeft: "-2rem",
//           }}
//         >
//           <h1
//             style={{
//               fontFamily: "Formula Bold",
//               fontSize: "clamp(3rem, 5vw, 6rem)",
//               fontWeight: "bold",
//               textTransform: "uppercase",
//               lineHeight: 0.9,
//               color: "#fff",
//               margin: 0,
//               whiteSpace: "nowrap",
//             }}
//           >
//             {titleBeforeOverlap}
//             <span
//               style={{
//                 position: "relative",
//                 zIndex: 15,
//                 background:
//                   "linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.4) 30%, rgba(0,0,0,0.4) 70%, rgba(0,0,0,0) 100%)",
//                 padding: "0 0.2em",
//               }}
//             >
//               {titleOverlap}
//             </span>
//           </h1>
//         </div>

//         {/* Video Container - Center */}
//         <div
//           style={{
//             position: "relative",
//             width: "500px",
//             height: "500px",
//             flexShrink: 0,
//             zIndex: 10,
//           }}
//         >
//           {/* Glow effect */}
//           <div
//             style={{
//               position: "absolute",
//               inset: "-20%",
//               borderRadius: "24px",
//               background: `radial-gradient(circle at 50% 50%, ${project.accentColor}40, transparent 70%)`,
//               opacity: 0.6,
//               zIndex: 0,
//               filter: "blur(40px)",
//             }}
//           />

//           {/* Video */}
//           <div
//             style={{
//               position: "relative",
//               width: "100%",
//               height: "100%",
//               borderRadius: "20px",
//               overflow: "hidden",
//               zIndex: 1,
//               boxShadow: "0 20px 60px rgba(0,0,0,0.5)",
//             }}
//           >
//             <video
//               src={project.videoSrc}
//               autoPlay
//               muted
//               loop
//               playsInline
//               style={{
//                 width: "100%",
//                 height: "100%",
//                 objectFit: "cover",
//                 filter: "brightness(.65) contrast(1.3) saturate(1.1)",
//               }}
//             />

//             {/* Cinematic overlay */}
//             <div
//               style={{
//                 position: "absolute",
//                 inset: 0,
//                 background:
//                   "linear-gradient(180deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%)",
//                 mixBlendMode: "multiply",
//                 opacity: 0.4,
//               }}
//             />

//             {/* Floating accent pill */}
//             <div
//               style={{
//                 position: "absolute",
//                 bottom: 16,
//                 left: 16,
//                 padding: "0.35rem 0.8rem",
//                 borderRadius: 999,
//                 fontSize: "0.7rem",
//                 letterSpacing: "0.14em",
//                 textTransform: "uppercase",
//                 background:
//                   "linear-gradient(120deg, rgba(0,0,0,0.85), rgba(0,0,0,0.3))",
//                 border: "1px solid rgba(255,255,255,0.2)",
//                 color: "#f5f5f5",
//                 zIndex: 3,
//                 backdropFilter: "blur(10px)",
//                 fontFamily: "Anton",
//               }}
//             >
//               {project.title} • CREATIVE AGENCY
//             </div>
//           </div>
//         </div>

//         {/* Description - Bottom Right of Video */}
//         <div
//           style={{
//             position: "absolute",
//             bottom: "-8rem",
//             right: "0",
//             maxWidth: "420px",
//             zIndex: 5,
//             fontFamily: "Anton",
//           }}
//         >
//           <p
//             style={{
//               fontSize: "1.1rem",
//               color: "#c9c9c9",
//               lineHeight: 1.7,
//               margin: 0,
//             }}
//           >
//             {project.description}
//           </p>
//         </div>
//       </div>

//       {/* Progress indicator */}
//       <div
//         style={{
//           position: "absolute",
//           bottom: "3rem",
//           left: "8rem",
//           fontSize: "0.85rem",
//           color: "#666",
//           letterSpacing: "0.1em",
//         }}
//       >
//         [{index + 1}/{total}]
//       </div>

//       {/* View all projects link */}
//       {index === total - 1 && (
//         <div
//           style={{
//             position: "absolute",
//             bottom: "3rem",
//             right: "8rem",
//             fontSize: "0.85rem",
//             color: "#fff",
//             letterSpacing: "0.05em",
//             cursor: "pointer",
//             display: "flex",
//             alignItems: "center",
//             gap: "0.5rem",
//             transition: "opacity 0.3s",
//           }}
//           onMouseEnter={(e) => (e.currentTarget.style.opacity = "0.7")}
//           onMouseLeave={(e) => (e.currentTarget.style.opacity = "1")}
//         >
//           VIEW ALL PROJECTS →
//         </div>
//       )}
//     </div>
//   );
// }

// // Project data based on your specifications
// const projects = [
//   {
//     label: "01 • Strategy & Story",
//     title: "Pre-production",
//     description:
//       "We start with questions, not cameras. Research, strategy, and concept development come together to build a narrative that actually means something to your audience. From moodboards and scripts to casting and logistics, we engineer every detail before the first light turns on.",
//     videoSrc: video1,
//     accentColor: "#DB4A41",
//   },
//   {
//     label: "02 • On Set",
//     title: "Production execution",
//     description:
//       "Lights, camera, controlled chaos. Our production crews move like a live performance—camera, sound, art direction, and talent all in sync. Whether it's a fast-paced social shoot or a cinematic brand film, we create sets that feel alive and stories that feel human.",
//     videoSrc: video2,
//     accentColor: "#DB4A41",
//   },
//   {
//     label: "03 • Edit, Grade, Design",
//     title: "Post production",
//     description:
//       "This is where everything clicks. Editing, sound design, VFX, color grading, and motion graphics come together to create a final piece that feels intentional in every frame. We polish, refine, and iterate until the story hits exactly the way it should.",
//     videoSrc: video3,
//     accentColor: "#DB4A41",
//   },
//   {
//     label: "04 • Identity & Systems",
//     title: "Branding",
//     description:
//       "We design brands that live beyond a logo. Naming, visual systems, tone of voice, and campaigns that work across screens, cities, and cultures. Every element is built to be recognisable, ownable, and hard to get out of your head.",
//     videoSrc: video4,
//     accentColor: "#DB4A41",
//   },
//   {
//     label: "05 • Always-On",
//     title: "Social media retainer",
//     description:
//       "Content that doesn't feel like content. We build long-term calendars, formats, and stories that let your brand show up every day with personality. From snackable videos to episodic series, we keep your brand in the conversation—not just in the feed.",
//     videoSrc: video1,
//     accentColor: "#DB4A41",
//   },
// ];

// // Main horizontal scroll component
// export default function HorizontalScrollSection() {
//   const containerRef = useRef(null);
//   const trackRef = useRef(null);
//   const [scrollProgress, setScrollProgress] = useState(0);

//   useEffect(() => {
//     const handleScroll = () => {
//       const container = containerRef.current;
//       const track = trackRef.current;
//       if (!container || !track) return;

//       const containerTop = container.offsetTop;
//       const containerHeight = container.offsetHeight;
//       const scrollY = window.scrollY;
//       const windowHeight = window.innerHeight;

//       // Calculate how far we've scrolled into the container
//       const scrollStart = containerTop;
//       const scrollEnd = containerTop + containerHeight - windowHeight;
//       const scrollRange = scrollEnd - scrollStart;
//       const scrolled = scrollY - scrollStart;

//       // Calculate progress (0 to 1)
//       const progress = Math.max(0, Math.min(1, scrolled / scrollRange));
//       setScrollProgress(progress);

//       // Calculate horizontal scroll amount
//       const maxScroll = track.scrollWidth - track.parentElement.clientWidth;
//       const horizontalScroll = progress * maxScroll;

//       // Apply horizontal scroll
//       track.style.transform = `translateX(-${horizontalScroll}px)`;
//     };

//     window.addEventListener("scroll", handleScroll, { passive: true });
//     handleScroll(); // Initial calculation

//     return () => window.removeEventListener("scroll", handleScroll);
//   }, []);

//   // Calculate the height needed for the scroll effect
//   const scrollHeight = `${250 * projects.length}vh`;

//   return (
//     <>
//       {/* Horizontal scroll section */}
//       <div
//         ref={containerRef}
//         style={{
//           height: scrollHeight,
//           position: "relative",
//           backgroundColor: "#000",
//         }}
//       >
//         {/* Sticky container that holds the horizontal scroller */}
//         <div
//           style={{
//             position: "sticky",
//             top: 0,
//             height: "100vh",
//             width: "100vw",
//             overflow: "hidden",
//             backgroundColor: "#000",
//           }}
//         >
//           {/* Horizontal scrolling track */}
//           <div
//             ref={trackRef}
//             style={{
//               display: "flex",
//               height: "100%",
//               width: `${projects.length * 100}vw`,
//               willChange: "transform",
//             }}
//           >
//             {projects.map((project, index) => (
//               <ProjectCard
//                 key={index}
//                 project={project}
//                 index={index}
//                 total={projects.length}
//               />
//             ))}
//           </div>
//         </div>

//         {/* Progress bar */}
//         <div
//           style={{
//             position: "fixed",
//             top: 0,
//             left: 0,
//             width: "100%",
//             height: "2px",
//             backgroundColor: "rgba(255,255,255,0.1)",
//             zIndex: 1000,
//           }}
//         >
//           <div
//             style={{
//               height: "100%",
//               width: `${scrollProgress * 100}%`,
//               backgroundColor: "#DB4A41",
//               transition: "width 0.05s linear",
//             }}
//           />
//         </div>
//       </div>
//     </>
//   );
// }
import React, { useEffect, useRef, useState } from "react";

const video1 =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/works/videos/1767018143288_Mustafa_Turk.mp4";
const video2 =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/works/videos/1767183610621_Car_Scene.mp4";
const video3 =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/works/videos/CUT%203.mp4";
const video4 =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/works/videos/1763463588842_Horizontal%202.mp4";
const video5 =
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/works/videos/1767001151205_Reel_3_Graded_2.mp4";

/* -------------------- PROJECT CARD -------------------- */

function ProjectCard({ project, index, total }) {
  const overlapChars = 3;
  const titleBeforeOverlap = project.title.slice(0, -overlapChars);
  const titleOverlap = project.title.slice(-overlapChars);

  return (
    <div
      className="project-card"
      style={{
        minWidth: "100vw",
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#000",
        position: "relative",
        padding: "0 2rem",
      }}
    >
      {/* Label above video (center) */}
      <div
        className="project-label"
        style={{
          position: "absolute",
          top: "25%",
          left: "16.4%",
          transform: "translateX(-50%)",
          padding: "0.5rem 1rem",
          fontSize: "0.8rem",
          letterSpacing: "0.12em",
          textTransform: "uppercase",
          color: "#f5f5f5",
          zIndex: 10,
        }}
      >
        {project.label}
      </div>

      {/* Title on left with last 3 letters overlaying video */}
      <h1
        className="project-title"
        style={{
          position: "absolute",
          left: "12%",
          top: "35%",
          transform: "translateY(-50%)",
          fontFamily: "Formula Bold",
          fontSize: "clamp(3rem, 5vw, 6rem)",
          textTransform: "uppercase",
          color: "#fff",
          margin: 0,
          whiteSpace: "nowrap",
          zIndex: 10,
          pointerEvents: "none",
          mixBlendMode: "difference",
        }}
      >
        {titleBeforeOverlap}
        <span
          style={{
            padding: "0 .0em",
            display: "inline-block",
            color: "#fff",
            fontFamily: "Formula Bold",
            whiteSpace: "nowrap",
            textAlign: "center",
          }}
        >
          {titleOverlap}
        </span>
      </h1>

      {/* VIDEO CENTER */}
      <div
        className="video-container"
        style={{
          width: "800px",
          height: "500px",
          borderRadius: "2px",
          overflow: "hidden",
          position: "relative",
          boxShadow: "0 30px 70px rgba(0,0,0,0.6)",
        }}
      >
        <video
          src={project.videoSrc}
          autoPlay
          muted
          loop
          playsInline
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            filter: "brightness(.85)",
          }}
        />
      </div>

      {/* DESCRIPTION bottom-right of video */}
      <p
        className="project-description"
        style={{
          position: "absolute",
          bottom: "22%",
          right: "10%",
          maxWidth: "360px",
          fontSize: "1rem",
          lineHeight: 1.6,
          color: "lightgray",
          fontFamily: "Anton",
        }}
      >
        {project.description}
      </p>

      {/* PROGRESS */}
      <div
        className="project-progress"
        style={{
          position: "absolute",
          bottom: "3rem",
          left: "8rem",
          fontSize: "0.85rem",
          color: "#fff",
          letterSpacing: "0.1em",
        }}
      >
        [{index + 1}/{total}]
      </div>

      {/* CTA on last project */}
      {index === total - 1 && (
        <div
          className="project-cta"
          style={{
            position: "absolute",
            bottom: "3rem",
            right: "8rem",
            fontSize: "0.85rem",
            color: "#fff",
            letterSpacing: "0.05em",
            cursor: "pointer",
            transition: ".3s",
          }}
          onMouseEnter={(e) => (e.currentTarget.style.opacity = "0.7")}
          onMouseLeave={(e) => (e.currentTarget.style.opacity = "1")}
        >
          VIEW ALL PROJECTS →
        </div>
      )}
    </div>
  );
}

/* -------------------- PROJECT DATA -------------------- */

const projects = [
  {
    label: "01 • Strategy & Story",
    title: "Pre-production",
    description:
      "We start with questions, not cameras. Research, strategy, and concept development come together to build a narrative that actually means something to your audience.",
    videoSrc: video1,
    accentColor: "#DB4A41",
  },
  {
    label: "02 • On Set",
    title: "Production execution",
    description:
      "Lights, camera, controlled chaos. Our production crews move like a live performance—camera, sound, art direction, and talent all in sync.",
    videoSrc: video2,
    accentColor: "#DB4A41",
  },
  {
    label: "03 • Edit, Grade, Design",
    title: "Post production",
    description:
      "Editing, sound design, VFX, color grading, and motion graphics come together to create a final piece that feels intentional in every frame.",
    videoSrc: video3,
    accentColor: "#DB4A41",
  },
  {
    label: "04 • Identity & Systems",
    title: "Branding",
    description:
      "We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    videoSrc: video4,
    accentColor: "#DB4A41",
  },
  {
    label: "05 • Always-On",
    title: "Social media retainer",
    description:
      "Content that doesn't feel like content. We help brands show up every day with personality, not just posts.",
    videoSrc: video5,
    accentColor: "#DB4A41",
  },
];

/* --------------- MAIN HORIZONTAL SCROLL COMPONENT --------------- */

export default function HorizontalScrollSection() {
  const containerRef = useRef(null);
  const trackRef = useRef(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const container = containerRef.current;
      const track = trackRef.current;
      if (!container || !track) return;

      const containerTop = container.offsetTop;
      const containerHeight = container.offsetHeight;
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      const scrollStart = containerTop;
      const scrollEnd = containerTop + containerHeight - windowHeight;
      const scrollRange = scrollEnd - scrollStart;
      const scrolled = scrollY - scrollStart;
      const progress = Math.max(0, Math.min(1, scrolled / scrollRange));
      setScrollProgress(progress);

      const maxScroll = track.scrollWidth - track.parentElement.clientWidth;
      const horizontalScroll = progress * maxScroll;
      track.style.transform = `translateX(-${horizontalScroll}px)`;
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollHeight = `${250 * projects.length}vh`;

  return (
    <>
      <style>{`
        /* Tablet styles */
        @media (max-width: 1024px) {
          .project-card {
            flex-direction: column;
            justify-content: flex-start !important;
            padding: 3rem 2rem !important;
            margin-top: 10rem !important;
          }
          
          .project-label {
            top: 1% !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            font-size: 0.7rem !important;
          }
          
          .project-title {
            position: relative !important;
            left: auto !important;
            top: auto !important;
            transform: none !important;
            text-align: left !important;
            font-size: clamp(2rem, 6vw, 3rem) !important;
            margin: 1rem 0 2rem 0 !important;
            width: 100%;
          }
          
          .video-container {
            width: 85vw !important;
            height: 50vh !important;
            max-width: 600px !important;
            margin: 0 auto !important;
          }
          
          .project-description {
            position: relative !important;
            bottom: auto !important;
            right: auto !important;
            max-width: 85vw !important;
            margin: 2rem auto 0 !important;
            text-align: center !important;
            font-size: 0.9rem !important;
          }
          
          .project-progress {
            bottom: 2rem !important;
            left: 2rem !important;
            font-size: 0.75rem !important;
          }
          
          .project-cta {
            bottom: 2rem !important;
            right: 2rem !important;
            font-size: 0.75rem !important;
          }
        }
        
        /* Mobile styles */
        @media (max-width: 768px) {
          .project-card {
            padding: 2rem 1rem !important;
            
          }
          
          .project-label {
            top: 1% !important;
            font-size: 0.65rem !important;
            padding: 0.3rem 0.8rem !important;
          }
          
          .project-title {
            font-size: clamp(1.5rem, 8vw, 2.5rem) !important;
            margin: 0.5rem 0 1.5rem 0 !important;
          }
          
          .video-container {
            width: 90vw !important;
            height: 40vh !important;
            max-width: 500px !important;
          }
          
          .project-description {
            max-width: 90vw !important;
            margin: 1.5rem auto 0 !important;
            font-size: 0.85rem !important;
            line-height: 1.5 !important;
          }
          
          .project-progress {
            bottom: 1.5rem !important;
            left: 1rem !important;
            font-size: 0.7rem !important;
          }
          
          .project-cta {
            bottom: 1.5rem !important;
            right: 1rem !important;
            font-size: 0.7rem !important;
          }
        }
      `}</style>

      {/* Horizontal scroll section */}
      <div
        ref={containerRef}
        style={{
          height: scrollHeight,
          position: "relative",
          backgroundColor: "#000",
          width: "100vw",
        }}
      >
        {/* Sticky container that holds the horizontal scroller */}
        <div
          style={{
            position: "sticky",
            top: 0,
            height: "100vh",
            width: "100vw",
            overflow: "hidden",
            backgroundColor: "#000",
            zIndex: 1,
          }}
        >
          {/* Horizontal scrolling track */}
          <div
            ref={trackRef}
            style={{
              display: "flex",
              height: "100%",
              width: `${projects.length * 100}vw`,
              willChange: "transform",
            }}
          >
            {projects.map((project, index) => (
              <ProjectCard
                key={index}
                project={project}
                index={index}
                total={projects.length}
              />
            ))}
          </div>
        </div>

        {/* Progress bar */}
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "2px",
            backgroundColor: "rgba(255,255,255,0.1)",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              height: "100%",
              width: `${scrollProgress * 100}%`,
              backgroundColor: "#DB4A41",
              transition: "width 0.05s linear",
            }}
          />
        </div>
      </div>
    </>
  );
}
