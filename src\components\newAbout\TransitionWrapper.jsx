import React, { useRef } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";

/**
 * TransitionWrapper creates a smooth transition between ProjectCard and SoftwareHouseSection
 * - Creates scroll space for the transition (200vh)
 * - Transitions background from black to white
 * - Uses scroll position to fade out ProjectCard and fade in SoftwareHouseSection
 */
export default function TransitionWrapper({ children }) {
  const containerRef = useRef(null);

  const transitionHeight = "200vh";

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    mass: 0.5,
  });

  const backgroundColor = useTransform(
    smoothProgress,
    [0, 1],
    ["rgb(0, 0, 0)", "rgb(250, 249, 246)"]
  );

  const fadeIn = useTransform(smoothProgress, [0.5, 1], [0, 1]);

  return (
    <div
      ref={containerRef}
      style={{ height: transitionHeight, position: "relative" }}
    >
      <motion.div
        style={{
          position: "sticky",
          top: 0,
          height: "100vh",
          width: "100vw",
          backgroundColor,
          zIndex: 100,
          overflow: "hidden",
        }}
      >
        <motion.div
          style={{
            opacity: fadeIn,
            pointerEvents: fadeIn < 0.1 ? "none" : "auto",
          }}
        >
          {children}
        </motion.div>
      </motion.div>
    </div>
  );
}
