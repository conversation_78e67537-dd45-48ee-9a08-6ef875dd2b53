import React from "react";

/**
 * LoadingSpinner - Reusable loading spinner component
 * Used for videos and images during loading states
 */
const LoadingSpinner = ({ size = 40, color = "#fff", style = {} }) => {
  return (
    <div
      style={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        zIndex: 10,
        ...style,
      }}
    >
      <div
        style={{
          border: `3px solid rgba(255, 255, 255, 0.3)`,
          borderTop: `3px solid ${color}`,
          borderRadius: "50%",
          width: `${size}px`,
          height: `${size}px`,
          animation: "spin 1s linear infinite",
        }}
      />
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

/**
 * ImagePlaceholder - Skeleton placeholder for images
 * Maintains aspect ratio and prevents layout shift
 */
export const ImagePlaceholder = ({ 
  width = "100%", 
  height = "100%", 
  style = {},
  showSpinner = true 
}) => {
  return (
    <div
      style={{
        width,
        height,
        backgroundColor: "#1a1a1a",
        borderRadius: "20px",
        position: "relative",
        overflow: "hidden",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        ...style,
      }}
    >
      {showSpinner && <LoadingSpinner size={30} color="#db4a41" />}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: "-100%",
          width: "100%",
          height: "100%",
          background: "linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent)",
          animation: "shimmer 1.5s infinite",
        }}
      />
      <style>
        {`
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingSpinner;

