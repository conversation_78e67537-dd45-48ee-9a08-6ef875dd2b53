@media screen and (max-width: 768px) {
  .btn:not(.btn-hover-alt, .link-btn) {
    display: none;
  }

  .nav-drawer-flex {
    & .MuiListItemText-primary {
      margin-bottom: -30px;
    }

    & .MuiTypography-h2 {
      font-size: 3rem;
      margin-top: 3rem;
    }

    & .drawer-social-media {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
    }
  }

  .hero-container {
    padding: 0 5px;
    height: 40vh;
  }

  .hero-container .hero-text {
    width: 70%;
    font-size: 1.5rem;
    letter-spacing: 1.5px;
    text-align: center;
    margin-top: 5rem;
  }

  .client-section .client-section-text {
    width: 100%;
  }

  .client-section .client-title {
    font-size: 2rem;
  }

  .client-list {
    grid-template-columns: repeat(4, 1fr); /* 4 items per row */
    justify-items: center;
    gap: 5px;
  }

  .client-list .client-item {
    width: 80px;
  }

  /* .numbers-list .numbers-item */
  .numbers-list .numbers-item {
    justify-content: space-between;
    gap: 0;
  }

  /* .people-section */
  .people-section {
    flex-direction: column;

    & .team-info-container {
      display: none;
    }

    & .people-list {
      margin: 0;
    }

    & .people-item {
      padding: 10px 0;
      margin: 10px;

      & h5 {
        font-size: 1.5rem;
      }

      & p {
        font-size: 0.6rem;
      }
    }
  }

  .service-item {
    flex-direction: column !important;
  }

  .desktop-teamPhoto {
    display: none;
  }

  .mobile-teamPhoto {
    display: block;
  }

  .slick-slide div {
    padding: 10px;
  }

  .slick-slide > div {
    padding: 0;
    padding: 0 15px 0 15px;
    height: 100%;
  }

  .testimonial-card .testimonial-comment {
    font-size: 16px;
  }

  .iftar-form .locationCapture {
    flex-direction: column;
    gap: 10px;
  }

  .iftar-form .locationCapture button {
    width: 99%;
    font-size: 12px;
  }

  .event-page .event-title {
    font-size: 3rem;
  }

  .event-page .event-location,
  .event-page .event-date {
    font-size: 16px;
  }

  .event-page .event-info-container {
    gap: 50px;
    flex-direction: column;
  }

  .gff-teaser-container {
    background-image: url("/assets/gff-mobile.webp");
  }

  .gff-teaser-text {
    font-size: 4.5rem !important;
  }
  .content__img {
    max-width: 50vw;
    border-width: 2.5px;
  }

  .content__img-inner {
    top: -8px;
    left: -8px;
  }
  .services-section {
    flex-direction: column;
    padding: 0 20px;
    height: auto;
    margin: 10rem 0 5rem 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .video-column,
  .text-column {
    width: 80%;
    margin: 0 auto;
  }

  .section-block {
    height: auto;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    width: 100%;
    background-color: transparent;
  }

  .section-block h2 {
    font-size: 2.5rem;
  }

  .section-block ul {
    font-size: 1.6rem;
  }
  .gallery-grid-event {
    grid-template-columns: repeat(1, 1fr); /* Tablets */
  }
  .gallery-item-event img {
    height: 250px;
  }
  .insights-grid {
    grid-template-columns: 1fr;
  }
  .reel-container {
    flex-direction: column;
    gap: 20px;
    padding: 0 10px;
    margin-bottom: -1px;
    margin-top: 5rem;
  }
  .thumbnail-container {
    margin-top: 3rem;
    grid-template-columns: repeat(3, 1fr);
  }

  .gallery-grid {
    gap: 15px;
  }

  .item-1,
  .item-2,
  .item-3,
  .item-4,
  .item-5,
  .item-6,
  .item-7,
  .item-8,
  .item-9,
  .item-10,
  .item-11 {
    height: 100% !important;
  }

  .title-overlay {
    font-size: 1rem; /* scale text down a bit */
  }
}

/* Extra small (like phones <480px) */
@media (max-width: 480px) {
  .gallery-grid {
    transform: scale(0.9);
    padding: 0;
    margin: 0 auto;
  }
}
/* Tablet layout: row direction but smaller scale */
@media (max-width: 1024px) and (min-width: 768px) {
  .reel-container {
    flex-direction: row; /* stay in row */
    gap: 40px; /* reduce big gap */
    max-width: 100%; /* allow full width */
    padding: 10px;
  }

  .iphone-wrapper {
    transform: scale(0.8); /* shrink overall size */
    transform-origin: center; /* keep it centered */
  }

  .thumbnail-container {
    grid-template-columns: repeat(4, 1fr); /* tighten thumbnails */
    gap: 10px;
  }

  .thumbnail-item {
    width: 80px;
    height: 120px;
  }
}
/* Tablet layout: row direction but smaller scale */
@media (max-width: 1024px) and (min-width: 768px) {
  .reel-container {
    flex-direction: row; /* stay in row */
    gap: 40px; /* reduce big gap */
    max-width: 100%; /* allow full width */
    padding: 10px;
  }

  .iphone-wrapper {
    transform: scale(0.7) !important; /* shrink overall size */
    transform-origin: center; /* keep it centered */
  }

  .thumbnail-container {
    grid-template-columns: repeat(4, 1fr); /* tighten thumbnails */
    gap: 10px;
  }

  .thumbnail-item {
    width: 70px;
    height: 90px;
  }
}
@media (max-width: 820px) {
  .iphone-wrapper {
    transform: scale(0.7);
  }
}
/* ===== Tablet (landscape & portrait) ===== */
@media (max-width: 1024px) and (min-width: 768px) {
  .reel-container {
    flex-direction: row;
    gap: 40px;
    max-width: 100%;
    padding: 10px;
  }

  .iphone-wrapper {
    transform: scale(0.8);
    transform-origin: center;
  }

  .thumbnail-container {
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
  }

  .thumbnail-item {
    width: 80px;
    height: 120px;
  }
}

/* ===== Mobile (phones) ===== */
@media (max-width: 767px) {
  .reel-container {
    flex-direction: column; /* stack vertically to fit narrow screens */
    gap: 20px;
    padding: 10px;
    max-width: 100%;
  }

  .iphone-wrapper {
    transform: scale(0.65) !important; /* smaller scale for phone width */
    transform-origin: center;
  }

  .thumbnail-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .thumbnail-item {
    width: 60px;
    height: 80px;
  }
}
