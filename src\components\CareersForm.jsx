import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import <PERSON> from "./Hero";
import { Box, Typography, List, ListItem, ListItemText } from "@mui/material";

const FileInput = ({ field, form, ...props }) => {
  const handleChange = (e) => {
    const file = e.currentTarget.files[0];
    form.setFieldValue(field.name, file);
  };
  return <input type="file" onChange={handleChange} {...props} />;
};

// Function to POST the application
const submitJobApplication = async (values, JobId) => {
  try {
    const formData = new FormData();
    formData.append("JobId", JobId); // must match backend exactly
    formData.append("fullName", values.fullName);
    formData.append("email", values.email);
    formData.append("phone", values.phone);
    formData.append("resume", values.resume);

    // Debug: Log all form data values
    console.log("📦 Sending application data:");
    for (let pair of formData.entries()) {
      console.log(pair[0], ":", pair[1]);
    }

    const response = await fetch(
      "https://youngproductions-768ada043db3.herokuapp.com/api/jobApplications",
      {
        method: "POST",
        body: formData,
      }
    );

    const data = await response.json();

    console.log("✅ Server response:", data);

    if (!response.ok) {
      throw new Error(data.error || "Failed to submit application");
    }

    alert("Application submitted successfully!");
  } catch (error) {
    console.error("❌ Error submitting application:", error);
    alert("An error occurred. Please try again.");
  }
};

const JobApplicationForm = ({
  JobId,
  positionName,
  description,
  requirements,
  responsibilities,
  benefits,
  department,
  jobType,
  workMode,
  experienceLevel,
  location,
  country,
  address,
  language,
  applicationDeadline,
  recruiterEmail,
}) => {
  return (
    <div>
      <Hero text="Job Application Form - " highlightText={positionName} />

      {/* Job details */}
      <Box sx={{ width: "70%", margin: "2% auto" }}>
        <Typography variant="h2" sx={{ fontFamily: "Formula Bold", mb: 2 }}>
          Job <span style={{ color: "#DB4A41" }}>Information</span>
        </Typography>

        <div
          style={{
            display: "flex",
            flexWrap: "wrap",
            justifyContent: "space-between",
            alignItems: "flex-start",
            gap: "20px",
            width: "100%",
          }}
        >
          {/* Left Column */}
          <div style={{ flex: "1", minWidth: "250px" }}>
            <Typography
              variant="h4"
              sx={{
                fontFamily: "Formula Bold",
                fontWeight: "bold",
                marginBottom: "10px",
                letterSpacing: "1px",
              }}
            >
              {positionName}
            </Typography>
            <Typography
              variant="h5"
              sx={{ fontFamily: "Anton", fontWeight: "lighter", mb: 1 }}
            >
              {department}
            </Typography>
            <Typography
              variant="h5"
              sx={{ fontFamily: "Anton", fontWeight: "lighter" }}
            >
              {location}, {country}
            </Typography>
          </div>

          {/* Right Column */}
          <div style={{ flex: "1", minWidth: "250px" }}>
            {[
              { label: "Job Type", value: jobType },
              { label: "Work Mode", value: workMode },
              { label: "Experience Level", value: experienceLevel },
              { label: "Address", value: address },
              {
                label: "Language",
                value: `${language?.language} (${language?.level})`,
              },
              {
                label: "Application Deadline",
                value: new Date(applicationDeadline).toLocaleDateString(),
              },
              { label: "Recruiter Email", value: recruiterEmail },
            ].map((item, idx) => (
              <div
                key={idx}
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginBottom: "6px",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{ fontFamily: "Anton", fontWeight: "lighter" }}
                >
                  {item.label}:
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "Anton",
                    fontWeight: "lighter",
                    color:
                      item.label === "Application Deadline"
                        ? "#DB4A41"
                        : "inherit",
                  }}
                >
                  {item.value}
                </Typography>
              </div>
            ))}
          </div>
        </div>

        {/* Optional sections */}
        {description && (
          <>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 3 }}>
              Job Description
            </Typography>
            <p
              style={{
                background: "black",
                color: "white",
                padding: "10px 15px",
                borderRadius: "5px",
                fontFamily: "Anton",
                fontSize: "1rem",
              }}
            >
              • {description}
            </p>
          </>
        )}

        {requirements?.length > 0 && (
          <>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 3 }}>
              Requirements
            </Typography>
            <List>
              {requirements.map((req, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primaryTypographyProps={{
                      fontFamily: "Anton",
                      fontSize: "1rem",
                    }}
                    primary={`• ${req}`}
                  />
                </ListItem>
              ))}
            </List>
          </>
        )}

        {responsibilities?.length > 0 && (
          <>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 3 }}>
              Responsibilities
            </Typography>
            <List>
              {responsibilities.map((res, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primaryTypographyProps={{
                      fontFamily: "Anton",
                      fontSize: "1rem",
                    }}
                    primary={`• ${res}`}
                  />
                </ListItem>
              ))}
            </List>
          </>
        )}

        {benefits?.length > 0 && (
          <>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 3 }}>
              Benefits
            </Typography>
            <List>
              {benefits.map((ben, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primaryTypographyProps={{
                      fontFamily: "Anton",
                      fontSize: "1rem",
                    }}
                    primary={`• ${ben}`}
                  />
                </ListItem>
              ))}
            </List>
          </>
        )}
      </Box>

      {/* Form */}
      <Formik
        initialValues={{
          fullName: "",
          email: "",
          phone: "",
          resume: null,
        }}
        validationSchema={Yup.object({
          fullName: Yup.string().required("Full Name is required"),
          email: Yup.string()
            .email("Invalid email address")
            .required("Email is required"),
          phone: Yup.string().required("Phone number is required"),
          resume: Yup.mixed()
            .required("Resume is required")
            .test(
              "fileSize",
              "File size is too large",
              (value) => value && value.size <= 1024 * 1024 * 5
            )
            .test(
              "fileFormat",
              "Unsupported file format",
              (value) =>
                value &&
                ["application/pdf", "image/jpeg", "image/png"].includes(
                  value.type
                )
            ),
        })}
        onSubmit={(values, { setSubmitting }) => {
          console.log("📝 Formik submit values:", values);
          submitJobApplication(values, JobId).finally(() => {
            setSubmitting(false);
          });
        }}
      >
        {({ isSubmitting }) => (
          <Form className="careers-form form">
            <h2>{positionName}</h2>
            <div className="form-group">
              <label htmlFor="fullName">Full Name</label>
              <Field className="form-input" type="text" name="fullName" />
              <ErrorMessage
                name="fullName"
                component="div"
                className="error-message"
              />
            </div>
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <Field className="form-input" type="email" name="email" />
              <ErrorMessage
                name="email"
                component="div"
                className="error-message"
              />
            </div>
            <div className="form-group">
              <label htmlFor="phone">Phone</label>
              <Field className="form-input" type="text" name="phone" />
              <ErrorMessage
                name="phone"
                component="div"
                className="error-message"
              />
            </div>
            <div className="form-group">
              <label htmlFor="resume">Portfolio</label>
              <Field
                name="resume"
                accept=".pdf,.jpg,.jpeg,.png"
                component={FileInput}
              />
              <ErrorMessage
                name="resume"
                component="div"
                className="error-message"
              />
            </div>
            <button
              className="form-submit"
              type="submit"
              disabled={isSubmitting}
            >
              Submit
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default JobApplicationForm;
