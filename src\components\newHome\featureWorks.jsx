import { useState, useRef, memo, useCallback, useEffect, startTransition } from "react";
import BlurText from "../animations/BlurText";
import OptimizedVideo from "../../contexts/OptimizedVideoHome";
import { useFeatureWork } from "../../hooks/useApi";
import SkeletonPlaceholder from "./SkeletonPlaceholder";

const GalleryGrid = () => {
  const [activeVideo, setActiveVideo] = useState(null);
  const modalRef = useRef();
  
  // Use React Query for feature work with caching and automatic filtering/sorting
  const { data: mediaItems = [] } = useFeatureWork();
  
  // Use startTransition for non-critical video modal state updates
  const handleSetActiveVideo = useCallback((video) => {
    startTransition(() => {
      setActiveVideo(video);
    });
  }, []);

  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape") {
        startTransition(() => {
          setActiveVideo(null);
        });
      }
    };
    document.addEventListener("keydown", handleEsc);
    
    // Non-blocking overflow management
    if (activeVideo) {
      document.body.classList.add("video-modal-open");
    } else {
      document.body.classList.remove("video-modal-open");
    }
    
    return () => {
      document.removeEventListener("keydown", handleEsc);
      document.body.classList.remove("video-modal-open");
    };
  }, [activeVideo]);

  const handleOutsideClick = useCallback((e) => {
    if (modalRef.current && !modalRef.current.contains(e.target)) {
      startTransition(() => {
        setActiveVideo(null);
      });
    }
  }, []);

  return (
    <>
      {/* HEADER */}
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          marginTop: "10vh",
          marginBottom: "5vh",
        }}
      >
        <h2
          style={{
            textAlign: "center",
            fontFamily: "Formula Bold",
            fontWeight: "bold",
            color: "#fff",
            fontSize: "clamp(3rem, 10vw, 12rem)",
            letterSpacing: "0.05em",
            margin: 0,
          }}
        >
          <BlurText
            text="Featured Works"
            delay={150}
            animateBy="words"
            colorMap={["white", "#DB4A41"]}
          />
        </h2>
      </div>

      {/* GRID */}
      <div className="gallery-grid">
        {mediaItems.map((item, idx) => (
          <GalleryItem
            key={item._id}
            item={item}
            index={idx}
            onClick={() =>
              item.src.toLowerCase().endsWith(".webm" || ".mp4") &&
              handleSetActiveVideo(item)
            }
          />
        ))}
      </div>

      {/* MODAL */}
      {activeVideo && (
        <div
          className="video-modal-overlay"
          onClick={handleOutsideClick}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0,0,0,0.8)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}
        >
          <div
            ref={modalRef}
            style={{
              position: "relative",
              overflow: "hidden",
              width: "80%",
              maxWidth: "1000px",
              aspectRatio: "16/9",
              background: "black",
            }}
          >
            <OptimizedVideo
              src={activeVideo.src.replace(
                "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
              )}
              controls
              autoPlay
              muted
              preload="metadata"
              lazy={false} // modal loads immediately
              maxRetries={2}
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          </div>
        </div>
      )}
    </>
  );
};

/* ---------- memoized grid item ---------- */
const GalleryItem = memo(({ item, index, onClick }) => {
  const isVideo = item.src.toLowerCase().endsWith(".webm" || ".mp4");

  return (
    <div
      className={`grid-item item-${index + 1}`}
      onClick={onClick}
      style={{ cursor: isVideo ? "pointer" : "default" }}
    >
      {isVideo ? (
        <OptimizedVideo
          src={
            item.src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            ) + "#t=0.5"
          }
          poster={
            item.src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            ) + "#t=0.5"
          }
          muted
          loop
          playsInline
          preload="metadata"
          lazy={true}
          delay={index * 50}
          maxRetries={2}
          style={{ width: "100%", height: "100%", objectFit: "cover" }}
        />
      ) : (
        <div style={{ position: "relative", width: "100%", height: "100%" }}>
          <SkeletonPlaceholder
            width="100%"
            height="100%"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              zIndex: 1,
            }}
          />
          <img
            src={item.src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            )}
            alt={item.title}
            loading="lazy"
            decoding="async"
            onLoad={(e) => {
              const skeleton = e.target.parentElement.querySelector('[data-skeleton]');
              if (skeleton) skeleton.style.opacity = 0;
              e.target.style.opacity = 1;
            }}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              objectFit: "cover",
              zIndex: 2,
              opacity: 0,
              transition: "opacity 0.3s ease",
            }}
          />
        </div>
      )}

      <div className="title-overlay">
        <img
          src={item.logo.replace(
            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
          )}
          alt={item.title}
          loading="lazy"
          decoding="async"
          style={{ maxHeight: "70%", maxWidth: "70%" }}
        />
      </div>
    </div>
  );
});

export default GalleryGrid;
