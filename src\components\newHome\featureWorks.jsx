import {
  useState,
  useRef,
  memo,
  useCallback,
  useEffect,
  startTransition,
} from "react";
import OptimizedVideo from "../../contexts/OptimizedVideoHome";
import SkeletonPlaceholder from "./SkeletonPlaceholder";
import { LazyLoadImage } from "react-lazy-load-image-component";
import { useFeatureWork } from "../../hooks/useApi";
import "react-lazy-load-image-component/src/effects/blur.css";
import BlurText from "../animations/BlurText";

const GalleryGrid = () => {
  const [activeVideo, setActiveVideo] = useState(null);
  const modalRef = useRef();

  // Fetch media items
  const { data: mediaItems = [] } = useFeatureWork();

  // Handle opening modal for videos
  const handleSetActiveVideo = useCallback((video) => {
    startTransition(() => {
      setActiveVideo(video);
    });
  }, []);

  // Close modal on Escape
  useEffect(() => {
    const handleEsc = (e) => {
      if (e.key === "Escape") startTransition(() => setActiveVideo(null));
    };
    document.addEventListener("keydown", handleEsc);

    // Manage body overflow
    if (activeVideo) document.body.classList.add("video-modal-open");
    else document.body.classList.remove("video-modal-open");

    return () => {
      document.removeEventListener("keydown", handleEsc);
      document.body.classList.remove("video-modal-open");
    };
  }, [activeVideo]);

  // Close modal when clicking outside
  const handleOutsideClick = useCallback((e) => {
    if (modalRef.current && !modalRef.current.contains(e.target)) {
      startTransition(() => setActiveVideo(null));
    }
  }, []);

  return (
    <>
      {/* HEADER */}
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "center",
          marginTop: "10vh",
          marginBottom: "5vh",
        }}
      >
        <h2
          style={{
            textAlign: "center",
            fontFamily: "Formula Bold",
            fontWeight: "bold",
            color: "#fff",
            fontSize: "clamp(3rem, 10vw, 12rem)",
            letterSpacing: "0.05em",
            margin: 0,
          }}
        >
          <BlurText
            text="Featured Works"
            delay={150}
            animateBy="words"
            colorMap={["white", "#DB4A41"]}
          />
        </h2>
      </div>

      {/* GRID */}
      <div className="gallery-grid">
        {mediaItems.map((item, idx) => (
          <GalleryItem
            key={item._id}
            item={item}
            index={idx}
            onClick={() =>
              item.src.toLowerCase().endsWith(".webm") ||
              item.src.toLowerCase().endsWith(".mp4")
                ? handleSetActiveVideo(item)
                : null
            }
          />
        ))}
      </div>

      {/* MODAL */}
      {activeVideo && (
        <div
          className="video-modal-overlay"
          onClick={handleOutsideClick}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0,0,0,0.8)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}
        >
          <div
            ref={modalRef}
            style={{
              position: "relative",
              overflow: "hidden",
              width: "80%",
              maxWidth: "1000px",
              aspectRatio: "16/9",
              background: "black",
            }}
          >
            <OptimizedVideo
              src={activeVideo.src.replace(
                "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
              )}
              controls
              autoPlay
              muted
              preload="metadata"
              lazy={false} // modal loads immediately
              maxRetries={2}
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          </div>
        </div>
      )}
    </>
  );
};

/* ---------- memoized grid item ---------- */
const GalleryItem = memo(({ item, index, onClick }) => {
  const isVideo =
    item.src.toLowerCase().endsWith(".webm") ||
    item.src.toLowerCase().endsWith(".mp4");

  return (
    <div
      className={`grid-item item-${index + 1}`}
      onClick={onClick}
      style={{ cursor: isVideo ? "pointer" : "default" }}
    >
      {isVideo ? (
        <OptimizedVideo
          src={
            item.src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            ) + "#t=0.5"
          }
          poster={
            item.src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            ) + "#t=0.5"
          }
          muted
          loop
          playsInline
          preload="metadata"
          lazy={true}
          delay={index * 50}
          maxRetries={2}
          style={{ width: "100%", height: "100%", objectFit: "cover" }}
        />
      ) : (
        <LazyLoadImage
          alt={item.title}
          src={item.src.replace(
            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
          )}
          effect="blur"
          width="100%"
          height="100%"
          style={{ objectFit: "cover", display: "block" }}
        />
      )}

      {/* LOGO OVERLAY */}
      <LazyLoadImage
        src={item.logo.replace(
          "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
          "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
        )}
        alt={item.title}
        effect="blur"
        style={{ maxHeight: "70%", maxWidth: "70%" }}
      />
    </div>
  );
});

export default GalleryGrid;
