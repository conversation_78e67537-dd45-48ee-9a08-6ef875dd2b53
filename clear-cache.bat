@echo off
echo Clearing development caches...

REM Kill any running Node processes
taskkill /f /im node.exe >nul 2>&1

REM Clear npm cache
npm cache clean --force

REM Remove node_modules cache if it exists
if exist "node_modules\.cache" (
    echo Removing node_modules cache...
    rmdir /s /q "node_modules\.cache"
)

REM Clear any ESLint cache
if exist ".eslintcache" (
    echo Removing ESLint cache...
    del ".eslintcache"
)

REM Clear any Webpack cache
if exist ".webpack" (
    echo Removing Webpack cache...
    rmdir /s /q ".webpack"
)

echo Cache cleared! You can now run: npm start
pause
