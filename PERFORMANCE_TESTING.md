# Performance Testing Guide

## 🎯 Quick Performance Test

### Manual Testing Steps

1. **Open Chrome DevTools**
   - Press F12 or right-click → Inspect
   - Go to **Network** tab
   - Check "Disable cache"
   - Set throttling to "Fast 3G" or "Slow 3G"

2. **CPU Throttling**
   - Go to **Performance** tab
   - Click gear icon (⚙️)
   - Set CPU throttling to "4× slowdown"

3. **Test Homepage**
   ```
   1. Clear cache: Ctrl+Shift+R (or Cmd+Shift+R on Mac)
   2. Navigate to: /test
   3. Wait 3 seconds
   4. Check console for performance metrics
   ```

### Expected Results ✅

- **Total Requests**: ≤15
- **Total Transfer**: ≤2MB
- **Video Requests**: 1 (hero only)
- **LCP**: <1.5 seconds
- **Video Start**: <2 seconds

### Console Output

You should see:
```
🎯 Performance validation active for homepage
📊 Monitoring: LCP <1.5s, Video Start <2s, ≤1 video, ≤15 requests, ≤2MB
📊 Performance Metrics: {
  totalRequests: 12,
  totalBytes: "1.2MB", 
  videoRequests: 1,
  loadTime: "1200ms"
}
```

### Warning Signs ⚠️

If you see these warnings, performance budgets are exceeded:
```
⚠️ Request budget exceeded: 20 requests
⚠️ Bytes budget exceeded: 3MB
⚠️ Video count exceeded: 3 videos
```

## 🔧 Debug Commands

Open browser console and run:

```javascript
// Check video load manager status
window.videoLoadManager?.getQueueStatus()

// Check budget enforcer status  
window.requestBudgetEnforcer?.getStatus()

// Check video performance metrics
window.videoPerformanceMonitor?.getMetrics()

// Manual performance check
const resources = performance.getEntriesByType('resource');
const videos = resources.filter(r => r.name.includes('.mp4') || r.name.includes('.webm'));
console.log('Videos loaded:', videos.length);
console.log('Total requests:', resources.length);
```

## 📊 Performance Validation

### Automated Checks

The system automatically:
- ✅ Monitors request count and transfer size
- ✅ Tracks video downloads
- ✅ Logs performance metrics to console
- ✅ Warns when budgets are exceeded
- ✅ Enforces build failures in development (for budget violations)

### Manual Verification

1. **Hero Video Only**: Only 1 video should download initially
2. **Thumbnail Videos**: Should show static posters, no video data
3. **Reel Videos**: Should load only on hover/click
4. **Feature Videos**: Should load only after scroll + interaction

## 🚀 Deployment Checklist

Before deploying:

- [ ] Run performance test on mobile 3G
- [ ] Verify only 1 video loads initially
- [ ] Check console for budget warnings
- [ ] Test on low-end devices
- [ ] Verify CDN headers are configured
- [ ] Test video codec fallbacks

## 🎉 Success Metrics

**Before Optimization:**
- Requests: 600+
- Transfer: 500MB+
- Videos: 20+ loading

**After Optimization:**
- Requests: ≤15 (>95% reduction)
- Transfer: ≤2MB (>99% reduction)  
- Videos: 1 initially (>95% reduction)

## 🔍 Troubleshooting

### Common Issues

1. **Too many requests**: Check for new video components not using tier system
2. **Large transfer size**: Verify videos use adaptive quality selection
3. **Multiple videos loading**: Ensure thumbnails use ThumbnailVideo component
4. **Slow LCP**: Check hero video is Tier 0 and loads immediately

### Quick Fixes

- Use `ThumbnailVideo` for grid items (zero bytes)
- Use `ReelVideo` for interactive videos (load on intent)
- Use `FeatureVideo` for decorative videos (load after interaction)
- Ensure only 1 `HeroVideo` with tier={0}

## ✅ Validation Complete

The video performance optimization system is now active and monitoring homepage performance in real-time. Check the browser console for performance metrics and budget status.
