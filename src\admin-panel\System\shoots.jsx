import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  Typography,
  Button,
  TextField,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Chip,
  Card,
  CardContent,
  Grid,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  FormControlLabel,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CloseIcon from "@mui/icons-material/Close";
// import CheckIcon from "@mui/icons-material/Check";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
// import DownloadIcon from "@mui/icons-material/Download";
import { exportShootAsPDF, exportShotListsAsPDF } from "../../utils/pdf";

function Shoots() {
  const [shoots, setShoots] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [viewingShoot, setViewingShoot] = useState(null);
  const [editingShoot, setEditingShoot] = useState(null);
  const [newShoot, setNewShoot] = useState({
    title: "",
    projectId: "",
    date: "",
    startTime: "",
    endTime: "",
    location: "",
    addressLink: "",
    creativeDirector: "",
    Director: "",
    producer: "",
    props: [],
    equipment: [],
    cast: [],
    crew: [],
    timing: [],
    shotLists: [],
    notes: "",
    status: "scheduled",
    deliveredFootage: false,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [employees, setEmployees] = useState([]); // New state for employees

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState("");
  const [projectFilter, setProjectFilter] = useState("");
  const [creativeDirectorFilter, setCreativeDirectorFilter] = useState("");
  const [directorFilter, setDirectorFilter] = useState("");
  const [producerFilter, setProducerFilter] = useState("");

  // Helper states for array inputs
  const [newProp, setNewProp] = useState("");
  const [newEquipment, setNewEquipment] = useState("");
  const [newCastName, setNewCastName] = useState("");
  const [newCastStartTime, setNewCastStartTime] = useState("");
  const [newCastEndTime, setNewCastEndTime] = useState("");
  const [newCrewName, setNewCrewName] = useState("");
  const [selectedCrewMember, setSelectedCrewMember] = useState(null); // Changed to use employee object
  const [newCrewRole, setNewCrewRole] = useState("");
  const [newCrewTime, setNewCrewTime] = useState("");

  // Timing helper states
  const [newTimingTitle, setNewTimingTitle] = useState("");
  const [newTimingTime, setNewTimingTime] = useState("");
  const [newTimingNotes, setNewTimingNotes] = useState("");
  const [editingTimingIndex, setEditingTimingIndex] = useState(null);

  // Cast editing states
  const [editingCastIndex, setEditingCastIndex] = useState(null);

  // Crew editing states
  const [editingCrewIndex, setEditingCrewIndex] = useState(null);

  // Shot Lists helper states
  const [newShotListTitle, setNewShotListTitle] = useState("");
  const [checklistInputs, setChecklistInputs] = useState({}); // Object to store input for each shot list

  const { user } = useUser();

  const fetchShoots = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/shoots/",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setShoots(data);
    } catch (error) {
      console.error("Error fetching shoots:", error);
      showSnackbar("Failed to fetch shoots", "error");
    }
  }, []);

  const fetchProjects = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/projects",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setProjects(data);
    } catch (error) {
      console.error("Error fetching projects:", error);
    }
  }, []);

  const fetchEmployees = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setEmployees(data);
    } catch (error) {
      console.error("Error fetching employees:", error);
      showSnackbar("Failed to fetch employees", "error");
    }
  }, []);

  useEffect(() => {
    fetchShoots();
    fetchProjects();
    fetchEmployees(); // Fetch employees on component mount
  }, [fetchShoots, fetchProjects, fetchEmployees]);

  const handleView = (shoot) => {
    setViewingShoot(shoot);
    setOpenViewModal(true);
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingShoot(null);
  };

  const handleAdd = () => {
    setEditingShoot(null);
    setNewShoot({
      title: "",
      projectId: "",
      date: "",
      startTime: "",
      endTime: "",
      location: "",
      addressLink: "",
      creativeDirector: "",
      Director: "",
      producer: "",
      props: [],
      equipment: [],
      cast: [],
      crew: [],
      timing: [],
      shotLists: [],
      notes: "",
      status: "scheduled",
      deliveredFootage: false,
    });
    clearArrayInputs();
    setOpenModal(true);
  };

  const handleEdit = (shoot) => {
    setEditingShoot(shoot);
    setNewShoot({
      title: shoot.title || "",
      projectId: shoot.projectId?._id || shoot.projectId || "",
      date: shoot.date ? new Date(shoot.date).toISOString().split("T")[0] : "",
      startTime: shoot.startTime || "",
      endTime: shoot.endTime || "",
      location: shoot.location || "",
      addressLink: shoot.addressLink || "",
      creativeDirector: shoot.creativeDirector || "",
      Director: shoot.Director || "",
      producer: shoot.producer || "",
      props: shoot.props || [],
      equipment: shoot.equipment || [],
      cast: shoot.cast || [],
      crew: shoot.crew || [],
      timing: shoot.timing || [],
      shotLists: shoot.shotLists || [],
      notes: shoot.notes || "",
      status: shoot.status || "scheduled",
      deliveredFootage: shoot.deliveredFootage || false,
    });
    clearArrayInputs();
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingShoot(null);
    setNewShoot({
      title: "",
      projectId: "",
      date: "",
      startTime: "",
      endTime: "",
      location: "",
      addressLink: "",
      creativeDirector: "",
      Director: "",
      producer: "",
      props: [],
      equipment: [],
      cast: [],
      crew: [],
      timing: [],
      shotLists: [],
      notes: "",
      status: "scheduled",
      deliveredFootage: false,
    });
    clearArrayInputs();
  };

  const clearArrayInputs = () => {
    setNewProp("");
    setNewEquipment("");
    setNewCastName("");
    setNewCastStartTime("");
    setNewCastEndTime("");
    setSelectedCrewMember(null); // Reset selected crew member
    setNewCrewTime("");
    setNewTimingTitle("");
    setNewTimingTime("");
    setNewTimingNotes("");
    setEditingTimingIndex(null);
    setEditingCastIndex(null);
    setEditingCrewIndex(null);
  };
  const handleSave = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const url = editingShoot
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${editingShoot._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/shoots/";

      const method = editingShoot ? "PUT" : "POST";

      console.log("Updating Shoot ID:", editingShoot?._id);
      console.log("Update payload being sent:", newShoot);

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(newShoot),
      });

      const responseData = await response.json();
      console.log("Response status:", response.status);
      console.log("Response body:", responseData);

      if (response.ok) {
        if (editingShoot) {
          setShoots(
            shoots.map((shoot) =>
              shoot._id === editingShoot._id ? responseData : shoot
            )
          );
          showSnackbar("Shoot updated successfully", "success");
        } else {
          setShoots([...shoots, responseData]);
          showSnackbar("Shoot added successfully", "success");
        }
        handleCloseModal();
      } else {
        throw new Error(responseData.message || "Failed to save shoot");
      }
    } catch (error) {
      console.error("Error saving shoot:", error);
      showSnackbar("Failed to save shoot", "error");
    }
    setLoading(false);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this shoot?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setShoots(shoots.filter((shoot) => shoot._id !== id));
        showSnackbar("Shoot deleted successfully", "success");
      } else {
        throw new Error("Failed to delete shoot");
      }
    } catch (error) {
      console.error("Error deleting shoot:", error);
      showSnackbar("Failed to delete shoot", "error");
    }
  };

  const handleStatusChange = async (shootId, newStatus) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (response.ok) {
        const updatedShoot = await response.json();

        // Update the shoots list
        setShoots(
          shoots.map((shoot) =>
            shoot._id === shootId ? { ...shoot, status: newStatus } : shoot
          )
        );

        // Update the viewing shoot if it's the same one
        if (viewingShoot && viewingShoot._id === shootId) {
          setViewingShoot({ ...viewingShoot, status: newStatus });
        }

        showSnackbar(`Shoot status updated to ${newStatus}`, "success");
      } else {
        throw new Error("Failed to update shoot status");
      }
    } catch (error) {
      console.error("Error updating shoot status:", error);
      showSnackbar("Failed to update shoot status", "error");
    }
  };

  const handleFootageDelivery = async (shootId, delivered) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/deliver-footage`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ deliveredFootage: delivered }),
        }
      );

      if (response.ok) {
        const updatedShoot = await response.json();

        // Update the shoots list
        setShoots(
          shoots.map((shoot) =>
            shoot._id === shootId
              ? { ...shoot, deliveredFootage: delivered }
              : shoot
          )
        );

        // Update the viewing shoot if it's the same one
        if (viewingShoot && viewingShoot._id === shootId) {
          setViewingShoot({ ...viewingShoot, deliveredFootage: delivered });
        }

        showSnackbar(
          `Footage ${
            delivered ? "marked as delivered" : "marked as not delivered"
          }`,
          "success"
        );
      } else {
        throw new Error("Failed to update footage delivery status");
      }
    } catch (error) {
      console.error("Error updating footage delivery:", error);
      showSnackbar("Failed to update footage delivery status", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "scheduled":
        return "#ff9800";
      case "in_progress":
        return "#2196f3";
      case "completed":
        return "#4caf50";
      case "cancelled":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Array manipulation helpers
  const addToArray = (arrayName, value, inputSetter) => {
    if (value.trim()) {
      setNewShoot((prev) => ({
        ...prev,
        [arrayName]: [...prev[arrayName], value.trim()],
      }));
      inputSetter("");
    }
  };

  const addCastMember = () => {
    if (
      newCastName.trim() &&
      newCastStartTime.trim() &&
      newCastEndTime.trim()
    ) {
      if (editingCastIndex !== null) {
        // Update existing cast member
        setNewShoot((prev) => ({
          ...prev,
          cast: prev.cast.map((member, index) =>
            index === editingCastIndex
              ? {
                  name: newCastName.trim(),
                  startTime: newCastStartTime.trim(),
                  endTime: newCastEndTime.trim(),
                }
              : member
          ),
        }));
        setEditingCastIndex(null);
      } else {
        // Add new cast member
        setNewShoot((prev) => ({
          ...prev,
          cast: [
            ...prev.cast,
            {
              name: newCastName.trim(),
              startTime: newCastStartTime.trim(),
              endTime: newCastEndTime.trim(),
            },
          ],
        }));
      }
      setNewCastName("");
      setNewCastStartTime("");
      setNewCastEndTime("");
    }
  };

  // Updated addCrewMember function to save user ObjectId
  const addCrewMember = () => {
    if (
      selectedCrewMember &&
      selectedCrewMember._id !== "other" &&
      newCrewTime.trim()
    ) {
      // Existing employee
      if (editingCrewIndex !== null) {
        // Update existing crew member
        setNewShoot((prev) => ({
          ...prev,
          crew: prev.crew.map((member, index) =>
            index === editingCrewIndex
              ? {
                  user: selectedCrewMember._id,
                  role: newCrewRole.trim() || selectedCrewMember.role || "",
                  time: newCrewTime.trim(),
                }
              : member
          ),
        }));
        setEditingCrewIndex(null);
      } else {
        // Add new crew member
        setNewShoot((prev) => ({
          ...prev,
          crew: [
            ...prev.crew,
            {
              user: selectedCrewMember._id,
              role: newCrewRole.trim() || selectedCrewMember.role || "",
              time: newCrewTime.trim(),
            },
          ],
        }));
      }
      setSelectedCrewMember(null);
      setNewCrewRole("");
      setNewCrewTime("");
    } else if (
      selectedCrewMember?._id === "other" &&
      newCrewName.trim() &&
      newCrewRole.trim() &&
      newCrewTime.trim()
    ) {
      // Manual entry
      if (editingCrewIndex !== null) {
        // Update existing crew member
        setNewShoot((prev) => ({
          ...prev,
          crew: prev.crew.map((member, index) =>
            index === editingCrewIndex
              ? {
                  name: newCrewName.trim(),
                  role: newCrewRole.trim(),
                  time: newCrewTime.trim(),
                }
              : member
          ),
        }));
        setEditingCrewIndex(null);
      } else {
        // Add new crew member
        setNewShoot((prev) => ({
          ...prev,
          crew: [
            ...prev.crew,
            {
              name: newCrewName.trim(),
              role: newCrewRole.trim(),
              time: newCrewTime.trim(),
            },
          ],
        }));
      }
      setSelectedCrewMember(null);
      setNewCrewName("");
      setNewCrewRole("");
      setNewCrewTime("");
    }
  };

  const addTimingItem = () => {
    if (newTimingTitle.trim() && newTimingTime.trim()) {
      if (editingTimingIndex !== null) {
        // Update existing timing item
        setNewShoot((prev) => ({
          ...prev,
          timing: prev.timing.map((item, index) =>
            index === editingTimingIndex
              ? {
                  title: newTimingTitle.trim(),
                  time: newTimingTime.trim(),
                  notes: newTimingNotes.trim(),
                }
              : item
          ),
        }));
        setEditingTimingIndex(null);
      } else {
        // Add new timing item
        setNewShoot((prev) => ({
          ...prev,
          timing: [
            ...prev.timing,
            {
              title: newTimingTitle.trim(),
              time: newTimingTime.trim(),
              notes: newTimingNotes.trim(),
            },
          ],
        }));
      }
      setNewTimingTitle("");
      setNewTimingTime("");
      setNewTimingNotes("");
    }
  };

  const editTimingItem = (index) => {
    const item = newShoot.timing[index];
    setNewTimingTitle(item.title);
    setNewTimingTime(item.time);
    setNewTimingNotes(item.notes || "");
    setEditingTimingIndex(index);
  };

  const editCastMember = (index) => {
    const member = newShoot.cast[index];
    setNewCastName(member.name);
    setNewCastStartTime(member.startTime);
    setNewCastEndTime(member.endTime);
    setEditingCastIndex(index);
  };

  const editCrewMember = (index) => {
    const member = newShoot.crew[index];
    if (member.user && typeof member.user === "string") {
      const employee = employees.find((emp) => emp._id === member.user);
      setSelectedCrewMember(employee || null);
    } else if (member.user && typeof member.user === "object") {
      setSelectedCrewMember(member.user);
    } else {
      setSelectedCrewMember({ _id: "other", name: "Other" });
      setNewCrewName(member.name || "");
    }
    setNewCrewRole(member.role || "");
    setNewCrewTime(member.time || "");
    setEditingCrewIndex(index);
  };

  const removeFromArray = (arrayName, index) => {
    setNewShoot((prev) => ({
      ...prev,
      [arrayName]: prev[arrayName].filter((_, i) => i !== index),
    }));
  };
  // Helper function to get employee name by ID
  const getEmployeeName = (userId) => {
    const employee = employees.find((emp) => emp._id === userId);
    return employee ? employee.name : "Unknown Employee";
  };
  const displayCrewMember = (crewMember) => {
    if (crewMember.user && typeof crewMember.user === "object") {
      return `${crewMember.user.name} (${crewMember.role || "N/A"}) - ${
        crewMember.time
      }`;
    } else if (crewMember.user && typeof crewMember.user === "string") {
      return `${getEmployeeName(crewMember.user)} (${
        crewMember.role || "N/A"
      }) - ${crewMember.time}`;
    } else if (crewMember.name) {
      return `${crewMember.name} (${crewMember.role || "N/A"}) - ${
        crewMember.time
      }`;
    }
    return `Unknown - ${crewMember.time}`;
  };

  // Timing Management Functions
  // const addTimingItemAPI = async (shootId = null) => {
  //   if (newTimingTitle.trim() && newTimingTime.trim()) {
  //     if (shootId) {
  //       // API call for existing shoot
  //       try {
  //         const token = localStorage.getItem("token");
  //         const response = await fetch(
  //           `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/timing`,
  //           {
  //             method: "POST",
  //             headers: {
  //               "Content-Type": "application/json",
  //               Authorization: `Bearer ${token}`,
  //             },
  //             body: JSON.stringify({
  //               title: newTimingTitle.trim(),
  //               time: newTimingTime.trim(),
  //               notes: newTimingNotes.trim(),
  //             }),
  //           }
  //         );

  //         if (response.ok) {
  //           const updatedShoot = await response.json();
  //           setShoots((prev) =>
  //             prev.map((shoot) =>
  //               shoot._id === shootId ? updatedShoot : shoot
  //             )
  //           );
  //           // Update the modal state as well
  //           setNewShoot((prev) => ({
  //             ...prev,
  //             timing: updatedShoot.timing || [],
  //           }));
  //           setNewTimingTitle("");
  //           setNewTimingTime("");
  //           setNewTimingNotes("");
  //           showSnackbar("Timing item added successfully", "success");
  //         }
  //       } catch (error) {
  //         console.error("Error adding timing item:", error);
  //         showSnackbar("Failed to add timing item", "error");
  //       }
  //     } else {
  //       // Local state for new shoot
  //       addTimingItem();
  //     }
  //   }
  // };

  // Shot Lists Management Functions
  const addShotList = async (shootId = null) => {
    if (newShotListTitle.trim()) {
      if (shootId) {
        // API call for existing shoot
        try {
          const token = localStorage.getItem("token");
          const response = await fetch(
            `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/shotlists`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify({
                title: newShotListTitle.trim(),
                checklist: [],
              }),
            }
          );

          if (response.ok) {
            const updatedShoot = await response.json();
            setShoots((prev) =>
              prev.map((shoot) =>
                shoot._id === shootId ? updatedShoot : shoot
              )
            );
            // Update the modal state as well
            setNewShoot((prev) => ({
              ...prev,
              shotLists: updatedShoot.shotLists || [],
            }));
            setNewShotListTitle("");
            showSnackbar("Shot list added successfully", "success");
          }
        } catch (error) {
          console.error("Error adding shot list:", error);
          showSnackbar("Failed to add shot list", "error");
        }
      } else {
        // Local state for new shoot
        setNewShoot((prev) => ({
          ...prev,
          shotLists: [
            ...(prev.shotLists || []),
            {
              title: newShotListTitle.trim(),
              checklist: [],
            },
          ],
        }));
        setNewShotListTitle("");
      }
    }
  };

  const addChecklistItem = async (
    shotListIndex,
    shootId = null,
    listId = null
  ) => {
    const inputText = checklistInputs[shotListIndex] || "";
    if (inputText.trim()) {
      if (shootId && listId) {
        // For existing shoots, we need to update the shot list with new checklist item
        // Since there's no direct API for adding checklist items, we'll update locally for now
        // and implement a proper API endpoint later
        setNewShoot((prev) => ({
          ...prev,
          shotLists: (prev.shotLists || []).map((list, index) =>
            index === shotListIndex
              ? {
                  ...list,
                  checklist: [
                    ...(list.checklist || []),
                    { text: inputText.trim(), done: false },
                  ],
                }
              : list
          ),
        }));
        setChecklistInputs((prev) => ({ ...prev, [shotListIndex]: "" }));
      } else {
        // Local state for new shoot
        setNewShoot((prev) => ({
          ...prev,
          shotLists: (prev.shotLists || []).map((list, index) =>
            index === shotListIndex
              ? {
                  ...list,
                  checklist: [
                    ...(list.checklist || []),
                    { text: inputText.trim(), done: false },
                  ],
                }
              : list
          ),
        }));
        setChecklistInputs((prev) => ({ ...prev, [shotListIndex]: "" }));
      }
    }
  };

  // Update timing item function
  // const updateTimingItem = async (shootId, itemId, updatedData) => {
  //   try {
  //     const token = localStorage.getItem("token");
  //     const response = await fetch(
  //       `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/timing/${itemId}`,
  //       {
  //         method: "PATCH",
  //         headers: {
  //           "Content-Type": "application/json",
  //           Authorization: `Bearer ${token}`,
  //         },
  //         body: JSON.stringify(updatedData),
  //       }
  //     );

  //     if (response.ok) {
  //       const updatedShoot = await response.json();
  //       setShoots((prev) =>
  //         prev.map((shoot) => (shoot._id === shootId ? updatedShoot : shoot))
  //       );
  //       // Update the modal state as well
  //       setNewShoot((prev) => ({
  //         ...prev,
  //         timing: updatedShoot.timing || [],
  //       }));
  //       showSnackbar("Timing item updated successfully", "success");
  //     }
  //   } catch (error) {
  //     console.error("Error updating timing item:", error);
  //     showSnackbar("Failed to update timing item", "error");
  //   }
  // };

  // // Delete timing item function
  // const deleteTimingItem = async (shootId, itemId) => {
  //   try {
  //     const token = localStorage.getItem("token");
  //     const response = await fetch(
  //       `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/timing/${itemId}`,
  //       {
  //         method: "DELETE",
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );

  //     if (response.ok) {
  //       const result = await response.json();
  //       setShoots((prev) =>
  //         prev.map((shoot) => (shoot._id === shootId ? result.shoot : shoot))
  //       );
  //       // Update the modal state as well
  //       setNewShoot((prev) => ({
  //         ...prev,
  //         timing: result.shoot.timing || [],
  //       }));
  //       showSnackbar("Timing item deleted successfully", "success");
  //     }
  //   } catch (error) {
  //     console.error("Error deleting timing item:", error);
  //     showSnackbar("Failed to delete timing item", "error");
  //   }
  // };

  const deleteShotList = async (shootId, listId) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/shotlists/${listId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        setShoots((prev) =>
          prev.map((shoot) => (shoot._id === shootId ? result.shoot : shoot))
        );
        // Update the modal state as well
        setNewShoot((prev) => ({
          ...prev,
          shotLists: result.shoot.shotLists || [],
        }));
        showSnackbar("Shot list deleted successfully", "success");
      }
    } catch (error) {
      console.error("Error deleting shot list:", error);
      showSnackbar("Failed to delete shot list", "error");
    }
  };

  const updateChecklistItem = async (shootId, listId, itemId, done) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${shootId}/shotlists/${listId}/checklist/${itemId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ done }),
        }
      );

      if (response.ok) {
        const updatedShoot = await response.json();
        setShoots((prev) =>
          prev.map((shoot) => (shoot._id === shootId ? updatedShoot : shoot))
        );
        // Update the modal state as well
        setNewShoot((prev) => ({
          ...prev,
          shotLists: updatedShoot.shotLists || [],
        }));
      }
    } catch (error) {
      console.error("Error updating checklist item:", error);
      showSnackbar("Failed to update checklist item", "error");
    }
  };

  // Filter and search logic
  const filteredShoots = React.useMemo(() => {
    let filtered = [...shoots];

    // Search filter - search by shoot title
    if (searchTerm) {
      filtered = filtered.filter((shoot) =>
        shoot.title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Project filter
    if (projectFilter) {
      filtered = filtered.filter(
        (shoot) => shoot.projectId?._id === projectFilter
      );
    }

    // Creative Director filter
    if (creativeDirectorFilter) {
      filtered = filtered.filter(
        (shoot) =>
          shoot.creativeDirector === creativeDirectorFilter ||
          shoot.crew?.some(
            (crewMember) =>
              crewMember.user?._id === creativeDirectorFilter ||
              crewMember.user === creativeDirectorFilter
          )
      );
    }

    // Director filter
    if (directorFilter) {
      filtered = filtered.filter(
        (shoot) =>
          shoot.Director === directorFilter ||
          shoot.crew?.some(
            (crewMember) =>
              crewMember.user?._id === directorFilter ||
              crewMember.user === directorFilter
          )
      );
    }

    // Producer filter
    if (producerFilter) {
      filtered = filtered.filter(
        (shoot) =>
          shoot.producer === producerFilter ||
          shoot.crew?.some(
            (crewMember) =>
              crewMember.user?._id === producerFilter ||
              crewMember.user === producerFilter
          )
      );
    }

    // Note: Removed automatic user assignment filter to show all shoots
    // Users can now use the specific role filters to see shoots they're assigned to

    return filtered;
  }, [
    shoots,
    searchTerm,
    projectFilter,
    creativeDirectorFilter,
    directorFilter,
    producerFilter,
  ]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Shoot Management
          </Typography>
          {(user?.tier === 2 || user?.tier === 3) &&
            (user?.role === "account_manager" ||
              user?.role === "general_manager") && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAdd}
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  fontFamily: "Formula Bold",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
              >
                Add Shoot
              </Button>
            )}
        </Box>

        {/* Filter Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                {/* Search Bar */}
                <Grid item xs={12} md={2.4}>
                  <TextField
                    fullWidth
                    placeholder="Search by shoot name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputBase-input::placeholder": {
                        color: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  />
                </Grid>

                {/* Project Filter */}
                <Grid item xs={12} md={2.4}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Filter by Project
                    </InputLabel>
                    <Select
                      value={projectFilter}
                      onChange={(e) => setProjectFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Projects</MenuItem>
                      {projects.map((project) => (
                        <MenuItem key={project._id} value={project._id}>
                          {project.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Creative Director Filter */}
                <Grid item xs={12} md={2.4}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Creative Director
                    </InputLabel>
                    <Select
                      value={creativeDirectorFilter}
                      onChange={(e) =>
                        setCreativeDirectorFilter(e.target.value)
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Creative Directors</MenuItem>
                      {employees.map((employee) => (
                        <MenuItem key={employee._id} value={employee._id}>
                          {employee.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Director Filter */}
                <Grid item xs={12} md={2.4}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Director
                    </InputLabel>
                    <Select
                      value={directorFilter}
                      onChange={(e) => setDirectorFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Directors</MenuItem>
                      {employees.map((employee) => (
                        <MenuItem key={employee._id} value={employee._id}>
                          {employee.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Producer Filter */}
                <Grid item xs={12} md={2.4}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Producer
                    </InputLabel>
                    <Select
                      value={producerFilter}
                      onChange={(e) => setProducerFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      <MenuItem value="">All Producers</MenuItem>
                      {employees.map((employee) => (
                        <MenuItem key={employee._id} value={employee._id}>
                          {employee.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Clear Filters */}
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setSearchTerm("");
                      setProjectFilter("");
                      setCreativeDirectorFilter("");
                      setDirectorFilter("");
                      setProducerFilter("");
                    }}
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Clear All Filters
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* Shoot Cards Grid */}
        <Box sx={{ padding: "0 5% 40px" }}>
          <Grid container spacing={3}>
            <AnimatePresence>
              {filteredShoots.map((shoot) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={shoot._id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                        display: "flex",

                        flexDirection: "column",
                        position: "relative",
                        "&:hover": {
                          background: "rgba(255, 255, 255, 0.08)",
                          borderColor: "rgba(219, 74, 65, 0.3)",
                        },
                      }}
                    >
                      {/* Admin Controls */}
                      <Box
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          display: "flex",
                          gap: 0.5,
                          zIndex: 10,
                        }}
                      >
                        <Tooltip title="View">
                          <IconButton
                            onClick={() => handleView(shoot)}
                            size="small"
                            sx={{
                              backgroundColor: "rgba(0, 0, 0, 0.7)",
                              color: "white",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.8)",
                              },
                            }}
                          >
                            <VisibilityIcon sx={{ fontSize: "1rem" }} />
                          </IconButton>
                        </Tooltip>
                        {user?.tier === 3 &&
                          (user?.role === "account_manager" ||
                            user?.role === "general_manager") && (
                            <>
                              <Tooltip title="Edit">
                                <IconButton
                                  onClick={() => handleEdit(shoot)}
                                  size="small"
                                  sx={{
                                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    color: "white",
                                    "&:hover": {
                                      backgroundColor: "rgba(219, 74, 65, 0.8)",
                                    },
                                  }}
                                >
                                  <EditIcon sx={{ fontSize: "1rem" }} />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <IconButton
                                  onClick={() => handleDelete(shoot._id)}
                                  size="small"
                                  sx={{
                                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    color: "white",
                                    "&:hover": {
                                      backgroundColor: "rgba(244, 67, 54, 0.8)",
                                    },
                                  }}
                                >
                                  <DeleteIcon sx={{ fontSize: "1rem" }} />
                                </IconButton>
                              </Tooltip>
                            </>
                          )}
                      </Box>

                      <CardContent sx={{ flexGrow: 1, padding: "20px" }}>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 1,
                            mt: 2,
                          }}
                        >
                          <Typography
                            variant="h1"
                            component="h1"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "white",
                              flexGrow: 1,
                              fontSize: "1.2rem",
                            }}
                          >
                            {shoot.title}
                          </Typography>
                          <Chip
                            label={shoot.status}
                            size="small"
                            sx={{
                              backgroundColor: getStatusColor(shoot.status),
                              color: "white",
                              fontSize: "0.8rem",
                              textTransform: "capitalize",
                              height: "30px",
                              padding: "15px 15px",
                            }}
                          />
                        </Box>

                        <Typography
                          variant="body2"
                          color="rgba(255, 255, 255, 0.7)"
                          sx={{
                            mb: 1,
                            fontFamily: "Anton",
                            letterSpacing: "0.05em",
                          }}
                        >
                          Project: {shoot.projectId?.title || "No Project"}
                        </Typography>

                        <Typography
                          variant="body2"
                          color="rgba(255, 255, 255, 0.5)"
                          sx={{ mb: 1, fontSize: "0.8rem" }}
                        >
                          📅 {formatDate(shoot.date)}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="rgba(255, 255, 255, 0.5)"
                          sx={{ mb: 1, fontSize: "0.8rem" }}
                        >
                          🕐 {shoot.startTime} - {shoot.endTime}
                        </Typography>

                        <Typography
                          variant="body2"
                          color="rgba(255, 255, 255, 0.5)"
                          sx={{ mb: 1, fontSize: "0.8rem" }}
                        >
                          📍 {shoot.location}
                        </Typography>

                        <Box sx={{ mb: 2 }}>
                          <Typography
                            variant="body2"
                            color="#db4a41"
                            sx={{ fontFamily: "Formula Bold", mb: 1 }}
                          >
                            Quick Stats:
                          </Typography>
                          <Box
                            sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}
                          >
                            <Chip
                              label={`Cast: ${shoot.cast?.length || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`Crew: ${shoot.crew?.length || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`Props: ${shoot.props?.length || 0}`}
                              size="small"
                              sx={{
                                backgroundColor: "rgba(219, 74, 65, 0.2)",
                                color: "#db4a41",
                                fontSize: "0.7rem",
                              }}
                            />
                          </Box>
                        </Box>

                        {shoot.notes && (
                          <Typography
                            variant="body2"
                            color="rgba(255, 255, 255, 0.5)"
                            sx={{ fontSize: "0.7rem", fontStyle: "italic" }}
                          >
                            📝 {shoot.notes.substring(0, 50)}...
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </AnimatePresence>
          </Grid>
        </Box>

        {/* Add/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(26, 26, 26, 0.95)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              color: "white",
            },
          }}
        >
          <DialogTitle
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            {editingShoot ? "Edit Shoot" : "Add New Shoot"}
            <IconButton
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ padding: "20px" }}>
            <Grid container spacing={3}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Basic Information
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Shoot Title"
                  value={newShoot.title}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, title: e.target.value })
                  }
                  variant="outlined"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Project
                  </InputLabel>
                  <Select
                    value={newShoot.projectId}
                    onChange={(e) =>
                      setNewShoot({ ...newShoot, projectId: e.target.value })
                    }
                    renderValue={(selected) => {
                      const project = projects.find((p) => p._id === selected);
                      return project ? project.title : "Select a project";
                    }}
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Select a project</em>
                    </MenuItem>
                    {projects.map((project) => (
                      <MenuItem key={project._id} value={project._id}>
                        {project.title}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date"
                  type="date"
                  value={newShoot.date}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, date: e.target.value })
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Location"
                  value={newShoot.location}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, location: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Address Link (Google Maps)"
                  value={newShoot.addressLink || ""}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, addressLink: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Time"
                  type="text"
                  value={newShoot.startTime}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, startTime: e.target.value })
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Time"
                  type="text"
                  value={newShoot.endTime}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, endTime: e.target.value })
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Status
                  </InputLabel>
                  <Select
                    value={newShoot.status}
                    onChange={(e) =>
                      setNewShoot({ ...newShoot, status: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="scheduled">Scheduled</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              {/* Key Roles */}
              <Grid item xs={12} sm={4}>
                <Autocomplete
                  options={employees}
                  getOptionLabel={(o) => o.name || ""}
                  value={
                    employees.find(
                      (e) => e._id === newShoot.creativeDirector
                    ) || null
                  }
                  onChange={(_, v) =>
                    setNewShoot({ ...newShoot, creativeDirector: v?._id || "" })
                  }
                  renderInput={(params) => (
                    <TextField {...params} label="Creative Director" />
                  )}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255,255,255,0.3)" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255,255,255,0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Autocomplete
                  options={employees}
                  getOptionLabel={(o) => o.name || ""}
                  value={
                    employees.find((e) => e._id === newShoot.Director) || null
                  }
                  onChange={(_, v) =>
                    setNewShoot({ ...newShoot, Director: v?._id || "" })
                  }
                  renderInput={(params) => (
                    <TextField {...params} label="Director" />
                  )}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255,255,255,0.3)" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255,255,255,0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Autocomplete
                  options={employees}
                  getOptionLabel={(o) => o.name || ""}
                  value={
                    employees.find((e) => e._id === newShoot.producer) || null
                  }
                  onChange={(_, v) =>
                    setNewShoot({ ...newShoot, producer: v?._id || "" })
                  }
                  renderInput={(params) => (
                    <TextField {...params} label="Producer" />
                  )}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255,255,255,0.3)" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255,255,255,0.7)",
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newShoot.deliveredFootage}
                      disabled={newShoot.status !== "in_progress"}
                      onChange={(e) => {
                        if (newShoot.status !== "in_progress") {
                          setSnackbar({
                            open: true,
                            message: "The shoot isn't finished yet!",
                            severity: "warning",
                          });
                          return;
                        }
                        setNewShoot({
                          ...newShoot,
                          deliveredFootage: e.target.checked,
                        });
                      }}
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        "&.Mui-checked": {
                          color: "#db4a41",
                        },
                        "&.Mui-disabled": {
                          color: "rgba(255, 255, 255, 0.3)",
                        },
                      }}
                    />
                  }
                  label={
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <span style={{ color: "#fff" }}>Footage Delivered</span>
                      {newShoot.status !== "in_progress" && (
                        <Tooltip title="Only available when shoot is in progress">
                          <Typography
                            variant="caption"
                            sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                          >
                            (Requires in-progress status)
                          </Typography>
                        </Tooltip>
                      )}
                    </Box>
                  }
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                />
              </Grid>

              {/* Props Section */}
              <Grid item xs={12}>
                <Divider
                  sx={{ backgroundColor: "rgba(255, 255, 255, 0.1)", my: 2 }}
                />
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Props
                </Typography>
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                  <TextField
                    label="Add Prop"
                    value={newProp}
                    onChange={(e) => setNewProp(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addToArray("props", newProp, setNewProp);
                      }
                    }}
                    sx={{
                      flexGrow: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <Button
                    onClick={() => addToArray("props", newProp, setNewProp)}
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Add
                  </Button>
                </Box>
                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                  {newShoot.props.map((prop, index) => (
                    <Chip
                      key={index}
                      label={prop}
                      onDelete={() => removeFromArray("props", index)}
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                      }}
                    />
                  ))}
                </Box>
              </Grid>

              {/* Equipment Section */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Equipment
                </Typography>
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                  <TextField
                    label="Add Equipment"
                    value={newEquipment}
                    onChange={(e) => setNewEquipment(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addToArray("equipment", newEquipment, setNewEquipment);
                      }
                    }}
                    sx={{
                      flexGrow: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <Button
                    onClick={() =>
                      addToArray("equipment", newEquipment, setNewEquipment)
                    }
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Add
                  </Button>
                </Box>
                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                  {newShoot.equipment.map((item, index) => (
                    <Chip
                      key={index}
                      label={item}
                      onDelete={() => removeFromArray("equipment", index)}
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                      }}
                    />
                  ))}
                </Box>
              </Grid>
              <Divider
                sx={{ backgroundColor: "rgba(255, 255, 255, 0.1)", my: 2 }}
              />
              {/* Cast Section */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Cast
                </Typography>
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                  <TextField
                    label="Cast Member Name"
                    value={newCastName}
                    onChange={(e) => setNewCastName(e.target.value)}
                    sx={{
                      flexGrow: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <TextField
                    label="Start Time (e.g., 8:30 AM)"
                    value={newCastStartTime}
                    onChange={(e) => setNewCastStartTime(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addCastMember();
                      }
                    }}
                    sx={{
                      width: "200px",
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <TextField
                    label="End Time (e.g., 9:30 AM)"
                    value={newCastEndTime}
                    onChange={(e) => setNewCastEndTime(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addCastMember();
                      }
                    }}
                    sx={{
                      width: "200px",
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <Button
                    onClick={addCastMember}
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    {editingCastIndex !== null ? "Update" : "Add"}
                  </Button>
                </Box>
                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                  {newShoot.cast.map((member, index) => (
                    <Chip
                      key={index}
                      label={`${member.name} - ${member.startTime || ""}${
                        member.endTime ? ` to ${member.endTime}` : ""
                      }`}
                      onClick={() => editCastMember(index)}
                      onDelete={() => removeFromArray("cast", index)}
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                        cursor: "pointer",
                        "&:hover": {
                          backgroundColor: "rgba(219, 74, 65, 0.3)",
                        },
                      }}
                    />
                  ))}
                </Box>
              </Grid>
              {/* Crew Section */}
              {/* Crew Section */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Crew
                </Typography>

                <Box sx={{ display: "flex", gap: 2, mb: 2, flexWrap: "wrap" }}>
                  {/* Autocomplete with "Other" option */}
                  <Autocomplete
                    options={[...employees, { _id: "other", name: "Other" }]}
                    getOptionLabel={(option) => option.name || ""}
                    value={selectedCrewMember}
                    onChange={(_, newValue) => setSelectedCrewMember(newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Crew Member"
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            color: "white",
                            "& fieldset": {
                              borderColor: "rgba(255, 255, 255, 0.3)",
                            },
                            "&:hover fieldset": {
                              borderColor: "rgba(219, 74, 65, 0.5)",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "#db4a41",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: "rgba(255, 255, 255, 0.7)",
                          },
                        }}
                      />
                    )}
                    sx={{ flexGrow: 1 }}
                  />

                  {/* Name field only when "Other"; Role field always visible */}
                  {selectedCrewMember?._id === "other" && (
                    <TextField
                      label="Crew Name"
                      value={newCrewName}
                      onChange={(e) => setNewCrewName(e.target.value)}
                      sx={{
                        width: "200px",
                        "& .MuiOutlinedInput-root": {
                          color: "white",
                          "& fieldset": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&:hover fieldset": {
                            borderColor: "rgba(219, 74, 65, 0.5)",
                          },
                          "&.Mui-focused fieldset": {
                            borderColor: "#db4a41",
                          },
                        },
                        "& .MuiInputLabel-root": {
                          color: "rgba(255, 255, 255, 0.7)",
                        },
                      }}
                    />
                  )}
                  <TextField
                    label="Role"
                    value={newCrewRole}
                    onChange={(e) => setNewCrewRole(e.target.value)}
                    sx={{
                      width: "180px",
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />

                  {/* Time input (always shown) */}
                  <TextField
                    label="Time (e.g., 8:30 AM)"
                    value={newCrewTime}
                    onChange={(e) => setNewCrewTime(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") addCrewMember();
                    }}
                    sx={{
                      width: "180px",
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />

                  <Button
                    onClick={addCrewMember}
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    {editingCrewIndex !== null ? "Update" : "Add"}
                  </Button>
                </Box>

                {/* Display added crew members */}
                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                  {newShoot.crew.map((member, index) => (
                    <Chip
                      key={index}
                      label={displayCrewMember(member)}
                      onClick={() => editCrewMember(index)}
                      onDelete={() => removeFromArray("crew", index)}
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                        cursor: "pointer",
                        "&:hover": {
                          backgroundColor: "rgba(219, 74, 65, 0.3)",
                        },
                      }}
                    />
                  ))}
                </Box>
              </Grid>
              <Divider
                sx={{ backgroundColor: "rgba(255, 255, 255, 0.1)", my: 2 }}
              />
              {/* Timing Section */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 2 }}
                >
                  Timing/Schedule
                </Typography>
                <Box sx={{ display: "flex", gap: 2, mb: 2, flexWrap: "wrap" }}>
                  <TextField
                    label="Title (e.g., Set props, Lunch break)"
                    value={newTimingTitle}
                    onChange={(e) => setNewTimingTitle(e.target.value)}
                    sx={{
                      flexGrow: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <TextField
                    label="Time (e.g., 8:30 AM)"
                    value={newTimingTime}
                    onChange={(e) => setNewTimingTime(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        addTimingItem();
                      }
                    }}
                    sx={{
                      width: "180px",
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <TextField
                    label="Notes"
                    multiline
                    rows={2}
                    value={newTimingNotes}
                    onChange={(e) => setNewTimingNotes(e.target.value)}
                    sx={{
                      flexGrow: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <Button
                    onClick={addTimingItem}
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    {editingTimingIndex !== null ? "Update" : "Add"}
                  </Button>
                </Box>
                {newShoot.timing.length > 0 ? (
                  <TableContainer
                    component={Paper}
                    sx={{ backgroundColor: "rgba(255, 255, 255, 0.05)", mt: 2 }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell
                            sx={{ color: "#db4a41", fontWeight: "bold" }}
                          >
                            Title
                          </TableCell>
                          <TableCell
                            sx={{ color: "#db4a41", fontWeight: "bold" }}
                          >
                            Time
                          </TableCell>
                          <TableCell
                            sx={{ color: "#db4a41", fontWeight: "bold" }}
                          >
                            Notes
                          </TableCell>
                          <TableCell
                            sx={{ color: "#db4a41", fontWeight: "bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {newShoot.timing.map((item, index) => (
                          <TableRow
                            key={index}
                            onClick={() => editTimingItem(index)}
                            sx={{
                              cursor: "pointer",
                              "&:hover": {
                                backgroundColor: "rgba(219, 74, 65, 0.1)",
                              },
                            }}
                          >
                            <TableCell sx={{ color: "white" }}>
                              {item.title}
                            </TableCell>
                            <TableCell sx={{ color: "white" }}>
                              {item.time}
                            </TableCell>
                            <TableCell sx={{ color: "white" }}>
                              {item.notes ? (
                                item.notes.split("\n").length > 1 ? (
                                  <ul
                                    style={{ margin: 0, paddingLeft: "20px" }}
                                  >
                                    {item.notes
                                      .split("\n")
                                      .filter((line) => line.trim())
                                      .map((line, i) => (
                                        <li key={i}>{line.trim()}</li>
                                      ))}
                                  </ul>
                                ) : (
                                  item.notes
                                )
                              ) : (
                                "—"
                              )}
                            </TableCell>
                            <TableCell>
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeFromArray("timing", index);
                                }}
                                sx={{ color: "#db4a41" }}
                                size="small"
                              >
                                ✕
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography
                    sx={{
                      color: "rgba(255, 255, 255, 0.5)",
                      mt: 2,
                      fontStyle: "italic",
                    }}
                  >
                    No timing items added yet
                  </Typography>
                )}
              </Grid>

              {/* Shot Lists Section */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", marginBottom: "10px" }}
                >
                  Shot Lists
                </Typography>

                {/* Add Shot List */}
                <Box sx={{ display: "flex", gap: 2, marginBottom: "15px" }}>
                  <TextField
                    label="Shot List Title"
                    value={newShotListTitle}
                    onChange={(e) => setNewShotListTitle(e.target.value)}
                    placeholder="Scene 1 - Opening"
                    sx={{
                      flex: 1,
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(219, 74, 65, 0.5)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                  <Button
                    onClick={() => addShotList(editingShoot?._id)}
                    variant="outlined"
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Add List
                  </Button>
                </Box>

                {/* Display Shot Lists */}
                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  {newShoot.shotLists &&
                    newShoot.shotLists.map((shotList, listIndex) => (
                      <Card
                        key={listIndex}
                        sx={{
                          backgroundColor: "rgba(255, 255, 255, 0.05)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                        }}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: "10px",
                            }}
                          >
                            <Typography variant="h6" sx={{ color: "#db4a41" }}>
                              {shotList.title}
                            </Typography>
                            <IconButton
                              onClick={() => {
                                if (editingShoot?._id && shotList._id) {
                                  deleteShotList(
                                    editingShoot._id,
                                    shotList._id
                                  );
                                } else {
                                  removeFromArray("shotLists", listIndex);
                                }
                              }}
                              sx={{ color: "#f44336" }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>

                          {/* Add Checklist Item */}
                          <Box
                            sx={{
                              display: "flex",
                              gap: 2,
                              marginBottom: "10px",
                            }}
                          >
                            <TextField
                              label="Checklist Item"
                              value={checklistInputs[listIndex] || ""}
                              onChange={(e) =>
                                setChecklistInputs((prev) => ({
                                  ...prev,
                                  [listIndex]: e.target.value,
                                }))
                              }
                              placeholder="Wide shot of room"
                              size="small"
                              sx={{
                                flex: 1,
                                "& .MuiOutlinedInput-root": {
                                  color: "white",
                                  "& fieldset": {
                                    borderColor: "rgba(255, 255, 255, 0.3)",
                                  },
                                  "&:hover fieldset": {
                                    borderColor: "rgba(219, 74, 65, 0.5)",
                                  },
                                  "&.Mui-focused fieldset": {
                                    borderColor: "#db4a41",
                                  },
                                },
                                "& .MuiInputLabel-root": {
                                  color: "rgba(255, 255, 255, 0.7)",
                                },
                              }}
                            />
                            <Button
                              onClick={() =>
                                addChecklistItem(
                                  listIndex,
                                  editingShoot?._id,
                                  shotList._id
                                )
                              }
                              variant="outlined"
                              size="small"
                              sx={{
                                borderColor: "#db4a41",
                                color: "#db4a41",
                                "&:hover": {
                                  borderColor: "#c62828",
                                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                                },
                              }}
                            >
                              Add
                            </Button>
                          </Box>

                          {/* Display Checklist Items */}
                          <List dense>
                            {shotList.checklist.map((item, itemIndex) => (
                              <ListItem
                                key={itemIndex}
                                sx={{
                                  padding: "4px 0",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                }}
                              >
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    flex: 1,
                                  }}
                                >
                                  <Checkbox
                                    checked={item.done}
                                    onChange={(e) => {
                                      if (
                                        editingShoot?._id &&
                                        shotList._id &&
                                        item._id
                                      ) {
                                        updateChecklistItem(
                                          editingShoot._id,
                                          shotList._id,
                                          item._id,
                                          e.target.checked
                                        );
                                      } else {
                                        setNewShoot((prev) => ({
                                          ...prev,
                                          shotLists: (prev.shotLists || []).map(
                                            (list, lIndex) =>
                                              lIndex === listIndex
                                                ? {
                                                    ...list,
                                                    checklist: (
                                                      list.checklist || []
                                                    ).map((checkItem, cIndex) =>
                                                      cIndex === itemIndex
                                                        ? {
                                                            ...checkItem,
                                                            done: e.target
                                                              .checked,
                                                          }
                                                        : checkItem
                                                    ),
                                                  }
                                                : list
                                          ),
                                        }));
                                      }
                                    }}
                                    sx={{
                                      color: "#db4a41",
                                      "&.Mui-checked": { color: "#db4a41" },
                                    }}
                                  />
                                  <ListItemText
                                    primary={item.text}
                                    sx={{
                                      "& .MuiListItemText-primary": {
                                        color: item.done
                                          ? "rgba(255, 255, 255, 0.5)"
                                          : "white",
                                        textDecoration: item.done
                                          ? "line-through"
                                          : "none",
                                      },
                                    }}
                                  />
                                </Box>
                                <IconButton
                                  onClick={() => {
                                    setNewShoot((prev) => ({
                                      ...prev,
                                      shotLists: (prev.shotLists || []).map(
                                        (list, lIndex) =>
                                          lIndex === listIndex
                                            ? {
                                                ...list,
                                                checklist: (
                                                  list.checklist || []
                                                ).filter(
                                                  (_, cIndex) =>
                                                    cIndex !== itemIndex
                                                ),
                                              }
                                            : list
                                      ),
                                    }));
                                  }}
                                  sx={{ color: "#f44336", padding: "4px" }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </ListItem>
                            ))}
                          </List>
                        </CardContent>
                      </Card>
                    ))}
                </Box>
              </Grid>

              {/* Notes */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={4}
                  value={newShoot.notes}
                  onChange={(e) =>
                    setNewShoot({ ...newShoot, notes: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(219, 74, 65, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#db4a41",
                      },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions sx={{ padding: "20px" }}>
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              variant="contained"
              disabled={loading}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
            >
              {loading ? (
                <CircularProgress size={20} />
              ) : editingShoot ? (
                "Update"
              ) : (
                "Add"
              )}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(26, 26, 26, 0.95)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              color: "white",
            },
          }}
        >
          <DialogTitle
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            Shoot Details
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {viewingShoot && (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<PictureAsPdfIcon />}
                    onClick={() => exportShootAsPDF(viewingShoot, employees)}
                    sx={{
                      color: "#db4a41",
                      borderColor: "#db4a41",
                      margin: "0 0.5rem",
                      "&:hover": {
                        borderColor: "white",
                      },
                    }}
                  >
                    Call Sheet
                  </Button>
                  {(user.role === "general_manager" ||
                    user.role === "account_manager") &&
                    viewingShoot.shotLists &&
                    viewingShoot.shotLists.length > 0 && (
                      <Button
                        variant="outlined"
                        startIcon={<PictureAsPdfIcon />}
                        onClick={() => exportShotListsAsPDF(viewingShoot)}
                        sx={{
                          color: "#db4a41",
                          borderColor: "#db4a41",
                          margin: "0 0.5rem",
                          "&:hover": {
                            borderColor: "white",
                          },
                        }}
                      >
                        Shot List
                      </Button>
                    )}
                </>
              )}
              <IconButton
                onClick={handleCloseViewModal}
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ padding: "20px" }}>
            {viewingShoot && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Typography
                      variant="h4"
                      sx={{ fontFamily: "Formula Bold", color: "white" }}
                    >
                      {viewingShoot.title}
                    </Typography>
                    {(user.role === "general_manager" ||
                      user.role === "account_manager") && (
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <FormControl size="small" sx={{ minWidth: 150 }}>
                          <InputLabel
                            sx={{
                              color: "rgba(255, 255, 255, 0.7)",
                              "&.Mui-focused": { color: "#db4a41" },
                            }}
                          >
                            Status
                          </InputLabel>
                          <Select
                            value={viewingShoot.status}
                            onChange={(e) =>
                              handleStatusChange(
                                viewingShoot._id,
                                e.target.value
                              )
                            }
                            sx={{
                              color: "white",
                              "& .MuiOutlinedInput-notchedOutline": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&:hover .MuiOutlinedInput-notchedOutline": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                              },
                              "&.Mui-focused .MuiOutlinedInput-notchedOutline":
                                {
                                  borderColor: "#db4a41",
                                },
                              "& .MuiSelect-icon": {
                                color: "rgba(255, 255, 255, 0.7)",
                              },
                            }}
                            MenuProps={{
                              PaperProps: {
                                sx: {
                                  backgroundColor: "rgba(26, 26, 26, 0.95)",
                                  backdropFilter: "blur(10px)",
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  "& .MuiMenuItem-root": {
                                    color: "white",
                                    "&:hover": {
                                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                                    },
                                    "&.Mui-selected": {
                                      backgroundColor: "rgba(219, 74, 65, 0.2)",
                                    },
                                  },
                                },
                              },
                            }}
                          >
                            <MenuItem value="scheduled">
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: 12,
                                    height: 12,
                                    borderRadius: "50%",
                                    backgroundColor:
                                      getStatusColor("scheduled"),
                                  }}
                                />
                                Scheduled
                              </Box>
                            </MenuItem>
                            <MenuItem value="in_progress">
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: 12,
                                    height: 12,
                                    borderRadius: "50%",
                                    backgroundColor:
                                      getStatusColor("in_progress"),
                                  }}
                                />
                                In Progress
                              </Box>
                            </MenuItem>
                            <MenuItem value="completed">
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: 12,
                                    height: 12,
                                    borderRadius: "50%",
                                    backgroundColor:
                                      getStatusColor("completed"),
                                  }}
                                />
                                Completed
                              </Box>
                            </MenuItem>
                            <MenuItem value="cancelled">
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: 12,
                                    height: 12,
                                    borderRadius: "50%",
                                    backgroundColor:
                                      getStatusColor("cancelled"),
                                  }}
                                />
                                Cancelled
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>

                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={viewingShoot.deliveredFootage}
                              disabled={viewingShoot.status !== "in_progress"}
                              onChange={(e) => {
                                if (viewingShoot.status !== "in_progress") {
                                  showSnackbar(
                                    "The shoot isn't finished yet!",
                                    "warning"
                                  );
                                  return;
                                }
                                handleFootageDelivery(
                                  viewingShoot._id,
                                  e.target.checked
                                );
                              }}
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&.Mui-checked": {
                                  color: "#4caf50",
                                },
                                "&.Mui-disabled": {
                                  color: "rgba(255, 255, 255, 0.3)",
                                },
                              }}
                            />
                          }
                          label={
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  color: viewingShoot.deliveredFootage
                                    ? "#4caf50"
                                    : "rgba(255, 255, 255, 0.7)",
                                  fontWeight: viewingShoot.deliveredFootage
                                    ? "bold"
                                    : "normal",
                                }}
                              >
                                Footage Delivered
                              </Typography>
                              {viewingShoot.status !== "in_progress" && (
                                <Tooltip title="Only available when shoot is in progress">
                                  <Typography
                                    variant="caption"
                                    sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                                  >
                                    (Requires in-progress status)
                                  </Typography>
                                </Tooltip>
                              )}
                            </Box>
                          }
                          sx={{
                            color: "rgba(255, 255, 255, 0.7)",
                            ml: 2,
                          }}
                        />
                      </Box>
                    )}
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 1 }}
                  >
                    Project
                  </Typography>
                  <Typography color="rgba(255, 255, 255, 0.8)">
                    {viewingShoot.projectId?.title || "No Project"}
                  </Typography>
                </Grid>

                {/* Key Roles */}
                {viewingShoot.creativeDirector && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Creative Director
                    </Typography>
                    <Typography color="rgba(255, 255, 255, 0.8)">
                      {getEmployeeName(viewingShoot.creativeDirector)}
                    </Typography>
                  </Grid>
                )}

                {viewingShoot.Director && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Director
                    </Typography>
                    <Typography color="rgba(255, 255, 255, 0.8)">
                      {getEmployeeName(viewingShoot.Director)}
                    </Typography>
                  </Grid>
                )}
                {viewingShoot.producer && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Producer
                    </Typography>
                    <Typography color="rgba(255, 255, 255, 0.8)">
                      {getEmployeeName(viewingShoot.producer)}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 1 }}
                  >
                    Location
                  </Typography>
                  <Typography color="rgba(255, 255, 255, 0.8)">
                    {viewingShoot.location}
                    {viewingShoot.addressLink && (
                      <>
                        {" "}
                        <a
                          href={viewingShoot.addressLink}
                          target="_blank"
                          rel="noreferrer"
                          style={{ color: "#9ecbff" }}
                        >
                          (Map)
                        </a>
                      </>
                    )}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 1 }}
                  >
                    Time
                  </Typography>
                  <Typography color="rgba(255, 255, 255, 0.8)">
                    {viewingShoot.startTime} - {viewingShoot.endTime}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ fontFamily: "Formula Bold", color: "#db4a41", mb: 1 }}
                  >
                    Date
                  </Typography>
                  <Typography color="rgba(255, 255, 255, 0.8)">
                    {formatDate(viewingShoot.date)}
                  </Typography>
                </Grid>

                {viewingShoot.props && viewingShoot.props.length > 0 && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Props
                    </Typography>
                    <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                      {viewingShoot.props.map((prop, index) => (
                        <Chip
                          key={index}
                          label={prop}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(219, 74, 65, 0.2)",
                            color: "#db4a41",
                          }}
                        />
                      ))}
                    </Box>
                  </Grid>
                )}

                {viewingShoot.equipment &&
                  viewingShoot.equipment.length > 0 && (
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#db4a41",
                          mb: 1,
                        }}
                      >
                        Equipment
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                        {viewingShoot.equipment.map((item, index) => (
                          <Chip
                            key={index}
                            label={item}
                            size="small"
                            sx={{
                              backgroundColor: "rgba(219, 74, 65, 0.2)",
                              color: "#db4a41",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}

                {viewingShoot.cast && viewingShoot.cast.length > 0 && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Cast
                    </Typography>
                    <List dense>
                      {viewingShoot.cast.map((member, index) => (
                        <ListItem key={index} sx={{ padding: "2px 0" }}>
                          <ListItemText
                            primary={`${member.name} - ${
                              member.startTime || ""
                            }${member.endTime ? ` to ${member.endTime}` : ""}`}
                            primaryTypographyProps={{
                              color: "rgba(255, 255, 255, 0.8)",
                              fontSize: "0.9rem",
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}

                {viewingShoot.crew && viewingShoot.crew.length > 0 && (
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Crew
                    </Typography>
                    <List dense>
                      {viewingShoot.crew.map((member, index) => (
                        <ListItem key={index} sx={{ padding: "2px 0" }}>
                          <ListItemText
                            primary={displayCrewMember(member)}
                            primaryTypographyProps={{
                              color: "rgba(255, 255, 255, 0.8)",
                              fontSize: "0.9rem",
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
                {viewingShoot.timing && viewingShoot.timing.length > 0 && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Schedule/Timing
                    </Typography>
                    <List dense>
                      {viewingShoot.timing.map((time, index) => (
                        <ListItem key={index} sx={{ padding: "2px 0" }}>
                          <ListItemText
                            primary={
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                              >
                                <Typography
                                  variant="body2"
                                  sx={{ fontSize: "0.9rem" }}
                                >
                                  {`${time.title} - ${time.time}`}
                                </Typography>
                                {time.notes && (
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontSize: "0.9rem",
                                      color: "rgba(255, 255, 255, 0.6)",
                                    }}
                                  >
                                    {time.notes}
                                  </Typography>
                                )}
                              </Box>
                            }
                            primaryTypographyProps={{
                              color: "rgba(255, 255, 255, 0.8)",
                              fontSize: "0.9rem",
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}

                {/* Shot Lists Section */}
                {(user.role === "general_manager" ||
                  user.role === "account_manager") &&
                  viewingShoot.shotLists &&
                  viewingShoot.shotLists.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#db4a41",
                          mb: 2,
                        }}
                      >
                        Shot Lists
                      </Typography>
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 2,
                        }}
                      >
                        {viewingShoot.shotLists &&
                          viewingShoot.shotLists.map((shotList, listIndex) => (
                            <Card
                              key={listIndex}
                              sx={{
                                backgroundColor: "rgba(255, 255, 255, 0.05)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <CardContent>
                                <Typography
                                  variant="h6"
                                  sx={{ color: "#db4a41", mb: 2 }}
                                >
                                  {shotList.title}
                                </Typography>
                                <List dense>
                                  {shotList.checklist &&
                                    shotList.checklist.map(
                                      (item, itemIndex) => (
                                        <ListItem
                                          key={itemIndex}
                                          sx={{
                                            padding: "4px 0",
                                            display: "flex",
                                            alignItems: "center",
                                          }}
                                        >
                                          <Checkbox
                                            checked={item.done}
                                            onChange={async (e) => {
                                              try {
                                                const token =
                                                  localStorage.getItem("token");
                                                const response = await fetch(
                                                  `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/${viewingShoot._id}/shotlists/${shotList._id}/checklist/${item._id}`,
                                                  {
                                                    method: "PATCH",
                                                    headers: {
                                                      "Content-Type":
                                                        "application/json",
                                                      Authorization: `Bearer ${token}`,
                                                    },
                                                    body: JSON.stringify({
                                                      done: e.target.checked,
                                                    }),
                                                  }
                                                );

                                                if (response.ok) {
                                                  const updatedShoot =
                                                    await response.json();
                                                  setShoots((prev) =>
                                                    prev.map((shoot) =>
                                                      shoot._id ===
                                                      viewingShoot._id
                                                        ? updatedShoot
                                                        : shoot
                                                    )
                                                  );
                                                  setViewingShoot(updatedShoot);
                                                  showSnackbar(
                                                    "Checklist item updated successfully",
                                                    "success"
                                                  );
                                                }
                                              } catch (error) {
                                                console.error(
                                                  "Error updating checklist item:",
                                                  error
                                                );
                                                showSnackbar(
                                                  "Failed to update checklist item",
                                                  "error"
                                                );
                                              }
                                            }}
                                            sx={{
                                              color: "#db4a41",
                                              "&.Mui-checked": {
                                                color: "#db4a41",
                                              },
                                            }}
                                          />
                                          <ListItemText
                                            primary={item.text}
                                            sx={{
                                              "& .MuiListItemText-primary": {
                                                color: item.done
                                                  ? "rgba(255, 255, 255, 0.5)"
                                                  : "rgba(255, 255, 255, 0.8)",
                                                textDecoration: item.done
                                                  ? "line-through"
                                                  : "none",
                                              },
                                            }}
                                          />
                                        </ListItem>
                                      )
                                    )}
                                </List>
                              </CardContent>
                            </Card>
                          ))}
                      </Box>
                    </Grid>
                  )}

                {viewingShoot.notes && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 1,
                      }}
                    >
                      Notes
                    </Typography>
                    <ul style={{ listStyle: "none", padding: 5, margin: 0 }}>
                      {viewingShoot.notes.split("\n").map((note, index) => (
                        <li
                          key={index}
                          style={{
                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            padding: "15px",
                            borderRadius: "8px",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            marginBottom: "10px",
                          }}
                        >
                          <Typography color="rgba(255, 255, 255, 0.8)">
                            {index + 1}. {note}
                          </Typography>
                        </li>
                      ))}
                    </ul>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions sx={{ padding: "20px" }}>
            <Button
              onClick={handleCloseViewModal}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Shoots;
