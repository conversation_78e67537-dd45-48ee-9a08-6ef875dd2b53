// import React, { useState, useEffect, useRef, useMemo } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import {
//   HiOutlineVolumeUp,
//   HiOutlineVolumeOff,
//   HiHome,
//   HiUser,
//   HiOutlineChat,
//   HiOutlineBookmark,
//   HiOutlineShare,
// } from "react-icons/hi";
// import { GoHeartFill } from "react-icons/go";
// import { FaUserFriends } from "react-icons/fa";
// import { AiOutlinePlus } from "react-icons/ai";
// import { useReelGallery } from "../hooks/useApi";
// import OptimizedVideo from "./OptimizedVideo";
// import LoadingScreen from "./LoadingScreen";

// const ReelPage = () => {
//   const { data: apiVideos = [], isLoading } = useReelGallery();
//   const [scrollProgress, setScrollProgress] = useState(0);
//   const [isFinished, setIsFinished] = useState(false);
//   const [currentVideo, setCurrentVideo] = useState(null);
//   const [isMuted, setIsMuted] = useState(true);
//   const [windowHeight, setWindowHeight] = useState(
//     typeof window !== "undefined" ? window.innerHeight : 800
//   );
//   const containerRef = useRef(null);
//   const sectionRef = useRef(null);
//   const spacerRef = useRef(null);
//   const mainVideoRef = useRef(null);

//   // Use unique videos only - no repetition
//   const videos = useMemo(() => {
//     return apiVideos || [];
//   }, [apiVideos]);

//   // Distribute videos across 6 columns
//   const columns = useMemo(() => {
//     const cols = [[], [], [], [], [], []];
//     videos.forEach((video, i) => {
//       cols[i % 6].push({ video, index: i });
//     });
//     return cols;
//   }, [videos]);

//   useEffect(() => {
//     if (isLoading) return;

//     // Calculate required scroll distance based on video count
//     // We need enough scroll to move all videos out of viewport
//     const maxVideos =
//       columns.length > 0 ? Math.max(...columns.map((col) => col.length)) : 0;
//     const currentHeight = window.innerHeight;
//     setWindowHeight(currentHeight);
//     // Adjust scroll distance based on video count, with minimum
//     const scrollDistance = Math.max(currentHeight * 3, maxVideos * 50);

//     const handleScroll = () => {
//       if (!spacerRef.current) return;

//       const scrollY = window.scrollY || window.pageYOffset;

//       // Calculate scroll progress (0 to 1)
//       // Progress starts when we've scrolled past initial viewport
//       const progress = Math.min(1, Math.max(0, scrollY / scrollDistance));

//       setScrollProgress(progress);

//       // Check if all videos have scrolled out
//       const isComplete = progress >= 0.98;
//       setIsFinished(isComplete);
//     };

//     // Set spacer height to create scroll space
//     // Footer will be positioned absolutely at the end of this scroll distance
//     if (spacerRef.current) {
//       spacerRef.current.style.height = `${
//         scrollDistance + window.innerHeight
//       }px`;
//     }

//     // Update footer position - place it right at the end of scroll distance using margin-top
//     const updateFooterPosition = () => {
//       if (containerRef.current) {
//         containerRef.current.style.marginTop = `${scrollDistance}px`;
//       }
//     };

//     // Update footer position after a short delay to ensure it's rendered
//     setTimeout(updateFooterPosition, 100);

//     // Initial calculation
//     handleScroll();

//     window.addEventListener("scroll", handleScroll, { passive: true });
//     window.addEventListener(
//       "resize",
//       () => {
//         // Recalculate on resize
//         const newHeight = window.innerHeight;
//         setWindowHeight(newHeight);
//         if (spacerRef.current) {
//           const maxVideos =
//             columns.length > 0
//               ? Math.max(...columns.map((col) => col.length))
//               : 0;
//           const newScrollDistance = Math.max(newHeight * 3, maxVideos * 50);
//           spacerRef.current.style.height = `${newScrollDistance}px`;
//           // Update footer position
//           if (containerRef.current) {
//             containerRef.current.style.marginTop = `${newScrollDistance}px`;
//           }
//         }
//         handleScroll();
//       },
//       { passive: true }
//     );

//     return () => {
//       window.removeEventListener("scroll", handleScroll);
//       window.removeEventListener("resize", handleScroll);
//     };
//   }, [isLoading, videos.length]);

//   // Ensure no extra scroll after footer
//   useEffect(() => {
//     if (isFinished && spacerRef.current && containerRef.current) {
//       const scrollDistance =
//         parseFloat(spacerRef.current.style.height) || window.innerHeight * 4;

//       // Ensure footer margin-top is set correctly
//       if (containerRef.current) {
//         containerRef.current.style.marginTop = `${scrollDistance}px`;
//       }

//       // After footer renders, ensure document height is correct
//       setTimeout(() => {
//         // The footer is now positioned with margin-top, so document height should be natural
//         // Just ensure we're not scrolling beyond the footer
//         const totalHeight = document.documentElement.scrollHeight;
//         if (window.scrollY + window.innerHeight > totalHeight) {
//           window.scrollTo(0, Math.max(0, totalHeight - window.innerHeight));
//         }
//       }, 200);
//     }
//   }, [isFinished]);

//   if (isLoading) return <LoadingScreen />;

//   // Calculate offset for each video based on scroll progress
//   const getVideoTransform = (colIndex, videoIndex, initialOffset) => {
//     // Each column and video gets different offset multipliers for staggered effect
//     const colOffset = colIndex * 0.15; // Column-based offset
//     const videoOffset = videoIndex * 0.1; // Video position offset
//     const randomOffset = ((colIndex * 7 + videoIndex * 3) % 8) / 80; // Slight randomness (0-0.1)

//     // Base movement - videos move upward as we scroll
//     const baseMovement = scrollProgress * windowHeight * 5;

//     // Staggered movement - each video moves at different speed for misalignment
//     const totalOffset = colOffset + videoOffset + randomOffset;
//     const scrollTranslateY = -baseMovement * (1 + totalOffset);

//     // Add horizontal offset for misalignment (swaying/waving effect)
//     const translateX =
//       Math.sin(
//         scrollProgress * Math.PI * 2.5 + colIndex * 0.8 + videoIndex * 0.3
//       ) *
//       50 *
//       scrollProgress;

//     // Combine initial offset with scroll-based movement
//     const totalTranslateY = initialOffset + scrollTranslateY;

//     return `translate(${translateX}px, ${totalTranslateY}px)`;
//   };

//   const getVideoOpacity = (colIndex, videoIndex) => {
//     const maxVideos = Math.max(...columns.map((col) => col.length), 1);
//     // Calculate when this video should fade out based on its position
//     const videoStartProgress = (videoIndex / maxVideos) * 0.4;
//     const videoEndProgress = videoStartProgress + 0.6;

//     if (scrollProgress < videoStartProgress) return 1;
//     if (scrollProgress > videoEndProgress) return 0;

//     // Fade out as it exits
//     return (
//       1 -
//       (scrollProgress - videoStartProgress) /
//         (videoEndProgress - videoStartProgress)
//     );
//   };

//   return (
//     <div
//       style={{
//         backgroundColor: "#000",
//         position: "relative",
//         minHeight: "100vh",
//       }}
//     >
//       {/* Spacer for scroll control - creates scroll distance */}
//       <div ref={spacerRef} style={{ height: "400vh" }} />

//       {/* Fixed Reel Section - stays in place while scrolling */}
//       <div
//         ref={sectionRef}
//         style={{
//           position: "fixed",
//           top: 0,
//           left: 0,
//           width: "100vw",
//           height: "100vh",
//           backgroundColor: "#000",
//           display: "flex",
//           overflow: "hidden",
//           zIndex: isFinished ? 1 : 100,
//           pointerEvents: isFinished ? "none" : "auto",
//         }}
//       >
//         {/* Center Sticky Text */}
//         <div
//           style={{
//             position: "absolute",
//             top: "50%",
//             left: "50%",
//             transform: "translate(-50%, -50%)",
//             zIndex: 100,
//             color: "#fff",
//             fontFamily: "Formula Bold",
//             fontSize: "clamp(2rem, 5vw, 8rem)",
//             whiteSpace: "nowrap",
//             pointerEvents: "none",
//             mixBlendMode: "difference",
//             textAlign: "center",
//           }}
//         >
//           Playing
//           <br />
//           The Long Game?
//         </div>

//         {/* 6 Columns */}
//         {columns.map((column, colIndex) => (
//           <div
//             key={colIndex}
//             style={{
//               flex: 1,
//               display: "flex",
//               flexDirection: "column",
//               position: "relative",
//               borderRight:
//                 colIndex < 5 ? "1px solid rgba(255, 255, 255, 0.3)" : "none",
//               overflow: "visible",
//             }}
//           >
//             {/* Column Videos */}
//             <div
//               style={{
//                 display: "flex",
//                 flexDirection: "column",
//                 gap: "20px",
//                 padding: "20px 10px",
//                 position: "relative",
//               }}
//             >
//               {column.map(({ video, index }) => {
//                 // Calculate initial vertical position - stagger videos
//                 // Start videos at different positions so they can scroll upward
//                 const initialOffset = windowHeight * 0.2 + index * 300; // Space videos vertically

//                 return (
//                   <div
//                     key={`${video._id || index}-${colIndex}-${index}`}
//                     style={{
//                       position: "relative",
//                       width: "100%",
//                       aspectRatio: "9/16",
//                       transform: getVideoTransform(
//                         colIndex,
//                         index,
//                         initialOffset
//                       ),
//                       opacity: getVideoOpacity(colIndex, index),
//                       transition: "opacity 0.3s ease",
//                       cursor: "pointer",
//                     }}
//                     onClick={() => setCurrentVideo(video)}
//                   >
//                     <OptimizedVideo
//                       src={video.src}
//                       muted
//                       loop
//                       autoPlay
//                       playsInline
//                       lazy={false}
//                       style={{
//                         width: "100%",
//                         height: "100%",
//                         objectFit: "cover",
//                         borderRadius: "8px",
//                       }}
//                     />
//                   </div>
//                 );
//               })}
//             </div>
//           </div>
//         ))}
//       </div>

//       {/* Footer - appears after scroll completes, positioned at end of scroll */}
//       {/* <div
//         ref={containerRef}
//         style={{
//           position: "relative",
//           width: "100%",
//           zIndex: isFinished ? 10 : 1,
//           opacity: isFinished ? 1 : 0,
//           transition: "opacity 0.8s ease",
//           pointerEvents: isFinished ? "auto" : "none",
//           backgroundColor: "#000",
//           marginTop: 0,
//         }}
//       >
//         <Footer />
//       </div> */}

//       {/* Mobile Mockup Modal */}
//       <AnimatePresence mode="wait">
//         {currentVideo && (
//           <motion.div
//             className="modal-overlay"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             exit={{ opacity: 0 }}
//             onClick={() => setCurrentVideo(null)}
//             style={{
//               position: "fixed",
//               inset: 0,
//               background: "rgba(0, 0, 0, 0.8)",
//               backdropFilter: "blur(8px)",
//               display: "flex",
//               alignItems: "center",
//               justifyContent: "center",
//               zIndex: 10000,
//             }}
//           >
//             <motion.div
//               key={currentVideo._id}
//               className="iphone-wrapper"
//               initial={{ opacity: 0, scale: 0.7 }}
//               animate={{ opacity: 1, scale: 1 }}
//               exit={{ opacity: 0, scale: 0.7 }}
//               transition={{ duration: 0.4, ease: "easeOut" }}
//               onClick={(e) => e.stopPropagation()}
//               onMouseEnter={() => {
//                 if (mainVideoRef.current) {
//                   mainVideoRef.current.muted = false;
//                   setIsMuted(false);
//                   mainVideoRef.current.play().catch(() => {});
//                 }
//               }}
//               onMouseLeave={() => {
//                 if (mainVideoRef.current) {
//                   mainVideoRef.current.muted = true;
//                   setIsMuted(true);
//                 }
//               }}
//             >
//               <div className="notch-style" />

//               <OptimizedVideo
//                 ref={mainVideoRef}
//                 className="main-video"
//                 src={currentVideo.src}
//                 autoPlay
//                 playsInline
//                 preload="auto"
//                 muted={isMuted}
//                 lazy={false}
//                 onCanPlay={(e) => e.target.play().catch(() => {})}
//                 onEnded={() => {
//                   const currentIndex = videos.findIndex(
//                     (v) => v._id === currentVideo._id
//                   );
//                   const nextIndex = (currentIndex + 1) % videos.length;
//                   setCurrentVideo(videos[nextIndex]);
//                 }}
//               />

//               {/* TikTok right-side actions */}
//               <div className="actions" style={{ bottom: "75px" }}>
//                 <div
//                   style={{
//                     borderRadius: "100%",
//                     border: "1px solid #fff",
//                     width: "40px",
//                     height: "40px",
//                     display: "flex",
//                     alignItems: "center",
//                     justifyContent: "center",
//                     marginBottom: "18px",
//                   }}
//                 >
//                   <img
//                     src="/assets/young-logo-white.webp"
//                     alt="logo"
//                     width={30}
//                     height={7}
//                   />
//                 </div>
//                 <div className="action-btn">
//                   <AiOutlinePlus size={20} />
//                 </div>
//                 <div className="action-btn">
//                   <GoHeartFill color="red" size={20} />
//                   <span>12.3K</span>
//                 </div>
//                 <div className="action-btn">
//                   <HiOutlineChat size={20} />
//                   <span>250K</span>
//                 </div>
//                 <div className="action-btn">
//                   <HiOutlineBookmark color="Yellow" fill="Yellow" size={20} />
//                   <span>Save</span>
//                 </div>
//                 <div className="action-btn">
//                   <HiOutlineShare size={20} />
//                   <span>Share</span>
//                 </div>
//               </div>

//               {/* Bottom nav */}
//               <div
//                 className="bottom-nav"
//                 style={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}
//               >
//                 <HiHome className="bottom-icon" />
//                 <FaUserFriends className="bottom-icon" />
//                 <AiOutlinePlus
//                   className="bottom-icon"
//                   style={{ fontSize: "24px" }}
//                 />
//                 <HiOutlineChat className="bottom-icon" />
//                 <HiUser className="bottom-icon" />
//               </div>

//               {/* Mute/unmute icon */}
//               <div className="speaker-icon">
//                 {isMuted ? (
//                   <HiOutlineVolumeOff size={20} color="white" />
//                 ) : (
//                   <HiOutlineVolumeUp size={20} color="white" />
//                 )}
//               </div>

//               <div
//                 className="video-title"
//                 style={{ fontFamily: "Formula Bold", fontSize: 16 }}
//               >
//                 {currentVideo.title || "Young Productions"}
//               </div>
//               <div
//                 className="video-subtitle"
//                 style={{
//                   fontFamily: "Formula Bold",
//                   color: "#fff",
//                   fontSize: 16,
//                 }}
//               >
//                 Young Productions
//               </div>
//             </motion.div>
//           </motion.div>
//         )}
//       </AnimatePresence>
//     </div>
//   );
// };

// export default ReelPage;
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  HiOutlineVolumeUp,
  HiOutlineVolumeOff,
  HiHome,
  HiUser,
  HiOutlineChat,
  HiOutlineBookmark,
  HiOutlineShare,
} from "react-icons/hi";
import { GoHeartFill } from "react-icons/go";
import { FaUserFriends } from "react-icons/fa";
import { AiOutlinePlus } from "react-icons/ai";
import { useReelGallery } from "../hooks/useApi";
import OptimizedVideo from "./OptimizedVideo";
import LoadingScreen from "./LoadingScreen";

// Lazy-loaded Video Item
const LazyVideoItem = React.memo(
  ({ video, style, onClick }) => {
    const ref = useRef();
    const [visible, setVisible] = useState(false);

    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) setVisible(true);
        },
        { rootMargin: "300px" }
      );
      if (ref.current) observer.observe(ref.current);
      return () => observer.disconnect();
    }, []);

    return (
      <div ref={ref} style={style} onClick={onClick}>
        {visible && (
          <OptimizedVideo
            src={video.src}
            autoPlay
            loop
            muted
            playsInline
            lazy={true}
            style={{ width: "100%", height: "100%", objectFit: "cover" }}
          />
        )}
      </div>
    );
  },
  (prev, next) => prev.video._id === next.video._id && prev.style === next.style
);

LazyVideoItem.displayName = "LazyVideoItem";

const ReelPage = () => {
  const { data: apiVideos = [], isLoading } = useReelGallery();
  const [scrollProgress, setScrollProgress] = useState(0);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [isMuted, setIsMuted] = useState(true);
  const [windowHeight, setWindowHeight] = useState(
    typeof window !== "undefined" ? window.innerHeight : 800
  );
  const [columnCount, setColumnCount] = useState(6);
  const [isMobile, setIsMobile] = useState(false);

  const spacerRef = useRef(null);
  const mainVideoRef = useRef(null);
  const rafRef = useRef(null);

  // =============================
  // RESPONSIVE COLUMN COUNT
  // =============================
  useEffect(() => {
    const updateColumns = () => {
      const w = window.innerWidth;
      const mobile = w < 768;
      setIsMobile(mobile);

      if (mobile) setColumnCount(3);
      else if (w < 1024) setColumnCount(4);
      else setColumnCount(6);
    };

    updateColumns();
    window.addEventListener("resize", updateColumns);
    return () => window.removeEventListener("resize", updateColumns);
  }, []);

  const videos = useMemo(() => apiVideos || [], [apiVideos]);

  const columns = useMemo(() => {
    const cols = Array.from({ length: columnCount }, () => []);
    videos.forEach((video, i) => {
      cols[i % columnCount].push({ video, index: i });
    });
    return cols;
  }, [videos, columnCount]);

  // =============================
  // SCROLL LOGIC WITH RAF
  // =============================
  useEffect(() => {
    if (isLoading) return;

    const maxVideos = Math.max(...columns.map((c) => c.length), 1);
    const scrollDistance = isMobile
      ? window.innerHeight * 2.5
      : Math.max(window.innerHeight * 3, maxVideos * 60);

    const handleScroll = () => {
      if (rafRef.current) cancelAnimationFrame(rafRef.current);

      rafRef.current = requestAnimationFrame(() => {
        const progress = Math.min(
          1,
          Math.max(0, window.scrollY / scrollDistance)
        );
        setScrollProgress(progress);
      });
    };

    if (spacerRef.current)
      spacerRef.current.style.height = `${
        scrollDistance + window.innerHeight
      }px`;

    setWindowHeight(window.innerHeight);
    handleScroll();

    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener(
      "resize",
      () => setWindowHeight(window.innerHeight),
      { passive: true }
    );

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", () =>
        setWindowHeight(window.innerHeight)
      );
      if (rafRef.current) cancelAnimationFrame(rafRef.current);
    };
  }, [isLoading, columns, columnCount, isMobile]);

  // =============================
  // TRANSFORM & OPACITY HELPERS
  // =============================
  const getVideoTransform = useCallback(
    (colIndex, videoIndex) => {
      const movementMultiplier = isMobile ? 1.2 : 5;
      const baseMovement = scrollProgress * windowHeight * movementMultiplier;

      const colOffset = colIndex * (isMobile ? 0.08 : 0.15);
      const videoOffset = videoIndex * (isMobile ? 0.05 : 0.1);
      const randomOffset =
        ((colIndex * 7 + videoIndex * 3) % 8) / (isMobile ? 120 : 80);

      const translateY =
        videoIndex * 200 -
        baseMovement * (1 + colOffset + videoOffset + randomOffset);

      const translateX = isMobile
        ? 0
        : Math.sin(
            scrollProgress * Math.PI * 2.5 + colIndex * 0.8 + videoIndex * 0.3
          ) *
          50 *
          scrollProgress;

      return `translate(${translateX}px, ${translateY}px)`;
    },
    [scrollProgress, windowHeight, isMobile]
  );

  const getVideoOpacity = useCallback(
    (colIndex, videoIndex) => {
      if (scrollProgress > 0.95)
        return Math.max(0, 1 - (scrollProgress - 0.95) / 0.05);
      return 1;
    },
    [scrollProgress]
  );

  const handleVideoClick = useCallback((video) => setCurrentVideo(video), []);
  const handleModalClose = useCallback(() => setCurrentVideo(null), []);
  const handleVideoEnded = useCallback(() => {
    const currentIndex = videos.findIndex((v) => v._id === currentVideo._id);
    const nextIndex = (currentIndex + 1) % videos.length;
    setCurrentVideo(videos[nextIndex]);
  }, [videos, currentVideo]);

  const handleMouseEnter = useCallback(() => {
    if (mainVideoRef.current) {
      mainVideoRef.current.muted = false;
      setIsMuted(false);
      mainVideoRef.current.play().catch(() => {});
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (mainVideoRef.current) {
      mainVideoRef.current.muted = true;
      setIsMuted(true);
    }
  }, []);

  if (isLoading) return <LoadingScreen />;

  return (
    <div
      style={{ background: "#000", minHeight: "100vh", position: "relative" }}
    >
      <div ref={spacerRef} />

      <div
        style={{
          position: "fixed",
          inset: 0,
          display: "flex",
          overflow: "hidden",
          zIndex: 100,
          touchAction: "pan-y",
        }}
      >
        {/* CENTER TEXT */}
        <div
          style={{
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#fff",
            fontFamily: "Formula Bold, sans-serif",
            fontSize: isMobile
              ? "clamp(1.5rem, 10vw, 3rem)"
              : "clamp(2rem, 6vw, 8rem)",
            textAlign: "center",
            mixBlendMode: "difference",
            pointerEvents: "none",
            zIndex: 20,
            lineHeight: 1.1,
            padding: isMobile ? "0 20px" : "0",
          }}
        >
          Playing
          <br />
          The Long Game?
        </div>

        {/* COLUMNS */}
        {columns.map((column, colIndex) => (
          <div
            key={colIndex}
            style={{
              flex: 1,
              borderRight:
                colIndex < columnCount - 1
                  ? "1px solid rgba(255,255,255,0.3)"
                  : "none",
            }}
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: isMobile ? "10px" : "20px",
                padding: isMobile ? "8px 4px" : "20px 10px",
              }}
            >
              {column.map(({ video, index }) => (
                <LazyVideoItem
                  key={video._id}
                  video={video}
                  onClick={() => handleVideoClick(video)}
                  style={{
                    aspectRatio: "9 / 16",
                    transform: getVideoTransform(colIndex, index),
                    opacity: getVideoOpacity(colIndex, index),
                    cursor: "pointer",
                    transition: "opacity 0.3s ease",
                    willChange: "transform, opacity",
                    borderRadius: isMobile ? 4 : 8,
                  }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* MODAL */}
      <AnimatePresence mode="wait">
        {currentVideo && (
          <motion.div
            className="modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleModalClose}
            style={{
              position: "fixed",
              inset: 0,
              background: "rgba(0,0,0,0.8)",
              backdropFilter: "blur(8px)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 10000,
            }}
          >
            <motion.div
              key={currentVideo._id}
              className="iphone-wrapper"
              initial={{ opacity: 0, scale: 0.7 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.7 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              onClick={(e) => e.stopPropagation()}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <div className="notch-style" />
              <OptimizedVideo
                ref={mainVideoRef}
                className="main-video"
                src={currentVideo.src}
                autoPlay
                playsInline
                preload="auto"
                muted={isMuted}
                lazy={false}
                onCanPlay={(e) => e.target.play().catch(() => {})}
                onEnded={handleVideoEnded}
              />

              {/* TikTok right-side actions */}
              <div className="actions" style={{ bottom: "75px" }}>
                <div
                  style={{
                    borderRadius: "100%",
                    border: "1px solid #fff",
                    width: "40px",
                    height: "40px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    marginBottom: "18px",
                  }}
                >
                  <img
                    src="/assets/young-logo-white.webp"
                    alt="logo"
                    width={30}
                  />
                </div>
                <div className="action-btn">
                  <AiOutlinePlus size={20} />
                </div>
                <div className="action-btn">
                  <GoHeartFill color="red" size={20} />
                  <span>12.3K</span>
                </div>
                <div className="action-btn">
                  <HiOutlineChat size={20} />
                  <span>250K</span>
                </div>
                <div className="action-btn">
                  <HiOutlineBookmark color="Yellow" fill="Yellow" size={20} />
                  <span>Save</span>
                </div>
                <div className="action-btn">
                  <HiOutlineShare size={20} />
                  <span>Share</span>
                </div>
              </div>

              {/* Bottom nav */}
              <div
                className="bottom-nav"
                style={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}
              >
                <HiHome className="bottom-icon" />
                <FaUserFriends className="bottom-icon" />
                <AiOutlinePlus
                  className="bottom-icon"
                  style={{ fontSize: "24px" }}
                />
                <HiOutlineChat className="bottom-icon" />
                <HiUser className="bottom-icon" />
              </div>

              {/* Mute/unmute icon */}
              <div className="speaker-icon">
                {isMuted ? (
                  <HiOutlineVolumeOff size={20} color="white" />
                ) : (
                  <HiOutlineVolumeUp size={20} color="white" />
                )}
              </div>

              <div
                className="video-title"
                style={{ fontFamily: "Formula Bold, sans-serif", fontSize: 16 }}
              >
                {currentVideo.title || "Young Productions"}
              </div>
              <div
                className="video-subtitle"
                style={{
                  fontFamily: "Formula Bold, sans-serif",
                  color: "#fff",
                  fontSize: 16,
                }}
              >
                Young Productions
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ReelPage;
