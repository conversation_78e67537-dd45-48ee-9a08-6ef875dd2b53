import React, { useState, useEffect } from "react";
import { Box, Typography, Paper, useTheme, useMediaQuery } from "@mui/material";
import { motion } from "framer-motion";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import LoadingScreen from "./LoadingScreen";

const Testimonial = ({ scrollDirection = "down" }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [testimonials, setTestimonials] = useState([]);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/testimonials"
        );
        if (!response.ok) throw new Error("Network response was not ok");
        const data = await response.json();
        setTestimonials(data);
        setError(null);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching testimonials:", error);
        setError(error.message);
        setLoading(false);
        if (retryCount < maxRetries) {
          setTimeout(() => setRetryCount(retryCount + 1), 1000);
        }
      }
    };
    fetchData();
  }, [retryCount, error]);

  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToScroll: 1,
    slidesToShow: 2,
    swipeToSlide: true,
    autoplay: true,
    autoplaySpeed: 5000,
    centerMode: true,
    pauseOnHover: true,
    responsive: [
      {
        breakpoint: 768,
        settings: { slidesToShow: 1 },
      },
    ],
  };

  if (loading) return <LoadingScreen />;

  // Animation variants based on scroll direction
  const captionVariants = {
    hidden: { opacity: 0, x: scrollDirection === "down" ? -50 : 50 }, // Reduced distance to prevent overflow
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: "easeOut" },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, x: scrollDirection === "down" ? 50 : -50 }, // Reduced distance to prevent overflow
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: "easeOut" },
    },
  };

  return (
    <Box
      sx={{
        padding: isMobile ? "50px" : "100px",
        backgroundColor: "black",
        overflow: "hidden", // Prevent horizontal overflow from animations
      }}
    >
      <Box
        component={motion.div}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: false, amount: 0.2 }}
        variants={{}} // Parent container for stagger, if needed
        sx={{
          display: "flex",
          flexDirection:
            isMobile || window.innerWidth <= 768 || window.innerWidth <= 1024
              ? "column"
              : "row",
          justifyContent: "space-around",
          gap: isMobile ? "20px" : "50px",
        }}
        className="testimonial-section"
      >
        {/* Caption / Heading */}
        <motion.div
          variants={captionVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.2 }}
          style={{ maxWidth: "600px" }}
        >
          <Typography variant="body1" sx={{ color: "white" }}>
            Proud
          </Typography>

          <Typography
            variant="h2"
            sx={{
              marginTop: "20px",
              fontFamily: "Formula Bold",
              color: "white",
            }}
          >
            What our dear<span style={{ color: "#DB4A41" }}> friends </span>and
            <span style={{ color: "#DB4A41" }}> clients</span> say...
          </Typography>
        </motion.div>

        {/* Cards */}
        <motion.div
          variants={cardVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.2 }}
          style={{
            width:
              isMobile || window.innerWidth <= 768 || window.innerWidth <= 1024
                ? "100%"
                : "50%",
            maxWidth: "800px",
            overflow: "hidden", // Prevent slider overflow
          }}
        >
          <Slider {...sliderSettings}>
            {testimonials.map((testimonial, index) => (
              <Paper
                className="testimonial-card"
                key={index}
                sx={{
                  backgroundColor: "black",
                  color: "white",
                  minHeight: "300px",
                  whiteSpace: "pre-wrap",
                  border: "1px solid white",
                  borderRadius: "20px",
                  padding: "30px",
                  position: "relative",
                }}
              >
                <img
                  src={testimonial.logoUrl}
                  alt={`Client ${index + 1}`}
                  style={{ width: "80px" }}
                />
                <Typography
                  variant="h4"
                  sx={{
                    fontStyle: "italic",
                    fontFamily: "Formula Bold",
                    letterSpacing: "1px",
                    textAlign: "left",
                    fontSize: "20px",
                    lineHeight: "1.5",
                    marginBottom: "60px",
                  }}
                >
                  "{testimonial.comment}"
                </Typography>
                <Typography
                  variant="subtitle1"
                  sx={{ position: "absolute", bottom: "45px" }}
                >
                  {testimonial.name}
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{
                    color: "gray",
                    position: "absolute",
                    bottom: "25px",
                    fontFamily: "Anton",
                  }}
                >
                  {testimonial.role}
                </Typography>
              </Paper>
            ))}
          </Slider>
        </motion.div>
      </Box>
    </Box>
  );
};

export default Testimonial;
