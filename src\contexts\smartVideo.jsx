// SmartHeroVideo.jsx
import { useRef, useEffect, useState, forwardRef } from "react";

const SmartHeroVideo = forwardRef(({ src, lazy = true, delay = 0, ...props }, ref) => {
  const videoRef = useRef(null);
  const [videoSrc, setVideoSrc] = useState(lazy ? undefined : src);
  const [shouldLoad, setShouldLoad] = useState(!lazy);
  const observerRef = useRef(null);

  // Combine refs properly for GSAP
  useEffect(() => {
    const node = videoRef.current;
    if (!node) return;

    if (typeof ref === "function") {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  }, [ref]);

  // IntersectionObserver for lazy loading with staggered delay
  useEffect(() => {
    if (!lazy || shouldLoad) return;

    const node = videoRef.current;
    if (!node) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (delay > 0) {
              setTimeout(() => {
                setShouldLoad(true);
                setVideoSrc(src);
                observerRef.current?.disconnect();
              }, delay);
            } else {
              setShouldLoad(true);
              setVideoSrc(src);
              observerRef.current?.disconnect();
            }
          }
        });
      },
      { rootMargin: "50px", threshold: 0 }
    );

    observerRef.current.observe(node);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy, src, shouldLoad, delay]);

  return (
    <video
      ref={videoRef}
      src={shouldLoad ? videoSrc : undefined}
      preload={lazy && !shouldLoad ? "none" : "metadata"}
      autoPlay
      muted
      loop
      playsInline
      style={{ willChange: "transform" }}
      {...props}
    />
  );
});

export default SmartHeroVideo;
