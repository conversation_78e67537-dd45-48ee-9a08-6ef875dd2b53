import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Typography,
  Button,
  TextField,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  LinearProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import Vimeo from "@u-wave/react-vimeo";
import { motion, AnimatePresence } from "framer-motion";

// Helper function to extract Vimeo ID from URL
const getVimeoId = (url) => {
  if (!url) return null;
  const match = url.match(/vimeo\.com\/(?:video\/)?(\d+)/);
  return match ? match[1] : null;
};

// Video upload utility functions using signed URLs
const getSignedUrl = async (videoFile) => {
  const token = localStorage.getItem("token");
  if (!token) {
    throw new Error("Authentication token not found");
  }

  const response = await fetch(
    "https://youngproductions-768ada043db3.herokuapp.com/api/work/signed-url",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        fileName: videoFile.name,
        fileType: videoFile.type,
        fileSize: videoFile.size,
      }),
    }
  );

  if (!response.ok) {
    // Try the v2 endpoint if the first one fails
    const v2Response = await fetch(
      "https://youngproductions-768ada043db3.herokuapp.com/api/work/signed-url-v2",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          fileName: videoFile.name,
          fileType: videoFile.type,
          fileSize: videoFile.size,
        }),
      }
    );

    if (!v2Response.ok) {
      throw new Error("Failed to get signed URL from both endpoints");
    }

    const v2Data = await v2Response.json();
    return v2Data.data;
  }

  const data = await response.json();
  return data.data;
};

const uploadVideoToR2 = async (videoFile, signedUrlData, onProgress) => {
  const response = await fetch(signedUrlData.signedUrl, {
    method: "PUT",
    headers: {
      "Content-Type": videoFile.type,
    },
    body: videoFile,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
  }

  // Simulate progress for user feedback
  onProgress(100);
  return signedUrlData.publicUrl;
};

const validateVideoFile = (file) => {
  const allowedTypes = [
    "video/mp4",
    "video/quicktime",
    "video/x-msvideo",
    "video/x-matroska",
    "video/webm",
  ];
  const maxSize = 1024 * 1024 * 1024; // 1GB

  if (!allowedTypes.includes(file.type)) {
    throw new Error("Invalid file type. Only video files are allowed.");
  }

  if (file.size > maxSize) {
    throw new Error("File too large. Maximum size is 1GB.");
  }

  return true;
};

const handleUploadWithRetry = async (videoFile, maxRetries = 3, onProgress) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const signedUrlData = await getSignedUrl(videoFile);
      const videoUrl = await uploadVideoToR2(
        videoFile,
        signedUrlData,
        onProgress
      );
      return videoUrl;
    } catch (error) {
      console.log(`Attempt ${attempt} failed:`, error.message);

      if (attempt === maxRetries) {
        throw new Error(
          `Upload failed after ${maxRetries} attempts: ${error.message}`
        );
      }

      // Wait before retry
      await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
    }
  }
};

const createWorkWithData = async (workData, videoUrl, otherFiles = {}) => {
  const token = localStorage.getItem("token");
  if (!token) {
    throw new Error("Authentication token not found");
  }

  const formData = new FormData();

  // Required fields
  formData.append("title", workData.title.trim());
  formData.append("description", workData.description.trim());
  formData.append("location", workData.location.trim());
  formData.append("date", workData.date);
  formData.append("headquarters", workData.headquarters.trim());
  formData.append("videoUrl", videoUrl);

  // Optional social media fields
  if (workData.facebook) formData.append("facebook", workData.facebook.trim());
  if (workData.instagram)
    formData.append("instagram", workData.instagram.trim());
  if (workData.tiktok) formData.append("tiktok", workData.tiktok.trim());

  // Optional file uploads
  if (otherFiles.imageFile && otherFiles.imageFile instanceof File) {
    formData.append("imageUrl", otherFiles.imageFile);
  }
  if (otherFiles.logoFile && otherFiles.logoFile instanceof File) {
    formData.append("logoUrl", otherFiles.logoFile);
  }
  if (otherFiles.galleryFiles && otherFiles.galleryFiles.length > 0) {
    otherFiles.galleryFiles.forEach((file) => {
      if (file instanceof File) {
        formData.append("galleryImages", file);
      }
    });
  }

  const response = await fetch(
    "https://youngproductions-768ada043db3.herokuapp.com/api/work",
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create work: ${response.status} - ${errorText}`);
  }

  return await response.json();
};

function Work() {
  const [workVideos, setWorkVideos] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingVideo, setEditingVideo] = useState(null);
  const [newVideo, setNewVideo] = useState({
    title: "",
    description: "",
    location: "",
    date: "",
    headquarters: "",
    facebook: "",
    instagram: "",
    tiktok: "",
    // File objects for upload (not stored in DB)
    videoFile: null,
    imageFile: null,
    logoFile: null,
    galleryFiles: [],
    // Existing URLs for editing (stored in DB)
    videoUrl: "",
    imageUrl: "",
    logoUrl: "",
    galleryImages: [],
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({
    video: { loading: false, progress: 0, currentChunk: 0, totalChunks: 0 },
    image: { loading: false, progress: 0 },
    logo: { loading: false, progress: 0 },
    gallery: { loading: false, progress: 0 },
  });

  useEffect(() => {
    fetchWorkVideos();
  }, []);

  const fetchWorkVideos = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/work",
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = await response.json();
      setWorkVideos(data);
    } catch (error) {
      console.error("Error fetching work:", error);
      showSnackbar("Failed to fetch work", "error");
    }
  };

  const handleAdd = () => {
    setEditingVideo(null);
    setNewVideo({
      title: "",
      description: "",
      location: "",
      date: "",
      headquarters: "",
      facebook: "",
      instagram: "",
      tiktok: "",
      videoFile: null,
      imageFile: null,
      logoFile: null,
      galleryFiles: [],
      videoUrl: "",
      imageUrl: "",
      logoUrl: "",
      galleryImages: [],
    });
    setOpenModal(true);
  };

  const handleEdit = (video) => {
    setEditingVideo(video);
    setNewVideo({
      title: video.title || "",
      description: video.description || "",
      location: video.location || "",
      date: video.date ? video.date.split("T")[0] : "", // Format date for input
      headquarters: video.headquarters || "",
      facebook: video.facebook || "",
      instagram: video.instagram || "",
      tiktok: video.tiktok || "",
      // Clear file objects for editing
      videoFile: null,
      imageFile: null,
      logoFile: null,
      galleryFiles: [],
      // Set existing URLs
      videoUrl: video.videoUrl || "",
      imageUrl: video.imageUrl || "",
      logoUrl: video.logoUrl || "",
      galleryImages: video.galleryImages || [],
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingVideo(null);
    setNewVideo({
      title: "",
      description: "",
      location: "",
      date: "",
      headquarters: "",
      facebook: "",
      instagram: "",
      tiktok: "",
      videoFile: null,
      imageFile: null,
      logoFile: null,
      galleryFiles: [],
      videoUrl: "",
      imageUrl: "",
      logoUrl: "",
      galleryImages: [],
    });
    setUploadProgress({
      video: { loading: false, progress: 0, currentChunk: 0, totalChunks: 0 },
      image: { loading: false, progress: 0 },
      logo: { loading: false, progress: 0 },
      gallery: { loading: false, progress: 0 },
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewVideo({
      ...newVideo,
      [name]: value,
    });
  };

  const handleFileChange = async (e, fileType) => {
    const files = e.target.files;

    if (!files || files.length === 0) return;

    // Set loading state for this file type
    const progressKey =
      fileType === "videoFile"
        ? "video"
        : fileType === "imageFile"
        ? "image"
        : fileType === "logoFile"
        ? "logo"
        : "gallery";

    // For video files, validate and handle differently
    if (fileType === "videoFile") {
      const file = files[0];

      // Validate video file
      try {
        validateVideoFile(file);
        setNewVideo({
          ...newVideo,
          [fileType]: file,
        });
        showSnackbar(
          `Video file "${file.name}" selected successfully`,
          "success"
        );
      } catch (error) {
        showSnackbar(error.message, "error");
        // Clear the file input
        e.target.value = "";
      }
      return;
    }

    // For other files, use the existing progress simulation
    setUploadProgress((prev) => ({
      ...prev,
      [progressKey]: { loading: true, progress: 0 },
    }));

    // Simulate realistic file processing with progress updates
    const totalSteps = 10;
    for (let i = 1; i <= totalSteps; i++) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      const progress = (i / totalSteps) * 100;
      setUploadProgress((prev) => ({
        ...prev,
        [progressKey]: { loading: true, progress },
      }));
    }

    if (fileType === "galleryFiles") {
      setNewVideo({
        ...newVideo,
        galleryFiles: Array.from(files),
      });
    } else {
      const file = files[0];
      setNewVideo({
        ...newVideo,
        [fileType]: file,
      });
    }

    // Complete loading
    setUploadProgress((prev) => ({
      ...prev,
      [progressKey]: { loading: false, progress: 100 },
    }));

    // Reset progress after a short delay
    setTimeout(() => {
      setUploadProgress((prev) => ({
        ...prev,
        [progressKey]: { loading: false, progress: 0 },
      }));
    }, 1000);
  };

  const uploadVideoFile = async (videoFile) => {
    // Validate video file first
    try {
      validateVideoFile(videoFile);
    } catch (error) {
      throw new Error(`File validation failed: ${error.message}`);
    }

    // Set video upload progress
    setUploadProgress((prev) => ({
      ...prev,
      video: { loading: true, progress: 0, currentChunk: 0, totalChunks: 0 },
    }));

    try {
      // Use retry logic for upload
      const videoUrl = await handleUploadWithRetry(videoFile, 3, (progress) => {
        setUploadProgress((prev) => ({
          ...prev,
          video: { ...prev.video, progress: Math.round(progress) },
        }));
      });

      setUploadProgress((prev) => ({
        ...prev,
        video: {
          loading: false,
          progress: 100,
          currentChunk: 0,
          totalChunks: 0,
        },
      }));

      return videoUrl;
    } catch (error) {
      setUploadProgress((prev) => ({
        ...prev,
        video: { loading: false, progress: 0, currentChunk: 0, totalChunks: 0 },
      }));
      throw error;
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (
        !newVideo.title ||
        !newVideo.description ||
        !newVideo.location ||
        !newVideo.date ||
        !newVideo.headquarters
      ) {
        showSnackbar("Please fill in all required fields", "error");
        setLoading(false);
        return;
      }

      const token = localStorage.getItem("token");
      if (!token) {
        showSnackbar(
          "Authentication token not found. Please login again.",
          "error"
        );
        setLoading(false);
        return;
      }

      // For new work creation using signed URL approach
      if (!editingVideo) {
        // Validate required files for new work
        if (!newVideo.videoFile || !(newVideo.videoFile instanceof File)) {
          showSnackbar("Please upload a video file", "error");
          setLoading(false);
          return;
        }
        if (!newVideo.logoFile || !(newVideo.logoFile instanceof File)) {
          showSnackbar("Please upload a logo file", "error");
          setLoading(false);
          return;
        }
        if (!newVideo.galleryFiles || newVideo.galleryFiles.length === 0) {
          showSnackbar("Please upload at least one gallery image", "error");
          setLoading(false);
          return;
        }

        try {
          // Step 1: Upload video using signed URL
          const videoUrl = await uploadVideoFile(newVideo.videoFile);
          if (!videoUrl) {
            throw new Error("Video upload completed but no URL returned");
          }

          // Step 2: Create work with video URL and other data
          const result = await createWorkWithData(newVideo, videoUrl, {
            imageFile: newVideo.imageFile,
            logoFile: newVideo.logoFile,
            galleryFiles: newVideo.galleryFiles,
          });

          setWorkVideos([result.data || result, ...workVideos]);
          showSnackbar("Work created successfully", "success");
          handleCloseModal();
          return;
        } catch (error) {
          console.error("Error creating work:", error);
          showSnackbar(`Failed to create work: ${error.message}`, "error");
          setLoading(false);
          return;
        }
      }

      // For editing existing work (keep existing logic)
      let finalVideoUrl = newVideo.videoUrl;

      // Upload video if a new video file is provided
      if (newVideo.videoFile && newVideo.videoFile instanceof File) {
        try {
          finalVideoUrl = await uploadVideoFile(newVideo.videoFile);
          if (!finalVideoUrl) {
            throw new Error("Video upload completed but no URL returned");
          }
        } catch (error) {
          console.error("Video upload error:", error);
          showSnackbar(`Video upload failed: ${error.message}`, "error");
          setLoading(false);
          return;
        }
      }

      const formData = new FormData();

      // Add all text fields
      formData.append("title", newVideo.title.trim());
      formData.append("description", newVideo.description.trim());
      formData.append("location", newVideo.location.trim());
      formData.append("date", newVideo.date);
      formData.append("headquarters", newVideo.headquarters.trim());
      formData.append("facebook", newVideo.facebook.trim());
      formData.append("instagram", newVideo.instagram.trim());
      formData.append("tiktok", newVideo.tiktok.trim());

      // Add video URL (either from chunk upload or existing)
      if (finalVideoUrl) {
        formData.append("videoUrl", finalVideoUrl);
        console.log("Using video URL:", finalVideoUrl);
      }

      // Add other file uploads - only add files that exist and are valid
      if (newVideo.imageFile && newVideo.imageFile instanceof File) {
        formData.append("imageUrl", newVideo.imageFile);
        console.log("Added image file:", newVideo.imageFile.name);
      }
      if (newVideo.logoFile && newVideo.logoFile instanceof File) {
        formData.append("logoUrl", newVideo.logoFile);
        console.log("Added logo file:", newVideo.logoFile.name);
      }
      if (newVideo.galleryFiles && newVideo.galleryFiles.length > 0) {
        newVideo.galleryFiles.forEach((file, index) => {
          if (file instanceof File) {
            formData.append("galleryImages", file);
            console.log(`Added gallery file ${index + 1}:`, file.name);
          }
        });
      }

      // Debug: Log what we're sending
      console.log("Form data being sent:");
      console.log(
        "Total form data entries:",
        Array.from(formData.entries()).length
      );
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(
            `${key}: File(${value.name}, ${value.size} bytes, ${value.type})`
          );
        } else {
          console.log(`${key}: ${value}`);
        }
      }

      // Additional validation
      const requiredFiles = ["videoUrl", "logoUrl", "galleryImages"];
      const missingFiles = requiredFiles.filter((field) => {
        const hasFile = Array.from(formData.entries()).some(
          ([key]) => key === field
        );
        if (!hasFile) console.warn(`Missing required file field: ${field}`);
        return !hasFile;
      });

      if (!editingVideo && missingFiles.length > 0) {
        showSnackbar(
          `Missing required files: ${missingFiles.join(", ")}`,
          "error"
        );
        setLoading(false);
        return;
      }

      const url = editingVideo
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/work/${editingVideo._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/work";

      const method = editingVideo ? "PUT" : "POST";

      console.log(`Making ${method} request to:`, url);
      console.log("Token:", token ? "Present" : "Missing");

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
          // Don't set Content-Type for FormData - let browser set it with boundary
        },
        body: formData,
      });
      if (!response.ok) {
        const errorData = await response.text();
        console.error("Server error response:", errorData);
        console.error("Request details:");
        console.error("- URL:", url);
        console.error("- Method:", method);
        console.error(
          "- Form data entries:",
          Array.from(formData.entries()).length
        );

        // Log the exact form data structure
        console.error("- Form data contents:");
        for (let [key, value] of formData.entries()) {
          if (value instanceof File) {
            console.error(
              `  ${key}: File(name: ${value.name}, size: ${value.size}, type: ${value.type})`
            );
          } else {
            console.error(`  ${key}: ${value}`);
          }
        }

        throw new Error(
          `Failed to save work: ${response.status} ${response.statusText}`
        );
      }

      const result = await response.json();
      const data = result.data || result;

      if (editingVideo) {
        setWorkVideos(
          workVideos.map((v) => (v._id === editingVideo._id ? data : v))
        );
        showSnackbar("Work updated successfully", "success");
      } else {
        setWorkVideos([data, ...workVideos]);
        showSnackbar("Work added successfully", "success");
      }

      handleCloseModal();
    } catch (error) {
      console.error("Error saving work:", error);
      showSnackbar("Failed to save work", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this work?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/work/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setWorkVideos(workVideos.filter((video) => video._id !== id));
        showSnackbar("Work deleted successfully", "success");
      } else {
        throw new Error("Failed to delete work");
      }
    } catch (error) {
      console.error("Error deleting work:", error);
      showSnackbar("Failed to delete work", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Work Projects
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Work
          </Button>
        </Box>

        {/* Insights Section with Custom CSS Classes */}
        <div
          className="insights-section"
          style={{ backgroundColor: "transparent" }}
        >
          <div className="insights-grid">
            <AnimatePresence>
              {workVideos.map((video, index) => {
                const vimeoId = video.videoUrl?.includes("vimeo")
                  ? getVimeoId(video.videoUrl)
                  : null;

                return (
                  <motion.div
                    key={video._id || index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="insights-card"
                    style={{ position: "relative" }}
                  >
                    {/* Admin Controls Overlay */}
                    <Box
                      sx={{
                        position: "absolute",
                        top: 8,
                        right: 8,
                        display: "flex",
                        gap: 0.5,
                        zIndex: 10,
                      }}
                    >
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={() => handleEdit(video)}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(0, 0, 0, 0.7)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.8)",
                            },
                          }}
                        >
                          <EditIcon sx={{ fontSize: "1rem" }} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => handleDelete(video._id)}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(0, 0, 0, 0.7)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(244, 67, 54, 0.8)",
                            },
                          }}
                        >
                          <DeleteIcon sx={{ fontSize: "1rem" }} />
                        </IconButton>
                      </Tooltip>
                    </Box>

                    {/* Video Wrapper */}
                    <div className="video-wrapper">
                      {vimeoId ? (
                        <Vimeo video={vimeoId} responsive autoplay={false} />
                      ) : (
                        <video
                          src={video.videoUrl.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          width="100%"
                          height="100%"
                          muted
                          loop
                          playsInline
                          style={{ objectFit: "cover" }}
                        />
                      )}
                    </div>

                    {/* Card Content */}
                    <div className="card-content">
                      <h2>{video.title}</h2>
                      <p
                        style={{
                          fontSize: "0.85rem",
                          color: "#aaa",
                          marginBottom: "8px",
                        }}
                      >
                        {video.location} •{" "}
                        {video.date
                          ? new Date(video.date).toLocaleDateString()
                          : "No date"}
                      </p>
                      <p>{video.description}</p>
                      <div
                        style={{
                          display: "flex",
                          gap: "10px",
                          marginTop: "auto",
                        }}
                      >
                        <button
                          className="btn btn-primary"
                          onClick={() => handleEdit(video)}
                          style={{ flex: 1 }}
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        </div>
      </Box>

      {/* Add/Edit Modal */}
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          {editingVideo ? "Edit Work Project" : "Add New Work Project"}
        </DialogTitle>
        <DialogContent sx={{ maxHeight: "70vh", overflowY: "auto" }}>
          {/* Basic Information */}
          <Typography variant="h6" sx={{ color: "#db4a41", mt: 2, mb: 1 }}>
            Basic Information
          </Typography>
          <TextField
            fullWidth
            label="Title"
            name="title"
            value={newVideo.title}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <TextField
            fullWidth
            label="Description"
            name="description"
            value={newVideo.description}
            onChange={handleInputChange}
            margin="normal"
            multiline
            rows={3}
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <Box sx={{ display: "flex", gap: 2 }}>
            <TextField
              fullWidth
              label="Location"
              name="location"
              value={newVideo.location}
              onChange={handleInputChange}
              margin="normal"
              required
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <TextField
              fullWidth
              label="Date"
              name="date"
              type="date"
              value={newVideo.date}
              onChange={handleInputChange}
              margin="normal"
              required
              InputLabelProps={{ shrink: true }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                // Style the date picker icon to be visible
                "& input[type='date']::-webkit-calendar-picker-indicator": {
                  filter: "invert(1)",
                  cursor: "pointer",
                },
                "& input[type='date']::-webkit-inner-spin-button": {
                  display: "none",
                },
                "& input[type='date']::-webkit-clear-button": {
                  display: "none",
                },
              }}
            />
          </Box>
          <TextField
            fullWidth
            label="Headquarters"
            name="headquarters"
            value={newVideo.headquarters}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />

          {/* Social Media */}
          <Typography variant="h6" sx={{ color: "#db4a41", mt: 3, mb: 1 }}>
            Social Media Links
          </Typography>
          <TextField
            fullWidth
            label="Facebook URL"
            name="facebook"
            value={newVideo.facebook}
            onChange={handleInputChange}
            margin="normal"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <TextField
            fullWidth
            label="Instagram URL"
            name="instagram"
            value={newVideo.instagram}
            onChange={handleInputChange}
            margin="normal"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <TextField
            fullWidth
            label="TikTok URL"
            name="tiktok"
            value={newVideo.tiktok}
            onChange={handleInputChange}
            margin="normal"
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />

          {/* File Uploads */}
          <Typography variant="h6" sx={{ color: "#db4a41", mt: 3, mb: 1 }}>
            Media Files
          </Typography>

          {/* Current Assets Preview (for editing) */}
          {editingVideo && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ color: "white", mb: 2 }}>
                Current Assets:
              </Typography>

              {/* Current Video Preview */}
              {editingVideo.videoUrl && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: "#db4a41", mb: 1 }}>
                    Current Video:
                  </Typography>
                  <Box
                    sx={{
                      width: "100%",
                      maxWidth: 300,
                      height: 200,
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      borderRadius: "8px",
                      border: "2px solid #db4a41",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      overflow: "hidden",
                    }}
                  >
                    <video
                      src={editingVideo.videoUrl.replace(
                        "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                        "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                      )}
                      controls
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                </Box>
              )}

              {/* Current Cover Image Preview */}
              {editingVideo.imageUrl && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: "#db4a41", mb: 1 }}>
                    Current Cover Image:
                  </Typography>
                  <Box
                    sx={{
                      width: 150,
                      height: 150,
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      borderRadius: "8px",
                      border: "2px solid #db4a41",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      overflow: "hidden",
                    }}
                  >
                    <img
                      src={editingVideo.imageUrl.replace(
                        "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                        "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                      )}
                      alt="Current cover"
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </Box>
                </Box>
              )}

              {/* Current Logo Preview */}
              {editingVideo.logoUrl && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: "#db4a41", mb: 1 }}>
                    Current Logo:
                  </Typography>
                  <Box
                    sx={{
                      width: 100,
                      height: 100,
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      borderRadius: "8px",
                      border: "2px solid #db4a41",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      overflow: "hidden",
                      p: 1,
                    }}
                  >
                    <img
                      src={editingVideo.logoUrl.replace(
                        "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                        "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                      )}
                      alt="Current logo"
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain",
                      }}
                    />
                  </Box>
                </Box>
              )}

              {/* Current Gallery Preview */}
              {editingVideo.galleryImages &&
                editingVideo.galleryImages.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body2"
                      sx={{ color: "#db4a41", mb: 1 }}
                    >
                      Current Gallery ({editingVideo.galleryImages.length}{" "}
                      images):
                    </Typography>
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                      {editingVideo.galleryImages
                        .slice(0, 6)
                        .map((imageUrl, index) => (
                          <Box
                            key={index}
                            sx={{
                              width: 80,
                              height: 80,
                              backgroundColor: "rgba(255, 255, 255, 0.1)",
                              borderRadius: "4px",
                              border: "1px solid #db4a41",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              overflow: "hidden",
                            }}
                          >
                            <img
                              src={imageUrl.replace(
                                "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                                "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                              )}
                              alt={`Gallery ${index + 1}`}
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                              }}
                            />
                          </Box>
                        ))}
                      {editingVideo.galleryImages.length > 6 && (
                        <Box
                          sx={{
                            width: 80,
                            height: 80,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            borderRadius: "4px",
                            border: "1px solid #db4a41",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "white",
                            fontSize: "0.8rem",
                          }}
                        >
                          +{editingVideo.galleryImages.length - 6}
                        </Box>
                      )}
                    </Box>
                  </Box>
                )}
            </Box>
          )}

          {/* Video Upload */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
              Video File (Required)
            </Typography>
            <input
              accept="video/*"
              style={{ display: "none" }}
              id="video-upload"
              type="file"
              onChange={(e) => handleFileChange(e, "videoFile")}
            />
            <label htmlFor="video-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={
                  uploadProgress.video.loading ? (
                    <CircularProgress
                      size={20}
                      variant="determinate"
                      value={uploadProgress.video.progress}
                      sx={{ color: "white" }}
                    />
                  ) : (
                    <CloudUploadIcon />
                  )
                }
                fullWidth
                disabled={uploadProgress.video.loading}
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                  "&.Mui-disabled": {
                    color: "rgba(255, 255, 255, 0.5)",
                    borderColor: "rgba(255, 255, 255, 0.12)",
                  },
                }}
              >
                {uploadProgress.video.loading
                  ? `Uploading... ${Math.round(uploadProgress.video.progress)}%`
                  : newVideo.videoFile
                  ? `${newVideo.videoFile.name} (${(
                      newVideo.videoFile.size /
                      1024 /
                      1024
                    ).toFixed(2)} MB)`
                  : "Upload Video File"}
              </Button>
            </label>
            {newVideo.videoFile && !uploadProgress.video.loading && (
              <Typography
                variant="body2"
                sx={{ color: "#4caf50", mt: 1, fontSize: "0.75rem" }}
              >
                ✓ Video file ready for upload: {newVideo.videoFile.name}
              </Typography>
            )}
            {/* Video Upload Progress */}
            {uploadProgress.video.loading && (
              <Box sx={{ mt: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress.video.progress}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    "& .MuiLinearProgress-bar": {
                      backgroundColor: "#db4a41",
                    },
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{ color: "white", mt: 0.5, fontSize: "0.75rem" }}
                >
                  {uploadProgress.video.progress < 10 &&
                    "Getting signed URL..."}
                  {uploadProgress.video.progress >= 10 &&
                    uploadProgress.video.progress < 100 &&
                    "Uploading to cloud storage..."}
                  {uploadProgress.video.progress === 100 && "Upload complete!"}{" "}
                  ({Math.round(uploadProgress.video.progress)}%)
                </Typography>
              </Box>
            )}
          </Box>

          {/* Image Upload */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
              Cover Image
            </Typography>
            <input
              accept="image/*"
              style={{ display: "none" }}
              id="image-upload"
              type="file"
              onChange={(e) => handleFileChange(e, "imageFile")}
            />
            <label htmlFor="image-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={
                  uploadProgress.image.loading ? (
                    <CircularProgress
                      size={20}
                      variant="determinate"
                      value={uploadProgress.image.progress}
                      sx={{ color: "white" }}
                    />
                  ) : (
                    <CloudUploadIcon />
                  )
                }
                fullWidth
                disabled={uploadProgress.image.loading}
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                  "&.Mui-disabled": {
                    color: "rgba(255, 255, 255, 0.5)",
                    borderColor: "rgba(255, 255, 255, 0.12)",
                  },
                }}
              >
                {uploadProgress.image.loading
                  ? `Processing... ${Math.round(
                      uploadProgress.image.progress
                    )}%`
                  : newVideo.imageFile
                  ? newVideo.imageFile.name
                  : "Upload Cover Image"}
              </Button>
            </label>
          </Box>

          {/* Logo Upload */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
              Logo (Required)
            </Typography>
            <input
              accept="image/*"
              style={{ display: "none" }}
              id="logo-upload"
              type="file"
              onChange={(e) => handleFileChange(e, "logoFile")}
            />
            <label htmlFor="logo-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={
                  uploadProgress.logo.loading ? (
                    <CircularProgress
                      size={20}
                      variant="determinate"
                      value={uploadProgress.logo.progress}
                      sx={{ color: "white" }}
                    />
                  ) : (
                    <CloudUploadIcon />
                  )
                }
                fullWidth
                disabled={uploadProgress.logo.loading}
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                  "&.Mui-disabled": {
                    color: "rgba(255, 255, 255, 0.5)",
                    borderColor: "rgba(255, 255, 255, 0.12)",
                  },
                }}
              >
                {uploadProgress.logo.loading
                  ? `Processing... ${Math.round(uploadProgress.logo.progress)}%`
                  : newVideo.logoFile
                  ? newVideo.logoFile.name
                  : "Upload Logo"}
              </Button>
            </label>
          </Box>

          {/* Gallery Upload */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ color: "white", mb: 1 }}>
              Gallery Images (Required)
            </Typography>
            <input
              accept="image/*"
              style={{ display: "none" }}
              id="gallery-upload"
              type="file"
              multiple
              onChange={(e) => handleFileChange(e, "galleryFiles")}
            />
            <label htmlFor="gallery-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={
                  uploadProgress.gallery.loading ? (
                    <CircularProgress
                      size={20}
                      variant="determinate"
                      value={uploadProgress.gallery.progress}
                      sx={{ color: "white" }}
                    />
                  ) : (
                    <CloudUploadIcon />
                  )
                }
                fullWidth
                disabled={uploadProgress.gallery.loading}
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                  "&.Mui-disabled": {
                    color: "rgba(255, 255, 255, 0.5)",
                    borderColor: "rgba(255, 255, 255, 0.12)",
                  },
                }}
              >
                {uploadProgress.gallery.loading
                  ? `Processing... ${Math.round(
                      uploadProgress.gallery.progress
                    )}%`
                  : newVideo.galleryFiles && newVideo.galleryFiles.length > 0
                  ? `${newVideo.galleryFiles.length} files selected`
                  : "Upload Gallery Images"}
              </Button>
            </label>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || uploadProgress.video.loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} sx={{ color: "white" }} />
              ) : null
            }
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
              "&:disabled": {
                backgroundColor: "rgba(219, 74, 65, 0.5)",
                color: "rgba(255, 255, 255, 0.7)",
              },
            }}
          >
            {loading ? "Saving..." : editingVideo ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background: snackbar.severity === "success" ? "#2e7d32" : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Work;
