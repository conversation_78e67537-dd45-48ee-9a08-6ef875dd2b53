import React, {
  useEffect,
  useState,
  createContext,
  Suspense,
  lazy,
} from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { QueryClientProvider } from "@tanstack/react-query";

import ButtonAppBar from "./components/Navbar";
import Footer from "./components/Footer";
import Leads from "./components/Leads";
import { UserProvider, useUser } from "./contexts/UserContext";
import { ClientProvider } from "./contexts/ClientContext";
import { queryClient } from "./utils/api";
import LoadingScreen from "./components/loading/newLoading";
import SmoothScroll from "./utils/smoothScroll";
import ScrollToTop from "./utils/scrollToTop";

// Context for authentication
export const AuthContext = createContext();

// Lazy-load pages
const ComingSoon = lazy(() => import("./components/comingsoon.jsx"));
const NewHome = lazy(() => import("./components/newHome"));
const NewAbout = lazy(() => import("./components/newAbout"));
const Insights = lazy(() => import("./components/Insights"));
const Careers = lazy(() => import("./components/Careers"));
const Contact = lazy(() => import("./components/Contact"));
const EventPage = lazy(() => import("./components/EventPage"));
const CareersFormPage = lazy(() => import("./components/CareersFormPage"));
const Login = lazy(() => import("./admin-panel/Login"));
const Admin = lazy(() => import("./admin-panel/Admin"));
const ReelPage = lazy(() => import("./components/ReelPage"));
const PeoplePage = lazy(() => import("./components/youngPeople"));
const ClientLogin = lazy(() => import("./client-portal/ClientLogin"));
const ClientDashboard = lazy(() => import("./client-portal/ClientDashboard"));
const NotFound = lazy(() => import("./components/NotFound"));
// Private Route for admin
function PrivateRoute({ children }) {
  const { isAuthenticated, loading } = useUser();
  const location = useLocation();

  if (loading) return <div>Loading...</div>;

  return isAuthenticated() ? (
    children
  ) : (
    <Navigate to="/admin/login" state={{ from: location }} />
  );
}

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(
    () => !!localStorage.getItem("token")
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) setIsAuthenticated(true);
  }, []);

  // Hide Navbar, Leads, Footer for admin/client/coming soon
  const hideGlobalComponents = () => {
    const path = window.location.pathname;
    const hidePaths = ["/admin", "/client-portal", "/test/reels", "/"];
    return hidePaths.some((p) => path === p || path.startsWith(p + "/"));
  };

  const shouldShowLoading = loading && window.location.pathname !== "/test";

  return (
    <>
      {shouldShowLoading && (
        <LoadingScreen onFinish={() => setLoading(false)} />
      )}

      {!shouldShowLoading && (
        <QueryClientProvider client={queryClient}>
          <AuthContext.Provider value={{ isAuthenticated, setIsAuthenticated }}>
            <Router>
              <UserProvider>
                <ClientProvider>
                  <div className="App">
                    {!hideGlobalComponents() && <ButtonAppBar />}
                    <ScrollToTop />
                    <SmoothScroll />

                    {/* Route-based lazy loading */}
                    <Suspense fallback={<LoadingScreen />}>
                      <Routes>
                        <Route path="/" element={<ComingSoon />} />
                        <Route path="/test" element={<NewHome />} />
                        <Route path="/test/about" element={<NewAbout />} />
                        <Route path="/test/insights" element={<Insights />} />
                        <Route path="/test/careers" element={<Careers />} />
                        <Route path="/test/contact" element={<Contact />} />
                        <Route
                          path="/test/event/:eventId"
                          element={<EventPage />}
                        />
                        <Route
                          path="/test/careers-form/:id"
                          element={<CareersFormPage />}
                        />
                        <Route path="/admin/login" element={<Login />} />
                        <Route path="/test/reels" element={<ReelPage />} />
                        <Route
                          path="/test/young-people"
                          element={<PeoplePage />}
                        />
                        <Route
                          path="/client-portal"
                          element={<ClientDashboard />}
                        />
                        <Route
                          path="/client-portal/login"
                          element={<ClientLogin />}
                        />
                        <Route
                          path="/client-portal/dashboard"
                          element={<ClientDashboard />}
                        />
                        <Route
                          path="/admin/*"
                          element={
                            <PrivateRoute>
                              <Admin />
                            </PrivateRoute>
                          }
                        />
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                    </Suspense>

                    {!hideGlobalComponents() && <Leads />}
                    {!hideGlobalComponents() && <Footer />}
                  </div>
                </ClientProvider>
              </UserProvider>
            </Router>
          </AuthContext.Provider>
        </QueryClientProvider>
      )}
    </>
  );
}

export default App;
