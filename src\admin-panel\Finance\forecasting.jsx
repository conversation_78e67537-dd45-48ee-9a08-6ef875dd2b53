import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Avatar,
  LinearProgress,
  Collapse,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import WarningIcon from "@mui/icons-material/Warning";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import RefreshIcon from "@mui/icons-material/Refresh";
import { motion } from "framer-motion";

// Utility function to parse MongoDB Decimal128 values
const parseDecimal = (value) => {
  if (value && typeof value === "object" && value.$numberDecimal) {
    return parseFloat(value.$numberDecimal);
  }
  return value;
};

function Forecasting() {
  const [forecastData, setForecastData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [viewingClient, setViewingClient] = useState(null);
  const [clientDetails, setClientDetails] = useState(null);
  const [clientDetailsLoading, setClientDetailsLoading] = useState(false);
  const [expandedClients, setExpandedClients] = useState({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Filter states
  const [filters, setFilters] = useState({
    filter_type: "",
    months: "",
    start_date: "",
    end_date: "",
  });

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account/revenue/projected";

  const showSnackbar = (message, severity = "success") => {
    setSnackbar({ open: true, message, severity });
  };

  const fetchForecastData = useCallback(async (filterParams = {}) => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (filterParams.filter_type) {
        queryParams.append("filter_type", filterParams.filter_type);
      }
      if (filterParams.months) {
        queryParams.append("months", filterParams.months);
      }
      if (filterParams.start_date) {
        queryParams.append("start_date", filterParams.start_date);
      }
      if (filterParams.end_date) {
        queryParams.append("end_date", filterParams.end_date);
      }

      const url = queryParams.toString()
        ? `${API_BASE_URL}?${queryParams.toString()}`
        : API_BASE_URL;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();

      if (result.success) {
        setForecastData(result.data);
      } else {
        showSnackbar(
          result.message || "Failed to fetch forecast data",
          "error"
        );
      }
    } catch (error) {
      console.error("Error fetching forecast data:", error);
      showSnackbar("Failed to fetch forecast data", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientDetails = async (clientId) => {
    setClientDetailsLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account/${clientId}/revenue/projected`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();

      if (result.success) {
        setClientDetails(result.data);
      } else {
        showSnackbar("Failed to fetch client details", "error");
      }
    } catch (error) {
      console.error("Error fetching client details:", error);
      showSnackbar("Failed to fetch client details", "error");
    } finally {
      setClientDetailsLoading(false);
    }
  };

  useEffect(() => {
    fetchForecastData();
  }, [fetchForecastData]);

  const handleFilterChange = (field, value) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleApplyFilters = () => {
    const filterParams = {};

    if (filters.filter_type) {
      filterParams.filter_type = filters.filter_type;

      if (filters.filter_type === "months" && filters.months) {
        filterParams.months = filters.months;
      }

      if (filters.filter_type === "custom") {
        if (filters.start_date) {
          filterParams.start_date = filters.start_date;
        }
        if (filters.end_date) {
          filterParams.end_date = filters.end_date;
        }
      }
    }

    fetchForecastData(filterParams);
  };

  const handleClearFilters = () => {
    setFilters({
      filter_type: "",
      months: "",
      start_date: "",
      end_date: "",
    });
    fetchForecastData();
  };

  const handleViewClient = async (client) => {
    setViewingClient(client);
    setOpenViewModal(true);
    await fetchClientDetails(client.client_id);
  };

  const toggleClientExpansion = (clientId) => {
    setExpandedClients((prev) => ({
      ...prev,
      [clientId]: !prev[clientId],
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "#4caf50";
      case "unpaid":
        return "#f44336";
      case "partially_paid":
        return "#ff9800";
      default:
        return "#757575";
    }
  };

  const getStatusChip = (status) => {
    const color = getStatusColor(status);
    return (
      <Chip
        label={status.replace("_", " ").toUpperCase()}
        size="small"
        sx={{
          backgroundColor: `${color}20`,
          color: color,
          fontWeight: "bold",
          fontSize: "0.75rem",
        }}
      />
    );
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "50vh",
        }}
      >
        <CircularProgress sx={{ color: "#4caf50" }} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, backgroundColor: "#0a0a0a", minHeight: "100vh" }}>
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            color: "white",
            fontFamily: "Formula Bold",
            display: "flex",
            alignItems: "center",
            gap: 2,
          }}
        >
          <TrendingUpIcon sx={{ color: "#4caf50", fontSize: "2rem" }} />
          Revenue Forecasting
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => fetchForecastData()}
          sx={{
            borderColor: "#4caf50",
            color: "#4caf50",
            fontFamily: "Formula Bold",
            "&:hover": {
              borderColor: "#388e3c",
              backgroundColor: "rgba(76, 175, 80, 0.1)",
            },
          }}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Filter Section */}
      <Card
        sx={{
          background: "rgba(255, 255, 255, 0.05)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "12px",
          mb: 3,
        }}
      >
        <CardContent>
          <Typography
            variant="h6"
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              mb: 2,
            }}
          >
            Filter Options
          </Typography>

          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Filter Type
                </InputLabel>
                <Select
                  value={filters.filter_type}
                  onChange={(e) =>
                    handleFilterChange("filter_type", e.target.value)
                  }
                  sx={{
                    color: "white",
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.3)",
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: "rgba(255, 255, 255, 0.5)",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: "#4caf50",
                    },
                  }}
                >
                  <MenuItem value="">All Active Contracts</MenuItem>
                  <MenuItem value="this_year">This Year</MenuItem>
                  <MenuItem value="next_year">Next Year</MenuItem>
                  <MenuItem value="months">Next X Months</MenuItem>
                  <MenuItem value="custom">Custom End Date</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {filters.filter_type === "months" && (
              <Grid item xs={12} sm={6} md={2}>
                <TextField
                  fullWidth
                  label="Number of Months"
                  type="number"
                  value={filters.months}
                  onChange={(e) => handleFilterChange("months", e.target.value)}
                  inputProps={{ min: 1, max: 12 }}
                  sx={{
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#4caf50",
                      },
                    },
                  }}
                />
              </Grid>
            )}

            {filters.filter_type === "custom" && (
              <>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    label="Start Date"
                    type="date"
                    value={filters.start_date}
                    onChange={(e) =>
                      handleFilterChange("start_date", e.target.value)
                    }
                    InputLabelProps={{
                      shrink: true,
                    }}
                    sx={{
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#4caf50",
                        },
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    value={filters.end_date}
                    onChange={(e) =>
                      handleFilterChange("end_date", e.target.value)
                    }
                    InputLabelProps={{
                      shrink: true,
                    }}
                    sx={{
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#4caf50",
                        },
                      },
                    }}
                  />
                </Grid>
              </>
            )}

            <Grid item xs={12} sm={6} md={2}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleApplyFilters}
                  sx={{
                    backgroundColor: "#4caf50",
                    color: "white",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      backgroundColor: "#388e3c",
                    },
                  }}
                >
                  Apply
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleClearFilters}
                  sx={{
                    borderColor: "#ff9800",
                    color: "#ff9800",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      borderColor: "#f57c00",
                      backgroundColor: "rgba(255, 152, 0, 0.1)",
                    },
                  }}
                >
                  Clear
                </Button>
              </Box>
            </Grid>
          </Grid>

          {/* Filter Description */}
          {forecastData?.filter_applied && (
            <Box
              sx={{
                mt: 2,
                p: 2,
                background: "rgba(76, 175, 80, 0.1)",
                borderRadius: "8px",
              }}
            >
              <Typography
                variant="body2"
                sx={{ color: "#4caf50", fontWeight: "bold" }}
              >
                Active Filter: {forecastData.filter_applied.description}
              </Typography>
              {forecastData.filter_applied.cutoff_date && (
                <Typography
                  variant="caption"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  Cutoff Date:{" "}
                  {new Date(
                    forecastData.filter_applied.cutoff_date
                  ).toLocaleDateString()}
                </Typography>
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {forecastData && (
        <>
          {/* Summary Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  textAlign: "center",
                  p: 3,
                  background: "rgba(76, 175, 80, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(76, 175, 80, 0.3)",
                }}
              >
                <AccountBalanceIcon
                  sx={{ color: "#4caf50", fontSize: "2.5rem", mb: 1 }}
                />
                <Typography
                  variant="h4"
                  sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                >
                  {forecastData.clients_in_period ||
                    forecastData.total_active_clients}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  {forecastData.clients_in_period
                    ? "Clients in Period"
                    : "Active Clients"}
                </Typography>
                {forecastData.clients_in_period &&
                  forecastData.total_active_clients !==
                    forecastData.clients_in_period && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: "rgba(255, 255, 255, 0.5)",
                        display: "block",
                      }}
                    >
                      ({forecastData.total_active_clients} total active)
                    </Typography>
                  )}
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  textAlign: "center",
                  p: 3,
                  background: "rgba(33, 150, 243, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(33, 150, 243, 0.3)",
                }}
              >
                <TrendingUpIcon
                  sx={{ color: "#2196f3", fontSize: "2.5rem", mb: 1 }}
                />
                <Typography
                  variant="h4"
                  sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                >
                  {parseDecimal(
                    forecastData.total_projected_revenue
                  ).toLocaleString()}{" "}
                  EGP
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  Projected Revenue
                </Typography>
                <Tooltip title="Counts only full months remaining within the selected period">
                  <Typography
                    variant="caption"
                    sx={{
                      color: "rgba(255, 255, 255, 0.5)",
                      cursor: "help",
                      textDecoration: "underline dotted",
                    }}
                  >
                    ⓘ Full months only
                  </Typography>
                </Tooltip>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  textAlign: "center",
                  p: 3,
                  background: "rgba(255, 152, 0, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 152, 0, 0.3)",
                }}
              >
                <WarningIcon
                  sx={{ color: "#ff9800", fontSize: "2.5rem", mb: 1 }}
                />
                <Typography
                  variant="h4"
                  sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                >
                  {parseDecimal(
                    forecastData.total_outstanding_amount
                  ).toLocaleString()}{" "}
                  EGP
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  Outstanding Amount
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box
                sx={{
                  textAlign: "center",
                  p: 3,
                  background: "rgba(156, 39, 176, 0.1)",
                  borderRadius: "12px",
                  border: "1px solid rgba(156, 39, 176, 0.3)",
                }}
              >
                <MonetizationOnIcon
                  sx={{ color: "#9c27b0", fontSize: "2.5rem", mb: 1 }}
                />
                <Typography
                  variant="h4"
                  sx={{ color: "#9c27b0", fontFamily: "Formula Bold" }}
                >
                  {parseDecimal(
                    forecastData.total_expected_revenue
                  ).toLocaleString()}{" "}
                  EGP
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  Total Expected Revenue
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Client Breakdown */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Typography
                variant="h5"
                sx={{
                  color: "white",
                  fontFamily: "Formula Bold",
                  mb: 3,
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                }}
              >
                <AccountBalanceIcon sx={{ color: "#4caf50" }} />
                Client Revenue Breakdown
              </Typography>

              <Grid container spacing={2}>
                {forecastData.breakdown_by_client.map((client, index) => (
                  <Grid item xs={12} md={6} lg={4} key={client.client_id}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.03)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "8px",
                          cursor: "pointer",
                          transition: "all 0.3s ease",
                          "&:hover": {
                            transform: "translateY(-2px)",
                            boxShadow: "0 8px 25px rgba(76, 175, 80, 0.15)",
                            border: "1px solid rgba(76, 175, 80, 0.3)",
                          },
                        }}
                        onClick={() => toggleClientExpansion(client.client_id)}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Avatar
                                sx={{
                                  bgcolor: "#4caf50",
                                  width: 32,
                                  height: 32,
                                  fontSize: "0.875rem",
                                }}
                              >
                                {client.client_name.charAt(0).toUpperCase()}
                              </Avatar>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Typography
                                  variant="h6"
                                  sx={{
                                    color: "white",
                                    fontFamily: "Formula Bold",
                                  }}
                                >
                                  {client.client_name}
                                </Typography>
                                {client.is_filtered && (
                                  <Chip
                                    label="Filtered"
                                    size="small"
                                    sx={{
                                      backgroundColor: "rgba(255, 152, 0, 0.2)",
                                      color: "#ff9800",
                                      fontSize: "0.7rem",
                                      height: "20px",
                                    }}
                                  />
                                )}
                              </Box>
                            </Box>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleViewClient(client);
                                  }}
                                  sx={{ color: "#4caf50" }}
                                >
                                  <VisibilityIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <IconButton size="small" sx={{ color: "white" }}>
                                {expandedClients[client.client_id] ? (
                                  <ExpandLessIcon />
                                ) : (
                                  <ExpandMoreIcon />
                                )}
                              </IconButton>
                            </Box>
                          </Box>

                          <Grid container spacing={2}>
                            <Grid item xs={6}>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Subscription Fee
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {parseDecimal(
                                  client.subscription_fee
                                ).toLocaleString()}{" "}
                                EGP
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Months Remaining
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#2196f3",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {client.months_remaining}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Projected Revenue
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#2196f3",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {parseDecimal(
                                  client.projected_revenue
                                ).toLocaleString()}{" "}
                                EGP
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Outstanding
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color:
                                    client.outstanding_amount > 0
                                      ? "#ff9800"
                                      : "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {parseDecimal(
                                  client.outstanding_amount
                                ).toLocaleString()}{" "}
                                EGP
                              </Typography>
                            </Grid>
                          </Grid>

                          <Box sx={{ mt: 2 }}>
                            <LinearProgress
                              variant="determinate"
                              value={
                                client.total_expected > 0
                                  ? ((client.total_expected -
                                      client.outstanding_amount) /
                                      client.total_expected) *
                                    100
                                  : 0
                              }
                              sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                                "& .MuiLinearProgress-bar": {
                                  backgroundColor: "#4caf50",
                                },
                              }}
                            />
                            <Typography
                              variant="caption"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                mt: 1,
                                display: "block",
                              }}
                            >
                              Contract ends:{" "}
                              {new Date(
                                client.contract_end_date
                              ).toLocaleDateString()}
                            </Typography>
                          </Box>

                          {/* Outstanding Cycles - Collapsible */}
                          <Collapse in={expandedClients[client.client_id]}>
                            <Box
                              sx={{
                                mt: 2,
                                pt: 2,
                                borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                  mb: 1,
                                }}
                              >
                                Outstanding Cycles (
                                {client.outstanding_cycles.length})
                              </Typography>
                              {client.outstanding_cycles.length > 0 ? (
                                client.outstanding_cycles.map(
                                  (cycle, cycleIndex) => (
                                    <Box
                                      key={cycleIndex}
                                      sx={{
                                        p: 2,
                                        mb: 1,
                                        background: "rgba(255, 255, 255, 0.05)",
                                        borderRadius: "6px",
                                        border:
                                          "1px solid rgba(255, 255, 255, 0.1)",
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          display: "flex",
                                          justifyContent: "space-between",
                                          alignItems: "center",
                                          mb: 1,
                                        }}
                                      >
                                        <Typography
                                          variant="body2"
                                          sx={{
                                            color: "white",
                                            fontWeight: "bold",
                                          }}
                                        >
                                          {cycle.cycle_name}
                                        </Typography>
                                        {getStatusChip(cycle.status)}
                                      </Box>
                                      <Grid container spacing={1}>
                                        <Grid item xs={4}>
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: "rgba(255, 255, 255, 0.7)",
                                            }}
                                          >
                                            Due Amount
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              color: "#ff9800",
                                              fontWeight: "bold",
                                            }}
                                          >
                                            {parseDecimal(
                                              cycle.due_amount
                                            ).toLocaleString()}{" "}
                                            EGP
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: "rgba(255, 255, 255, 0.7)",
                                            }}
                                          >
                                            Paid Amount
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              color: "#4caf50",
                                              fontWeight: "bold",
                                            }}
                                          >
                                            {parseDecimal(
                                              cycle.paid_amount
                                            ).toLocaleString()}{" "}
                                            EGP
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: "rgba(255, 255, 255, 0.7)",
                                            }}
                                          >
                                            Remaining
                                          </Typography>
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              color: "#f44336",
                                              fontWeight: "bold",
                                            }}
                                          >
                                            {parseDecimal(
                                              cycle.remaining_amount
                                            ).toLocaleString()}{" "}
                                            EGP
                                          </Typography>
                                        </Grid>
                                      </Grid>
                                    </Box>
                                  )
                                )
                              ) : (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.7)",
                                    fontStyle: "italic",
                                  }}
                                >
                                  No outstanding cycles
                                </Typography>
                              )}
                            </Box>
                          </Collapse>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </>
      )}

      {/* Client Details Modal */}
      <Dialog
        open={openViewModal}
        onClose={() => setOpenViewModal(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(10, 10, 10, 0.95)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle
          sx={{
            color: "white",
            fontFamily: "Formula Bold",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          Client Revenue Details
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          {clientDetailsLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
              <CircularProgress sx={{ color: "#4caf50" }} />
            </Box>
          ) : clientDetails ? (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography
                  variant="h5"
                  sx={{ color: "white", fontFamily: "Formula Bold", mb: 2 }}
                >
                  {clientDetails.client_name}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box
                  sx={{
                    p: 2,
                    background: "rgba(76, 175, 80, 0.1)",
                    borderRadius: "8px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                  >
                    Subscription Fee
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                  >
                    {parseDecimal(
                      clientDetails.subscription_fee
                    ).toLocaleString()}{" "}
                    EGP
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box
                  sx={{
                    p: 2,
                    background: "rgba(33, 150, 243, 0.1)",
                    borderRadius: "8px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                  >
                    Projected Revenue
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                  >
                    {parseDecimal(
                      clientDetails.projected_revenue
                    ).toLocaleString()}{" "}
                    EGP
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box
                  sx={{
                    p: 2,
                    background: "rgba(255, 152, 0, 0.1)",
                    borderRadius: "8px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                  >
                    Outstanding Amount
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                  >
                    {parseDecimal(
                      clientDetails.outstanding_amount
                    ).toLocaleString()}{" "}
                    EGP
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Box
                  sx={{
                    p: 2,
                    background: "rgba(156, 39, 176, 0.1)",
                    borderRadius: "8px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                  >
                    Total Expected
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{ color: "#9c27b0", fontFamily: "Formula Bold" }}
                  >
                    {parseDecimal(
                      clientDetails.total_expected_revenue
                    ).toLocaleString()}{" "}
                    EGP
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "white", fontFamily: "Formula Bold", mb: 2 }}
                >
                  Contract Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Start Date
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {new Date(
                        clientDetails.contract_start_date
                      ).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      End Date
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {new Date(
                        clientDetails.contract_end_date
                      ).toLocaleDateString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Months Remaining
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ color: "#2196f3", fontWeight: "bold" }}
                    >
                      {clientDetails.months_remaining} months
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          ) : (
            <Typography sx={{ color: "white" }}>
              No details available
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenViewModal(false)}
            sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Forecasting;
