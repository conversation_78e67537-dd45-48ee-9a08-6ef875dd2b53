import { useRef, useLayoutEffect, useEffect, useState, useMemo } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import LightRays from "../animations/lightRays";
import SmartHeroVideo from "../../contexts/smartVideo";
import { useHeroVideos } from "../../hooks/useApi";

gsap.registerPlugin(ScrollTrigger);

// Fallback videos for error state
const FALLBACK_VIDEOS = [
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/Creative%20Aubaine.mp4",
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/GFF%20Editions.mp4",
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/Ghoneimi%20Brochure.mp4",
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/NourXDara.mp4",
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/GFF2.mp4",
  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/CircleK%20Ramadan.mp4",
];

export default function HeroSection() {
  const container = useRef();
  const videos = useRef([]);
  const textFillRef = useRef();
  const strokeRef = useRef();
  const [isLoading, setIsLoading] = useState(true);
  const [showLightRays, setShowLightRays] = useState(false);

  // Use React Query for hero videos with caching
  const {
    data: heroVideosData,
    isLoading: isLoadingVideos,
    error,
  } = useHeroVideos();

  // Memoize hero videos with fallback
  const heroVideos = useMemo(() => {
    if (error || !heroVideosData || heroVideosData.length === 0) {
      return FALLBACK_VIDEOS;
    }
    return heroVideosData;
  }, [heroVideosData, error]);

  useEffect(() => {
    // Delay mounting LightRays to force re-render each time
    setShowLightRays(false);
    const timeout = setTimeout(() => {
      setShowLightRays(true);
    }, 50); // tiny delay so it unmounts first

    return () => clearTimeout(timeout);
  }, []); // run every time component mounts
  useEffect(() => {
    // Reset loading state when videos change
    if (heroVideos.length > 0 && isLoadingVideos) {
      setIsLoading(false);
      return;
    }

    // Only start loading check when we have hero videos
    if (heroVideos.length === 0) return;

    let loadedCount = 0;
    const currentVideos = videos.current;

    const handleLoaded = () => {
      loadedCount++;
      if (loadedCount === currentVideos.length) {
        setIsLoading(false);
      }
    };

    currentVideos.forEach((video) => {
      if (video?.readyState >= 3) {
        handleLoaded();
      } else {
        video?.addEventListener("loadeddata", handleLoaded, { once: true });
      }
    });

    return () => {
      currentVideos.forEach((video) => {
        video?.removeEventListener("loadeddata", handleLoaded);
      });
    };
  }, [heroVideos, isLoadingVideos]);

  useLayoutEffect(() => {
    // Only run animations when we have hero videos
    if (heroVideos.length === 0) return;

    const ctx = gsap.context(() => {
      // Initial scattered videos
      gsap.set(videos.current, {
        x: (i) =>
          i === 0
            ? "-25vw"
            : i === 1
            ? "30vw"
            : i === 2
            ? "-25vw"
            : i === 3
            ? "20vw"
            : i === 4
            ? "-35vw"
            : typeof window !== "undefined" && window.innerWidth < 768
            ? "55vw"
            : "35vw",
        y: (i) =>
          i === 0
            ? "-25vh"
            : i === 1
            ? "-23vh"
            : i === 2
            ? "40vh"
            : i === 3
            ? "40vh"
            : "10vh",
        scale: 0.8,
        opacity: 1,
      });

      // Timeline for scroll-based animation
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: container.current,
          start: "top top",
          end: "+=1000vh",
          scrub: 1.5,
          pin: true,
        },
      });

      // Step 1: Gather videos to center
      tl.to(
        videos.current,
        {
          x: 0,
          y: 50,
          stagger: 0.1,
          ease: "power2.inOut",
        },
        0
      );

      // Step 2: Scale to normal (from scattered scale)
      tl.to(
        videos.current,
        {
          scale: 1,
          ease: "power2.inOut",
          stagger: 0,
        },
        0
      );

      // Step 3: Switch logos — fade out textFill, fade in stroke
      tl.to(
        textFillRef.current,
        {
          opacity: 0,
          ease: "power2.inOut",
        },
        ">-0.2"
      );
      tl.to(
        strokeRef.current,
        {
          opacity: 1,
          ease: "power2.inOut",
        },
        "<"
      );

      // Step 4: Scale videos and logo together
      tl.to(
        videos.current,
        {
          scale: 3.5,
          ease: "power2.inOut",
          stagger: 0,
        },
        ">0.5"
      );
      tl.to(
        strokeRef.current,
        {
          scale: 2,
          ease: "power2.inOut",
        },
        "<"
      );

      // Background fades to black
      tl.to(
        container.current,
        {
          backgroundColor: "black",
        },
        "<"
      );
    }, container);
    return () => ctx.revert();
  }, [heroVideos]);

  return (
    <section
      ref={container}
      style={{
        position: "relative",
        height: "100vh",
        overflow: "hidden",
        backgroundColor: "#000",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1100000,
      }}
    >
      {/* Video Layer */}
      <div style={{ position: "absolute", inset: 0, zIndex: 1 }}>
        {/* <Orb
          hoverIntensity={0.5}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        /> */}
        {showLightRays && ( // 🔹 will unmount & remount every time
          <LightRays
            raysOrigin="top-center"
            raysColor="#fff"
            raysSpeed={1.5}
            lightSpread={0.8}
            rayLength={1.2}
            followMouse={false}
            mouseInfluence={0.1}
            noiseAmount={0.1}
            distortion={0.05}
            className="custom-rays"
          />
        )}

        {heroVideos.map((src, i) => (
          <SmartHeroVideo
            key={i}
            ref={(el) => (videos.current[i] = el)}
            src={src.replace(
              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
            )}
            delay={i * 100}
            style={{
              position: "absolute",
              width: "28vw",
              maxWidth: "1200px",
              minWidth: "200px",
              height: "16vw",
              maxHeight: "675px",
              minHeight: "110px",
              objectFit: "cover",
              top:
                typeof window !== "undefined" && window.innerWidth < 768
                  ? "43%"
                  : "43%",
              left:
                typeof window !== "undefined" && window.innerWidth < 768
                  ? "50%"
                  : "50%",
              transform: "translate(-50%, -50%)",
              zIndex: 3 - i,
              filter: "brightness(0.9)",
              borderRadius: "0px",
              boxShadow: "0 0px 0px rgba(0,0,0,1.5)",
            }}
          />
        ))}
      </div>

      {/* Black Logo (Text Fill Layer) */}
      <div
        ref={textFillRef}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 1000,
          pointerEvents: "none",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontFamily: "Formula Bold",
          fontSize: "clamp(4rem, 25vw, 13rem)",
          letterSpacing: "0.07em",
          textAlign: "center",
          lineHeight: 1,
          border: "2px solid black",
          color: "black",
          opacity: 1,
        }}
      >
        <img
          src="/assets/young-logo-white.webp"
          width={
            typeof window !== "undefined" && window.innerWidth < 768 ? 200 : 500
          }
          alt="Young-logo"
          loading="eager"
          decoding="async"
          fetchPriority="high"
        />
      </div>

      {/* White Logo (Stroke Layer with negative fill effect) */}
      <div
        ref={strokeRef}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          zIndex: 6,
          pointerEvents: "none",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mixBlendMode: "difference",
          opacity: 0, // start hidden
          transform: "scale(1)", // for GSAP scale animation
        }}
      >
        <img
          src="/assets/young-logo-white.webp"
          width={
            typeof window !== "undefined" && window.innerWidth < 768 ? 100 : 500
          }
          style={{
            filter: "brightness(100%)",
          }}
          alt="Young-logo-white"
          loading="lazy"
          decoding="async"
        />
      </div>
      {/* <div
        style={{
          position: "absolute",
          bottom: "0%",
          left: "50%",
          transform: "translateX(-50%)",
          textAlign: "center",
          zIndex: 1000,
          color: "#fff",
          fontFamily: "Formula Bold",
          fontSize: "clamp(1.5rem, 5vw, 1rem)",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <motion.svg
          width="40"
          height="40"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          initial={{ y: 0 }}
          animate={{ y: [0, 5, 0] }}
          transition={{ ease: "easeOut", duration: 0.5, repeat: Infinity }}
        >
          <path
            d="M4 10L12 18L20 10"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </motion.svg>{" "}
        <p>Scroll Down For Full Experience</p>
      </div> */}
    </section>
  );
}
// import { useLayoutEffect, useRef, useState, useEffect } from "react";
// import gsap from "gsap";
// import { ScrollTrigger } from "gsap/ScrollTrigger";
// import LightRays from "../animations/lightRays";
// import SmartHeroVideo from "../../contexts/smartVideo";

// gsap.registerPlugin(ScrollTrigger);

// export default function HeroParallaxOptimized() {
//   const containerRef = useRef(null);
//   const wrappersRef = useRef([]);
//   const videosRef = useRef([]);
//   const fillLogoRef = useRef(null);
//   const strokeLogoRef = useRef(null);

//   const [heroVideos, setHeroVideos] = useState([]);

//   // Fetch videos from API
//   useEffect(() => {
//     const fetchHeroVideos = async () => {
//       try {
//         const res = await fetch(
//           "https://youngproductions-768ada043db3.herokuapp.com/api/HeroVideos"
//         );
//         const data = await res.json();

//         const visibleVideos = data
//           .filter((v) => v.visibility === true || v.visible === true)
//           .slice(0, 6) // max 6 videos
//           .map((v) =>
//             v.videoUrl.replace(
//               "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
//               "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
//             )
//           );

//         if (visibleVideos.length) setHeroVideos(visibleVideos);
//         else
//           setHeroVideos([
//             // fallback if API fails or empty
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/Creative%20Aubaine.mp4",
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/GFF%20Editions.mp4",
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/Ghoneimi%20Brochure.mp4",
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/NourXDara.mp4",
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/GFF2.mp4",
//             "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/CircleK%20Ramadan.mp4",
//           ]);
//       } catch (err) {
//         console.error("Failed to fetch hero videos:", err);
//       }
//     };

//     fetchHeroVideos();
//   }, []);

//   // GSAP animations
//   useLayoutEffect(() => {
//     if (!heroVideos.length) return; // wait until videos are loaded

//     const ctx = gsap.context(() => {
//       const wrappers = wrappersRef.current;

//       // Initial scattered positions
//       gsap.set(wrappers, {
//         x: (i) =>
//           i === 0
//             ? "-25vw"
//             : i === 1
//             ? "30vw"
//             : i === 2
//             ? "-25vw"
//             : i === 3
//             ? "20vw"
//             : i === 4
//             ? "-35vw"
//             : "35vw",
//         y: (i) =>
//           i === 0
//             ? "-25vh"
//             : i === 1
//             ? "-23vh"
//             : i === 2
//             ? "40vh"
//             : i === 3
//             ? "40vh"
//             : "10vh",
//         scale: 0.8,
//         willChange: "transform",
//       });

//       // Scroll-based timeline
//       const tl = gsap.timeline({
//         scrollTrigger: {
//           trigger: containerRef.current,
//           start: "top top",
//           end: "+=300vh",
//           scrub: 0.6,
//           pin: true,
//           anticipatePin: 1,
//           fastScrollEnd: true,
//         },
//       });

//       tl.to(wrappers, { x: 0, y: 50, stagger: 0.1, ease: "power2.out" });
//       tl.to(wrappers, { scale: 1, ease: "power2.out" }, "<");
//       tl.to(fillLogoRef.current, { opacity: 0 }, ">-0.2");
//       tl.to(strokeLogoRef.current, { opacity: 1 }, "<");
//       tl.to(wrappers, { scale: 3.5, ease: "power2.inOut" }, ">0.4");
//       tl.to(strokeLogoRef.current, { scale: 2, ease: "power2.inOut" }, "<");
//     }, containerRef);

//     // Play/pause videos on scroll
//     ScrollTrigger.create({
//       trigger: containerRef.current,
//       start: "top top",
//       onEnter: () => videosRef.current.forEach((v) => v?.play()),
//       onLeaveBack: () => videosRef.current.forEach((v) => v?.pause()),
//     });

//     return () => ctx.revert();
//   }, [heroVideos]);

//   return (
//     <section
//       ref={containerRef}
//       style={{
//         position: "relative",
//         height: "100vh",
//         overflow: "hidden",
//         background: "#000",
//       }}
//     >
//       {/* Light Rays */}
//       <LightRays
//         raysOrigin="top-center"
//         raysColor="#fff"
//         raysSpeed={1}
//         followMouse={false}
//         className="custom-rays"
//       />

//       {/* Videos */}
//       {heroVideos.map((src, i) => (
//         <div
//           ref={(el) => (wrappersRef.current[i] = el)}
//           className="video-proxy"
//           style={{
//             position: "absolute",
//             top: "43%",
//             left: "50%",
//             transform: "translate(-50%, -50%)",
//             width: "28vw",
//             height: "16vw",
//             maxWidth: "1200px",
//             maxHeight: "675px",
//             minWidth: "200px",
//             minHeight: "110px",
//             zIndex: 3 - i,
//           }}
//         >
//           <SmartHeroVideo
//             ref={(el) => (videosRef.current[i] = el)}
//             src={src}
//             style={{
//               width: "100%",
//               height: "100%",
//               objectFit: "cover",
//               filter: "brightness(0.9)",
//               borderRadius: 0,
//               boxShadow: "0 0 0 rgba(0,0,0,1.5)",
//             }}
//           />
//         </div>
//       ))}

//       {/* Filled logo */}
//       <div
//         ref={fillLogoRef}
//         style={{
//           position: "absolute",
//           top: 0,
//           left: 0,
//           width: "100%",
//           height: "100%",
//           zIndex: 1000,
//           pointerEvents: "none",
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "center",
//           fontFamily: "Formula Bold",
//           fontSize: "clamp(4rem, 25vw, 13rem)",
//           letterSpacing: "0.07em",
//           textAlign: "center",
//           lineHeight: 1,
//           opacity: 1,
//         }}
//       >
//         <img
//           src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/young-logo-white.webp"
//           width={
//             typeof window !== "undefined" && window.innerWidth < 768 ? 200 : 500
//           }
//           alt="Young-logo"
//           fetchPriority="high"
//           loading="eager"
//           decoding="async"
//         />
//       </div>

//       {/* Stroke logo */}
//       <div
//         ref={strokeLogoRef}
//         style={{
//           position: "absolute",
//           top: 0,
//           left: 0,
//           width: "100%",
//           height: "100%",
//           zIndex: 6,
//           pointerEvents: "none",
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "center",
//           mixBlendMode: "difference",
//           opacity: 0,
//         }}
//       >
//         <img
//           src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/young-logo-white.webp"
//           width={
//             typeof window !== "undefined" && window.innerWidth < 768 ? 100 : 500
//           }
//           style={{ filter: "brightness(100%)" }}
//           alt="Young-logo-white"
//           loading="lazy"
//           decoding="async"
//         />
//       </div>
//     </section>
//   );
// }
