import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
} from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import EditIcon from "@mui/icons-material/Edit";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ReceiptIcon from "@mui/icons-material/Receipt";
import CalculateIcon from "@mui/icons-material/Calculate";
import { motion } from "framer-motion";
import {
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Legend,
  Tooltip as RechartsTooltip,
} from "recharts";

function Records() {
  const navigate = useNavigate();
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [filters, setFilters] = useState({ year: "", month: "", search: "" });

  const [openDetail, setOpenDetail] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState("");
  const [detailRecord, setDetailRecord] = useState(null);
  const [openRevenueModal, setOpenRevenueModal] = useState(false);
  const [clientRevenue, setClientRevenue] = useState("");

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/agency-finance";

  const showSnackbar = (message, severity) =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const periodToNumber = (p) => {
    if (!p) return 0;
    const [y, m] = p.split("-").map((v) => parseInt(v, 10));
    return (y || 0) * 100 + (m || 0);
  };

  const fetchRecords = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(API_BASE_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await res.json();
      setRecords(Array.isArray(result.data) ? result.data : []);
    } catch (err) {
      console.error("Error fetching finance records:", err);
      showSnackbar("Failed to fetch finance records", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchRecordByPeriod = useCallback(async (period) => {
    setDetailLoading(true);
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${period}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await res.json();
      if (result?.data) {
        setDetailRecord(result.data);
      } else {
        setDetailRecord(null);
      }
    } catch (err) {
      console.error("Error fetching record:", err);
      showSnackbar("Failed to fetch record details", "error");
      setDetailRecord(null);
    } finally {
      setDetailLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchRecords();
  }, [fetchRecords]);

  const filteredRecords = useMemo(() => {
    const q = (filters.search || "").toLowerCase();
    return (records || [])
      .filter((r) => {
        if (filters.year) {
          const y = String(r.period).split("-")[0];
          if (y !== String(filters.year)) return false;
        }
        if (filters.month) {
          const m = String(r.period).split("-")[1];
          if (m !== String(filters.month).padStart(2, "0")) return false;
        }
        if (!q) return true;
        return String(r.period).toLowerCase().includes(q);
      })
      .sort((a, b) => periodToNumber(b.period) - periodToNumber(a.period));
  }, [records, filters]);

  const openDetailsFor = (period) => {
    navigate(`/admin/financial/records/${period}`);
  };

  const handleRecalculateFinance = async (period) => {
    try {
      const response = await fetch(`${API_BASE_URL}/${period}/recalculate`, {
        method: "PUT",
      });
      if (response.ok) {
        showSnackbar("Finance recalculated successfully", "success");
        // refresh both lists and detail
        fetchRecords();
        if (openDetail && selectedPeriod) fetchRecordByPeriod(selectedPeriod);
      } else {
        throw new Error("Failed to recalculate finance");
      }
    } catch (err) {
      console.error("Error recalculating finance:", err);
      showSnackbar("Failed to recalculate finance", "error");
    }
  };

  const handleUpdateClientRevenue = async () => {
    if (!selectedPeriod || !clientRevenue) {
      showSnackbar("Please set revenue amount", "warning");
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${API_BASE_URL}/${selectedPeriod}/client-revenue`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ client_revenue: parseFloat(clientRevenue) }),
        }
      );
      if (response.ok) {
        showSnackbar("Client revenue updated", "success");
        setOpenRevenueModal(false);
        setClientRevenue("");
        fetchRecords();
        fetchRecordByPeriod(selectedPeriod);
      } else {
        throw new Error("Failed to update client revenue");
      }
    } catch (err) {
      console.error("Error updating client revenue:", err);
      showSnackbar("Failed to update client revenue", "error");
    }
  };

  const COLORS = ["#2196f3", "#ff9800", "#4caf50", "#f44336", "#9c27b0"];

  const expenseBreakdown = useMemo(() => {
    if (!detailRecord) return [];
    return [
      {
        name: "Salaries",
        value: parseFloat(detailRecord.salaries_expense?.$numberDecimal || 0),
      },
      {
        name: "Agency Expenses",
        value: parseFloat(detailRecord.agency_expenses?.$numberDecimal || 0),
      },
    ].filter((item) => item.value > 0);
  }, [detailRecord]);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Finance Records
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchRecords}
            sx={{
              borderColor: "#db4a41",
              color: "#db4a41",
              fontFamily: "Formula Bold",
              "&:hover": {
                borderColor: "#c62828",
                backgroundColor: "rgba(219, 74, 65, 0.1)",
              },
            }}
          >
            Refresh
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Filters */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Year
                    </InputLabel>
                    <Select
                      value={filters.year}
                      onChange={(e) =>
                        setFilters((f) => ({ ...f, year: e.target.value }))
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.from(
                        new Set(
                          (records || []).map(
                            (r) => String(r.period).split("-")[0]
                          )
                        )
                      )
                        .sort()
                        .map((y) => (
                          <MenuItem key={y} value={y}>
                            {y}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Month
                    </InputLabel>
                    <Select
                      value={filters.month}
                      onChange={(e) =>
                        setFilters((f) => ({ ...f, month: e.target.value }))
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.from({ length: 12 }, (_, i) =>
                        String(i + 1).padStart(2, "0")
                      ).map((m) => (
                        <MenuItem key={m} value={m}>
                          {m}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4} md={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Search (period)"
                    value={filters.search}
                    onChange={(e) =>
                      setFilters((f) => ({ ...f, search: e.target.value }))
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Table */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Period
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Client Revenue
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Salaries
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Agency Expenses
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Inflow
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Outflow
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Net Profit
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredRecords
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((r) => (
                            <TableRow
                              key={r._id || r.period}
                              hover
                              onClick={() => openDetailsFor(r.period)}
                              sx={{
                                cursor: "pointer",
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                <Typography
                                  variant="body2"
                                  sx={{ fontFamily: "Formula Bold" }}
                                >
                                  {r.period}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                >
                                  Click to view details
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(r.client_revenue)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(r.salaries_expense)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(r.agency_expenses)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(r.total_inflow)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#f44336",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(r.total_outflow)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color:
                                    parseFloat(
                                      r.net_profit?.$numberDecimal || 0
                                    ) >= 0
                                      ? "#4caf50"
                                      : "#f44336",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(r.net_profit)}
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredRecords.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                      "& .MuiTablePagination-selectIcon": { color: "white" },
                      "& .MuiTablePagination-select": { color: "white" },
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Detail Dialog (single period) */}
        <Dialog
          open={openDetail}
          onClose={() => setOpenDetail(false)}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Finance Record — {selectedPeriod || ""}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {detailLoading ? (
              <Box sx={{ display: "flex", justifyContent: "center", py: 6 }}>
                <CircularProgress sx={{ color: "#db4a41" }} />
              </Box>
            ) : detailRecord ? (
              <>
                {/* KPI Cards */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          height: "100%",
                        }}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            <MonetizationOnIcon
                              sx={{ color: "#4caf50", mr: 1 }}
                            />
                            <Typography
                              variant="h6"
                              sx={{
                                fontFamily: "Formula Bold",
                                color: "#4caf50",
                              }}
                            >
                              Revenue
                            </Typography>
                          </Box>
                          <Typography
                            variant="h4"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "white",
                              mb: 1,
                            }}
                          >
                            {formatCurrency(detailRecord.client_revenue)}
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          height: "100%",
                        }}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            <ReceiptIcon sx={{ color: "#f44336", mr: 1 }} />
                            <Typography
                              variant="h6"
                              sx={{
                                fontFamily: "Formula Bold",
                                color: "#f44336",
                              }}
                            >
                              Expenses
                            </Typography>
                          </Box>
                          <Typography
                            variant="h4"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "white",
                              mb: 1,
                            }}
                          >
                            {formatCurrency(detailRecord.total_outflow)}
                          </Typography>
                          <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                            <Chip
                              label={`Salaries: ${formatCurrency(
                                detailRecord.salaries_expense
                              )}`}
                              size="small"
                              sx={{
                                backgroundColor: "#2196f3",
                                color: "white",
                                fontSize: "0.7rem",
                              }}
                            />
                            <Chip
                              label={`Agency: ${formatCurrency(
                                detailRecord.agency_expenses
                              )}`}
                              size="small"
                              sx={{
                                backgroundColor: "#ff9800",
                                color: "white",
                                fontSize: "0.7rem",
                              }}
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          height: "100%",
                        }}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            {parseFloat(
                              detailRecord.net_profit?.$numberDecimal || 0
                            ) >= 0 ? (
                              <TrendingUpIcon
                                sx={{ color: "#4caf50", mr: 1 }}
                              />
                            ) : (
                              <TrendingDownIcon
                                sx={{ color: "#f44336", mr: 1 }}
                              />
                            )}
                            <Typography
                              variant="h6"
                              sx={{
                                fontFamily: "Formula Bold",
                                color:
                                  parseFloat(
                                    detailRecord.net_profit?.$numberDecimal || 0
                                  ) >= 0
                                    ? "#4caf50"
                                    : "#f44336",
                              }}
                            >
                              Net Profit
                            </Typography>
                          </Box>
                          <Typography
                            variant="h4"
                            sx={{
                              fontFamily: "Formula Bold",
                              color:
                                parseFloat(
                                  detailRecord.net_profit?.$numberDecimal || 0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                              mb: 1,
                            }}
                          >
                            {formatCurrency(detailRecord.net_profit)}
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(
                              (Math.abs(
                                parseFloat(
                                  detailRecord.net_profit?.$numberDecimal || 0
                                )
                              ) /
                                Math.max(
                                  parseFloat(
                                    detailRecord.total_inflow?.$numberDecimal ||
                                      1
                                  ),
                                  1
                                )) *
                                100,
                              100
                            )}
                            sx={{
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: "rgba(255, 255, 255, 0.1)",
                              "& .MuiLinearProgress-bar": {
                                backgroundColor:
                                  parseFloat(
                                    detailRecord.net_profit?.$numberDecimal || 0
                                  ) >= 0
                                    ? "#4caf50"
                                    : "#f44336",
                              },
                            }}
                          />
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 }}
                    >
                      <Card
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "12px",
                          height: "100%",
                        }}
                      >
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 2,
                            }}
                          >
                            <AccountBalanceIcon
                              sx={{ color: "#2196f3", mr: 1 }}
                            />
                            <Typography
                              variant="h6"
                              sx={{
                                fontFamily: "Formula Bold",
                                color: "#2196f3",
                              }}
                            >
                              Cash Flow
                            </Typography>
                          </Box>
                          <Typography
                            variant="h4"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "white",
                              mb: 1,
                            }}
                          >
                            {formatCurrency(detailRecord.total_inflow)}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              mt: 1,
                            }}
                          >
                            <Typography
                              variant="caption"
                              sx={{ color: "#4caf50" }}
                            >
                              In: {formatCurrency(detailRecord.total_inflow)}
                            </Typography>
                            <Typography
                              variant="caption"
                              sx={{ color: "#f44336" }}
                            >
                              Out: {formatCurrency(detailRecord.total_outflow)}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                </Grid>

                {/* Expense Breakdown */}
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 3 }}
                        >
                          <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#db4a41",
                            }}
                          >
                            Expense Breakdown
                          </Typography>
                        </Box>
                        {expenseBreakdown.length > 0 ? (
                          <Box sx={{ height: 300 }}>
                            <ResponsiveContainer width="100%" height={300}>
                              <PieChart>
                                <Pie
                                  data={expenseBreakdown}
                                  cx="50%"
                                  cy="50%"
                                  outerRadius={80}
                                  dataKey="value"
                                >
                                  {expenseBreakdown.map((entry, index) => (
                                    <Cell
                                      key={`cell-${index}`}
                                      fill={COLORS[index % COLORS.length]}
                                      name={entry.name}
                                    />
                                  ))}
                                </Pie>
                                <Legend
                                  layout="horizontal"
                                  verticalAlign="top"
                                  align="center"
                                  formatter={(value) => (
                                    <span style={{ color: "#fff" }}>
                                      {value}
                                    </span>
                                  )}
                                />
                                <RechartsTooltip
                                  contentStyle={{
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.1)",
                                    borderRadius: "8px",
                                    color: "white",
                                  }}
                                  formatter={(value) => [
                                    `${parseFloat(value).toLocaleString()} EGP`,
                                    "Amount",
                                  ]}
                                />
                              </PieChart>
                            </ResponsiveContainer>
                          </Box>
                        ) : (
                          <Box sx={{ textAlign: "center", py: 4 }}>
                            <Typography
                              variant="body1"
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            >
                              No expenses recorded for this period
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 3 }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#db4a41",
                            }}
                          >
                            Actions
                          </Typography>
                        </Box>
                        <Box sx={{ display: "flex", gap: 2 }}>
                          <Button
                            variant="contained"
                            startIcon={<EditIcon />}
                            onClick={() => setOpenRevenueModal(true)}
                            sx={{
                              backgroundColor: "#db4a41",
                              color: "white",
                              fontFamily: "Formula Bold",
                              "&:hover": { backgroundColor: "#c62828" },
                            }}
                          >
                            Update Revenue
                          </Button>
                          <Button
                            variant="outlined"
                            startIcon={<RefreshIcon />}
                            onClick={() =>
                              handleRecalculateFinance(selectedPeriod)
                            }
                            sx={{
                              borderColor: "#db4a41",
                              color: "#db4a41",
                              fontFamily: "Formula Bold",
                              "&:hover": {
                                borderColor: "#c62828",
                                backgroundColor: "rgba(219, 74, 65, 0.1)",
                              },
                            }}
                          >
                            Recalculate
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {/* Salary Payments */}
                <Grid container spacing={3} sx={{ mt: 1 }}>
                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 3 }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            Salary Payments (
                            {detailRecord.salary_payments?.length || 0})
                          </Typography>
                        </Box>
                        {detailRecord.salary_payments?.length ? (
                          <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                            {detailRecord.salary_payments.map(
                              (payment, index) => (
                                <Box
                                  key={payment._id || index}
                                  sx={{
                                    p: 2,
                                    mb: 2,
                                    background: "rgba(33, 150, 243, 0.1)",
                                    borderRadius: "8px",
                                    border: "1px solid rgba(33, 150, 243, 0.2)",
                                  }}
                                >
                                  <Typography
                                    variant="body1"
                                    sx={{
                                      color: "white",
                                      fontFamily: "Formula Bold",
                                    }}
                                  >
                                    {formatCurrency(payment.net_amount)}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                  >
                                    Employee Name: {payment.userId?.name}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                  >
                                    Employee Role: {payment.userId?.role}
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                  >
                                    Month: {payment.month}/{payment.year}
                                  </Typography>
                                  <Chip
                                    label={payment.paid ? "PAID" : "PENDING"}
                                    size="small"
                                    sx={{
                                      mt: 1,
                                      backgroundColor: payment.paid
                                        ? "#4caf50"
                                        : "#ff9800",
                                      color: "white",
                                    }}
                                  />
                                </Box>
                              )
                            )}
                          </Box>
                        ) : (
                          <Box sx={{ textAlign: "center", py: 4 }}>
                            <Typography
                              variant="body1"
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            >
                              No salary payments for this period
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Agency Expenses */}
                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 3 }}
                        >
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#ff9800",
                            }}
                          >
                            Agency Expenses (
                            {detailRecord.expenses?.length || 0})
                          </Typography>
                        </Box>
                        {detailRecord.expenses?.length ? (
                          <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                            {detailRecord.expenses.map((expense, index) => (
                              <Box
                                key={expense._id || index}
                                sx={{
                                  p: 2,
                                  mb: 2,
                                  background: "rgba(255, 152, 0, 0.1)",
                                  borderRadius: "8px",
                                  border: "1px solid rgba(255, 152, 0, 0.2)",
                                }}
                              >
                                <Typography
                                  variant="body1"
                                  sx={{
                                    color: "white",
                                    fontFamily: "Formula Bold",
                                  }}
                                >
                                  {expense.title}
                                </Typography>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    color: "#ff9800",
                                    fontFamily: "Formula Bold",
                                  }}
                                >
                                  {formatCurrency(expense.amount)}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                >
                                  Category: {expense.category}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                                >
                                  Date:{" "}
                                  {new Date(expense.date).toLocaleDateString()}
                                </Typography>
                                {expense.description && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      color: "rgba(255, 255, 255, 0.6)",
                                      fontStyle: "italic",
                                    }}
                                  >
                                    {expense.description}
                                  </Typography>
                                )}
                              </Box>
                            ))}
                          </Box>
                        ) : (
                          <Box sx={{ textAlign: "center", py: 4 }}>
                            <Typography
                              variant="body1"
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            >
                              No agency expenses for this period
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </>
            ) : (
              <Box sx={{ textAlign: "center", py: 6 }}>
                <Typography
                  variant="body1"
                  sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                >
                  No data for this period
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenDetail(false)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Update Revenue Modal within Detail */}
        <Dialog
          open={openRevenueModal}
          onClose={() => setOpenRevenueModal(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Update Client Revenue — {selectedPeriod}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Client Revenue"
              type="number"
              value={clientRevenue}
              onChange={(e) => setClientRevenue(e.target.value)}
              placeholder="Enter revenue amount"
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <Typography
              variant="body2"
              sx={{ color: "rgba(255, 255, 255, 0.7)", mt: 2 }}
            >
              This updates the client revenue for {selectedPeriod} and
              recalculates inflow and profit.
            </Typography>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenRevenueModal(false)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateClientRevenue}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              Update Revenue
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Records;
