import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { fetchApi, API_ENDPOINTS, QUERY_KEYS } from '../utils/api';

// Clients data hook
export const useClients = () => {
  return useQuery({
    queryKey: QUERY_KEYS.CLIENTS,
    queryFn: () => fetchApi(API_ENDPOINTS.CLIENTS),
    staleTime: 10 * 60 * 1000, // 10 minutes - clients don't change often
  });
};

// Testimonials data hook
export const useTestimonials = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TESTIMONIALS,
    queryFn: () => fetchApi(API_ENDPOINTS.TESTIMONIALS),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Work videos hook (for Latest section)
export const useWorkVideos = () => {
  return useQuery({
    queryKey: QUERY_KEYS.WORK_VIDEOS,
    queryFn: () => fetchApi(API_ENDPOINTS.WORK_VIDEOS),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Work data hook (for Insights)
export const useWork = () => {
  return useQuery({
    queryKey: QUERY_KEYS.WORK,
    queryFn: () => fetchApi(API_ENDPOINTS.WORK),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Individual work item hook
export const useWorkDetail = (id) => {
  return useQuery({
    queryKey: QUERY_KEYS.WORK_DETAIL(id),
    queryFn: () => fetchApi(`${API_ENDPOINTS.WORK}/${id}`),
    enabled: !!id, // Only fetch if id exists
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Reel gallery hook
export const useReelGallery = () => {
  return useQuery({
    queryKey: QUERY_KEYS.REEL_GALLERY,
    queryFn: () => fetchApi(API_ENDPOINTS.REEL_GALLERY),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Feature work hook
export const useFeatureWork = () => {
  return useQuery({
    queryKey: QUERY_KEYS.FEATURE_WORK,
    queryFn: () => fetchApi(API_ENDPOINTS.FEATURE_WORK),
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Filter and sort visible items
      return data
        .filter((item) => item.visibility)
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    },
  });
};

// Hero videos hook
export const useHeroVideos = () => {
  return useQuery({
    queryKey: QUERY_KEYS.HERO_VIDEOS,
    queryFn: () => fetchApi(API_ENDPOINTS.HERO_VIDEOS),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => {
      // Filter for visible videos and limit to 6
      return data
        .filter((video) => video.visibility === true || video.visible === true)
        .slice(0, 6)
        .map((video) => video.videoUrl);
    },
  });
};

// Teams hook
export const useTeams = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TEAMS,
    queryFn: () => fetchApi(API_ENDPOINTS.TEAMS),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => {
      // Filter activated teams and sort by order
      return data
        .filter((member) => member.status === 'activated')
        .sort((a, b) => (a.order || 0) - (b.order || 0));
    },
  });
};

// Jobs hook
export const useJobs = () => {
  return useQuery({
    queryKey: QUERY_KEYS.JOBS,
    queryFn: () => fetchApi(API_ENDPOINTS.JOBS),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Infinite scroll hook for work videos (for future pagination)
export const useInfiniteWorkVideos = (limit = 10) => {
  return useInfiniteQuery({
    queryKey: ['workVideos', 'infinite', limit],
    queryFn: ({ pageParam = 1 }) =>
      fetchApi(`${API_ENDPOINTS.WORK_VIDEOS}?page=${pageParam}&limit=${limit}`),
    getNextPageParam: (lastPage, pages) => {
      return lastPage.hasMore ? pages.length + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
};
