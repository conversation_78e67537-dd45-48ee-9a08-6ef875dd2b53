import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CheckIcon from "@mui/icons-material/CheckCircle";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import { useUser } from "../../contexts/UserContext";

function Adjustments() {
  const { user } = useUser();
  const [adjustments, setAdjustments] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [cycles, setCycles] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectionType, setSelectionType] = useState("cycle"); // "cycle" or "project"
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingAdjustment, setEditingAdjustment] = useState(null);
  const [viewingAdjustment, setViewingAdjustment] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [filters, setFilters] = useState({
    resolved: "",
    client_id: "",
    cycle_id: "",
    category: "",
  });

  const role = useMemo(
    () => user?.role || localStorage.getItem("role") || "employee",
    [user]
  );
  const isManager = useMemo(
    () =>
      ["finance_manager", "general_manager", "admin", "manager"].includes(role),
    [role]
  );
  const [newAdjustment, setNewAdjustment] = useState({
    client_id: "",
    cycle_id: "",
    project_id: "",
    category: "props",
    description: "",
    impact_amount: "",
    notes: "",
    spendings: [],
  });
  const [spendingFiles, setSpendingFiles] = useState([]);
  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/adjustments";
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";
  const CYCLES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles";
  const PROJECTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/one-time-projects";

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const getResolvedColor = (resolved) => (resolved ? "#4caf50" : "#ff9800");

  // Calculate total spending amount
  const calculateSpendingTotal = (spendings) => {
    return spendings.reduce((total, spending) => {
      return total + (parseFloat(spending.amount) || 0);
    }, 0);
  };

  // Add new spending item
  const addSpending = () => {
    setNewAdjustment((prev) => ({
      ...prev,
      spendings: [
        ...prev.spendings,
        {
          amount: "",
          whoSpendIt: "",
          spendFor: "",
          screenshot: "",
        },
      ],
    }));
    setSpendingFiles((prev) => [...prev, null]);
  };

  // Remove spending item
  const removeSpending = (index) => {
    setNewAdjustment((prev) => ({
      ...prev,
      spendings: prev.spendings.filter((_, i) => i !== index),
    }));
    setSpendingFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Update spending item
  const updateSpending = (index, field, value) => {
    setNewAdjustment((prev) => {
      const updatedSpendings = [...prev.spendings];
      updatedSpendings[index] = {
        ...updatedSpendings[index],
        [field]: value,
      };
      return {
        ...prev,
        spendings: updatedSpendings,
      };
    });
  };

  // Handle file upload for spending
  const handleSpendingFileChange = (index, file) => {
    setSpendingFiles((prev) => {
      const updatedFiles = [...prev];
      updatedFiles[index] = file;
      return updatedFiles;
    });
  };

  // Calculate total spending amount in real-time
  const totalSpendingAmount = useMemo(() => {
    return calculateSpendingTotal(newAdjustment.spendings);
  }, [newAdjustment.spendings]);

  // Update impact_amount when spendings change
  useEffect(() => {
    setNewAdjustment((prev) => ({
      ...prev,
      impact_amount: totalSpendingAmount.toString(),
    }));
  }, [totalSpendingAmount]);

  // Export adjustment details to CSV
  const exportAdjustmentToCSV = (adjustment) => {
    if (!adjustment) return;

    const csvData = [];

    // Header information
    csvData.push(["**ADJUSTMENT DETAILS REPORT**"]);
    csvData.push([""]);
    csvData.push(["*Basic Information*"]);
    csvData.push(["Client Name", adjustment.client_id?.client_name || "N/A"]);
    csvData.push(["Cycle", adjustment.cycle_id?.cycle_name || "N/A"]);
    csvData.push(["Project", adjustment.project_id?.name || "N/A"]);
    csvData.push(["Category", adjustment.category]);
    csvData.push(["Description", adjustment.description]);
    csvData.push(["Status", adjustment.resolved ? "Resolved" : "Pending"]);
    csvData.push(["Created By", adjustment.created_by?.name || "N/A"]);
    csvData.push([
      "Created Date",
      new Date(adjustment.createdAt).toLocaleDateString(),
    ]);
    csvData.push(["Notes", adjustment.notes || "N/A"]);
    csvData.push([""]);

    // Spendings section
    if (adjustment.spendings && adjustment.spendings.length > 0) {
      csvData.push([
        `**SPENDINGS BREAKDOWN (${adjustment.spendings.length})**`,
      ]);
      csvData.push([""]);
      csvData.push([
        "*No.*",
        "*Amount (EGP)*",
        "*Who Spent It*",
        "*Spent For*",
        "*Receipt Available*",
      ]);

      adjustment.spendings.forEach((spending, index) => {
        csvData.push([
          index + 1,
          parseFloat(spending.amount).toLocaleString(),
          spending.whoSpendIt,
          spending.spendFor,
          spending.screenshot ? "Yes" : "No",
        ]);
      });

      csvData.push([""]);
      csvData.push([
        "**TOTAL SPENDING AMOUNT**",
        `${calculateSpendingTotal(adjustment.spendings).toLocaleString()} EGP`,
      ]);
    } else {
      csvData.push(["**NO SPENDINGS RECORDED**"]);
    }

    csvData.push([""]);
    csvData.push(["**FINANCIAL SUMMARY**"]);
    csvData.push([
      "Impact Amount",
      `${formatCurrency(adjustment.impact_amount)}`,
    ]);
    csvData.push([""]);
    csvData.push(["Report Generated", new Date().toLocaleString()]);

    // Convert to CSV format
    const csvContent = csvData
      .map((row) =>
        row
          .map((cell) => {
            const cellStr = String(cell || "");
            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            if (
              cellStr.includes(",") ||
              cellStr.includes('"') ||
              cellStr.includes("\n")
            ) {
              return `"${cellStr.replace(/"/g, '""')}"`;
            }
            return cellStr;
          })
          .join(",")
      )
      .join("\n");

    // Download CSV
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `adjustment-${adjustment.client_id?.client_name || "unknown"}-${
        new Date().toISOString().split("T")[0]
      }.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showSnackbar("Adjustment details exported successfully", "success");
  };

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const fetchAdjustments = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      setAdjustments(result.data || result || []);
    } catch (err) {
      console.error("Error fetching adjustments:", err);
      showSnackbar("Failed to fetch adjustments", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const list = result?.data || (Array.isArray(result) ? result : []);
      setClientAccounts(list);
    } catch (err) {
      console.error("Error fetching client accounts:", err);
      setClientAccounts([]);
    }
  }, []);

  const fetchCyclesByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setCycles([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${CYCLES_API_URL}/${clientId}/get-client-cycles`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      const result = await response.json();

      // ✅ endpoint returns { success: true, cycles: [...] }
      setCycles(result.cycles || []);
    } catch (err) {
      console.error("Error fetching client cycles:", err);
      setCycles([]);
    }
  }, []);

  const fetchProjectsByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setProjects([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${PROJECTS_API_URL}/client/${clientId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();

      // Handle the response structure based on the provided data format
      if (result.success && result.data) {
        // If single project returned, wrap in array
        const projectsData = Array.isArray(result.data)
          ? result.data
          : [result.data];
        setProjects(projectsData);
      } else {
        setProjects([]);
      }
    } catch (err) {
      console.error("Error fetching client projects:", err);
      setProjects([]);
    }
  }, []);

  // when client changes, fetch cycles and projects
  useEffect(() => {
    if (newAdjustment.client_id) {
      fetchCyclesByClient(newAdjustment.client_id);
      fetchProjectsByClient(newAdjustment.client_id);
    }
  }, [newAdjustment.client_id, fetchCyclesByClient, fetchProjectsByClient]);

  useEffect(() => {
    fetchAdjustments();
    fetchClientAccounts();
    fetchCyclesByClient();
  }, [fetchAdjustments, fetchClientAccounts, fetchCyclesByClient]);

  const filteredAdjustments = useMemo(() => {
    return (adjustments || []).filter((a) => {
      if (
        filters.resolved !== "" &&
        String(a.resolved) !== String(filters.resolved)
      )
        return false;
      if (
        filters.client_id &&
        (a.client_id?._id || a.client_id) !== filters.client_id
      )
        return false;
      if (
        filters.cycle_id &&
        (a.cycle_id?._id || a.cycle_id) !== filters.cycle_id
      )
        return false;
      if (filters.category && a.category !== filters.category) return false;
      return true;
    });
  }, [adjustments, filters]);

  const handleAdd = () => {
    setEditingAdjustment(null);
    setNewAdjustment({
      client_id: "",
      cycle_id: "",
      project_id: "",
      category: "props",
      description: "",
      impact_amount: "",
      notes: "",
      spendings: [],
    });
    setSpendingFiles([]);
    setOpenModal(true);
  };

  const handleEdit = (adjustment) => {
    setEditingAdjustment(adjustment);
    setNewAdjustment({
      client_id: adjustment.client_id?._id || adjustment.client_id,
      cycle_id: adjustment.cycle_id?._id || adjustment.cycle_id || "",
      project_id: adjustment.project_id?._id || adjustment.project_id || "",
      category: adjustment.category,
      description: adjustment.description,
      impact_amount:
        adjustment.impact_amount?.$numberDecimal ||
        adjustment.impact_amount ||
        "",
      notes: adjustment.notes || "",
      spendings: adjustment.spendings || [],
    });
    // Set selection type based on what's available
    if (adjustment.cycle_id) {
      setSelectionType("cycle");
    } else if (adjustment.project_id) {
      setSelectionType("project");
    }
    setSpendingFiles(new Array(adjustment.spendings?.length || 0).fill(null));
    setOpenModal(true);
  };

  const handleView = async (a) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${a._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const full = result?.data || a;
      setViewingAdjustment(full);
      setOpenViewModal(true);
    } catch (err) {
      console.error("Failed to fetch adjustment details", err);
      setViewingAdjustment(a);
      setOpenViewModal(true);
    }
  };
  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingAdjustment(null);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingAdjustment(null);
    setNewAdjustment({
      client_id: "",
      cycle_id: "",
      project_id: "",
      category: "props",
      description: "",
      impact_amount: "",
      notes: "",
      spendings: [],
    });
    setSpendingFiles([]);
    setSelectionType("cycle");
    setCycles([]);
    setProjects([]);
  };

  const handleSelectionTypeChange = (event, newType) => {
    if (newType !== null) {
      setSelectionType(newType);
      // Clear the opposite selection when switching
      setNewAdjustment((prev) => ({
        ...prev,
        cycle_id: newType === "cycle" ? prev.cycle_id : "",
        project_id: newType === "project" ? prev.project_id : "",
      }));
    }
  };

  const handleCreateOrUpdate = async () => {
    try {
      const token = localStorage.getItem("token");
      const isEditing = !!editingAdjustment;

      // Validate required fields based on selection type
      const hasValidSelection =
        selectionType === "cycle"
          ? newAdjustment.cycle_id
          : newAdjustment.project_id;

      if (
        !newAdjustment.client_id ||
        !hasValidSelection ||
        !newAdjustment.category ||
        !newAdjustment.description
      ) {
        showSnackbar(
          `Please fill all required fields (including ${selectionType})`,
          "warning"
        );
        return;
      }

      // Validate spendings if any exist
      if (newAdjustment.spendings.length > 0) {
        const invalidSpending = newAdjustment.spendings.find(
          (spending) =>
            !spending.amount ||
            !spending.whoSpendIt ||
            !spending.spendFor ||
            parseFloat(spending.amount) <= 0
        );
        if (invalidSpending) {
          showSnackbar(
            "Please fill all spending fields with valid amounts",
            "warning"
          );
          return;
        }
      }

      // Create FormData for multipart upload
      const formData = new FormData();
      formData.append("client_id", newAdjustment.client_id);
      formData.append("category", newAdjustment.category);
      formData.append("description", newAdjustment.description);
      formData.append("notes", newAdjustment.notes || "");

      if (!isEditing) {
        formData.append("created_by", user?._id || user?.id);
      }

      // Add either cycle_id or project_id based on selection type
      if (selectionType === "cycle") {
        formData.append("cycle_id", newAdjustment.cycle_id);
      } else {
        formData.append("project_id", newAdjustment.project_id);
      }

      // Add spendings as JSON string
      formData.append("spendings", JSON.stringify(newAdjustment.spendings));

      // Add screenshot files
      spendingFiles.forEach((file) => {
        if (file) {
          formData.append("screenshots", file);
        }
      });

      const url = isEditing
        ? `${API_BASE_URL}/${editingAdjustment._id}`
        : API_BASE_URL;
      const method = isEditing ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const body = await response.json().catch(() => ({}));
        throw new Error(
          body.message ||
            `Failed to ${isEditing ? "update" : "create"} adjustment`
        );
      }

      await fetchAdjustments();
      showSnackbar(
        `Adjustment ${isEditing ? "updated" : "created"} successfully`,
        "success"
      );
      setOpenModal(false);
    } catch (err) {
      console.error(err);
      showSnackbar(
        err.message ||
          `Failed to ${editingAdjustment ? "update" : "create"} adjustment`,
        "error"
      );
    }
  };

  const resolveAdjustment = async (id) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}/resolve`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ resolved_by: user?._id || user?.id }),
      });
      if (!response.ok) throw new Error("Failed to resolve adjustment");
      await fetchAdjustments();
      showSnackbar("Adjustment resolved", "success");
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to resolve adjustment", "error");
    }
  };

  const [confirmResolve, setConfirmResolve] = useState({
    open: false,
    id: null,
  });
  const openConfirmResolve = (id) => setConfirmResolve({ open: true, id });
  const closeConfirmResolve = () =>
    setConfirmResolve({ open: false, id: null });
  const submitResolve = async () => {
    await resolveAdjustment(confirmResolve.id);
    closeConfirmResolve();
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Adjustments
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            New Adjustment
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Resolved
                    </InputLabel>
                    <Select
                      value={filters.resolved}
                      onChange={(e) =>
                        setFilters({ ...filters, resolved: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      <MenuItem value="true">Resolved</MenuItem>
                      <MenuItem value="false">Unresolved</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Client
                    </InputLabel>
                    <Select
                      value={filters.client_id}
                      onChange={(e) =>
                        setFilters({ ...filters, client_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(clientAccounts) &&
                        clientAccounts.map((c) => (
                          <MenuItem key={c._id} value={c._id}>
                            {c.client_name}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Cycle
                    </InputLabel>
                    <Select
                      value={filters.cycle_id}
                      onChange={(e) =>
                        setFilters({ ...filters, cycle_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(cycles) &&
                        cycles.map((cy) => (
                          <MenuItem key={cy._id} value={cy._id}>
                            {cy.cycle_name ||
                              `${cy.client_id?.client_name} - ${cy.month}`}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Category
                    </InputLabel>
                    <Select
                      value={filters.category}
                      onChange={(e) =>
                        setFilters({ ...filters, category: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {[
                        "props",
                        "models",
                        "missing",
                        "transportation",
                        "others",
                      ].map((c) => (
                        <MenuItem
                          key={c}
                          value={c}
                          sx={{ textTransform: "capitalize" }}
                        >
                          {c}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{ background: "transparent" }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Client
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Cycle
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Category
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Impact
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Resolved
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredAdjustments
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((a) => (
                            <TableRow
                              key={a._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                {a.client_id?.client_name || "-"}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {a.cycle_id?.cycle_name ||
                                  a.cycle_id?.month ||
                                  "-"}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  textTransform: "capitalize",
                                }}
                              >
                                {a.category}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(a.impact_amount)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={a.resolved ? "Resolved" : "Unresolved"}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getResolvedColor(
                                      a.resolved
                                    )}20`,
                                    color: getResolvedColor(a.resolved),
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(a)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  {isManager && !a.resolved && (
                                    <>
                                      <Tooltip title="Edit">
                                        <IconButton
                                          size="small"
                                          onClick={() => handleEdit(a)}
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                            "&:hover": { color: "#2196f3" },
                                          }}
                                        >
                                          <EditIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title="Mark as resolved">
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            openConfirmResolve(a._id)
                                          }
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                            "&:hover": { color: "#4caf50" },
                                          }}
                                        >
                                          <CheckIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                    </>
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredAdjustments.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Create Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingAdjustment ? "Edit Adjustment" : "Create New Adjustment"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newAdjustment.client_id}
                    onChange={(e) =>
                      setNewAdjustment({
                        ...newAdjustment,
                        client_id: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((c) => (
                        <MenuItem key={c._id} value={c._id}>
                          {c.client_name}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Toggle Button for Cycle/Project Selection */}
              <Grid item xs={12}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)", mr: 2 }}
                  >
                    Select Type:
                  </Typography>
                  <ToggleButtonGroup
                    value={selectionType}
                    exclusive
                    onChange={handleSelectionTypeChange}
                    sx={{
                      "& .MuiToggleButton-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&.Mui-selected": {
                          backgroundColor: "#db4a41",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#c43a31",
                          },
                        },
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                        },
                      },
                    }}
                  >
                    <ToggleButton value="cycle">
                      Subscription Cycle
                    </ToggleButton>
                    <ToggleButton value="project">
                      One-Time Project
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    {selectionType === "cycle"
                      ? "Subscription Cycle"
                      : "One-Time Project"}
                  </InputLabel>
                  <Select
                    value={
                      selectionType === "cycle"
                        ? newAdjustment.cycle_id
                        : newAdjustment.project_id
                    }
                    onChange={(e) =>
                      setNewAdjustment({
                        ...newAdjustment,
                        [selectionType === "cycle" ? "cycle_id" : "project_id"]:
                          e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {selectionType === "cycle"
                      ? // Render Cycles
                        Array.isArray(cycles) &&
                        cycles
                          .filter((cy) => {
                            if (user.role === "general_manager") {
                              return true; // GM sees all
                            }

                            const now = new Date();
                            const currentMonth = String(
                              now.getMonth() + 1
                            ).padStart(2, "0");
                            const currentYear = now.getFullYear();

                            if (cy.year && cy.month) {
                              return (
                                String(cy.year) === String(currentYear) &&
                                String(cy.month).padStart(2, "0") ===
                                  currentMonth
                              );
                            }

                            if (cy.start_date) {
                              const cycleDate = new Date(cy.start_date);
                              return (
                                cycleDate.getFullYear() === currentYear &&
                                String(cycleDate.getMonth() + 1).padStart(
                                  2,
                                  "0"
                                ) === currentMonth
                              );
                            }

                            if (cy.cycle_name) {
                              return cy.cycle_name.includes(
                                `CY${currentYear}-${currentMonth}`
                              );
                            }

                            return false;
                          })
                          .map((cy) => (
                            <MenuItem key={cy._id} value={cy._id}>
                              {cy.cycle_name ||
                                `${cy.client_id?.client_name} - ${cy.month}/${cy.year}`}
                            </MenuItem>
                          ))
                      : // Render Projects
                        Array.isArray(projects) &&
                        projects.map((project) => (
                          <MenuItem key={project._id} value={project._id}>
                            {`${project.name} - ${new Date(
                              project.date
                            ).toLocaleDateString()} - ${formatCurrency(
                              project.fees
                            )}`}
                          </MenuItem>
                        ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Category
                  </InputLabel>

                  <Select
                    value={newAdjustment.category}
                    onChange={(e) =>
                      setNewAdjustment({
                        ...newAdjustment,
                        category: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {[
                      "props",
                      "models",
                      "missing",
                      "transportation",
                      "others",
                    ].map((c) => (
                      <MenuItem
                        key={c}
                        value={c}
                        sx={{ textTransform: "capitalize" }}
                      >
                        {c}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Impact Amount (Auto-calculated)"
                  type="number"
                  value={newAdjustment.impact_amount}
                  InputProps={{
                    readOnly: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      backgroundColor: "rgba(255, 255, 255, 0.05)",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={newAdjustment.description}
                  onChange={(e) =>
                    setNewAdjustment({
                      ...newAdjustment,
                      description: e.target.value,
                    })
                  }
                  multiline
                  minRows={2}
                  maxRows={4}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  value={newAdjustment.notes}
                  onChange={(e) =>
                    setNewAdjustment({
                      ...newAdjustment,
                      notes: e.target.value,
                    })
                  }
                  multiline
                  minRows={1}
                  maxRows={4}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              {/* Spendings Section */}
              <Grid item xs={12}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mt: 2,
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{ color: "white", fontFamily: "Formula Bold" }}
                  >
                    Spendings
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={addSpending}
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Add Spending
                  </Button>
                </Box>
              </Grid>

              {newAdjustment.spendings.map((spending, index) => (
                <Grid item xs={12} key={index}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "8px",
                      p: 2,
                    }}
                  >
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="Amount"
                          type="number"
                          value={spending.amount}
                          onChange={(e) =>
                            updateSpending(index, "amount", e.target.value)
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              color: "white",
                              "& fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&:hover fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "#db4a41",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              color: "rgba(255, 255, 255, 0.7)",
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="Who Spent It"
                          value={spending.whoSpendIt}
                          onChange={(e) =>
                            updateSpending(index, "whoSpendIt", e.target.value)
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              color: "white",
                              "& fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&:hover fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "#db4a41",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              color: "rgba(255, 255, 255, 0.7)",
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Spent For"
                          value={spending.spendFor}
                          onChange={(e) =>
                            updateSpending(index, "spendFor", e.target.value)
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              color: "white",
                              "& fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.3)",
                              },
                              "&:hover fieldset": {
                                borderColor: "rgba(255, 255, 255, 0.5)",
                              },
                              "&.Mui-focused fieldset": {
                                borderColor: "#db4a41",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              color: "rgba(255, 255, 255, 0.7)",
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          variant="outlined"
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={() => removeSpending(index)}
                          sx={{
                            height: "56px",
                            borderColor: "#f44336",
                            color: "#f44336",
                            "&:hover": {
                              borderColor: "#d32f2f",
                              backgroundColor: "rgba(244, 67, 54, 0.1)",
                            },
                          }}
                        >
                          Remove
                        </Button>
                      </Grid>
                      <Grid item xs={12}>
                        <Box>
                          <input
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) =>
                              handleSpendingFileChange(index, e.target.files[0])
                            }
                            style={{ display: "none" }}
                            id={`screenshot-upload-${index}`}
                          />
                          <label htmlFor={`screenshot-upload-${index}`}>
                            <Button
                              variant="outlined"
                              component="span"
                              sx={{
                                borderColor: "rgba(255, 255, 255, 0.3)",
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": {
                                  borderColor: "rgba(255, 255, 255, 0.5)",
                                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                                },
                              }}
                            >
                              Upload Screenshot
                            </Button>
                          </label>
                          {spendingFiles[index] && (
                            <Typography
                              variant="caption"
                              sx={{ color: "#4caf50", ml: 2 }}
                            >
                              File selected: {spendingFiles[index].name}
                            </Typography>
                          )}
                          {spending.screenshot && !spendingFiles[index] && (
                            <Typography
                              variant="caption"
                              sx={{ color: "#2196f3", ml: 2 }}
                            >
                              Existing file attached
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                    </Grid>
                  </Card>
                </Grid>
              ))}

              {newAdjustment.spendings.length > 0 && (
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 2,
                      background: "rgba(76, 175, 80, 0.1)",
                      borderRadius: "8px",
                      border: "1px solid rgba(76, 175, 80, 0.3)",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      Total Spending Amount:{" "}
                      {totalSpendingAmount.toLocaleString()} EGP
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateOrUpdate}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingAdjustment ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            Adjustment Details
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => exportAdjustmentToCSV(viewingAdjustment)}
              sx={{
                borderColor: "#4caf50",
                color: "#4caf50",
                "&:hover": {
                  borderColor: "#388e3c",
                  backgroundColor: "rgba(76, 175, 80, 0.1)",
                },
              }}
            >
              Export CSV
            </Button>
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingAdjustment && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Client:</strong>{" "}
                    {viewingAdjustment.client_id?.client_name || "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Cycle:</strong>{" "}
                    {viewingAdjustment.cycle_id?.cycle_name ||
                      viewingAdjustment.cycle_id?.month ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Resolved:</strong>{" "}
                    {viewingAdjustment.resolved ? "Yes" : "No"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Category:</strong> {viewingAdjustment.category}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Impact Amount:</strong>{" "}
                    {formatCurrency(viewingAdjustment.impact_amount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Created By:</strong>{" "}
                    {viewingAdjustment.created_by?.name ||
                      viewingAdjustment.created_by?.email ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Date:</strong>{" "}
                    {new Date(
                      viewingAdjustment.date || viewingAdjustment.createdAt
                    ).toLocaleDateString()}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Description:</strong>{" "}
                    {viewingAdjustment.description}
                  </Typography>
                </Grid>
                {viewingAdjustment.notes && (
                  <Grid item xs={12}>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      <strong>Notes:</strong> {viewingAdjustment.notes}
                    </Typography>
                  </Grid>
                )}

                {/* Spendings Display */}
                {viewingAdjustment.spendings &&
                  viewingAdjustment.spendings.length > 0 && (
                    <Grid item xs={12}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          mt: 2,
                          mb: 2,
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{
                            color: "white",
                            fontFamily: "Formula Bold",
                          }}
                        >
                          Spendings Breakdown (
                          {viewingAdjustment.spendings.length} items)
                        </Typography>
                        <Chip
                          label={`Total: ${calculateSpendingTotal(
                            viewingAdjustment.spendings
                          ).toLocaleString()} EGP`}
                          sx={{
                            backgroundColor: "rgba(76, 175, 80, 0.2)",
                            color: "#4caf50",
                            fontWeight: "bold",
                          }}
                        />
                      </Box>

                      {/* Spendings Table */}
                      <TableContainer
                        component={Paper}
                        sx={{
                          background: "rgba(255, 255, 255, 0.05)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          borderRadius: "8px",
                          mb: 2,
                        }}
                      >
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell
                                sx={{
                                  color: "white",
                                  fontWeight: "bold",
                                  borderBottom:
                                    "1px solid rgba(255, 255, 255, 0.1)",
                                }}
                              >
                                #
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  fontWeight: "bold",
                                  borderBottom:
                                    "1px solid rgba(255, 255, 255, 0.1)",
                                }}
                              >
                                Amount (EGP)
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  fontWeight: "bold",
                                  borderBottom:
                                    "1px solid rgba(255, 255, 255, 0.1)",
                                }}
                              >
                                Who Spent It
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  fontWeight: "bold",
                                  borderBottom:
                                    "1px solid rgba(255, 255, 255, 0.1)",
                                }}
                              >
                                Spent For
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  fontWeight: "bold",
                                  borderBottom:
                                    "1px solid rgba(255, 255, 255, 0.1)",
                                }}
                              >
                                Receipt
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {viewingAdjustment.spendings.map(
                              (spending, index) => (
                                <TableRow key={index}>
                                  <TableCell
                                    sx={{
                                      color: "white",
                                      borderBottom:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {index + 1}
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      color: "#4caf50",
                                      fontWeight: "bold",
                                      borderBottom:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {parseFloat(
                                      spending.amount
                                    ).toLocaleString()}
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      color: "white",
                                      borderBottom:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {spending.whoSpendIt}
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      color: "white",
                                      borderBottom:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {spending.spendFor}
                                  </TableCell>
                                  <TableCell
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(255, 255, 255, 0.1)",
                                    }}
                                  >
                                    {spending.screenshot ? (
                                      <Button
                                        variant="outlined"
                                        size="small"
                                        onClick={() =>
                                          window.open(
                                            spending.screenshot.replace(
                                              "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                                            ),
                                            "_blank"
                                          )
                                        }
                                        sx={{
                                          borderColor: "#2196f3",
                                          color: "#2196f3",
                                          fontSize: "0.75rem",
                                          "&:hover": {
                                            borderColor: "#1976d2",
                                            backgroundColor:
                                              "rgba(33, 150, 243, 0.1)",
                                          },
                                        }}
                                      >
                                        View
                                      </Button>
                                    ) : (
                                      <Chip
                                        label="No Receipt"
                                        size="small"
                                        sx={{
                                          backgroundColor:
                                            "rgba(255, 152, 0, 0.2)",
                                          color: "#ff9800",
                                          fontSize: "0.7rem",
                                        }}
                                      />
                                    )}
                                  </TableCell>
                                </TableRow>
                              )
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {/* Summary Box */}
                      <Box
                        sx={{
                          p: 2,
                          background: "rgba(76, 175, 80, 0.1)",
                          borderRadius: "8px",
                          border: "1px solid rgba(76, 175, 80, 0.3)",
                          mt: 1,
                        }}
                      >
                        <Typography
                          variant="h6"
                          sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                        >
                          Total Spending Amount:{" "}
                          {calculateSpendingTotal(
                            viewingAdjustment.spendings
                          ).toLocaleString()}{" "}
                          EGP
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)", mt: 1 }}
                        >
                          Impact Amount:{" "}
                          {formatCurrency(viewingAdjustment.impact_amount)}
                        </Typography>
                      </Box>
                    </Grid>
                  )}

                {/* No Spendings Message */}
                {(!viewingAdjustment.spendings ||
                  viewingAdjustment.spendings.length === 0) && (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        p: 3,
                        background: "rgba(255, 152, 0, 0.1)",
                        borderRadius: "8px",
                        border: "1px solid rgba(255, 152, 0, 0.3)",
                        textAlign: "center",
                        mt: 2,
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                      >
                        No Spendings Recorded
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.7)", mt: 1 }}
                      >
                        This adjustment has no detailed spending breakdown.
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Resolve Confirm */}
        <Dialog
          open={confirmResolve.open}
          onClose={closeConfirmResolve}
          maxWidth="xs"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Confirm Resolve
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Typography sx={{ color: "rgba(255, 255, 255, 0.9)" }}>
              Are you sure you want to mark this adjustment as resolved?
            </Typography>
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button
              onClick={closeConfirmResolve}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitResolve}
              variant="contained"
              sx={{ backgroundColor: "#4caf50", "&:hover": { opacity: 0.9 } }}
            >
              Resolve
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Adjustments;
