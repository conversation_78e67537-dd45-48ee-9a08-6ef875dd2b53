import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
} from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import EditIcon from "@mui/icons-material/Edit";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ReceiptIcon from "@mui/icons-material/Receipt";
import PeopleIcon from "@mui/icons-material/People";
import BusinessIcon from "@mui/icons-material/Business";
import CalculateIcon from "@mui/icons-material/Calculate";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import WorkOutlineIcon from "@mui/icons-material/WorkOutline";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { motion } from "framer-motion";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
} from "recharts";
import {
  exportAgencyFinanceToCSV,
  exportAgencyFinanceSummaryToCSV,
} from "../../utils/csvExport";
function AgencyFinance() {
  const [financeData, setFinanceData] = useState([]);
  const [selectedStartPeriod, setSelectedStartPeriod] = useState("");
  const [selectedEndPeriod, setSelectedEndPeriod] = useState("");
  const [loading, setLoading] = useState(false);
  const [openRevenueModal, setOpenRevenueModal] = useState(false);
  const [clientRevenue, setClientRevenue] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [cycles, setCycles] = useState([]);
  const [cyclesLoading, setCyclesLoading] = useState(false);
  const [expandedCycles, setExpandedCycles] = useState({});
  const [paymentsData, setPaymentsData] = useState([]);

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/agency-finance";
  const CYCLES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles/cycles/period";
  const PAYMENTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/payments/period";

  const fetchFinanceData = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setFinanceData(result.data || []);

      // Set YTD as default (from January to current month)
      const currentDate = new Date();
      const currentPeriod = `${currentDate.getFullYear()}-${String(
        currentDate.getMonth() + 1
      ).padStart(2, "0")}`;
      const startOfYear = `${currentDate.getFullYear()}-01`;
      setSelectedStartPeriod(startOfYear);
      setSelectedEndPeriod(currentPeriod);
    } catch (error) {
      console.error("Error fetching finance data:", error);
      showSnackbar("Failed to fetch finance data", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchCycles = useCallback(async () => {
    if (!selectedStartPeriod || !selectedEndPeriod) return;

    setCyclesLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${CYCLES_API_URL}?start=${selectedStartPeriod}&end=${selectedEndPeriod}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (result.success) {
        setCycles(result.cycles || []);
      } else {
        setCycles([]);
      }
    } catch (error) {
      console.error("Error fetching cycles:", error);
      showSnackbar("Failed to fetch cycles data", "error");
      setCycles([]);
    } finally {
      setCyclesLoading(false);
    }
  }, [selectedStartPeriod, selectedEndPeriod]);

  const fetchPayments = useCallback(async () => {
    if (!selectedStartPeriod || !selectedEndPeriod) return;

    try {
      const token = localStorage.getItem("token");
      // Convert period format from YYYY-MM to MM/YYYY
      const formatPeriod = (period) => {
        const [year, month] = period.split("-");
        return `${month}/${year}`;
      };

      const startFormatted = formatPeriod(selectedStartPeriod);
      const endFormatted = formatPeriod(selectedEndPeriod);

      const response = await fetch(
        `${PAYMENTS_API_URL}?start=${startFormatted}&end=${endFormatted}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const result = await response.json();
      if (result.success && result.data && result.data.summary) {
        // Extract payments from the nested structure
        let allPayments = [];
        const paymentsByMonth = result.data.summary.payments_by_month;

        // Iterate through each month and collect all payments
        Object.keys(paymentsByMonth).forEach((month) => {
          if (paymentsByMonth[month].payments) {
            allPayments = allPayments.concat(paymentsByMonth[month].payments);
          }
        });
        console.log("all payment data from the api:", allPayments);
        setPaymentsData(allPayments);
      } else {
        setPaymentsData([]);
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      showSnackbar("Failed to fetch payments data", "error");
      setPaymentsData([]);
    }
  }, [selectedStartPeriod, selectedEndPeriod]);

  useEffect(() => {
    fetchFinanceData();
  }, [fetchFinanceData]);

  useEffect(() => {
    fetchCycles();
  }, [fetchCycles]);

  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  const handleUpdateClientRevenue = async () => {
    const isSingleMonth =
      selectedStartPeriod &&
      selectedEndPeriod &&
      selectedStartPeriod === selectedEndPeriod;
    if (!isSingleMonth) {
      showSnackbar("Select a single month to update revenue", "warning");
      return;
    }
    if (!selectedStartPeriod || !clientRevenue) {
      showSnackbar(
        "Please select a period and enter revenue amount",
        "warning"
      );
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${API_BASE_URL}/${selectedStartPeriod}/client-revenue`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ client_revenue: parseFloat(clientRevenue) }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        // Update the finance data
        setFinanceData((prev) =>
          prev
            .map((item) =>
              item.period === selectedStartPeriod ? result.data : item
            )
            .concat(
              prev.find((item) => item.period === selectedStartPeriod)
                ? []
                : [result.data]
            )
        );
        setOpenRevenueModal(false);
        setClientRevenue("");
        showSnackbar("Client revenue updated successfully", "success");
      } else {
        throw new Error("Failed to update client revenue");
      }
    } catch (error) {
      console.error("Error updating client revenue:", error);
      showSnackbar("Failed to update client revenue", "error");
    }
  };

  const handleRecalculateFinance = async (period) => {
    console.log("Recalculating finance for period:", period);
    try {
      const response = await fetch(`${API_BASE_URL}/${period}/recalculate`, {
        method: "PUT",
      });

      if (response.ok) {
        showSnackbar("Finance recalculated successfully", "success");
        fetchFinanceData(); // Refresh data
      } else {
        throw new Error("Failed to recalculate finance");
      }
    } catch (error) {
      console.error("Error recalculating finance:", error);
      showSnackbar("Failed to recalculate finance", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // CSV Export functionality
  const handleExportCSV = () => {
    exportAgencyFinanceToCSV(
      currentData,
      cycles,
      selectedStartPeriod,
      selectedEndPeriod,
      showSnackbar,
      paymentsData
    );
  };
  // CSV Export (Summary Only) functionality
  const handleExportSummaryCSV = () => {
    exportAgencyFinanceSummaryToCSV(
      currentData,
      selectedStartPeriod,
      selectedEndPeriod,
      showSnackbar
    );
  };

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const toggleCycleExpansion = (cycleId) => {
    setExpandedCycles((prev) => ({
      ...prev,
      [cycleId]: !prev[cycleId],
    }));
  };

  const periodToNumber = (p) => {
    if (!p) return 0;
    const [y, m] = p.split("-").map((v) => parseInt(v, 10));
    return (y || 0) * 100 + (m || 0);
  };

  const getAggregatedData = () => {
    const startNum = periodToNumber(selectedStartPeriod);
    const endNum = periodToNumber(selectedEndPeriod);
    const inRange = financeData.filter((item) => {
      const n = periodToNumber(item.period);
      return n >= startNum && n <= endNum;
    });

    if (inRange.length === 0) {
      return {
        periodRange: `${selectedStartPeriod} to ${selectedEndPeriod}`,
        client_revenue: { $numberDecimal: "0" },
        salaries_expense: { $numberDecimal: "0" },
        agency_expenses: { $numberDecimal: "0" },
        total_inflow: { $numberDecimal: "0" },
        total_outflow: { $numberDecimal: "0" },
        net_profit: { $numberDecimal: "0" },
        salary_payments: [],
        expenses: [],
        client_outflows: { $numberDecimal: "0" },
        total_cycles_fees: { $numberDecimal: "0" },
        client_paid: { $numberDecimal: "0" },
        client_due_amount: { $numberDecimal: "0" },
        project_paid: { $numberDecimal: "0" },
        project_due_amount: { $numberDecimal: "0" },
        project_outflows: { $numberDecimal: "0" },
        project_inflows: { $numberDecimal: "0" },
        total_project_fees: { $numberDecimal: "0" },
      };
    }

    const sumDecimal = (arr, key) =>
      arr.reduce(
        (acc, it) => acc + parseFloat(it[key]?.$numberDecimal || it[key] || 0),
        0
      );

    return {
      periodRange: `${selectedStartPeriod} to ${selectedEndPeriod}`,
      client_revenue: {
        $numberDecimal: String(sumDecimal(inRange, "client_revenue")),
      },
      salaries_expense: {
        $numberDecimal: String(sumDecimal(inRange, "salaries_expense")),
      },
      agency_expenses: {
        $numberDecimal: String(sumDecimal(inRange, "agency_expenses")),
      },
      total_inflow: {
        $numberDecimal: String(sumDecimal(inRange, "total_inflow")),
      },
      total_outflow: {
        $numberDecimal: String(sumDecimal(inRange, "total_outflow")),
      },
      net_profit: { $numberDecimal: String(sumDecimal(inRange, "net_profit")) },
      salary_payments: inRange.flatMap((it) => it.salary_payments || []),
      expenses: inRange.flatMap((it) => it.expenses || []),
      client_outflows: {
        $numberDecimal: String(sumDecimal(inRange, "client_outflows")),
      }, // Initialize with 0
      total_cycles_fees: {
        $numberDecimal: String(sumDecimal(inRange, "total_cycles_fees")),
      },
      client_paid: {
        $numberDecimal: String(sumDecimal(inRange, "client_paid")),
      },
      client_due_amount: {
        $numberDecimal: String(sumDecimal(inRange, "client_due_amount")),
      },
      project_paid: {
        $numberDecimal: String(sumDecimal(inRange, "project_paid")),
      },
      project_due_amount: {
        $numberDecimal: String(sumDecimal(inRange, "project_due_amount")),
      },
      project_outflows: {
        $numberDecimal: String(sumDecimal(inRange, "project_outflows")),
      },
      project_inflows: {
        $numberDecimal: String(sumDecimal(inRange, "project_inflows")),
      },
      total_project_fees: {
        $numberDecimal: String(sumDecimal(inRange, "total_project_fees")),
      },
    };
  };

  const currentData = getAggregatedData();

  // Prepare chart data
  const chartData = financeData
    .filter((item) => {
      const n = periodToNumber(item.period);
      return (
        n >= periodToNumber(selectedStartPeriod) &&
        n <= periodToNumber(selectedEndPeriod)
      );
    })
    .map((item) => ({
      period: item.period,
      inflow: parseFloat(item.total_inflow?.$numberDecimal || 0),
      outflow: parseFloat(item.total_outflow?.$numberDecimal || 0),
      profit: parseFloat(item.net_profit?.$numberDecimal || 0),
      revenue: parseFloat(item.client_paid?.$numberDecimal || 0),
    }))
    .sort((a, b) => a.period.localeCompare(b.period));

  // Pie chart data for current period expenses
  const expenseBreakdown = [
    {
      name: "Salaries",
      value: parseFloat(currentData.salaries_expense?.$numberDecimal || 0),
      color: "#2196f3",
    },
    {
      name: "Agency Expenses",
      value: parseFloat(currentData.agency_expenses?.$numberDecimal || 0),
      color: "#ff9800",
    },
  ].filter((item) => item.value > 0);

  const COLORS = ["#2196f3", "#ff9800", "#4caf50", "#f44336", "#9c27b0"];

  const availablePeriods = [
    ...new Set([
      ...financeData.map((item) => item.period),
      `${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(
        2,
        "0"
      )}`,
    ]),
  ].sort();

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Agency Finance Dashboard
          </Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                Start Month
              </InputLabel>
              <Select
                value={selectedStartPeriod}
                onChange={(e) => {
                  const val = e.target.value;
                  setSelectedStartPeriod(val);
                  if (periodToNumber(val) > periodToNumber(selectedEndPeriod)) {
                    setSelectedEndPeriod(val);
                  }
                }}
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                }}
              >
                {availablePeriods.map((period) => (
                  <MenuItem key={period} value={period}>
                    {period}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                End Month
              </InputLabel>
              <Select
                value={selectedEndPeriod}
                onChange={(e) => {
                  const val = e.target.value;
                  setSelectedEndPeriod(val);
                  if (
                    periodToNumber(val) < periodToNumber(selectedStartPeriod)
                  ) {
                    setSelectedStartPeriod(val);
                  }
                }}
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                }}
              >
                {availablePeriods.map((period) => (
                  <MenuItem key={period} value={period}>
                    {period}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportSummaryCSV}
              disabled={!selectedStartPeriod || !selectedEndPeriod}
              sx={{
                borderColor: "#4caf50",
                color: "#4caf50",
                fontFamily: "Formula Bold",
                "&:hover": {
                  borderColor: "#388e3c",
                  backgroundColor: "rgba(76, 175, 80, 0.1)",
                },
                "&:disabled": {
                  borderColor: "rgba(76, 175, 80, 0.3)",
                  color: "rgba(76, 175, 80, 0.3)",
                },
              }}
            >
              Export CSV
            </Button>
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportCSV}
              disabled={!selectedStartPeriod || !selectedEndPeriod}
              sx={{
                borderColor: "#4caf50",
                color: "#4caf50",
                fontFamily: "Formula Bold",
                "&:hover": {
                  borderColor: "#388e3c",
                  backgroundColor: "rgba(76, 175, 80, 0.1)",
                },
                "&:disabled": {
                  borderColor: "rgba(76, 175, 80, 0.3)",
                  color: "rgba(76, 175, 80, 0.3)",
                },
              }}
            >
              Export Detailed CSV
            </Button>

            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => setOpenRevenueModal(true)}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                fontFamily: "Formula Bold",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
                opacity: selectedStartPeriod === selectedEndPeriod ? 1 : 0.5,
                pointerEvents:
                  selectedStartPeriod === selectedEndPeriod ? "auto" : "none",
              }}
            >
              Update Revenue
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => handleRecalculateFinance(selectedStartPeriod)}
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
                "&:hover": {
                  borderColor: "#c62828",
                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                },
                opacity: selectedStartPeriod === selectedEndPeriod ? 1 : 0.5,
                pointerEvents:
                  selectedStartPeriod === selectedEndPeriod ? "auto" : "none",
              }}
            >
              Recalculate
            </Button>
          </Box>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* KPI Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#4caf50", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Total Inflow
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.total_inflow)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                        >
                          Period:{" "}
                          {selectedStartPeriod === selectedEndPeriod
                            ? selectedStartPeriod
                            : `${selectedStartPeriod} to ${selectedEndPeriod}`}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#db4a41", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#db4a41",
                            }}
                          >
                            Total Cycles Fees
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.total_cycles_fees)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                        >
                          Period:{" "}
                          {selectedStartPeriod === selectedEndPeriod
                            ? selectedStartPeriod
                            : `${selectedStartPeriod} to ${selectedEndPeriod}`}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#3BC2CEFF", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#3BC2CEFF",
                            }}
                          >
                            Client Paid Amount
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.client_paid)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                        >
                          Period:{" "}
                          {selectedStartPeriod === selectedEndPeriod
                            ? selectedStartPeriod
                            : `${selectedStartPeriod} to ${selectedEndPeriod}`}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#330071FF", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#330071FF",
                            }}
                          >
                            Client Due Amount
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.client_due_amount)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                        >
                          Period:{" "}
                          {selectedStartPeriod === selectedEndPeriod
                            ? selectedStartPeriod
                            : `${selectedStartPeriod} to ${selectedEndPeriod}`}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <ReceiptIcon sx={{ color: "#f44336", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#f44336",
                            }}
                          >
                            Total Expenses
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.total_outflow)}
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                          <Chip
                            label={`Salaries: ${formatCurrency(
                              currentData.salaries_expense
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#2196f3",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                          <Chip
                            label={`Agency: ${formatCurrency(
                              currentData.agency_expenses
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#ff9800",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                          <Chip
                            label={`Client Outflow: ${formatCurrency(
                              currentData.client_outflows
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#9808E6FF",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                          <Chip
                            label={`Client Outflow: ${formatCurrency(
                              currentData.project_outflows
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#F3218AFF",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <WorkOutlineIcon sx={{ color: "#4caf50", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            One-Time Project Overview
                          </Typography>
                        </Box>

                        {/* Total (Paid + Due) headline */}
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(
                            parseFloat(currentData.project_paid) +
                              parseFloat(currentData.project_due_amount)
                          )}
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mt: 1,
                          }}
                        >
                          <Chip
                            label={`Total Fees: ${formatCurrency(
                              currentData.total_project_fees
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#F3218AFF",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                        {/* Detail chips */}
                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mt: 1,
                          }}
                        >
                          <Chip
                            label={`Paid: ${formatCurrency(
                              currentData.project_paid
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#2196f3",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                          <Chip
                            label={`Due: ${formatCurrency(
                              currentData.project_due_amount
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#f44336",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>

                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mt: 1,
                          }}
                        >
                          <Chip
                            label={`Outflows: ${formatCurrency(
                              currentData.project_outflows
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#ff9800",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                          <Chip
                            label={`Inflows: ${formatCurrency(
                              currentData.project_inflows
                            )}`}
                            size="small"
                            sx={{
                              backgroundColor: "#9c27b0",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <AccountBalanceIcon
                            sx={{ color: "#2196f3", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            Cash Flow of Agency
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.total_inflow)}
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 1,
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{ color: "#4caf50" }}
                          >
                            In: {formatCurrency(currentData.total_inflow)}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "#f44336" }}
                          >
                            Out: {formatCurrency(currentData.total_outflow)}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          {parseFloat(
                            currentData.net_profit?.$numberDecimal || 0
                          ) >= 0 ? (
                            <TrendingUpIcon sx={{ color: "#4caf50", mr: 1 }} />
                          ) : (
                            <TrendingDownIcon
                              sx={{ color: "#f44336", mr: 1 }}
                            />
                          )}
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color:
                                parseFloat(
                                  currentData.net_profit?.$numberDecimal || 0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            }}
                          >
                            Net Profit
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color:
                              parseFloat(
                                currentData.net_profit?.$numberDecimal || 0
                              ) >= 0
                                ? "#4caf50"
                                : "#f44336",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(currentData.net_profit)}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(
                            (Math.abs(
                              parseFloat(
                                currentData.net_profit?.$numberDecimal || 0
                              )
                            ) /
                              Math.max(
                                parseFloat(
                                  currentData.total_inflow?.$numberDecimal || 1
                                ),
                                1
                              )) *
                              100,
                            100
                          )}
                          sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            "& .MuiLinearProgress-bar": {
                              backgroundColor:
                                parseFloat(
                                  currentData.net_profit?.$numberDecimal || 0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            },
                          }}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Charts Section */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {/* Financial Trend Chart */}
                <Grid item xs={12} lg={8}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <TrendingUpIcon sx={{ color: "#db4a41", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                        >
                          Financial Trend Analysis
                        </Typography>
                      </Box>
                      <Box sx={{ height: 400 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={chartData}>
                            <CartesianGrid
                              strokeDasharray="3 3"
                              stroke="rgba(255, 255, 255, 0.1)"
                            />
                            <XAxis
                              dataKey="period"
                              stroke="rgba(255, 255, 255, 0.7)"
                              fontSize={12}
                            />
                            <YAxis
                              stroke="rgba(255, 255, 255, 0.7)"
                              fontSize={12}
                              tickFormatter={(value) =>
                                `${(value / 1000).toFixed(0)}K`
                              }
                            />
                            <RechartsTooltip
                              contentStyle={{
                                backgroundColor: "rgba(0, 0, 0, 0.8)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                                color: "white",
                              }}
                              formatter={(value, name) => {
                                const labels = {
                                  inflow: "Total Inflow",
                                  outflow: "Total Outflow",
                                  profit: "Net Profit",
                                  revenue: "Total Revenue",
                                  fees: "Fees",
                                  // add any other dataKey → label pairs here
                                };

                                return [
                                  `${parseFloat(value).toLocaleString()} EGP`,
                                  labels[name] ?? name,
                                ];
                              }}
                            />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="revenue"
                              stroke="#4caf50"
                              strokeWidth={3}
                              name="Client Paid"
                              dot={{ fill: "#4caf50", strokeWidth: 2, r: 4 }}
                            />
                            <Line
                              type="monotone"
                              dataKey="inflow"
                              stroke="#2196f3"
                              strokeWidth={3}
                              name="Total Inflow"
                              dot={{ fill: "#2196f3", strokeWidth: 2, r: 4 }}
                            />
                            <Line
                              type="monotone"
                              dataKey="outflow"
                              stroke="#f44336"
                              strokeWidth={3}
                              name="Total Outflow"
                              dot={{ fill: "#f44336", strokeWidth: 2, r: 4 }}
                            />
                            <Line
                              type="monotone"
                              dataKey="profit"
                              stroke="#ff9800"
                              strokeWidth={3}
                              name="Net Profit"
                              dot={{ fill: "#ff9800", strokeWidth: 2, r: 4 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Expense Breakdown Pie Chart */}
                <Grid item xs={12} lg={4}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                        >
                          Expense Breakdown
                        </Typography>
                      </Box>
                      {expenseBreakdown.length > 0 ? (
                        <Box sx={{ height: 400 }}>
                          <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                              <Pie
                                data={expenseBreakdown}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                dataKey="value"
                                labelLine={false}
                                label={({
                                  cx,
                                  cy,
                                  midAngle,
                                  innerRadius,
                                  outerRadius,
                                  percent,
                                }) => {
                                  const RADIAN = Math.PI / 180;
                                  const radius =
                                    innerRadius +
                                    (outerRadius - innerRadius) / 2;
                                  const x =
                                    cx + radius * Math.cos(-midAngle * RADIAN);
                                  const y =
                                    cy + radius * Math.sin(-midAngle * RADIAN);

                                  return (
                                    <text
                                      x={x}
                                      y={y}
                                      fill="white"
                                      textAnchor="middle"
                                      dominantBaseline="central"
                                      fontSize={12}
                                      fontWeight="bold"
                                    >
                                      {(percent * 100).toFixed(0)}%
                                    </text>
                                  );
                                }}
                              >
                                {expenseBreakdown.map((entry, index) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={COLORS[index % COLORS.length]}
                                    name={entry.name}
                                  />
                                ))}
                              </Pie>

                              {/* ✅ Legend for names (above or below) */}
                              <Legend
                                layout="horizontal"
                                verticalAlign="top" // change to "bottom" if you want it below
                                align="center"
                                formatter={(value) => (
                                  <span style={{ color: "#fff" }}>{value}</span>
                                )}
                              />

                              <RechartsTooltip
                                contentStyle={{
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "8px",
                                  color: "white",
                                }}
                                formatter={(value) => [
                                  `${parseFloat(value).toLocaleString()} EGP`,
                                  "Amount",
                                ]}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No expenses recorded for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Detailed Records */}
              <Grid container spacing={3}>
                {/* Salary Payments */}
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <PeopleIcon sx={{ color: "#2196f3", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#2196f3" }}
                        >
                          Salary Payments (
                          {currentData.salary_payments?.length || 0})
                        </Typography>
                      </Box>
                      {currentData.salary_payments &&
                      currentData.salary_payments.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {currentData.salary_payments.map((payment, index) => (
                            <Box
                              key={payment._id || index}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(33, 150, 243, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(33, 150, 243, 0.2)",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(payment.net_amount)}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Employee Name: {payment.userId.name}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Employee Role: {payment.userId.role}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Month: {payment.month}/{payment.year}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Paid: {formatCurrency(payment.net_amount)}
                              </Typography>
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  mt: 1,
                                }}
                              >
                                <Chip
                                  label={payment.paid ? "PAID" : "PENDING"}
                                  size="small"
                                  sx={{
                                    backgroundColor: payment.paid
                                      ? "#4caf50"
                                      : "#ff9800",
                                    color: "white",
                                  }}
                                />
                                {payment.paid_at && (
                                  <Typography
                                    variant="caption"
                                    sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                                  >
                                    Paid:{" "}
                                    {new Date(
                                      payment.paid_at
                                    ).toLocaleDateString()}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No salary payments for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Agency Expenses */}
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <BusinessIcon sx={{ color: "#ff9800", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#ff9800" }}
                        >
                          Agency Expenses ({currentData.expenses?.length || 0})
                        </Typography>
                      </Box>
                      {currentData.expenses &&
                      currentData.expenses.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {currentData.expenses.map((expense, index) => (
                            <Box
                              key={expense._id || index}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(255, 152, 0, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(255, 152, 0, 0.2)",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {expense.title}
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#ff9800",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(expense.amount)}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Category: {expense.category}
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                              >
                                Date:{" "}
                                {new Date(expense.date).toLocaleDateString()}
                              </Typography>
                              {expense.description && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.6)",
                                    fontStyle: "italic",
                                  }}
                                >
                                  {expense.description}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No agency expenses for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Subscription Cycles Section */}
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 2 }}
                      >
                        <AccountCircleIcon sx={{ color: "#4caf50", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#4caf50" }}
                        >
                          Subscription Cycles ({cycles.length})
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: "rgba(255, 255, 255, 0.7)",
                            ml: 2,
                            fontStyle: "italic",
                          }}
                        >
                          {selectedStartPeriod} to {selectedEndPeriod}
                        </Typography>
                      </Box>

                      {cyclesLoading ? (
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            py: 4,
                          }}
                        >
                          <CircularProgress sx={{ color: "#4caf50" }} />
                        </Box>
                      ) : cycles.length > 0 ? (
                        <Box sx={{ maxHeight: 400, overflowY: "auto" }}>
                          {cycles.map((cycle, index) => (
                            <motion.div
                              key={cycle._id}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                            >
                              <Card
                                sx={{
                                  background: "rgba(255, 255, 255, 0.03)",
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "8px",
                                  mb: 2,
                                  cursor: "pointer",
                                  transition: "all 0.3s ease",
                                  "&:hover": {
                                    background: "rgba(255, 255, 255, 0.08)",
                                    transform: "translateY(-2px)",
                                  },
                                }}
                                onClick={() => toggleCycleExpansion(cycle._id)}
                              >
                                <CardContent sx={{ p: 2 }}>
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                      alignItems: "center",
                                    }}
                                  >
                                    <Box sx={{ flex: 1 }}>
                                      <Typography
                                        variant="subtitle1"
                                        sx={{
                                          fontFamily: "Formula Bold",
                                          color: "white",
                                          mb: 1,
                                        }}
                                      >
                                        {cycle.cycle_name}
                                      </Typography>
                                      <Box
                                        sx={{
                                          display: "flex",
                                          gap: 2,
                                          flexWrap: "wrap",
                                        }}
                                      >
                                        <Chip
                                          label={`Client: ${cycle.client_id?.client_name}`}
                                          size="small"
                                          sx={{
                                            backgroundColor:
                                              "rgba(76, 175, 80, 0.2)",
                                            color: "#4caf50",
                                            fontSize: "0.7rem",
                                          }}
                                        />
                                        <Chip
                                          label={`Status: ${cycle.status}`}
                                          size="small"
                                          sx={{
                                            backgroundColor:
                                              cycle.status === "paid"
                                                ? "rgba(76, 175, 80, 0.2)"
                                                : "rgba(255, 152, 0, 0.2)",
                                            color:
                                              cycle.status === "paid"
                                                ? "#4caf50"
                                                : "#ff9800",
                                            fontSize: "0.7rem",
                                          }}
                                        />
                                        <Chip
                                          label={`Profit: ${formatCurrency(
                                            cycle.actual_profit
                                          )}`}
                                          size="small"
                                          sx={{
                                            backgroundColor:
                                              parseFloat(
                                                cycle.actual_profit
                                                  ?.$numberDecimal || 0
                                              ) > 0
                                                ? "rgba(76, 175, 80, 0.2)"
                                                : "rgba(244, 67, 54, 0.2)",
                                            color:
                                              parseFloat(
                                                cycle.actual_profit
                                                  ?.$numberDecimal || 0
                                              ) > 0
                                                ? "#4caf50"
                                                : "#f44336",
                                            fontSize: "0.7rem",
                                          }}
                                        />
                                      </Box>
                                    </Box>
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      {expandedCycles[cycle._id] ? (
                                        <ExpandLessIcon
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                          }}
                                        />
                                      ) : (
                                        <ExpandMoreIcon
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                          }}
                                        />
                                      )}
                                    </Box>
                                  </Box>

                                  {/* Expanded Details */}
                                  {expandedCycles[cycle._id] && (
                                    <motion.div
                                      initial={{ opacity: 0, height: 0 }}
                                      animate={{ opacity: 1, height: "auto" }}
                                      exit={{ opacity: 0, height: 0 }}
                                      transition={{ duration: 0.3 }}
                                    >
                                      <Box
                                        sx={{
                                          mt: 3,
                                          pt: 2,
                                          borderTop:
                                            "1px solid rgba(255, 255, 255, 0.1)",
                                        }}
                                      >
                                        <Grid container spacing={2}>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Due Amount
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "white",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(cycle.due_amount)}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Paid Amount
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "white",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(
                                                cycle.paid_amount
                                              )}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Inflow
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "#4caf50",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(cycle.inflow)}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Outflow
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "#f44336",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(cycle.outflow)}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Goal Profit
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "white",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(
                                                cycle.goal_profit
                                              )}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Actual Profit
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color:
                                                  parseFloat(
                                                    cycle.actual_profit
                                                      ?.$numberDecimal || 0
                                                  ) > 0
                                                    ? "#4caf50"
                                                    : "#f44336",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(
                                                cycle.actual_profit
                                              )}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Profit Status
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "white",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {cycle.profit_status}
                                            </Typography>
                                          </Grid>
                                          <Grid item xs={12} sm={6} md={3}>
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                              }}
                                            >
                                              Penalties
                                            </Typography>
                                            <Typography
                                              variant="body2"
                                              sx={{
                                                color: "#f44336",
                                                fontWeight: "bold",
                                              }}
                                            >
                                              {formatCurrency(cycle.penalties)}
                                            </Typography>
                                          </Grid>
                                        </Grid>

                                        {/* Additional Details */}
                                        {(cycle.quotations?.length > 0 ||
                                          cycle.expenses?.length > 0 ||
                                          cycle.adjustments?.length > 0) && (
                                          <Box
                                            sx={{
                                              mt: 2,
                                              pt: 2,
                                              borderTop:
                                                "1px solid rgba(255, 255, 255, 0.1)",
                                            }}
                                          >
                                            <Typography
                                              variant="caption"
                                              sx={{
                                                color:
                                                  "rgba(255, 255, 255, 0.7)",
                                                mb: 1,
                                                display: "block",
                                              }}
                                            >
                                              Associated Records
                                            </Typography>
                                            <Box
                                              sx={{
                                                display: "flex",
                                                gap: 1,
                                                flexWrap: "wrap",
                                              }}
                                            >
                                              {cycle.quotations?.length > 0 && (
                                                <Chip
                                                  label={`${cycle.quotations.length} Quotations`}
                                                  size="small"
                                                  sx={{
                                                    backgroundColor:
                                                      "rgba(33, 150, 243, 0.2)",
                                                    color: "#2196f3",
                                                    fontSize: "0.6rem",
                                                  }}
                                                />
                                              )}
                                              {cycle.expenses?.length > 0 && (
                                                <Chip
                                                  label={`${cycle.expenses.length} Expenses`}
                                                  size="small"
                                                  sx={{
                                                    backgroundColor:
                                                      "rgba(255, 152, 0, 0.2)",
                                                    color: "#ff9800",
                                                    fontSize: "0.6rem",
                                                  }}
                                                />
                                              )}
                                              {cycle.adjustments?.length >
                                                0 && (
                                                <Chip
                                                  label={`${cycle.adjustments.length} Adjustments`}
                                                  size="small"
                                                  sx={{
                                                    backgroundColor:
                                                      "rgba(156, 39, 176, 0.2)",
                                                    color: "#9c27b0",
                                                    fontSize: "0.6rem",
                                                  }}
                                                />
                                              )}
                                            </Box>
                                          </Box>
                                        )}
                                      </Box>
                                    </motion.div>
                                  )}
                                </CardContent>
                              </Card>
                            </motion.div>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No subscription cycles found for this period
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </>
          )}
        </Box>

        {/* Update Revenue Modal */}
        <Dialog
          open={openRevenueModal}
          onClose={() => setOpenRevenueModal(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Update Client Revenue - {selectedStartPeriod}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Client Revenue"
              type="number"
              value={clientRevenue}
              onChange={(e) => setClientRevenue(e.target.value)}
              placeholder="Enter revenue amount"
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              }}
            />
            <Typography
              variant="body2"
              sx={{ color: "rgba(255, 255, 255, 0.7)", mt: 2 }}
            >
              This will update the client revenue for {selectedStartPeriod} and
              recalculate the total inflow and net profit.
            </Typography>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenRevenueModal(false)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateClientRevenue}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              Update Revenue
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default AgencyFinance;
