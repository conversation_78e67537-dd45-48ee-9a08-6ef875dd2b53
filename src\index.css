@font-face {
  font-family: "Formula Bold";
  src: url("../public/fonts/FormulaFont.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* cursor: url("/public/assets/youngitooo-12-3.png") 0 0, auto; */
  /* a,
  button,
  input[type="submit"],
  [role="button"],
  [class*="btn"],
  [onclick] {
    cursor: url("/public/assets/youngitooo-12-3.png") 0 0, pointer;
  } */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Non-blocking overflow for loading states */
body.loading-clients,
body.video-modal-open {
  overflow: hidden;
}
