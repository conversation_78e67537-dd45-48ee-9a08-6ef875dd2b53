import { useRef, useLayoutEffect } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function ServicesSection() {
  const sectionRef = useRef(null);
  const textWrapperRef = useRef(null);
  useLayoutEffect(() => {
    const sectionEl = sectionRef.current;
    const blockHeight = sectionEl.offsetHeight;
    const scrollDistance = blockHeight; // 2 blocks to scroll

    let ctx = gsap.context(() => {
      // Text Column Vertical Scroll
      gsap.to(textWrapperRef.current, {
        y: -scrollDistance,
        ease: "fade-in-out",
        scrollTrigger: {
          trigger: sectionEl,
          start: "center center",
          end: `+=${scrollDistance}`,
          scrub: true,
          pin: true,
          anticipatePin: 1,
        },
      });

      // Entrance animation for video column (from left)
      gsap.from(".video-column", {
        x: "-100%",
        opacity: 0,
        duration: 2.5,
        ease: "power4.out",
        scrollTrigger: {
          trigger: sectionEl,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      });

      // Entrance animation for text column (from right)
      gsap.from(".text-column", {
        x: "100%",
        opacity: 0,
        duration: 2.5,
        ease: "power4.out",
        scrollTrigger: {
          trigger: sectionEl,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      });
      // Fade in <li> items on scroll
      const allLi = gsap.utils.toArray(".section-block ul li");
      gsap.from(allLi, {
        opacity: 0,
        y: 40,
        duration: 1.2,
        ease: "power3.out",
        stagger: 0.2,
        scrollTrigger: {
          trigger: textWrapperRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      });
    }, sectionEl);

    return () => ctx.revert();
  }, []);

  return (
    <section className="services-section" ref={sectionRef}>
      <div className="video-column">
        <video
          src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/Creative%20Aubaine.mp4"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          loading="lazy"
        />
        <video
          src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/GFF%20Editions.mp4"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          loading="lazy"
        />
        <video
          src="https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/videos/CircleK%20Ramadan.mp4"
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          loading="lazy"
        />

        {/* 
        <div className="video-overlay-text top-text">
          <h2>Young</h2>
        </div>

        <div className="video-overlay-text bottom-text">
          <h2>Productions</h2>
        </div> */}
      </div>

      <div className="text-column">
        <div className="text-wrapper" ref={textWrapperRef}>
          <div className="section-block">
            <h2>Strategy</h2>
            <ul>
              <li>Brand Platforms</li>
              <li>Brand Naming</li>
              <li>Identity Design</li>
              <li>Comms Planning</li>
              <li>Go-to-Market Planning</li>
            </ul>
          </div>
          <div className="section-block">
            <h2>Creative</h2>
            <ul>
              <li>Campaign Development</li>
              <li>Creative Direction</li>
              <li>Copywriting</li>
              <li>Art Direction</li>
              <li>Design</li>
            </ul>
          </div>
          <div className="section-block">
            <h2>Production</h2>
            <ul>
              <li>Video Production</li>
              <li>Post-Production</li>
              <li>Photography</li>
              <li>Animation</li>
              <li>Sound Design</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
