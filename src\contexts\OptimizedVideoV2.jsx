import React, { useRef, useState, useEffect, forwardRef } from "react";
import { useInView } from "react-intersection-observer";
import videoLoadManager from "./VideoLoadManager";

/**
 * OptimizedVideo V2 - Zero-byte video loading with controlled attachment
 * Uses Global Video Load Manager for coordinated downloads
 */
const OptimizedVideoV2 = forwardRef(
  (
    {
      dataSrc, // Use data-src instead of src
      tier = 1, // Video tier: 0=critical, 1=interactive, 2=decorative
      poster,
      autoPlay = false,
      loop = false,
      muted = true,
      playsInline = true,
      controls = false,
      className = "",
      style = {},
      onCanPlay,
      onLoadedData,
      onMouseEnter,
      onMouseLeave,
      onEnded,
      onError,
      userIntent = false, // Explicit user interaction detected
      ...props
    },
    ref,
  ) => {
    const videoRef = useRef(null);
    const [isLoaded, setIsLoaded] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [videoId] = useState(
      () => `video_${Math.random().toString(36).substr(2, 9)}`,
    );

    // Intersection observer with strict thresholds
    const [inViewRef, inView] = useInView({
      threshold: 0.1,
      rootMargin: "50px", // Reduced from 200px
      triggerOnce: true,
    });

    // Combine refs
    const combinedRef = (node) => {
      videoRef.current = node;
      inViewRef(node);
      if (ref) {
        if (typeof ref === "function") {
          ref(node);
        } else {
          ref.current = node;
        }
      }
    };

    // Determine loading priority based on tier and conditions
    const getLoadingPriority = () => {
      if (tier === 0) return 0; // Critical - hero videos
      if (userIntent || inView) return 1; // Interactive - user engaged
      return 2; // Decorative - lowest priority
    };

    // Check if video should be loaded based on conditions
    const shouldLoadVideo = () => {
      // Tier 0 (Critical): Load immediately after first paint
      if (tier === 0) {
        return document.readyState === "complete" || performance.now() > 500; // Reduced from 1s to 500ms
      }

      // Tier 1 (Interactive): Load on user intent or visibility
      if (tier === 1) {
        return userIntent || inView;
      }

      // Tier 2 (Decorative): Load when visible (simplified conditions)
      if (tier === 2) {
        return inView; // Simplified - just need to be in view
      }

      return false;
    };

    // Simple network idle detection
    const isNetworkIdle = () => {
      // Check if there are no active network requests
      if (performance && performance.getEntriesByType) {
        const resources = performance.getEntriesByType("resource");
        const recentRequests = resources.filter(
          (r) => performance.now() - r.startTime < 2000,
        );
        return recentRequests.length === 0;
      }
      return true; // Assume idle if can't detect
    };

    // Load video through the global manager
    const loadVideo = async () => {
      if (!dataSrc || isLoaded || isLoading) return;

      setIsLoading(true);

      try {
        const priority = getLoadingPriority();
        await videoLoadManager.requestVideoLoad(
          videoId,
          dataSrc,
          priority,
          videoRef.current,
          {
            timeout: 15000,
            tier,
          },
        );

        setIsLoaded(true);
      } catch (error) {
        console.error(`Failed to load video ${videoId}:`, error);
        setHasError(true);
        onError?.(error);
      } finally {
        setIsLoading(false);
      }
    };

    // Effect to trigger loading based on conditions
    useEffect(() => {
      const loadCondition = shouldLoadVideo();
      console.log(
        `🎬 Video ${videoId} (tier ${tier}): shouldLoad=${loadCondition}, userIntent=${userIntent}, inView=${inView}, dataSrc=${!!dataSrc}`,
      );

      if (loadCondition) {
        // Add delay for non-critical videos to avoid blocking
        const delay = tier === 0 ? 0 : tier === 1 ? 100 : 500;
        console.log(
          `🚀 Scheduling video ${videoId} load with ${delay}ms delay`,
        );

        const timer = setTimeout(() => {
          loadVideo();
        }, delay);

        return () => clearTimeout(timer);
      }
    }, [inView, userIntent, dataSrc, tier]);

    // Handle mouse enter for hover-to-play
    const handleMouseEnter = (e) => {
      if (!isLoaded && tier <= 1) {
        // Trigger loading on hover for interactive videos
        loadVideo();
      }

      if (isLoaded && !autoPlay) {
        e.target.play().catch(console.warn);
      }

      onMouseEnter?.(e);
    };

    // Handle mouse leave
    const handleMouseLeave = (e) => {
      if (isLoaded && !autoPlay) {
        e.target.pause();
      }
      onMouseLeave?.(e);
    };

    // Handle video events
    const handleCanPlay = (e) => {
      if (autoPlay) {
        e.target.play().catch(console.warn);
      }
      onCanPlay?.(e);
    };

    const handleLoadedData = (e) => {
      onLoadedData?.(e);
    };

    const handleEnded = (e) => {
      onEnded?.(e);
    };

    const handleVideoError = (e) => {
      setHasError(true);
      onError?.(e);
    };

    // Render placeholder for unloaded videos
    if (!isLoaded && !isLoading) {
      return (
        <div
          ref={combinedRef}
          className={`video-placeholder ${className}`}
          style={{
            backgroundColor: "#000",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#666",
            fontSize: "14px",
            ...style,
          }}
          data-video-id={videoId}
          data-tier={tier}
        >
          {poster && (
            <img
              src={poster}
              alt="Video thumbnail"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              loading="lazy"
              decoding="async"
            />
          )}
        </div>
      );
    }

    // Render loading state
    if (isLoading) {
      return (
        <div
          ref={combinedRef}
          className={`video-loading ${className}`}
          style={{
            backgroundColor: "#000",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#fff",
            fontSize: "14px",
            ...style,
          }}
        >
          Loading video...
        </div>
      );
    }

    // Render video element (only when loaded)
    return (
      <video
        ref={combinedRef}
        // src is set by VideoLoadManager, not here
        poster={poster}
        autoPlay={autoPlay}
        loop={loop}
        muted={muted}
        playsInline={playsInline}
        controls={controls}
        preload="none" // Always none - we control loading
        className={className}
        style={style}
        onCanPlay={handleCanPlay}
        onLoadedData={handleLoadedData}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onEnded={handleEnded}
        onError={handleVideoError}
        data-video-id={videoId}
        data-tier={tier}
        {...props}
      >
        Your browser does not support the video tag.
      </video>
    );
  },
);

OptimizedVideoV2.displayName = "OptimizedVideoV2";

export default OptimizedVideoV2;
