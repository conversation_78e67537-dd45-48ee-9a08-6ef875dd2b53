# Young Productions - React Website

## Overview

Young Productions is a versatile production company specializing in cinematography, website development, production services, and professional photoshoots. This README provides an overview of the project structure, dependencies, and scripts used in the development of the Young Productions React website.

## Table of Contents

- [Dependencies](#dependencies)
- [Scripts](#scripts)
- [Usage](#usage)
- [Contributing](#contributing)
- [License](#license)

## Dependencies

The project relies on the following dependencies:

- **@emotion/react@11.11.1:** CSS-in-JS library for styling React components.
- **@emotion/styled@11.11.0:** Styled components library for Emotion.
- **@mui/icons-material@5.14.18:** Material-UI icons for React.
- **@mui/material@5.14.18:** React components following the Material Design guidelines.
- **@mui/styled-engine-sc@6.0.0-alpha.6:** Styled-components engine for Material-UI.
- **@testing-library/jest-dom@5.17.0:** Custom Jest matchers for better testing.
- **@testing-library/react@13.4.0:** Simple and complete React testing utilities.
- **@testing-library/user-event@13.5.0:** Fire events the same way the user does in testing.
- **react@18.2.0:** JavaScript library for building user interfaces.
- **react-dom@18.2.0:** DOM-specific methods for React.
- **react-router-dom@6.21.0:** Routing library for React applications.
- **react-scripts@5.0.1:** Set of scripts and configuration used by Create React App.
- **react-slick@0.29.0:** React carousel component built with styled-components.
- **react-use-gesture@9.1.3:** Utility library for handling gestures in React.
- **slick-carousel@1.8.1:** Carousel component for React.
- **styled-components@6.1.1:** CSS-in-JS library for styling React components.
- **web-vitals@2.1.4:** Library for measuring web vitals.

## Scripts

The following scripts are available for project development:

- **start:** Run the development server using `react-scripts start`.
- **build:** Build the production-ready application using `react-scripts build`.
- **test:** Run tests using `react-scripts test`.
- **eject:** Eject from Create React App for advanced configuration.

## Usage

1. Clone the repository.
2. Run `npm install` to install the project dependencies.
3. Use the available scripts (`start`, `build`, `test`, `eject`) to manage the project.

## Contributing

Feel free to contribute by opening issues, submitting pull requests, or suggesting improvements. Follow the project's coding standards and guidelines.

## License

This project is licensed under the [MIT License](LICENSE.md).
