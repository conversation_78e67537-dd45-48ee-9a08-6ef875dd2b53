import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Avatar,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import PaymentsIcon from "@mui/icons-material/Payments";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import BoltIcon from "@mui/icons-material/Bolt";
import PlaylistAddIcon from "@mui/icons-material/PlaylistAdd";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import EditIcon from "@mui/icons-material/Edit";
import { useUser } from "../../contexts/UserContext";
import { exportPayrollToCSV } from "../../utils/csvExport";

function Payroll() {
  const { user } = useUser();
  const [payments, setPayments] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [viewing, setViewing] = useState(null);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [editing, setEditing] = useState(null);
  const [editForm, setEditForm] = useState({
    additional_bonuses: "",
    additional_deductions: "",
    processed_by: "",
    notes: "",
    auto_pay: false,
  });

  // Generate User Modal State
  const [openGenerateModal, setOpenGenerateModal] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [filters, setFilters] = useState({ userId: "", month: "", year: "" });
  const [backfillYear, setBackfillYear] = useState(
    String(new Date().getFullYear())
  );

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/salaries-payments";
  const EMPLOYEES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/employees";

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  // Check if user is general manager
  const isGeneralManager = () => {
    return user?.role === "general_manager";
  };

  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return "0 EGP";
    const value = amount?.$numberDecimal ?? amount;
    return `${parseFloat(value || 0).toLocaleString()} EGP`;
  };
  // const parseDecimal = (v) => parseFloat(v?.$numberDecimal ?? v ?? 0) || 0;

  // CSV Export functionality
  const handleExportCSV = () => {
    exportPayrollToCSV(payments, filters, showSnackbar);
  };

  const stringToColor = (string) => {
    if (!string) return "#666";
    let hash = 0;
    for (let i = 0; i < string.length; i += 1)
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    let color = "#";
    for (let i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    return color;
  };

  const fetchPayments = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const params = new URLSearchParams();
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.month) params.append("month", filters.month);
      if (filters.year) params.append("year", filters.year);
      const res = await fetch(`${API_BASE_URL}?${params.toString()}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      setPayments(json.data || []);
    } catch (e) {
      console.error("Error fetching payments:", e);
      showSnackbar("Failed to fetch payroll", "error");
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const fetchUsers = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(EMPLOYEES_API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      const list = json?.data || json || [];
      setUsers(Array.isArray(list) ? list : []);
    } catch (e) {
      console.error("Error fetching employees:", e);
      setUsers([]);
    }
  }, []);

  useEffect(() => {
    fetchPayments();
    fetchUsers();
  }, [fetchPayments, fetchUsers]);

  const filtered = useMemo(() => payments, [payments]);

  const handleView = async (p) => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${p._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const json = await res.json();
      setViewing(json.data || p);
      setOpenViewModal(true);
    } catch (e) {
      setViewing(p);
      setOpenViewModal(true);
    }
  };

  const handleEdit = (p) => {
    setEditing(p);
    setEditForm({
      additional_bonuses: p.additional_bonuses || "",
      additional_deductions: p.additional_deductions || "",
      processed_by:
        p.processed_by?._id || p.processed_by || user?._id || user?.id,
      notes: p.notes || "",
      auto_pay: p.auto_pay || false,
    });
    setOpenEditModal(true);
  };

  const handleEditSubmit = async () => {
    if (!editing) return;

    try {
      const token = localStorage.getItem("token");
      const payload = {
        additional_bonuses: parseFloat(editForm.additional_bonuses) || 0,
        additional_deductions: parseFloat(editForm.additional_deductions) || 0,
        processed_by: isGeneralManager()
          ? editForm.processed_by || user?._id || user?.id
          : user?._id || user?.id,
        notes: editForm.notes,
        auto_pay: editForm.auto_pay,
      };

      const res = await fetch(`${API_BASE_URL}/${editing._id}/edit`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body.message || "Failed to update payroll");
      }

      await fetchPayments();
      setOpenEditModal(false);
      setEditing(null);
      setEditForm({
        additional_bonuses: "",
        additional_deductions: "",
        processed_by: "",
        notes: "",
      });
      showSnackbar("Payroll updated successfully", "success");
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to update payroll", "error");
    }
  };

  const handleEditClose = () => {
    setOpenEditModal(false);
    setEditing(null);
    setEditForm({
      additional_bonuses: "",
      additional_deductions: "",
      processed_by: "",
      notes: "",
      auto_pay: false,
    });
  };

  const markAsPaid = async (id) => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/${id}/pay`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ processed_by: user?._id || user?.id }),
      });
      if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body.message || "Failed to mark as paid");
      }
      await fetchPayments();
      showSnackbar("Marked as paid", "success");
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to mark as paid", "error");
    }
  };

  const generateAll = async () => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/generate-all`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body.message || "Failed to generate");
      }
      await fetchPayments();
      showSnackbar("Generated payroll for all", "success");
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to generate", "error");
    }
  };

  const [genForm, setGenForm] = useState({ userId: "", month: "", year: "" });
  const handleOpenGenerateModal = () => {
    setOpenGenerateModal(true);
  };

  const handleCloseGenerateModal = () => {
    setOpenGenerateModal(false);
    setGenForm({ userId: "", month: "", year: "" }); // Reset form when closing
  };

  const generateForUser = async () => {
    // Validation
    if (!genForm.userId) {
      showSnackbar("Please select a user", "warning");
      return;
    }
    if (!genForm.month || genForm.month < 1 || genForm.month > 12) {
      showSnackbar("Please enter a valid month (1-12)", "warning");
      return;
    }
    if (!genForm.year || genForm.year < 2020) {
      showSnackbar("Please enter a valid year", "warning");
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const payload = {
        userId: genForm.userId,
        month: parseInt(genForm.month),
        year: parseInt(genForm.year),
      };
      const res = await fetch(`${API_BASE_URL}/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body.message || "Failed to generate for user");
      }
      await fetchPayments();
      setGenForm({ userId: "", month: "", year: "" }); // Reset form
      setOpenGenerateModal(false); // Close modal
      showSnackbar("Generated payroll for user", "success");
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to generate for user", "error");
    }
  };

  const backfillSalaries = async () => {
    try {
      if (!/^\d{4}$/.test(backfillYear)) {
        showSnackbar("Please enter a valid year (e.g., 2024)", "warning");
        return;
      }
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}/backfill`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ year: parseInt(backfillYear) }),
      });
      const body = await res.json().catch(() => ({}));
      if (!res.ok || body.success === false) {
        throw new Error(body.message || "Failed to backfill salaries");
      }
      await fetchPayments();
      // Proactively recalculate finance for each month of the selected year
      const agencyFinanceBase =
        "https://youngproductions-768ada043db3.herokuapp.com/api/financial/agency-finance";
      const currentMonth =
        new Date().getFullYear() === parseInt(backfillYear)
          ? new Date().getMonth() + 1
          : 12;
      const recalcPromises = [];
      for (let m = 1; m <= currentMonth; m++) {
        const period = `${backfillYear}-${String(m).padStart(2, "0")}`;
        recalcPromises.push(
          fetch(`${agencyFinanceBase}/${period}/recalculate`, {
            method: "PUT",
            headers: { Authorization: `Bearer ${token}` },
          }).catch(() => null)
        );
      }
      await Promise.allSettled(recalcPromises);
      showSnackbar(
        "Backfilled salaries and refreshed finance totals",
        "success"
      );
    } catch (e) {
      console.error(e);
      showSnackbar(e.message || "Failed to backfill salaries", "error");
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Payroll
          </Typography>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportCSV}
              disabled={!filters.month || !filters.year}
              sx={{
                color: "#4caf50",
                borderColor: "#4caf50",
                "&:hover": {
                  backgroundColor: "rgba(76,175,80,0.1)",
                  borderColor: "#4caf50",
                },
                "&:disabled": {
                  borderColor: "rgba(76,175,80,0.3)",
                  color: "rgba(76,175,80,0.3)",
                },
              }}
            >
              Export CSV
            </Button>
            <Button
              variant="outlined"
              startIcon={<PlaylistAddIcon />}
              onClick={generateAll}
              sx={{
                color: "#db4a41",
                borderColor: "#db4a41",
                "&:hover": {
                  backgroundColor: "rgba(219,74,65,0.1)",
                  borderColor: "#db4a41",
                },
              }}
            >
              Generate All
            </Button>
            <Button
              variant="contained"
              startIcon={<BoltIcon />}
              onClick={handleOpenGenerateModal}
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              Generate User
            </Button>
            <TextField
              size="small"
              label="Backfill Year"
              value={backfillYear}
              onChange={(e) => setBackfillYear(e.target.value)}
              sx={{
                width: 130,
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                },
                "& .MuiInputLabel-root": {
                  color: "rgba(255, 255, 255, 0.7)",
                },
              }}
            />
            <Button
              variant="outlined"
              startIcon={<PaymentsIcon />}
              onClick={backfillSalaries}
              sx={{
                color: "#db4a41",
                borderColor: "#db4a41",
                "&:hover": {
                  backgroundColor: "rgba(219,74,65,0.1)",
                  borderColor: "#db4a41",
                },
              }}
            >
              Backfill Salaries
            </Button>
          </Box>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Filters & Generate for user */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      User
                    </InputLabel>
                    <Select
                      value={filters.userId}
                      onChange={(e) =>
                        setFilters({ ...filters, userId: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {users.map((u) => (
                        <MenuItem key={u._id} value={u._id}>
                          {u.name || u.email || u.username}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={6} sm={3} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Month"
                    placeholder="1-12"
                    value={filters.month}
                    onChange={(e) =>
                      setFilters({ ...filters, month: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={6} sm={3} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Year"
                    placeholder="2025"
                    value={filters.year}
                    onChange={(e) =>
                      setFilters({ ...filters, year: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Table */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{ background: "transparent" }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            User
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Month
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Gross
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Deductions
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Net
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filtered
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((p) => (
                            <TableRow
                              key={p._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: stringToColor(
                                        p.userId?.name || p.userId?.email || "U"
                                      ),
                                      width: 32,
                                      height: 32,
                                    }}
                                  >
                                    <PaymentsIcon fontSize="small" />
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontFamily: "Formula Bold" }}
                                    >
                                      {p.userId?.name ||
                                        p.userId?.email ||
                                        "User"}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                    >
                                      #{p.salary_id?._id?.slice(-6) || "—"}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {String(p.month).padStart(2, "0")}/{p.year}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(p.gross_amount)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(p.deductions)}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(p.net_amount)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {p.paid ? (
                                  <Chip
                                    label="Paid"
                                    size="small"
                                    sx={{
                                      backgroundColor: "#4caf50",
                                      color: "white",
                                    }}
                                  />
                                ) : (
                                  <Chip
                                    label="Unpaid"
                                    size="small"
                                    sx={{
                                      backgroundColor: "#ff9800",
                                      color: "white",
                                    }}
                                  />
                                )}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(p)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  {!p.paid && (
                                    <Tooltip title="Edit">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleEdit(p)}
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          "&:hover": { color: "#ff9800" },
                                        }}
                                      >
                                        <EditIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                  {!p.paid && (
                                    <Tooltip title="Mark as Paid">
                                      <IconButton
                                        size="small"
                                        onClick={() => markAsPaid(p._id)}
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          "&:hover": { color: "#4caf50" },
                                        }}
                                      >
                                        <DoneAllIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filtered.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={() => {
            setOpenViewModal(false);
            setViewing(null);
          }}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Payment Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewing && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          viewing.userId?.name || viewing.userId?.email || "U"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <PaymentsIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h5"
                        sx={{ color: "white", fontFamily: "Formula Bold" }}
                      >
                        {viewing.userId?.name ||
                          viewing.userId?.email ||
                          "User"}
                      </Typography>
                      <Chip
                        label={viewing.paid ? "Paid" : "Unpaid"}
                        size="small"
                        sx={{
                          ml: 1,
                          backgroundColor: viewing.paid ? "#4caf50" : "#ff9800",
                          color: "white",
                          textTransform: "capitalize",
                        }}
                      />
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography sx={{ color: "white" }}>
                    <strong>Month:</strong>{" "}
                    {String(viewing.month).padStart(2, "0")}/{viewing.year}
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    <strong>Gross:</strong>{" "}
                    {formatCurrency(viewing.gross_amount)}
                  </Typography>
                  <Typography sx={{ color: "white" }}>
                    <strong>Deductions:</strong>{" "}
                    {formatCurrency(viewing.deductions)}
                  </Typography>
                  <Typography
                    sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                  >
                    <strong>Net:</strong> {formatCurrency(viewing.net_amount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography sx={{ color: "white" }}>
                    <strong>Paid:</strong> {viewing.paid ? "Yes" : "No"}
                  </Typography>
                  {viewing.paid_at && (
                    <Typography sx={{ color: "white" }}>
                      <strong>Paid At:</strong>{" "}
                      {new Date(viewing.paid_at).toLocaleString()}
                    </Typography>
                  )}
                  {viewing.processed_by && (
                    <Typography sx={{ color: "white" }}>
                      <strong>Processed By:</strong>{" "}
                      {viewing.processed_by?.name || viewing.processed_by}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => {
                setOpenViewModal(false);
                setViewing(null);
              }}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Edit Modal */}
        <Dialog
          open={openEditModal}
          onClose={handleEditClose}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Edit Payroll
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {editing && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 3,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          editing.userId?.name || editing.userId?.email || "U"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <PaymentsIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h5"
                        sx={{ color: "white", fontFamily: "Formula Bold" }}
                      >
                        {editing.userId?.name ||
                          editing.userId?.email ||
                          "User"}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                      >
                        {String(editing.month).padStart(2, "0")}/{editing.year}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Additional Bonuses"
                    type="number"
                    value={editForm.additional_bonuses}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        additional_bonuses: e.target.value,
                      })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Additional Deductions"
                    type="number"
                    value={editForm.additional_deductions}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        additional_deductions: e.target.value,
                      })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>

                {/* Processed By - Only for General Managers */}
                {isGeneralManager() && (
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Processed By
                      </InputLabel>
                      <Select
                        value={editForm.processed_by}
                        onChange={(e) =>
                          setEditForm({
                            ...editForm,
                            processed_by: e.target.value,
                          })
                        }
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.5)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                          "& .MuiSelect-icon": { color: "white" },
                        }}
                      >
                        <MenuItem value="">Select User</MenuItem>
                        {users.map((u) => (
                          <MenuItem key={u._id} value={u._id}>
                            {u.name || u.email || u.username}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                {/* Auto Pay Checkbox */}
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={editForm.auto_pay}
                        onChange={(e) =>
                          setEditForm({
                            ...editForm,
                            auto_pay: e.target.checked,
                          })
                        }
                        sx={{
                          color: "rgba(255, 255, 255, 0.7)",
                          "&.Mui-checked": {
                            color: "#db4a41",
                          },
                        }}
                      />
                    }
                    label="Auto Pay"
                    sx={{
                      color: "rgba(255, 255, 255, 0.7)",
                      "& .MuiFormControlLabel-label": {
                        fontFamily: "Formula Bold",
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={3}
                    value={editForm.notes}
                    onChange={(e) =>
                      setEditForm({
                        ...editForm,
                        notes: e.target.value,
                      })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleEditClose}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>

        {/* Generate User Modal */}
        <Dialog
          open={openGenerateModal}
          onClose={handleCloseGenerateModal}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            <BoltIcon sx={{ color: "#db4a41" }} />
            Generate Payroll for User
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Select User
                  </InputLabel>
                  <Select
                    value={genForm.userId}
                    onChange={(e) =>
                      setGenForm({ ...genForm, userId: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                      "& .MuiSelect-icon": { color: "white" },
                    }}
                  >
                    <MenuItem value="">Select User</MenuItem>
                    {users.map((u) => (
                      <MenuItem key={u._id} value={u._id}>
                        {u.name || u.email || u.username}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Month"
                  placeholder="1-12"
                  type="number"
                  inputProps={{ min: 1, max: 12 }}
                  value={genForm.month}
                  onChange={(e) =>
                    setGenForm({ ...genForm, month: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>

              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Year"
                  placeholder="2025"
                  type="number"
                  inputProps={{ min: 2020 }}
                  value={genForm.year}
                  onChange={(e) =>
                    setGenForm({ ...genForm, year: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseGenerateModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={generateForUser}
              variant="contained"
              startIcon={<BoltIcon />}
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              Generate Payroll
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Payroll;
