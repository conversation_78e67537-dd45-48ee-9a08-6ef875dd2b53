{"name": "young", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.14.18", "@mui/styled-engine-sc": "^6.0.0-alpha.6", "@mui/x-date-pickers": "^6.18.7", "@react-google-maps/api": "^2.19.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.8.1", "@studio-freight/lenis": "^1.0.42", "@tanstack/react-query": "^5.90.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@u-wave/react-vimeo": "^0.9.10", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.0.10", "axios": "^1.9.0", "date-fns": "^2.30.0", "formik": "^2.4.5", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "mathjs": "^15.1.0", "nodemon": "^3.1.10", "ogl": "^1.0.11", "react": "^18.2.0", "react-big-calendar": "^1.19.4", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-lazy-load-image-component": "^1.6.3", "react-router-dom": "^6.21.0", "react-scripts": "5.0.1", "react-scroll-parallax": "^3.4.5", "react-simple-typewriter": "^5.0.1", "react-slick": "^0.29.0", "react-use-gesture": "^9.1.3", "recharts": "^2.10.3", "slick-carousel": "^1.8.1", "styled-components": "^6.1.1", "three": "^0.179.1", "vimeo": "^2.3.1", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}