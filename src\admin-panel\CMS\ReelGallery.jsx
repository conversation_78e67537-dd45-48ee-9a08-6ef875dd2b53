import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Button,
  TextField,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { motion, AnimatePresence } from "framer-motion";

function ReelGallery() {
  const [reels, setReels] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingReel, setEditingReel] = useState(null);
  const [newReel, setNewReel] = useState({
    title: "",
    src: "",
    thumbnail: null,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadTimeLeft, setUploadTimeLeft] = useState(null);
  const [uploadStatus, setUploadStatus] = useState("");
  const [videoFile, setVideoFile] = useState(null);
  const [thumbnailFile, setThumbnailFile] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("token");
        const response = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery",
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        const data = await response.json();
        console.log("Fetched reels:", data);
        setReels(data);
      } catch (error) {
        console.error("Error fetching reels:", error);
        showSnackbar("Failed to fetch reels", "error");
      }
    };
    fetchData();
  }, []);

  const handleAdd = () => {
    setEditingReel(null);
    setNewReel({ title: "", src: "", thumbnail: null });
    setOpenModal(true);
  };

  const handleEdit = (reel) => {
    setEditingReel(reel);
    setNewReel({
      title: reel.title,
      src: reel.src,
      thumbnail: null,
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingReel(null);
    setNewReel({ title: "", src: "", thumbnail: null });
    setVideoFile(null);
    setThumbnailFile(null);
    setUploadProgress(0);
    setUploadStatus("");
    setUploadTimeLeft(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewReel({
      ...newReel,
      [name]: value,
    });
  };

  const handleFileChange = (e, fieldName) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file based on type
    try {
      if (fieldName === "src") {
        validateVideoFile(file);
        setVideoFile(file);
        setNewReel({ ...newReel, src: file });
      } else if (fieldName === "thumbnail") {
        validateThumbnailFile(file);
        setThumbnailFile(file);
        setNewReel({ ...newReel, thumbnail: file });
      }
    } catch (error) {
      showSnackbar(error.message, "error");
      e.target.value = ""; // Clear invalid file
    }
  };

  // File validation functions
  const validateVideoFile = (file) => {
    const allowedTypes = [
      "video/mp4",
      "video/quicktime",
      "video/x-msvideo",
      "video/x-matroska",
      "video/webm",
    ];
    const maxSize = 1024 * 1024 * 1024; // 1GB

    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        "Invalid video format. Allowed: mp4, mov, avi, mkv, webm"
      );
    }

    if (file.size > maxSize) {
      throw new Error("Video file too large. Maximum size: 1GB");
    }

    return true;
  };

  const validateThumbnailFile = (file) => {
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        "Invalid thumbnail format. Allowed: jpeg, jpg, png, gif, webp"
      );
    }

    if (file.size > maxSize) {
      throw new Error("Thumbnail file too large. Maximum size: 10MB");
    }

    return true;
  };

  // Signed URL functions
  const getSignedUrl = async (file, uploadType) => {
    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/signed-url",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            uploadType: uploadType,
          }),
        }
      );

      if (!response.ok) {
        // Try v2 endpoint if first fails
        return getSignedUrlV2(file, uploadType);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error("Error getting signed URL:", error);
      throw error;
    }
  };

  const getSignedUrlV2 = async (file, uploadType) => {
    const token = localStorage.getItem("token");

    const response = await fetch(
      "https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/signed-url-v2",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          uploadType: uploadType,
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to get signed URL");
    }

    const data = await response.json();
    return data.data;
  };

  const uploadFileToR2 = async (file, signedUrlData) => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      let startTime = Date.now();

      xhr.upload.addEventListener("progress", (e) => {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(percent);

          // Estimate time left
          const elapsed = (Date.now() - startTime) / 1000;
          const speed = e.loaded / elapsed;
          const remaining = (e.total - e.loaded) / speed;
          setUploadTimeLeft(Math.round(remaining));
        }
      });

      xhr.onreadystatechange = () => {
        if (xhr.readyState === XMLHttpRequest.DONE) {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(signedUrlData.publicUrl);
          } else {
            reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
          }
        }
      };

      xhr.open("PUT", signedUrlData.signedUrl);
      xhr.setRequestHeader("Content-Type", file.type);
      xhr.send(file);
    });
  };

  const handleSubmit = async () => {
    if (!newReel.title.trim()) {
      showSnackbar("Please enter a title", "error");
      return;
    }

    // For editing, if no new files are selected, just update the title
    if (editingReel && !videoFile && !thumbnailFile) {
      await updateReelMetadata();
      return;
    }

    // For new reels, video is required
    if (!editingReel && !videoFile) {
      showSnackbar("Please select a video file", "error");
      return;
    }

    try {
      setLoading(true);
      setUploadProgress(0);
      setUploadTimeLeft(null);

      let videoUrl = editingReel ? editingReel.src : null;
      let thumbnailUrl = editingReel ? editingReel.thumbnail : null;

      // Upload video if new file is selected
      if (videoFile) {
        setUploadStatus("Getting signed URL for video...");
        const videoSignedData = await getSignedUrl(videoFile, "video");

        setUploadStatus("Uploading video to cloud storage...");
        videoUrl = await uploadFileToR2(videoFile, videoSignedData);
      }

      // Upload thumbnail if new file is selected
      if (thumbnailFile) {
        setUploadStatus("Processing thumbnail...");
        const thumbnailSignedData = await getSignedUrl(
          thumbnailFile,
          "thumbnail"
        );
        thumbnailUrl = await uploadFileToR2(thumbnailFile, thumbnailSignedData);
      }

      // Create or update reel with URLs
      setUploadStatus("Saving reel...");
      await createOrUpdateReel(videoUrl, thumbnailUrl);

      setUploadStatus("Success!");
      showSnackbar(
        editingReel ? "Reel updated successfully" : "Reel created successfully",
        "success"
      );
      handleCloseModal();

      // Refresh the reels list
      await fetchReels();
    } catch (error) {
      console.error("Error saving reel:", error);
      showSnackbar("Error: " + error.message, "error");
    } finally {
      setLoading(false);
      setUploadProgress(0);
      setUploadStatus("");
      setUploadTimeLeft(null);
    }
  };

  const updateReelMetadata = async () => {
    const token = localStorage.getItem("token");
    const formData = new FormData();
    formData.append("title", newReel.title);

    const response = await fetch(
      `https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/${editingReel._id}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      throw new Error("Failed to update reel");
    }

    const result = await response.json();
    const updatedReel = result.data || result;

    setReels((prev) =>
      prev.map((r) => (r._id === editingReel._id ? updatedReel : r))
    );
    showSnackbar("Reel updated successfully", "success");
    handleCloseModal();
  };

  const createOrUpdateReel = async (videoUrl, thumbnailUrl) => {
    const token = localStorage.getItem("token");
    const formData = new FormData();

    formData.append("title", newReel.title);
    formData.append("src", videoUrl);

    if (thumbnailUrl) {
      formData.append("thumbnail", thumbnailUrl);
    }

    const url = editingReel
      ? `https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/${editingReel._id}`
      : "https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/";

    const method = editingReel ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to save reel");
    }

    return await response.json();
  };

  const fetchReels = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery",
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      const data = await response.json();
      setReels(data);
    } catch (error) {
      console.error("Error fetching reels:", error);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this reel?")) {
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `https://youngproductions-768ada043db3.herokuapp.com/api/reel-gallery/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        setReels(reels.filter((reel) => reel._id !== id));
        showSnackbar("Reel deleted successfully", "success");
      } else {
        throw new Error("Failed to delete reel");
      }
    } catch (error) {
      console.error("Error deleting reel:", error);
      showSnackbar("Failed to delete reel", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 4,
          }}
        >
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontFamily: "Formula Bold",
                color: "#db4a41",
                fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
                textShadow: "0 2px 4px rgba(0,0,0,0.3)",
              }}
            >
              Reel Gallery
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                fontSize: { xs: "0.8rem", sm: "0.9rem" },
                mt: 0.5,
              }}
            >
              {reels.length} reels in gallery
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Reel
          </Button>
        </Box>

        {/* Reels Grid */}
        <Grid container spacing={3}>
          <AnimatePresence>
            {reels.map((reel) => (
              <Grid item xs={12} sm={6} md={4} key={reel._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      borderRadius: "12px",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      transition: "transform 0.3s ease, box-shadow 0.3s ease",
                      height: "100%",
                      display: "flex",
                      flexDirection: "column",
                      "&:hover": {
                        transform: "translateY(-5px)",
                        boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
                      },
                    }}
                  >
                    <Box sx={{ position: "relative" }}>
                      {reel.src ? (
                        <video
                          style={{
                            width: "100%",
                            height: "200px",
                            objectFit: "cover",
                            borderRadius: "12px 12px 0 0",
                          }}
                          muted
                          loop
                          autoPlay
                          preload="metadata"
                          onError={(e) => {
                            console.error("Video load error:", e.target.error);
                            console.log("Video URL:", reel.src);
                          }}
                        >
                          <source
                            src={reel.src.replace(
                              "https://youngproductionss.com/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/mp4"
                          />
                          <source
                            src={reel.src.replace(
                              "https://youngproductionss.com/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/webm"
                          />
                          <source
                            src={reel.src.replace(
                              "https://youngproductionss.com/",
                              "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                            )}
                            type="video/ogg"
                          />
                          Your browser does not support the video tag.
                        </video>
                      ) : (
                        <Box
                          sx={{
                            width: "100%",
                            height: "200px",
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            borderRadius: "12px 12px 0 0",
                          }}
                        >
                          <Typography
                            sx={{ color: "rgba(255, 255, 255, 0.5)" }}
                          >
                            No video available
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          mb: 2,
                        }}
                      >
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              color: "white",
                              fontWeight: "bold",
                              mb: 0.5,
                              fontSize: {
                                xs: "0.9rem",
                                sm: "1rem",
                                md: "1.1rem",
                              },
                            }}
                          >
                            {reel.title}
                          </Typography>
                        </Box>
                        <Box sx={{ display: "flex", gap: 0.5 }}>
                          <Tooltip title="Edit">
                            <IconButton
                              onClick={() => handleEdit(reel)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "white" },
                              }}
                            >
                              <EditIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              onClick={() => handleDelete(reel._id)}
                              size="small"
                              sx={{
                                color: "rgba(255, 255, 255, 0.7)",
                                "&:hover": { color: "#db4a41" },
                              }}
                            >
                              <DeleteIcon
                                sx={{
                                  fontSize: { xs: "1.1rem", sm: "1.2rem" },
                                }}
                              />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </AnimatePresence>
        </Grid>
      </Box>

      {/* Add/Edit Modal */}
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle sx={{ color: "white" }}>
          {editingReel ? "Edit Reel" : "Add New Reel"}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Reel Title"
            name="title"
            value={newReel.title}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />
          <Box sx={{ mt: 2 }}>
            <input
              accept="video/mp4,video/quicktime,video/x-msvideo,video/x-matroska,video/webm"
              style={{ display: "none" }}
              id="video-upload"
              type="file"
              onChange={(e) => handleFileChange(e, "src")}
            />
            <label htmlFor="video-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                {videoFile
                  ? `Selected: ${videoFile.name}`
                  : editingReel && !videoFile
                  ? "Change Video (Optional)"
                  : "Upload Video *"}
              </Button>
            </label>
            <Typography
              variant="caption"
              sx={{
                color: "rgba(255, 255, 255, 0.5)",
                mt: 0.5,
                display: "block",
              }}
            >
              Supported: MP4, MOV, AVI, MKV, WebM • Max size: 1GB
            </Typography>
          </Box>
          {loading && (
            <Box sx={{ mt: 2 }}>
              <Typography sx={{ color: "white", mb: 1 }}>
                {uploadStatus || "Processing..."} {uploadProgress}%
                {uploadTimeLeft !== null &&
                  uploadTimeLeft > 0 &&
                  ` • ~${uploadTimeLeft}s left`}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{
                    height: 8,
                    borderRadius: 5,
                    "& .MuiLinearProgress-bar": { backgroundColor: "#db4a41" },
                  }}
                />
              </Box>
            </Box>
          )}

          {/* File size display */}
          {videoFile && (
            <Box sx={{ mt: 1 }}>
              <Typography
                variant="body2"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                Video: {videoFile.name} (
                {(videoFile.size / 1024 / 1024).toFixed(2)} MB)
              </Typography>
            </Box>
          )}
          {thumbnailFile && (
            <Box sx={{ mt: 1 }}>
              <Typography
                variant="body2"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                Thumbnail: {thumbnailFile.name} (
                {(thumbnailFile.size / 1024 / 1024).toFixed(2)} MB)
              </Typography>
            </Box>
          )}

          <Box sx={{ mt: 2 }}>
            <input
              accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
              style={{ display: "none" }}
              id="thumbnail-upload"
              type="file"
              onChange={(e) => handleFileChange(e, "thumbnail")}
            />
            <label htmlFor="thumbnail-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                {thumbnailFile
                  ? `Selected: ${thumbnailFile.name}`
                  : "Upload Thumbnail (Optional)"}
              </Button>
            </label>
            <Typography
              variant="caption"
              sx={{
                color: "rgba(255, 255, 255, 0.5)",
                mt: 0.5,
                display: "block",
              }}
            >
              Supported: JPEG, PNG, GIF, WebP • Max size: 10MB
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            {loading ? "Saving..." : editingReel ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background:
              snackbar.severity === "success"
                ? "#2e7d32"
                : snackbar.severity === "warning"
                ? "#f57c00"
                : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ReelGallery;
