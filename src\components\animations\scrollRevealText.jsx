import React, { useEffect, useRef, useMemo } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const ScrollRevealText = ({
  children,
  enableBlur = true,
  baseOpacity = 0.1,
  blurStrength = 5,
  textClassName = "",
}) => {
  const wrapRef = useRef(null);

  const splitContent = useMemo(() => {
    // Handle string children
    if (typeof children === "string") {
      return children.split(/(\s+)/).map((word, i) => {
        if (word.match(/^\s+$/)) return word;
        return (
          <span className="inline-block word" key={i}>
            {word}
          </span>
        );
      });
    }

    // Handle mixed content (text with <br />)
    return React.Children.map(children, (child, i) => {
      if (typeof child === "string") {
        return child.split(/(\s+)/).map((word, j) => {
          if (word.match(/^\s+$/)) return word;
          return (
            <span className="inline-block word" key={`${i}-${j}`}>
              {word}
            </span>
          );
        });
      }
      return child; // Return non-string children as-is (like <br />)
    });
  }, [children]);

  useEffect(() => {
    const el = wrapRef.current;
    if (!el) return;

    const words = el.querySelectorAll(".word");

    const animation = gsap.fromTo(
      words,
      {
        opacity: baseOpacity,
        y: 12,
        filter: `blur(${enableBlur ? blurStrength : 0}px)`,
      },
      {
        opacity: 1,
        y: 0,
        filter: "blur(0px)",
        stagger: 0.08,
        ease: "power2.out",
        scrollTrigger: {
          trigger: el,
          start: "top 85%",
          end: "top 30%",
          scrub: true,
        },
      }
    );

    return () => {
      if (animation.scrollTrigger) animation.scrollTrigger.kill();
      animation.kill();
    };
  }, [baseOpacity, blurStrength, enableBlur]);

  return (
    <p ref={wrapRef} className={textClassName}>
      {splitContent}
    </p>
  );
};

export default ScrollRevealText;
