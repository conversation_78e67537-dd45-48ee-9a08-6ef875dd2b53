import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
  lazy,
  Suspense,
} from "react";
import { Typography, Box, Divider } from "@mui/material";
import { useLocation } from "react-router-dom";
import { HiOutlineSpeakerWave, HiOutlineSpeakerXMark } from "react-icons/hi2";
import { FaChevronLeft, FaChevronRight, FaPlay, FaPause } from "react-icons/fa";
import LoadingSpinner, { ImagePlaceholder } from "./loading/LoadingSpinner";

// Lazy-loaded components
const Vimeo = lazy(() => import("@u-wave/react-vimeo"));
const CircularText = lazy(() => import("./animations/CircularText"));

// Utility Hooks
const useViewport = (options = {}) => {
  const ref = useRef(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    if (!ref.current) return;
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      options
    );
    observer.observe(ref.current);
    return () => observer.disconnect();
  }, [ref, options]);

  return [ref, inView];
};

const useDebounce = (value, delay) => {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const timer = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(timer);
  }, [value, delay]);
  return debounced;
};

// Lazy Image Component
const LazyImage = React.memo(
  ({ src, alt, style = {}, showPlaceholder = true }) => {
    const [loaded, setLoaded] = useState(false);
    const [error, setError] = useState(false);

    return (
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          ...style,
        }}
      >
        {showPlaceholder && !loaded && !error && (
          <ImagePlaceholder
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              zIndex: 1,
              width: "100%",
              height: "100%",
            }}
          />
        )}
        <img
          src={src}
          alt={alt}
          loading="lazy"
          decoding="async"
          onLoad={() => setLoaded(true)}
          onError={() => setError(true)}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            opacity: loaded ? 1 : 0,
            transition: "opacity 0.3s ease-in",
            position: "relative",
            zIndex: 2,
          }}
        />
      </div>
    );
  }
);

LazyImage.displayName = "LazyImage";

// Video Player Component
const VideoPlayer = ({ src, isVimeo = false, muted = true, loop = true }) => {
  const [ref, inView] = useViewport({ threshold: 0.25 });
  const [loading, setLoading] = useState(true);

  const handleLoaded = useCallback(() => setLoading(false), []);

  return (
    <div
      ref={ref}
      style={{ width: "100%", height: "100%", position: "relative" }}
    >
      {loading && (
        <LoadingSpinner
          size={50}
          color="#db4a41"
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 2,
          }}
        />
      )}
      {inView &&
        (isVimeo ? (
          <iframe
            src={`${src}?autoplay=1&muted=${muted ? 1 : 0}&loop=${
              loop ? 1 : 0
            }`}
            style={{ width: "100%", height: "100%", border: "none" }}
            allow="autoplay; fullscreen"
            allowFullScreen
            onLoad={handleLoaded}
          />
        ) : (
          <video
            src={src}
            autoPlay
            muted={muted}
            loop={loop}
            playsInline
            preload="metadata"
            onLoadedData={handleLoaded}
            style={{ width: "100%", height: "100%", objectFit: "cover" }}
          />
        ))}
    </div>
  );
};

// Social Link Component
const SocialLink = React.memo(({ href, children }) => (
  <a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    style={{
      padding: "10px 20px",
      backgroundColor: "#db4a41",
      color: "#fff",
      textDecoration: "none",
      borderRadius: "8px",
      fontFamily: "Anton",
      fontSize: "0.9rem",
      transition: "background-color 0.3s",
    }}
    onMouseEnter={(e) => (e.target.style.backgroundColor = "#c0392b")}
    onMouseLeave={(e) => (e.target.style.backgroundColor = "#db4a41")}
  >
    {children}
  </a>
));

SocialLink.displayName = "SocialLink";

// Main Event Component
export default function Event({
  location,
  date,
  title,
  image,
  videoUrl,
  description,
  headquarters,
  facebook,
  instagram,
  tiktok,
  logoUrl,
  galleryImages = [],
}) {
  const routerLocation = useLocation();
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== "undefined" ? window.innerWidth : 1024
  );
  const isMobile = windowWidth <= 768;
  const isTablet = windowWidth <= 1024;

  const [isMuted, setIsMuted] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(false);

  const [titleRef, descRef] = [useRef(null), useRef(null)];
  const [visible, setVisible] = useState({ title: false, desc: false });

  // Transform media URLs
  const mediaBase = "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/";
  const transformUrl = (url) =>
    url.replace("https://youngproductionss.com/", mediaBase);

  const transformedLogoUrl = useMemo(() => transformUrl(logoUrl), [logoUrl]);
  const transformedVideoUrl = useMemo(() => transformUrl(videoUrl), [videoUrl]);
  const transformedGalleryImages = useMemo(
    () => galleryImages.map(transformUrl),
    [galleryImages]
  );

  // Fade-in styles
  const fadeIn = useCallback(
    (v) => ({
      opacity: v ? 1 : 0,
      transform: v ? "translateY(0)" : "translateY(20px)",
      transition: "opacity 0.6s ease-out, transform 0.6s ease-out",
      willChange: "opacity, transform",
    }),
    []
  );

  // Toggle mute
  const toggleMute = () => setIsMuted((prev) => !prev);

  // Gallery navigation
  const nextImage = useCallback(
    () => setCurrentImageIndex((prev) => (prev + 1) % galleryImages.length),
    [galleryImages.length]
  );
  const prevImage = useCallback(
    () =>
      setCurrentImageIndex(
        (prev) => (prev - 1 + galleryImages.length) % galleryImages.length
      ),
    [galleryImages.length]
  );

  // Auto-play gallery
  useEffect(() => {
    if (isAutoPlaying && galleryImages.length > 1) {
      const interval = setInterval(nextImage, 4000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, galleryImages.length, nextImage]);

  // Keyboard navigation
  useEffect(() => {
    const handleKey = (e) => {
      if (galleryImages.length === 0) return;
      if (e.key === "ArrowLeft") prevImage();
      if (e.key === "ArrowRight") nextImage();
      if (e.key === " ") {
        e.preventDefault();
        setIsAutoPlaying((prev) => !prev);
      }
    };
    window.addEventListener("keydown", handleKey);
    return () => window.removeEventListener("keydown", handleKey);
  }, [galleryImages.length, nextImage, prevImage]);

  // Responsive handling
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Intersection Observer for fade-in animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const key = entry.target.dataset.observeKey;
            if (key) setVisible((prev) => ({ ...prev, [key]: true }));
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1, rootMargin: "50px" }
    );

    if (titleRef.current) {
      titleRef.current.dataset.observeKey = "title";
      observer.observe(titleRef.current);
    }
    if (descRef.current) {
      descRef.current.dataset.observeKey = "desc";
      observer.observe(descRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div
      style={{
        backgroundColor: "#000",
        color: "#fff",
        fontFamily: "Formula Bold, sans-serif",
        overflowX: "hidden",
      }}
    >
      {/* CircularText on desktop */}
      {!isMobile && (
        <Suspense fallback={<div />}>
          <div
            style={{
              zIndex: 1000000,
              position: "fixed",
              top: "40%",
              right: isTablet ? "-50%" : "-5%",
            }}
          >
            <CircularText
              text={`YOUNG*PRODUCTIONS*${title.toUpperCase()}*`}
              onHover="speedUp"
              spinDuration={30}
            />
          </div>
        </Suspense>
      )}

      {/* Video Section */}
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100vh",
          overflow: "hidden",
          margin: "0 auto",
          zIndex: 100000,
        }}
      >
        <VideoPlayer
          src={videoUrl.includes("vimeo") ? videoUrl : transformedVideoUrl}
          isVimeo={videoUrl.includes("vimeo")}
          muted={isMuted}
        />
        <div
          onClick={toggleMute}
          style={{
            position: "absolute",
            top: 15,
            left: 15,
            borderRadius: "50%",
            padding: 8,
            fontSize: 22,
            background: "rgba(0,0,0,0.4)",
            cursor: "pointer",
            zIndex: 100001,
          }}
        >
          {isMuted ? <HiOutlineSpeakerXMark /> : <HiOutlineSpeakerWave />}
        </div>
      </div>

      {/* Title & Description */}
      <section style={{ textAlign: "center", padding: "10vh 5vw" }}>
        <div
          style={{
            width: 300,
            height: 150,
            margin: "0 auto 1rem",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            ...fadeIn(visible.title),
          }}
        >
          <LazyImage
            src={transformedLogoUrl}
            style={{
              maxWidth: "100%",
              maxHeight: "100%",
              objectFit: "contain",
            }}
            alt={title}
            showPlaceholder={false}
          />
        </div>
        <h1
          ref={titleRef}
          style={{
            fontSize: isMobile ? "2.5rem" : "4rem",
            marginBottom: "1rem",
            ...fadeIn(visible.title),
          }}
        >
          {title}
        </h1>
        <Typography
          variant="h5"
          sx={{
            color: "#777",
            marginBottom: "1rem",
            fontSize: "0.9rem",
            fontFamily: "Anton",
          }}
          style={fadeIn(visible.title)}
        >
          {new Date(date).toLocaleDateString()} • {location}
        </Typography>
        <p
          ref={descRef}
          style={{
            maxWidth: 800,
            margin: "0 auto",
            fontSize: isMobile ? "1rem" : "1.2rem",
            lineHeight: 1.6,
            textAlign: "justify",
            ...fadeIn(visible.desc),
          }}
        >
          {description.split(" ").slice(0, -2).join(" ")}
          <span style={{ color: "#db4a41" }}>{` ${description
            .split(" ")
            .slice(-2)
            .join(" ")}`}</span>
        </p>
      </section>

      {/* Gallery Slider */}
      {galleryImages.length > 0 && (
        <section
          style={{
            padding: "5rem 0",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: "#000",
          }}
        >
          <div
            style={{
              position: "relative",
              width: isMobile ? "95%" : "80%",
              maxWidth: 800,
              height: isMobile ? 300 : 500,
              background: "rgba(255,255,255,0.05)",
              borderRadius: 20,
              overflow: "hidden",
              boxShadow: "0 20px 60px rgba(0,0,0,0.5)",
              border: "1px solid rgba(255,255,255,0.1)",
            }}
          >
            <div
              style={{
                display: "flex",
                width: "100%",
                height: "100%",
                transition: "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)",
                transform: `translateX(-${currentImageIndex * 100}%)`,
              }}
            >
              {transformedGalleryImages.map((img, i) => (
                <div
                  key={i}
                  style={{
                    width: "100%",
                    height: "100%",
                    flexShrink: 0,
                    position: "relative",
                  }}
                >
                  <LazyImage
                    src={img}
                    alt={`Gallery ${i + 1}`}
                    showPlaceholder
                  />
                </div>
              ))}
            </div>

            {/* Navigation Arrows */}
            {!isMobile && (
              <>
                <button
                  onClick={prevImage}
                  style={arrowStyle("left")}
                  aria-label="Previous image"
                >
                  <FaChevronLeft />
                </button>
                <button
                  onClick={nextImage}
                  style={arrowStyle("right")}
                  aria-label="Next image"
                >
                  <FaChevronRight />
                </button>
              </>
            )}

            {/* Mobile touch */}
            {isMobile && (
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "50%",
                  height: "100%",
                  zIndex: 5,
                }}
                onClick={prevImage}
              />
            )}
            {isMobile && (
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  width: "50%",
                  height: "100%",
                  zIndex: 5,
                }}
                onClick={nextImage}
              />
            )}

            {/* Controls */}
            {galleryImages.length > 1 && (
              <div
                style={{
                  position: "absolute",
                  bottom: 20,
                  left: "50%",
                  transform: "translateX(-50%)",
                  display: "flex",
                  alignItems: "center",
                  gap: 15,
                  background: "rgba(0,0,0,0.7)",
                  color: "white",
                  padding: "8px 16px",
                  borderRadius: 20,
                  fontSize: 14,
                  fontFamily: "Anton",
                  zIndex: 10,
                }}
              >
                <button
                  onClick={() => setIsAutoPlaying(!isAutoPlaying)}
                  style={{
                    background: "transparent",
                    border: "none",
                    color: "white",
                    cursor: "pointer",
                    fontSize: 12,
                    display: "flex",
                    alignItems: "center",
                    padding: 4,
                  }}
                  aria-label={
                    isAutoPlaying ? "Pause slideshow" : "Play slideshow"
                  }
                >
                  {isAutoPlaying ? <FaPause /> : <FaPlay />}
                </button>
                <span>
                  {currentImageIndex + 1} / {galleryImages.length}
                </span>
              </div>
            )}
          </div>
        </section>
      )}

      {/* 2-Column Grid */}
      {galleryImages.length > 0 && (
        <section
          style={{
            width: "95%",
            display: "grid",
            gridTemplateColumns: isMobile ? "1fr" : "1fr 1fr",
            gap: 20,
            padding: "40px 0",
            margin: "0 auto",
          }}
        >
          {transformedGalleryImages.map((media, i) => {
            const isFullWidth = (i + 1) % 3 === 0 && !isMobile;
            const isVideo =
              media.endsWith(".mp4") ||
              media.endsWith(".mov") ||
              media.includes("vimeo") ||
              media.includes("youtube");
            return (
              <div
                key={i}
                style={{
                  gridColumn: isFullWidth ? "1 / -1" : "auto",
                  width: "100%",
                  overflow: "hidden",
                  borderRadius: 20,
                  position: "relative",
                  aspectRatio: isFullWidth ? "16/9" : "1/1",
                }}
              >
                {isVideo ? (
                  <VideoPlayer src={media} isVimeo={media.includes("vimeo")} />
                ) : (
                  <LazyImage
                    src={media}
                    alt={`Gallery image ${i + 1}`}
                    showPlaceholder
                    style={{
                      width: "100%",
                      height: "100%",
                      display: "block",
                      borderRadius: 20,
                      objectFit: "cover",
                    }}
                  />
                )}
              </div>
            );
          })}
        </section>
      )}

      {/* Divider & Footer */}
      <Divider sx={{ background: "#222", my: 4 }} />
      <Box sx={{ padding: "0 5% 50px" }}>
        <Typography variant="h4" sx={{ fontFamily: "Formula Bold", mb: 1 }}>
          Headquarters
        </Typography>
        <Typography sx={{ color: "#aaa", mb: 4, fontFamily: "Anton" }}>
          {headquarters}
        </Typography>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          {instagram && <SocialLink href={instagram}>INSTAGRAM</SocialLink>}
          {tiktok && <SocialLink href={tiktok}>TIKTOK</SocialLink>}
          {facebook && <SocialLink href={facebook}>FACEBOOK</SocialLink>}
        </Box>
      </Box>
    </div>
  );
}

// Arrow button styles
const arrowStyle = (position) => ({
  position: "absolute",
  [position]: "20px",
  top: "50%",
  transform: "translateY(-50%)",
  width: 50,
  height: 50,
  borderRadius: "50%",
  border: "2px solid #db4a41",
  background: "transparent",
  color: "#db4a41",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: 18,
  transition: "all 0.3s ease",
  zIndex: 10,
});
