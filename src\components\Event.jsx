// import React, { useState } from "react";
// import { Typography, Box, CardMedia } from "@mui/material";

// const Event = ({
//   location,
//   date,
//   title,
//   videoUrl,
//   description,
//   headquarters,
//   facebook,
//   instagram,
//   tiktok,
// }) => {
//   const [showFullDescription, setShowFullDescription] = useState(false);

//   const toggleDescription = () => {
//     setShowFullDescription(!showFullDescription);
//   };

//   return (
//     <>
//       <Box
//         className="event-page"
//         sx={{ padding: "20px", paddingBottom: "60px" }}
//       >
//         <Box sx={{ margin: "20px 5px", marginTop: "100px" }}>
//           <Typography
//             variant="h5"
//             sx={{ color: "#777777" }}
//             className="event-location"
//           >
//             {location}
//           </Typography>
//           <Typography
//             variant="h5"
//             sx={{ color: "#777777" }}
//             className="event-date"
//           >
//             {date}
//           </Typography>
//         </Box>

//         <Typography
//           variant="h2"
//           sx={{
//             fontFamily: "Formula Bold",
//             margin: "40px 0 40px 0",
//             color: "white",
//           }}
//           className="event-title"
//         >
//           {title}
//         </Typography>

//         <CardMedia
//           component="iframe"
//           allowFullScreen
//           controls
//           autoPlay
//           loop
//           muted
//           height="300"
//           src={videoUrl}
//           title={title}
//           sx={{
//             borderRadius: "10px",
//             border: "none",
//           }}
//         />

//         <Box className="event-info-container">
//           <Box className="event-desc-container">
//             <Typography sx={{ color: "white" }} variant="body1">
//               {showFullDescription
//                 ? description
//                 : `${description.slice(0, 160)}...`}
//             </Typography>
//             {!showFullDescription && (
//               <button className="btn-read-more" onClick={toggleDescription}>
//                 Read More...
//               </button>
//             )}

//             <button className="btn btn-primary link-btn">Share</button>
//           </Box>

//           <Box className="event-sharing-container">
//             <Typography
//               variant="h5"
//               sx={{ fontFamily: "Formula Bold", color: "white" }}
//             >
//               {title}
//             </Typography>

//   <Typography variant="body1" sx={{ color: "white" }}>
//     Headquarters for {title}
//   </Typography>

//   <Typography sx={{ color: "#777777" }}>{headquarters}</Typography>

//   <Box className="event-social-media">
//     <a
//       href={instagram}
//       target="_blank"
//       className="btn btn-primary link-btn"
//       rel="noreferrer"
//     >
//       INSTAGRAM
//     </a>
//     <a
//       href={tiktok}
//       target="_blank"
//       className="btn btn-primary link-btn"
//       rel="noreferrer"
//     >
//       TIKTOK
//     </a>
//     <a
//       href={facebook}
//       target="_blank"
//       className="btn btn-primary link-btn"
//       rel="noreferrer"
//     >
//       FACEBOOK
//     </a>
//   </Box>
// </Box>
//         </Box>
//       </Box>
//     </>
//   );
// };

// export default Event;

/*second concept*/
// import React, { useState } from "react";
// import { Typography, Box, CardMedia, Divider, Grid } from "@mui/material";
// import { motion } from "framer-motion";

// const Event = ({
//   location,
//   date,
//   title,
//   videoUrl,
//   description,
//   headquarters,
//   facebook,
//   instagram,
//   tiktok,
//   galleryImages = [],
// }) => {
//   // Combine video and images into one gallery array
//   const mediaGallery = [
//     { type: "video", src: videoUrl },
//     ...galleryImages.map((img) => ({ type: "image", src: img })),
//   ];

//   const [showFullDescription, setShowFullDescription] = useState(false);
//   const [selectedMedia, setSelectedMedia] = useState(mediaGallery[0]); // start with video

//   return (
//     <Box sx={{ background: "#000", color: "white", minHeight: "100vh" }}>
//       {/* Top Section */}
//       <Box
//         sx={{
//           display: "flex",
//           flexDirection: { xs: "column", md: "row" },
//           gap: 4,
//           padding: "100px 5% 50px",
//           alignItems: "center",
//         }}
//       >
//         {/* Main Viewer */}
//         <motion.div
//           initial={{ opacity: 0, y: 30 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6 }}
//           style={{
//             flex: 1,
//             background: "rgba(255,255,255,0.05)",
//             padding: 10,
//             borderRadius: 12,
//             boxShadow: "0 8px 20px rgba(0,0,0,0.4)",
//           }}
//         >
//           {selectedMedia.type === "video" ? (
//             <CardMedia
//               component="iframe"
//               allowFullScreen
//               src={selectedMedia.src}
//               height="350"
//               sx={{
//                 borderRadius: 2,
//                 border: "none",
//                 width: "100%",
//               }}
//             />
//           ) : (
//             <img
//               src={selectedMedia.src}
//               alt="Selected"
//               style={{
//                 width: "100%",
//                 maxHeight: "350px",
//                 objectFit: "cover",
//                 borderRadius: "8px",
//               }}
//             />
//           )}
//         </motion.div>

//         {/* Info */}
//         <motion.div
//           initial={{ opacity: 0, y: 30 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ delay: 0.2, duration: 0.6 }}
//           style={{ flex: 1 }}
//         >
//           <Typography
//             variant="h5"
//             sx={{ color: "#777", fontWeight: 400, mb: 1 }}
//           >
//             {location} • {date}
//           </Typography>

//           <Typography
//             variant="h2"
//             sx={{
//               fontFamily: "Formula Bold",
//               mb: 2,
//               borderBottom: "3px solid white",
//               display: "inline-block",
//             }}
//           >
//             {title}
//           </Typography>

//           <Typography
//             variant="body1"
//             sx={{ color: "#ccc", mb: 2, lineHeight: 1.6 }}
//           >
//             {showFullDescription
//               ? description
//               : `${description.slice(0, 200)}...`}
//           </Typography>

//           <button
//             onClick={() => setShowFullDescription(!showFullDescription)}
//             style={{
//               background: "transparent",
//               border: "none",
//               color: "#fff",
//               textDecoration: "underline",
//               cursor: "pointer",
//               padding: 0,
//               fontSize: "1rem",
//               fontFamily: "inherit",
//             }}
//           >
//             {showFullDescription ? "Show Less" : "Read More"}
//           </button>
//         </motion.div>
//       </Box>

//       {/* Thumbnails */}
//       {mediaGallery.length > 1 && (
//         <Box sx={{ padding: "0 5% 50px", mt: 4 }}>
//           <Grid container spacing={2}>
//             {mediaGallery.map((media, index) => (
//               <Grid item xs={12} sm={4} key={index}>
//                 <motion.div
//                   whileHover={{ scale: 1.05 }}
//                   style={{
//                     cursor: "pointer",
//                     border:
//                       selectedMedia.src === media.src
//                         ? "3px solid white"
//                         : "none",
//                   }}
//                   onClick={() => setSelectedMedia(media)}
//                 >
//                   {media.type === "video" ? (
//                     <CardMedia
//                       component="iframe"
//                       src={media.src}
//                       height="200"
//                       sx={{
//                         borderRadius: "4px",
//                         border: "none",
//                         width: "100%",
//                       }}
//                     />
//                   ) : (
//                     <img
//                       src={media.src}
//                       alt={`Thumbnail ${index + 1}`}
//                       style={{
//                         width: "100%",
//                         height: "200px",
//                         objectFit: "cover",
//                       }}
//                     />
//                   )}
//                 </motion.div>
//               </Grid>
//             ))}
//           </Grid>
//         </Box>
//       )}

// {/* Divider */}
// <Divider sx={{ background: "#222", my: 4 }} />

// {/* Bottom Info */}
// <Box sx={{ padding: "0 5% 50px" }}>
//   <Typography variant="h4" sx={{ fontFamily: "Formula Bold", mb: 1 }}>
//     Headquarters
//   </Typography>
//   <Typography sx={{ color: "#aaa", mb: 4 }}>{headquarters}</Typography>

//   <Box sx={{ display: "flex", gap: 2 }}>
//     <a
//       href={instagram}
//       target="_blank"
//       rel="noreferrer"
//       style={{ borderRadius: "8px" }}
//     >
//       INSTAGRAM
//     </a>
//     <a
//       href={tiktok}
//       target="_blank"
//       rel="noreferrer"
//       style={{ borderRadius: "8px" }}
//     >
//       TIKTOK
//     </a>
//     <a
//       href={facebook}
//       target="_blank"
//       rel="noreferrer"
//       style={{ borderRadius: "8px" }}
//     >
//       FACEBOOK
//     </a>
//   </Box>
// </Box>
//     </Box>
//   );
// };

// export default Event;

/* Third Concept*/
import React, { useEffect, useRef, useState, useCallback } from "react";
import { Typography, Box, Divider } from "@mui/material";
import Vimeo from "@u-wave/react-vimeo";
import CircularText from "./animations/CircularText";
import { useLocation } from "react-router-dom";
import { HiOutlineSpeakerWave, HiOutlineSpeakerXMark } from "react-icons/hi2";
import { FaChevronLeft, FaChevronRight, FaPlay, FaPause } from "react-icons/fa";
import { FastImage, FastVideo } from "./FastMedia";
import { LoadingPlaceholder } from "./LoadingSpinner";

export default function Event({
  location,
  date,
  title,
  image,
  videoUrl,
  description,
  headquarters,
  facebook,
  instagram,
  tiktok,
  logoUrl,
  galleryImages = [],
  recommendations = [],
}) {
  const titleRef = useRef(null);
  const descRef = useRef(null);
  const [visible, setVisible] = useState({ title: false, desc: false });
  const routerLocation = useLocation();
  const [isMuted, setIsMuted] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(false);
  const videoRef = useRef(null);

  const handleToggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };
  const nextImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev === galleryImages.length - 1 ? 0 : prev + 1
    );
  }, [galleryImages.length]);

  const prevImage = useCallback(() => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? galleryImages.length - 1 : prev - 1
    );
  }, [galleryImages.length]);

  // Auto-play
  useEffect(() => {
    if (isAutoPlaying && galleryImages.length > 1) {
      const interval = setInterval(() => {
        nextImage();
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, galleryImages.length, nextImage]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (galleryImages.length === 0) return;

      if (e.key === "ArrowLeft") {
        prevImage();
      } else if (e.key === "ArrowRight") {
        nextImage();
      } else if (e.key === " ") {
        e.preventDefault();
        setIsAutoPlaying((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [galleryImages.length, isAutoPlaying, nextImage, prevImage]);
  /** Fade-in animation when elements enter viewport */
  useEffect(() => {
    const observerOptions = { threshold: 0.3 };

    const fadeInOnScroll = (ref, key) => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisible((prev) => ({ ...prev, [key]: true }));
          }
        });
      }, observerOptions);

      if (ref.current) observer.observe(ref.current);
    };

    fadeInOnScroll(titleRef, "title");
    fadeInOnScroll(descRef, "desc");
  }, []);

  /** Save scroll position before refresh */
  useEffect(() => {
    const handleSaveScroll = () => {
      const maxScroll = document.body.scrollHeight - window.innerHeight;
      if (window.scrollY < maxScroll - 200) {
        sessionStorage.setItem(
          `scroll-${routerLocation.pathname}`,
          window.scrollY
        );
      }
    };
    window.addEventListener("beforeunload", handleSaveScroll);
    return () => window.removeEventListener("beforeunload", handleSaveScroll);
  }, [routerLocation.pathname]);

  /** Restore scroll position after refresh */
  useEffect(() => {
    const saved = sessionStorage.getItem(`scroll-${routerLocation.pathname}`);
    const maxScroll = document.body.scrollHeight - window.innerHeight;
    if (saved && parseInt(saved, 10) < maxScroll - 200) {
      window.scrollTo(0, parseInt(saved, 10));
    }
  }, [routerLocation.pathname]);

  const fadeInStyle = (isVisible) => ({
    opacity: isVisible ? 1 : 0,
    transform: isVisible ? "translateY(0px)" : "translateY(40px)",
    transition: "all 1s ease-out",
  });

  return (
    <div
      style={{
        backgroundColor: "#000",
        color: "#fff",
        fontFamily: "Formula Bold, sans-serif",
      }}
    >
      {window.innerWidth >= 768 && (
        <div
          style={{
            zIndex: 1000000,
            position: "fixed",
            top: "40%",
            right: window.innerWidth < 1024 ? "-50%" : "-5%",
          }}
        >
          <CircularText
            text={`YOUNG*PRODUCTIONS*${title.toUpperCase()}*`}
            onHover="speedUp"
            spinDuration={30}
          />
        </div>
      )}

      {/* Fullscreen Video */}
      <div
        style={{
          position: "relative",
          width: "100%",
          height: "100vh",
          overflow: "hidden",
          margin: "0 auto",
          zIndex: 100000,
        }}
      >
        {videoUrl.includes("vimeo") ? (
          <Vimeo
            video={videoUrl}
            responsive
            autoplay
            muted={isMuted}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
            }}
          />
        ) : (
          <FastVideo
            ref={videoRef}
            src={videoUrl}
            autoPlay
            muted={isMuted}
            loop
            playsInline
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            placeholder={
              <LoadingPlaceholder
                width="100%"
                height="100%"
                backgroundColor="#000"
                text="Loading video..."
                spinnerSize={24}
              />
            }
            onLoad={() => {
              // Video loaded successfully
              if (videoRef.current) {
                videoRef.current.play().catch(() => {
                  // Ignore autoplay policy errors
                });
              }
            }}
          />
        )}

        {/* Mute / Unmute button */}
        <div
          onClick={handleToggleMute}
          style={{
            position: "absolute",
            top: "15px",
            left: "15px",
            borderRadius: "50%",
            padding: "8px",
            color: "#fff",
            fontSize: "22px",
            background: "rgba(0,0,0,0.4)", // 👈 helps visibility on bright videos
            cursor: "pointer",
          }}
        >
          {isMuted ? <HiOutlineSpeakerXMark /> : <HiOutlineSpeakerWave />}
        </div>
      </div>

      {/* Title + Description */}
      <section style={{ textAlign: "center", padding: "10vh 5vw" }}>
        <FastImage
          src={logoUrl}
          alt={title}
          style={{
            width: "200px",
            height: "50px",
            marginBottom: "1rem",
            ...fadeInStyle(visible.title),
          }}
          placeholder={
            <LoadingPlaceholder
              width="200px"
              height="50px"
              backgroundColor="#333"
              text=""
              showSpinner={true}
              spinnerSize={16}
              style={{ borderRadius: "4px" }}
            />
          }
        />
        <h1
          ref={titleRef}
          style={{
            fontSize: "4rem",
            marginBottom: "1rem",
            ...fadeInStyle(visible.title),
          }}
        >
          {title}
        </h1>
        <Typography
          variant="h5"
          sx={{
            color: "#777",
            marginBottom: "1rem",
            fontSize: "0.9rem",
            fontFamily: "Anton",
          }}
          ref={titleRef}
          style={fadeInStyle(visible.title)}
        >
          {new Date(date).toLocaleDateString()} • {location}
        </Typography>
        <p
          ref={descRef}
          style={{
            maxWidth: "800px",
            margin: "0 auto",
            fontSize: "1.2rem",
            lineHeight: "1.6",
            textAlign: "justify",
            ...fadeInStyle(visible.desc),
          }}
        >
          {description.split(" ").slice(0, -2).join(" ")}
          <span style={{ color: "#db4a41" }}>
            {` ${description.split(" ").slice(-2).join(" ")}`}
          </span>
        </p>
      </section>

      {/* Gallery Slider */}
      {galleryImages.length > 0 && (
        <section
          style={{
            padding: "5rem 0",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: "#000",
          }}
        >
          <div
            className="gallery-slider-window"
            style={{
              position: "relative",
              width: window.innerWidth <= 768 ? "95%" : "80%",
              maxWidth: "800px",
              height: window.innerWidth <= 768 ? "300px" : "500px",
              background: "rgba(255,255,255,0.05)",
              borderRadius: "20px",
              overflow: "hidden",
              boxShadow: "0 20px 60px rgba(0,0,0,0.5)",
              border: "1px solid rgba(255,255,255,0.1)",
            }}
          >
            {/* Current Image */}
            {/* Current Image */}
            <div
              className="gallery-slider-track"
              style={{
                display: "flex",
                width: "100%",
                height: "100%",
                transition: "transform 0.8s ease-in-out",
                transform: `translateX(-${currentImageIndex * 100}%)`,
              }}
            >
              {galleryImages.map((img, index) => (
                <FastImage
                  key={index}
                  src={img}
                  alt={`Gallery ${index + 1}`}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    flexShrink: 0,
                    filter:
                      currentImageIndex === index
                        ? "brightness(1)"
                        : "brightness(0.8)",
                    transition: "filter 0.5s ease",
                  }}
                  placeholder={
                    <LoadingPlaceholder
                      width="100%"
                      height="100%"
                      backgroundColor="#222"
                      text="Loading..."
                      spinnerSize={16}
                    />
                  }
                />
              ))}
            </div>

            {/* Navigation Arrows */}
            <button
              className="gallery-nav-arrow"
              onClick={prevImage}
              style={{
                position: "absolute",
                left: window.innerWidth <= 768 ? "10px" : "20px",
                top: "50%",
                transform: "translateY(-50%)",
                width: window.innerWidth <= 768 ? "40px" : "50px",
                height: window.innerWidth <= 768 ? "40px" : "50px",
                borderRadius: "50%",
                border: "2px solid #db4a41",
                background: "transparent",
                color: "#db4a41",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: window.innerWidth <= 768 ? "14px" : "18px",
                transition: "all 0.3s ease",
                zIndex: 10,
              }}
              onMouseEnter={(e) => {
                e.target.style.background = "#db4a41";
                e.target.style.color = "white";
              }}
              onMouseLeave={(e) => {
                e.target.style.background = "transparent";
                e.target.style.color = "#db4a41";
              }}
            >
              <FaChevronLeft />
            </button>

            <button
              className="gallery-nav-arrow"
              onClick={nextImage}
              style={{
                position: "absolute",
                right: window.innerWidth <= 768 ? "10px" : "20px",
                top: "50%",
                transform: "translateY(-50%)",
                width: window.innerWidth <= 768 ? "40px" : "50px",
                height: window.innerWidth <= 768 ? "40px" : "50px",
                borderRadius: "50%",
                border: "2px solid #db4a41",
                background: "transparent",
                color: "#db4a41",
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: window.innerWidth <= 768 ? "14px" : "18px",
                transition: "all 0.3s ease",
                zIndex: 10,
              }}
              onMouseEnter={(e) => {
                e.target.style.background = "#db4a41";
                e.target.style.color = "white";
              }}
              onMouseLeave={(e) => {
                e.target.style.background = "transparent";
                e.target.style.color = "#db4a41";
              }}
            >
              <FaChevronRight />
            </button>

            {/* Controls */}
            <div
              style={{
                position: "absolute",
                bottom: "20px",
                left: "50%",
                transform: "translateX(-50%)",
                display: "flex",
                alignItems: "center",
                gap: "15px",
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "8px 16px",
                borderRadius: "20px",
                fontSize: "14px",
                fontFamily: "Anton",
              }}
            >
              {/* Play/Pause Button */}
              {galleryImages.length > 1 && (
                <button
                  onClick={() => setIsAutoPlaying(!isAutoPlaying)}
                  style={{
                    background: "transparent",
                    border: "none",
                    color: "white",
                    cursor: "pointer",
                    fontSize: "12px",
                    display: "flex",
                    alignItems: "center",
                    padding: "4px",
                  }}
                >
                  {isAutoPlaying ? <FaPause /> : <FaPlay />}
                </button>
              )}

              {/* Image Counter */}
              <span>
                {currentImageIndex + 1} / {galleryImages.length}
              </span>
            </div>
          </div>
        </section>
      )}
      {/* //grid of 2 at a row continers have image every two take the width of the screen */}

      {/* 2-Column Grid of Images + Videos */}
      <section
        style={{
          width: "95%",
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "40px",
          padding: "40px 0",
          margin: "0 auto",
        }}
      >
        {galleryImages.map((media, i) => {
          const isFullWidth = (i + 1) % 3 === 0;

          const isVideo =
            media.endsWith(".mp4") ||
            media.endsWith(".mov") ||
            media.includes("vimeo") ||
            media.includes("youtube");

          return (
            <div
              key={i}
              style={{
                gridColumn: isFullWidth ? "1 / -1" : "auto",
                width: "100%",
                overflow: "hidden",
                borderRadius: "20px",
                position: "relative",
              }}
            >
              {isVideo ? (
                <FastVideo
                  src={media}
                  autoPlay
                  muted
                  loop
                  playsInline
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    borderRadius: "20px",
                    display: "block",
                  }}
                  placeholder={
                    <LoadingPlaceholder
                      width="100%"
                      height="300px"
                      backgroundColor="#1a1a1a"
                      text="Loading video..."
                      spinnerSize={20}
                      style={{ borderRadius: "20px" }}
                    />
                  }
                />
              ) : (
                <FastImage
                  src={media}
                  alt={`Image ${i + 1}`}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    borderRadius: "20px",
                    display: "block",
                  }}
                  placeholder={
                    <LoadingPlaceholder
                      width="100%"
                      height="300px"
                      backgroundColor="#1a1a1a"
                      text="Loading image..."
                      spinnerSize={20}
                      style={{ borderRadius: "20px" }}
                    />
                  }
                />
              )}
            </div>
          );
        })}
      </section>

      {/* Divider */}
      <Divider sx={{ background: "#222", my: 4 }} />
      {/* Bottom Info */}
      <Box sx={{ padding: "0 5% 50px" }}>
        <Typography variant="h4" sx={{ fontFamily: "Formula Bold", mb: 1 }}>
          Headquarters
        </Typography>
        <Typography sx={{ color: "#aaa", mb: 4, fontFamily: "Anton" }}>
          {headquarters}
        </Typography>

        <Box sx={{ display: "flex", gap: 2 }}>
          {instagram && (
            <a
              href={instagram}
              target="_blank"
              className="btn btn-primary link-btn"
              rel="noreferrer"
              style={{ borderRadius: "8px", fontFamily: "Anton" }}
            >
              INSTAGRAM
            </a>
          )}
          {tiktok && (
            <a
              href={tiktok}
              target="_blank"
              className="btn btn-primary link-btn"
              rel="noreferrer"
              style={{ borderRadius: "8px", fontFamily: "Anton" }}
            >
              TIKTOK
            </a>
          )}
          {facebook && (
            <a
              href={facebook}
              target="_blank"
              className="btn btn-primary link-btn"
              rel="noreferrer"
              style={{ borderRadius: "8px", fontFamily: "Anton" }}
            >
              FACEBOOK
            </a>
          )}
        </Box>
      </Box>
    </div>
  );
}
