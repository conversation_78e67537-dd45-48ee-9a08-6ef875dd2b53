import { QueryClient } from '@tanstack/react-query';

// Create query client with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
    },
  },
});

// API base URL
const API_BASE_URL = 'https://youngproductions-768ada043db3.herokuapp.com/api';

// API endpoints
export const API_ENDPOINTS = {
  CLIENTS: `${API_BASE_URL}/clientsLogos`,
  TESTIMONIALS: `${API_BASE_URL}/testimonials`,
  WORK_VIDEOS: `${API_BASE_URL}/workVideos`,
  WORK: `${API_BASE_URL}/work`,
  REEL_GALLERY: `${API_BASE_URL}/reel-gallery`,
  FEATURE_WORK: `${API_BASE_URL}/feature-work`,
  HERO_VIDEOS: `${API_BASE_URL}/HeroVideos`,
  TEAMS: `${API_BASE_URL}/teams`,
  JOBS: `${API_BASE_URL}/jobs`,
};

// Generic fetch function with error handling
export const fetchApi = async (url, options = {}) => {
  const token = localStorage.getItem('token');
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response.json();
};

// Query keys for consistent caching
export const QUERY_KEYS = {
  CLIENTS: ['clients'],
  TESTIMONIALS: ['testimonials'],
  WORK_VIDEOS: ['workVideos'],
  WORK: ['work'],
  WORK_DETAIL: (id) => ['work', id],
  REEL_GALLERY: ['reelGallery'],
  FEATURE_WORK: ['featureWork'],
  HERO_VIDEOS: ['heroVideos'],
  TEAMS: ['teams'],
  JOBS: ['jobs'],
};
