import React, { useState } from "react";
import axios from "axios";
import Hero from "./Hero";
import Box from "@mui/material/Box";
import Chip from "@mui/material/Chip";
import TextField from "@mui/material/TextField";
import MenuItem from "@mui/material/MenuItem";
import { Typography, <PERSON><PERSON>kbar, Alert, Grid } from "@mui/material";
import Testimonials from "./Testimonials";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import BlurText from "./animations/BlurText";
const contactVideo =
  process.env.PUBLIC_URL + "/assets/header-videos/contact-video.mp4";
const formStyle = {
  margin: "10px 0",
  width: "100%",
  borderRadius: "8px",
  "& .MuiInputLabel-root.Mui-focused": {
    color: "#db4a41",
  },
  "& .MuiOutlinedInput-root:hover fieldset": {
    borderColor: "#db4a41",
  },
  "& .MuiOutlinedInput-root.Mui-focused fieldset": {
    borderColor: "#db4a41",
  },
};

function Contact() {
  const [selectedType, setSelectedType] = useState([]);
  const [selectedBudget, setSelectedBudget] = useState("");
  const [selectedDate, setSelectedDate] = useState("");
  const [meetingDateTime, setMeetingDateTime] = useState("");
  const [description, setDescription] = useState("");
  const [hearing, setHearing] = useState("");
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    companyName: "",
    phoneNumber: "",
  });
  const [errors, setErrors] = useState({});
  const [toast, setToast] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const chipOptionsType = [
    "Commercial",
    "Post Production",
    "Podcast",
    "Documentary",
    "Short Film",
    "Photography",
    "Campaign",
    "Motion Design",
    "Website",
    "Logo Design",
    "Graphic Design",
    "Other",
  ];

  const handleChipClick = (type, value) => {
    if (type === "type") {
      setSelectedType(
        (prev) =>
          prev.includes(value)
            ? prev.filter((item) => item !== value) // remove
            : [...prev, value] // add
      );
    }
  };
  const handleAnimationComplete = () => {
    console.log("Animation completed!");
  };
  const validateForm = () => {
    const newErrors = {};
    if (!formData.firstName.trim())
      newErrors.firstName = "First name is required";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    if (!formData.phoneNumber.trim())
      newErrors.phoneNumber = "Phone number is required";
    if (selectedType.length === 0)
      newErrors.selectedType = "At least one project type is required";
    if (!selectedBudget) newErrors.selectedBudget = "Budget is required";
    if (!selectedDate) newErrors.selectedDate = "Delivery date is required";
    if (!meetingDateTime)
      newErrors.meetingDateTime = "Meeting date and time is required";
    if (!description.trim()) newErrors.description = "Description is required";
    if (!hearing) newErrors.hearing = "Please select how you heard about us";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      setToast({
        open: true,
        message: "Please fill in all required fields",
        severity: "error",
      });
      return;
    }

    try {
      await axios.post(
        "https://youngproductions-768ada043db3.herokuapp.com/api/contactForm",
        {
          ...formData,
          selectedType,
          selectedBudget,
          selectedDate,
          meetingDateTime,
          description,
          hearing,
        }
      );

      setToast({
        open: true,
        message: "Form submitted successfully!",
        severity: "success",
      });

      // Reset form
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        companyName: "",
        phoneNumber: "",
      });
      setSelectedType([]);
      setSelectedBudget("");
      setSelectedDate("");
      setMeetingDateTime("");
      setDescription("");
      setHearing("");
      setErrors({});
    } catch (error) {
      console.error("Error submitting form:", error);
      setToast({
        open: true,
        message: "Error submitting form. Please try again.",
        severity: "error",
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleCloseToast = () => {
    setToast({ ...toast, open: false });
  };

  // Animation variants for staggered entrance
  const formVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut", staggerChildren: 0.15 },
    },
  };

  const fieldVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" },
    },
  };

  const contactInfoVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <Box sx={{ backgroundColor: "#fff", overflow: "hidden" }}>
      {/* INTRO TEXT */}
      <Box sx={{ px: { xs: 3, md: 10 }, pt: 16, maxWidth: 7200 }}>
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#000",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="You've got a project? Awesome."
            delay={100}
            animateBy="words"
            direction="top"
            onAnimationComplete={handleAnimationComplete}
            colorMap={["#000", "#000", "#000", "#000", "#000"]}
          />
        </Box>

        {/* <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "140px",
            },
            color: "#000",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="Project? Awesome."
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#000", "#000", "#000"]}
          />
        </Box> */}

        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "140px",
            },
            color: "#000",
            lineHeight: 1.6,
          }}
        >
          <BlurText
            text="We Love Projects!"
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#000", "#000", "#000"]}
          />
        </Box>
      </Box>

      <Typography
        variant="h5"
        sx={{ fontFamily: "Formula Bold", textAlign: "center", mt: 4 }}
        className="contact-form-header"
      >
        Tell us everything about your project and we'll get back to you once the
        bell rings. 🔔
      </Typography>

      {/* Contact Information Section */}
      <Box
        sx={{ py: 10, px: { xs: 2, md: 10 }, width: "80%", margin: "0 auto" }}
      >
        <motion.div
          variants={contactInfoVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Wrap the entire content in a Box with hover effects */}
          <Box
            sx={{
              cursor: `url("../../public/assets/contact-2.png") 16 16, auto`,
              "&:hover": {
                cursor: `url("../../public/assets/contact-2.png") 16 16, auto`,
              },
            }}
          >
            <Typography
              variant="h2"
              sx={{
                fontFamily: "Formula Bold",
                textAlign: "left",
                mb: 6,
                fontSize: { xs: "2rem", md: "3rem" },
              }}
            >
              Get in touch with Good People
            </Typography>

            <Grid container spacing={6}>
              {/* Business Contacts */}
              <Grid item xs={12} md={6}>
                <Typography
                  variant="h3"
                  sx={{
                    fontFamily: "Formula Bold",
                    mb: 3,
                    color: "#db4a41",
                  }}
                >
                  If you wanna talk business:
                </Typography>

                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{ fontFamily: "Formula Bold", mb: 1 }}
                  >
                    Youssef Tarek – Executive Producer
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    <EMAIL>
                  </Typography>
                  <Typography variant="body1">+201002512085</Typography>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{ fontFamily: "Formula Bold", mb: 1 }}
                  >
                    Khaled Amr – Producer
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    <EMAIL>
                  </Typography>
                  <Typography variant="body1">+201001313187</Typography>
                </Box>

                <Typography
                  variant="h4"
                  sx={{
                    fontFamily: "Formula Bold",
                    mb: 2,
                    color: "#db4a41",
                  }}
                >
                  Or if you're visiting us, we live in:
                </Typography>
                <Typography variant="h6" sx={{ fontFamily: "Anton" }}>
                  <span
                    style={{
                      color: "#db4a41",
                      fontFamily: "Formula Bold",
                      fontSize: "2.5rem",
                    }}
                  >
                    📍Cairo
                  </span>
                  : 24 Abou Gaafar El-Nahaas, Masaken Al Mohandesin, Nasr City,
                  Cairo Governorate 11341
                </Typography>
                <Typography variant="h6" sx={{ fontFamily: "Anton" }}>
                  <span
                    style={{
                      color: "#db4a41",
                      fontFamily: "Formula Bold",
                      fontSize: "2.5rem",
                    }}
                  >
                    📍United States
                  </span>
                  : New Jersey
                </Typography>
              </Grid>

              {/* General Inquiries */}
              <Grid item xs={12} md={6}>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: "Formula Bold",
                      mb: 2,
                      color: "#db4a41",
                    }}
                  >
                    Only the sharp survive. You in?:
                  </Typography>
                  <Link href="mailto:<EMAIL>">
                    <Typography
                      variant="h5"
                      sx={{ fontFamily: "Formula Bold", color: "#000" }}
                    >
                      <EMAIL>
                    </Typography>
                  </Link>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontFamily: "Formula Bold",
                      mb: 2,
                      color: "#db4a41",
                    }}
                  >
                    Need answers? Young's got you covered.
                  </Typography>
                  <Link href="mailto:<EMAIL>">
                    <Typography
                      variant="h5"
                      sx={{ fontFamily: "Formula Bold", color: "#000" }}
                    >
                      <EMAIL>
                    </Typography>
                  </Link>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </motion.div>
      </Box>
      {/* Contact Form Section */}
      <Box
        className="contact-form"
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          p: 3,
        }}
      >
        <motion.form
          onSubmit={handleSubmit}
          style={{ width: "100%" }}
          variants={formVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mb: 2 }}>
              Brief us of what you need
            </Typography>
            {chipOptionsType.map((option) => (
              <Chip
                key={option}
                label={option}
                onClick={() => handleChipClick("type", option)}
                onTouchStart={() => handleChipClick("type", option)}
                color={selectedType.includes(option) ? "primary" : "default"}
                sx={{
                  margin: "5px",
                  backgroundColor: selectedType.includes(option)
                    ? "#db4a41 !important"
                    : null,
                  color: selectedType.includes(option) ? "white" : null,
                  borderRadius: "10px",
                }}
              />
            ))}
            {errors.selectedType && (
              <Typography color="error" variant="caption">
                {errors.selectedType}
              </Typography>
            )}
          </motion.div>

          {/* Rest of your form remains the same */}
          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 4 }}>
              Book a Meeting
            </Typography>
            <TextField
              label="Select date and time for meeting"
              type="datetime-local"
              fullWidth
              value={meetingDateTime}
              onChange={(e) => setMeetingDateTime(e.target.value)}
              error={!!errors.meetingDateTime}
              helperText={errors.meetingDateTime}
              sx={formStyle}
              InputLabelProps={{ shrink: true }}
            />
          </motion.div>

          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 4 }}>
              More About You...
            </Typography>
            <TextField
              label="First Name"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              error={!!errors.firstName}
              helperText={errors.firstName}
              fullWidth
              sx={formStyle}
            />
            <TextField
              label="Last Name"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              error={!!errors.lastName}
              helperText={errors.lastName}
              fullWidth
              sx={formStyle}
            />
            <TextField
              label="Company Name"
              name="companyName"
              value={formData.companyName}
              onChange={handleChange}
              fullWidth
              sx={formStyle}
            />
            <TextField
              label="Phone Number"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleChange}
              error={!!errors.phoneNumber}
              helperText={errors.phoneNumber}
              fullWidth
              sx={formStyle}
            />
            <TextField
              label="Email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              error={!!errors.email}
              helperText={errors.email}
              fullWidth
              sx={formStyle}
            />
            <TextField
              label="Description"
              name="description"
              multiline
              rows={4}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              error={!!errors.description}
              helperText={errors.description}
              fullWidth
              sx={formStyle}
            />
          </motion.div>

          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 4 }}>
              Expected Project Delivery Date
            </Typography>
            <TextField
              label="Select Delivery Date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              error={!!errors.selectedDate}
              helperText={errors.selectedDate}
              sx={formStyle}
              InputLabelProps={{ shrink: true }}
              fullWidth
            />
          </motion.div>

          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 4 }}>
              Project Budget (EGP)
            </Typography>
            <TextField
              label="Enter budget (EGP)"
              variant="outlined"
              fullWidth
              type="number"
              value={selectedBudget}
              onChange={(e) => setSelectedBudget(e.target.value)}
              error={!!errors.selectedBudget}
              helperText={errors.selectedBudget}
              sx={formStyle}
            />
          </motion.div>

          <motion.div variants={fieldVariants}>
            <Typography variant="h3" sx={{ fontFamily: "Formula Bold", mt: 4 }}>
              How did you hear about us?
            </Typography>
            <TextField
              label="How did you hear about us"
              name="hearing"
              value={hearing}
              onChange={(e) => setHearing(e.target.value)}
              error={!!errors.hearing}
              helperText={errors.hearing}
              fullWidth
              sx={formStyle}
              select
            >
              <MenuItem value="Social Media">Social Media</MenuItem>
              <MenuItem value="Google Search">Google Search</MenuItem>
              <MenuItem value="Friend">From a friend</MenuItem>
              <MenuItem value="Event">Saw us in an event</MenuItem>
            </TextField>
          </motion.div>

          <motion.button
            type="submit"
            className="form-submit"
            style={{
              borderRadius: "10px",
              marginTop: "20px",
              backgroundColor: "#db4a41",
              color: "white",
              border: "none",
              padding: "12px 30px",
              fontSize: "1.1rem",
              fontFamily: "Formula Bold",
              cursor: "pointer",
              width: "40%",
              margin: "0 auto",
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Send
          </motion.button>
        </motion.form>
      </Box>

      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleCloseToast}
          severity={toast.severity}
          sx={{ width: "100%" }}
        >
          {toast.message}
        </Alert>
      </Snackbar>

      <Testimonials />
    </Box>
  );
}

export default Contact;
