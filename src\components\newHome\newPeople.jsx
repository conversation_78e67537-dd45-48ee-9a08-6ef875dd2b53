import React, { useEffect, useState } from "react";
import axios from "axios";

const TeamGrid = () => {
  const [teams, setTeams] = useState([]);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  useEffect(() => {
    axios
      .get("https://youngproductions-768ada043db3.herokuapp.com/api/teams")
      .then((response) => {
        const activatedTeams = response.data.filter(
          (member) => member.status === "activated"
        );
        const sortedTeams = activatedTeams.sort(
          (a, b) => (a.order || 0) - (b.order || 0)
        );
        setTeams(sortedTeams);
      })
      .catch((error) => {
        console.error("Error fetching teams:", error);
      });
  }, []);

  // Responsive columns
  const applyResponsiveColumns = () => {
    const width = window.innerWidth;
    if (width >= 1024) return "repeat(4, 1fr)";
    if (width >= 768) return "repeat(2, 1fr)";
    return "repeat(1, 1fr)";
  };

  const [columns, setColumns] = useState(applyResponsiveColumns());

  useEffect(() => {
    const handleResize = () => setColumns(applyResponsiveColumns());
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const containerStyle = {
    display: "grid",
    gap: "40px",
    padding: "40px",
    alignItems: "start",
    backgroundColor: "#000",
    gridTemplateColumns: columns,
  };

  const cardStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    textAlign: "left",
    fontFamily: "sans-serif",
    lineHeight: "1.5",
    position: "relative",
  };

  const imageWrapperStyle = (isCircle) => ({
    width: "100%",
    aspectRatio: "1 / 1",
    overflow: "hidden",
    borderRadius: isCircle ? "50%" : "12px",
    transition: "border-radius 0.4s ease-in-out",
    position: "relative",
  });

  const imageStyle = (hovered, isCircle) => ({
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: hovered || isCircle ? "50%" : "0",
    transition: "border-radius 0.4s ease-in-out, transform 0.3s ease-in-out",
  });

  const emailBoxStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translateX(-50%)",
    backgroundColor: "#111",
    color: "#fff",
    border: "1px solid #444",
    borderRadius: "8px",
    padding: "10px 12px",
    fontSize: "13px",
    zIndex: 1,
    opacity: 1,
    transition: "opacity 0.3s ease",
    fontFamily: "Anton",
    pointerEvents: "none",
  };

  const nameStyle = {
    marginTop: "16px",
    fontWeight: "bold",
    fontSize: "3rem",
    color: "#fff",
    fontFamily: "Formula Bold",
    letterSpacing: "0.03em",
  };

  const titleStyle = {
    fontSize: "1.5rem",
    color: "#db4a41",
    marginTop: "-3rem",
    fontFamily: "Formula Bold",
  };

  // const tagContainerStyle = {
  //   display: "flex",
  //   flexWrap: "wrap",
  //   gap: "6px",
  //   marginBottom: "8px",
  //   fontFamily: "Anton",
  // };

  // const tagStyle = {
  //   backgroundColor: "#fff",
  //   color: "#000",
  //   fontSize: "14px",
  //   padding: "4px 10px",
  //   borderRadius: "6px",
  // };

  const descriptionStyle = {
    fontSize: "14px",
    color: "#fff",
    marginTop: "4px",
    fontFamily: "Anton",
  };

  return (
    <div style={containerStyle}>
      {teams.map((person, index) => (
        <div key={index} style={cardStyle}>
          <div
            style={imageWrapperStyle(person?.isCircle)}
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
          >
            <img
              src={
                person?.profilePicture?.replace(
                  "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                ) ||
                person?.image ||
                "/fallback.jpg"
              }
              alt={person.name}
              style={imageStyle(hoveredIndex === index, person?.isCircle)}
            />{" "}
            {/* Logo at bottom left */}
            <img
              src="/assets/young-logo-white.webp"
              alt="Young Logo"
              style={{
                position: "absolute",
                bottom: "10px",
                right: "10px",
                height: "20px",
                padding: "5px",
                borderRadius: "6px",
                filter: "brightness(100%)",
                opacity: hoveredIndex === index ? 0 : 1,
                transition: "opacity 0.3s ease-in-out",
              }}
            />
            {hoveredIndex === index && person?.email && (
              <div style={emailBoxStyle}>{person.email}</div>
            )}
          </div>
          <h3 style={nameStyle}>{person.name}</h3>
          <p style={titleStyle}>{person.role}</p>
          {/* <div style={tagContainerStyle}>
            {(person.tags || ["Young", "Creative"]).map((tag, i) => (
              <span key={i} style={tagStyle}>
                {tag || "No Tag"}
              </span>
            ))}
          </div> */}
          <p style={descriptionStyle}>{person.bio}</p>
        </div>
      ))}
    </div>
  );
};

export default TeamGrid;
