import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import axios from "axios";
import { motion } from "framer-motion";

const CDN_URL = "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/profiles/";

const People = () => {
  const [hoveredPersonIndex, setHoveredPersonIndex] = useState(null);
  const [teams, setTeams] = useState([]);

  const handleMouseOver = (index) => {
    setHoveredPersonIndex(index);
  };

  const handleMouseOut = () => {
    setHoveredPersonIndex(null);
  };

  useEffect(() => {
    axios
      .get("https://youngproductions-768ada043db3.herokuapp.com/api/teams")
      .then((response) => {
        // Only show activated team members
        const activatedTeams = response.data.filter(
          (member) => member.status === "activated"
        );
        // Sort by order if present, otherwise by name
        const sortedTeams = activatedTeams.sort(
          (a, b) => (a.order || 0) - (b.order || 0)
        );
        setTeams(sortedTeams);
      })
      .catch((error) => {
        console.error("Error fetching teams:", error);
      });
  }, []);

  return (
    <Box
      sx={{
        backgroundColor: "black",
        color: "white",
        textAlign: "left",
        padding: "30px 20px",
      }}
    >
      <Typography
        variant="h4"
        sx={{ fontFamily: "Formula Bold", color: "#777777" }}
      >
        Our People
      </Typography>

      <Box
        className="people-section"
        sx={{
          marginTop: "20px",
          display: "flex",
          justifyContent: "space-around",
        }}
      >
        <Box className="people-list" sx={{ flex: 2 }}>
          {teams.map((team, index) => (
            <Box
              key={index}
              className="people-item"
              sx={{
                borderBottom: "1px solid #303030",
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: "30px 0px",
              }}
              onMouseEnter={() => handleMouseOver(index)}
              onMouseLeave={handleMouseOut}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <img
                  src={
                    team.image
                      ? `${CDN_URL}${team.image}`
                      : "/assets/default-avatar.webp"
                  }
                  alt={team.name}
                  style={{
                    width: "65px",
                    height: "70px",
                    borderRadius: "50%",
                    marginRight: "20px",
                  }}
                />
                <div>
                  <Typography
                    variant="h5"
                    sx={{
                      color: "#777777",
                      fontFamily: "Formula Bold",
                      marginBottom: "5px",
                    }}
                  >
                    {team.name}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      textTransform: "uppercase",
                      fontSize: "0.8rem",
                      color: "#777777",
                    }}
                  >
                    {team.role}
                  </Typography>
                </div>
              </Box>

              {/* Animated Bio always rendered but hidden unless hovered */}
              <motion.div
                initial={false}
                animate={{
                  opacity: hoveredPersonIndex === index ? 1 : 0,
                  x: hoveredPersonIndex === index ? 0 : -10, // slide in from left
                }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
                style={{ overflow: "hidden" }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "#ccc",
                    marginTop: "10px",
                    fontSize: "0.9rem",
                    lineHeight: 1.6,
                  }}
                >
                  {team.bio}
                </Typography>
              </motion.div>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

export default People;
