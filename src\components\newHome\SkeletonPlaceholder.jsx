/**
 * SkeletonPlaceholder - Grey skeleton box placeholder for images/videos
 * Maintains exact dimensions to prevent CLS
 */
const SkeletonPlaceholder = ({ width = "100%", height = "100%", style = {} }) => {
  return (
    <div
      data-skeleton
      style={{
        width,
        height,
        backgroundColor: "#1a1a1a",
        borderRadius: "0px",
        position: "relative",
        overflow: "hidden",
        transition: "opacity 0.3s ease",
        ...style,
      }}
    >
      <div
        style={{
          position: "absolute",
          top: 0,
          left: "-100%",
          width: "100%",
          height: "100%",
          background: "linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent)",
          animation: "shimmer 1.5s infinite",
        }}
      />
      <style>
        {`
          @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
          }
        `}
      </style>
    </div>
  );
};

export default SkeletonPlaceholder;

