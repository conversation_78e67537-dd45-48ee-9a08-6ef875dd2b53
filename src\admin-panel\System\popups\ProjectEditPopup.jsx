import React from "react";
import {
  Modal,
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
} from "@mui/material";
import { Upload } from "@mui/icons-material";

const ProjectEditPopup = ({
  open,
  onClose,
  project,
  formData,
  onFormChange,
  onUpdate,
  clients,
  onFileChange,
  newDocuments,
}) => {
  if (!project) return null;

  return (
    <Modal
      open={open}
      onClose={onClose}
      closeAfterTransition
      BackdropProps={{
        sx: {
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          backdropFilter: "blur(5px)",
        },
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: { xs: "90%", sm: 500 },
          maxHeight: "90vh",
          overflowY: "auto",
          bgcolor: "rgba(0, 0, 0, 0.9)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          borderRadius: "12px",
          boxShadow: "0 8px 32px rgba(219, 74, 65, 0.3)",
          p: 4,
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            mb: 3,
            textAlign: "center",
          }}
        >
          Edit Project: {project.title}
        </Typography>
        <TextField
          fullWidth
          label="Title"
          value={formData.title}
          onChange={(e) => onFormChange("title", e.target.value)}
          sx={{
            mb: 2,
            "& .MuiOutlinedInput-root": {
              color: "white",
              "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
              "&:hover fieldset": { borderColor: "#db4a41" },
            },
            "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
          }}
        />

        <TextField
          fullWidth
          label="Description"
          multiline
          rows={3}
          value={formData.description}
          onChange={(e) => onFormChange("description", e.target.value)}
          sx={{
            mb: 2,
            "& .MuiOutlinedInput-root": {
              color: "white",
              "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
              "&:hover fieldset": { borderColor: "#db4a41" },
            },
            "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
          }}
        />
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
            Client
          </InputLabel>
          <Select
            value={formData.clientId}
            onChange={(e) => onFormChange("clientId", e.target.value)}
            sx={{
              color: "white",
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "rgba(255, 255, 255, 0.23)",
              },
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#db4a41",
              },
            }}
          >
            {clients.map((client) => (
              <MenuItem key={client._id} value={client._id}>
                {client.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          fullWidth
          label="Start Date"
          type="date"
          value={formData.startDate}
          onChange={(e) => onFormChange("startDate", e.target.value)}
          sx={{
            mb: 2,
            "& .MuiOutlinedInput-root": {
              color: "white",
              "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
              "&:hover fieldset": { borderColor: "#db4a41" },
            },
            "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
          }}
        />
        <TextField
          fullWidth
          label="End Date"
          type="date"
          value={formData.endDate}
          onChange={(e) => onFormChange("endDate", e.target.value)}
          sx={{
            mb: 2,
            "& .MuiOutlinedInput-root": {
              color: "white",
              "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
              "&:hover fieldset": { borderColor: "#db4a41" },
            },
            "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
          }}
        />
        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
            Status
          </InputLabel>
          <Select
            value={formData.status}
            onChange={(e) => onFormChange("status", e.target.value)}
            sx={{
              color: "white",
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "rgba(255, 255, 255, 0.23)",
              },
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "#db4a41",
              },
            }}
          >
            <MenuItem value="planning">Planning</MenuItem>
            <MenuItem value="shooting">Shooting</MenuItem>
            <MenuItem value="editing">Editing</MenuItem>
            <MenuItem value="delivered">Delivered</MenuItem>
          </Select>
        </FormControl>
        <Typography
          variant="h6"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            mb: 1,
          }}
        >
          Project Documents
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: "rgba(255, 255, 255, 0.7)",
            mb: 2,
          }}
        >
          Upload additional project files
        </Typography>
        <Button
          variant="outlined"
          component="label"
          fullWidth
          sx={{
            borderColor: "#db4a41",
            color: "#db4a41",
            fontFamily: "Formula Bold",
            mb: 3,
            "&:hover": {
              borderColor: "#c62828",
              backgroundColor: "rgba(219, 74, 65, 0.1)",
            },
          }}
        >
          Choose Files
          <input type="file" multiple hidden onChange={onFileChange} />
        </Button>
        {newDocuments && newDocuments.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
            >
              Selected files:
            </Typography>
            {Array.from(newDocuments).map((file, index) => (
              <Typography
                key={index}
                variant="body2"
                sx={{ color: "rgba(255, 255, 255, 0.9)" }}
              >
                • {file.name}
              </Typography>
            ))}
          </Box>
        )}
        <Button
          variant="contained"
          onClick={onUpdate}
          fullWidth
          sx={{
            backgroundColor: "#db4a41",
            color: "white",
            fontFamily: "Formula Bold",
            "&:hover": {
              backgroundColor: "#c62828",
            },
          }}
        >
          Update Project
        </Button>
      </Box>
    </Modal>
  );
};

export default ProjectEditPopup;
