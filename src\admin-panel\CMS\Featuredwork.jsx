import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Snackbar,
  Alert,
  FormControlLabel,
  Switch,
  LinearProgress,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { motion } from "framer-motion";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import axios from "axios";

const GalleryManager = () => {
  const [items, setItems] = useState([]);
  const [availableWorks, setAvailableWorks] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    src: "",
    logo: "",
    workId: "",
    visibility: false,
    order: 0,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadTimeLeft, setUploadTimeLeft] = useState(null);
  const [fetchingItems, setFetchingItems] = useState(false);
  const [activeVideo, setActiveVideo] = useState(null);

  const fetchItems = useCallback(async () => {
    try {
      setFetchingItems(true);
      const res = await axios.get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/feature-work"
      );
      setItems(res.data);
    } catch (error) {
      console.error("Error fetching items:", error);
      showSnackbar("Failed to fetch items", "error");
    } finally {
      setFetchingItems(false);
    }
  }, []);

  const fetchAvailableWorks = useCallback(async () => {
    try {
      console.log("Fetching available works...");
      const token = localStorage.getItem("token");
      const config = token
        ? { headers: { Authorization: `Bearer ${token}` } }
        : {};

      // Try the available-works endpoint first
      let res;
      try {
        res = await axios.get(
          "https://youngproductions-768ada043db3.herokuapp.com/api/available-works",
          config
        );
      } catch (firstError) {
        console.log("First endpoint failed, trying /work endpoint...");
        // If that fails, try the work endpoint
        res = await axios.get(
          "https://youngproductions-768ada043db3.herokuapp.com/api/work",
          config
        );
      }
      console.log("Available works response:", res.data);
      setAvailableWorks(res.data);
    } catch (error) {
      console.error("Error fetching available works:", error);
      showSnackbar("Failed to fetch available works", "error");
    }
  }, []);

  useEffect(() => {
    fetchItems();
    fetchAvailableWorks();
  }, [fetchItems, fetchAvailableWorks]);

  const handleAdd = () => {
    setEditingItem(null);
    // Ensure visibility is explicitly set to false for new items
    // Set order to next available number
    const nextOrder =
      items.length > 0
        ? Math.max(...items.map((item) => item.order || 0)) + 1
        : 1;
    setFormData({
      title: "",
      src: "",
      logo: "",
      workId: "",
      visibility: false,
      order: nextOrder,
    });
    console.log("New form data initialized:", {
      title: "",
      src: "",
      logo: "",
      workId: "",
      visibility: false,
      order: nextOrder,
    });
    setOpenModal(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      title: item.title,
      src: "",
      logo: "",
      workId: item.work?._id || "",
      visibility: item.visibility || false,
      order: item.order || 0,
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingItem(null);
    setFormData({
      title: "",
      src: "",
      logo: "",
      workId: "",
      visibility: false,
      order: 0,
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    setFormData({
      ...formData,
      [name]: files[0],
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setUploadProgress(0);
      setUploadTimeLeft(null);

      // Check visibility limit before submitting

      let visibilityWarning = false;
      if (formData.visibility) {
        const currentVisibleCount = items.filter(
          (item) => item.visibility
        ).length;
        let finalVisibility = formData.visibility;

        if (editingItem) {
          // For editing: check if we're changing from false to true
          const wasVisible = editingItem.visibility;
          if (!wasVisible && currentVisibleCount >= 10) {
            finalVisibility = false;
            visibilityWarning = true;
          }
        } else {
          // For new item: check if we already have 10 visible items
          if (currentVisibleCount >= 10) {
            finalVisibility = false;
            visibilityWarning = true;
          }
        }

        // Update formData with final visibility
        setFormData((prev) => ({ ...prev, visibility: finalVisibility }));
      }

      const token = localStorage.getItem("token");
      const form = new FormData();
      form.append("title", formData.title);
      form.append("visibility", formData.visibility ? "true" : "false");
      form.append("order", formData.order.toString());
      if (formData.workId) form.append("workId", formData.workId);
      if (formData.src) form.append("src", formData.src);
      if (formData.logo) form.append("logo", formData.logo);

      const url = editingItem
        ? `https://youngproductions-768ada043db3.herokuapp.com/api/feature-work/${editingItem._id}`
        : "https://youngproductions-768ada043db3.herokuapp.com/api/feature-work";
      const method = editingItem ? "PUT" : "POST";

      const xhr = new XMLHttpRequest();
      let startTime = Date.now();

      xhr.upload.addEventListener("progress", (e) => {
        if (e.lengthComputable) {
          const percent = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(percent);

          // Estimate time left
          const elapsed = (Date.now() - startTime) / 1000; // in seconds
          const speed = e.loaded / elapsed; // bytes per sec
          const remaining = (e.total - e.loaded) / speed;
          setUploadTimeLeft(Math.round(remaining));
        }
      });

      xhr.onreadystatechange = () => {
        if (xhr.readyState === XMLHttpRequest.DONE) {
          setLoading(false);
          setUploadProgress(0);
          setUploadTimeLeft(null);

          if (xhr.status >= 200 && xhr.status < 300) {
            // const res = JSON.parse(xhr.responseText);

            if (visibilityWarning) {
              showSnackbar(
                `Item ${
                  editingItem ? "updated" : "added"
                } successfully. Visibility set to false - maximum 10 items can be visible.`,
                "warning"
              );
            } else {
              showSnackbar(
                `Item ${editingItem ? "updated" : "added"} successfully`,
                "success"
              );
            }

            fetchItems();
            handleCloseModal();
          } else {
            console.error("Upload failed:", xhr.responseText);
            showSnackbar("Failed to save item", "error");
          }
        }
      };

      xhr.open(method, url);
      xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      xhr.send(form);
    } catch (error) {
      console.error("Upload error:", error);
      showSnackbar("Failed to save item", "error");
      setLoading(false);
      setUploadProgress(0);
      setUploadTimeLeft(null);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this item?")) {
      return;
    }

    try {
      await axios.delete(
        `https://youngproductions-768ada043db3.herokuapp.com/api/feature-work/${id}`
      );
      setItems(items.filter((item) => item._id !== id));
      showSnackbar("Item deleted successfully", "success");
    } catch (error) {
      console.error("Error deleting item:", error);
      showSnackbar("Failed to delete item", "error");
    }
  };

  const toggleVisibility = async (id, currentVisibility) => {
    try {
      const newVisibility = !currentVisibility;

      // Check visibility limit before allowing change
      if (newVisibility) {
        const currentVisibleCount = items.filter(
          (item) => item.visibility
        ).length;
        if (currentVisibleCount >= 10) {
          showSnackbar(
            "Cannot make item visible - maximum 10 items can be visible at once",
            "error"
          );
          return;
        }
      }

      const form = new FormData();
      form.append("visibility", newVisibility ? "true" : "false");

      console.log("Toggling visibility:", {
        id,
        currentVisibility,
        newVisibility,
        currentVisibleCount: items.filter((item) => item.visibility).length,
      });

      const response = await axios.put(
        `https://youngproductions-768ada043db3.herokuapp.com/api/feature-work/${id}`,
        form
      );

      if (response.status === 200) {
        const updatedItem = response.data;
        console.log("Visibility toggle response:", updatedItem);
        setItems(items.map((item) => (item._id === id ? updatedItem : item)));
        showSnackbar(
          `Item ${updatedItem.visibility ? "shown" : "hidden"}`,
          "success"
        );
      }
    } catch (error) {
      console.error("Error toggling visibility:", error);
      showSnackbar("Failed to update visibility", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // const handleReorder = async (id, newOrder) => {
  //   try {
  //     const form = new FormData();
  //     form.append("order", newOrder.toString());

  //     const response = await axios.put(
  //       `https://youngproductions-768ada043db3.herokuapp.com/api/feature-work/${id}`,
  //       form
  //     );

  //     if (response.status === 200) {
  //       const updatedItem = response.data;
  //       setItems(items.map((item) => (item._id === id ? updatedItem : item)));
  //       showSnackbar(`Item order updated to ${newOrder}`, "success");
  //     }
  //   } catch (error) {
  //     console.error("Error updating order:", error);
  //     showSnackbar("Failed to update order", "error");
  //   }
  // };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 4,
          }}
        >
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontFamily: "Formula Bold",
                color: "#db4a41",
                fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
                textShadow: "0 2px 4px rgba(0,0,0,0.3)",
              }}
            >
              Featured Works Gallery
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.7)",
                fontSize: { xs: "0.8rem", sm: "0.9rem" },
                mt: 0.5,
              }}
            >
              Manage your featured works gallery •{" "}
              {items.filter((item) => item.visibility).length} of {items.length}{" "}
              visible
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add New Work
          </Button>
        </Box>

        {/* Gallery Grid - matches featureWorks.jsx layout exactly */}
        {fetchingItems ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              mt: 4,
              py: 8,
            }}
          >
            <CircularProgress size={60} sx={{ color: "#db4a41", mb: 2 }} />
            <Typography variant="h6" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
              Loading gallery...
            </Typography>
          </Box>
        ) : (
          <div className="gallery-grid" style={{ marginTop: "2rem" }}>
            {items
              .sort((a, b) => (a.order || 0) - (b.order || 0))
              .map((item, idx) => {
                const isVideo =
                  item.src &&
                  (item.src.endsWith(".mp4") ||
                    item.src.endsWith(".webm") ||
                    item.src.endsWith(".ogg"));
                return (
                  <motion.div
                    key={item._id}
                    className={`grid-item item-${idx + 1}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    onClick={() => isVideo && setActiveVideo(item.src)}
                    style={{ cursor: isVideo ? "pointer" : "default" }}
                  >
                    {item.src ? (
                      isVideo ? (
                        <video
                          src={item.src.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          autoPlay
                          muted
                          loop
                          playsInline
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      ) : (
                        <img
                          src={item.src.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          alt={item.title}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      )
                    ) : (
                      <Box
                        sx={{
                          width: "100%",
                          height: "100%",
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          color: "rgba(255, 255, 255, 0.5)",
                        }}
                      >
                        No media available
                      </Box>
                    )}

                    {/* Title overlay with logo - matches frontend */}
                    <div className="title-overlay">
                      {item.logo && (
                        <img
                          src={item.logo.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          alt={item.title}
                          style={{
                            width: "150px",
                            height: "150px",
                            objectFit: "contain",
                          }}
                        />
                      )}
                    </div>

                    {/* Order display */}
                    <Box
                      sx={{
                        position: "absolute",
                        top: "10px",
                        left: "10px",
                        backgroundColor: "rgba(0,0,0,0.8)",
                        color: "white",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "0.8rem",
                        fontWeight: "bold",
                        zIndex: 3,
                      }}
                    >
                      #{item.order || idx + 1}
                    </Box>

                    {/* Admin controls overlay */}
                    <Box
                      sx={{
                        position: "absolute",
                        top: "10px",
                        right: "10px",
                        display: "flex",
                        gap: 0.8,
                        zIndex: 3,
                      }}
                    >
                      <Tooltip
                        title={
                          item.visibility
                            ? "Visible"
                            : items.filter((item) => item.visibility).length >=
                              10
                            ? "Cannot make visible - maximum 10 items visible"
                            : "Hidden"
                        }
                      >
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleVisibility(
                              item._id,
                              item.visibility || false
                            );
                          }}
                          disabled={
                            !item.visibility &&
                            items.filter((item) => item.visibility).length >= 10
                          }
                          size="small"
                          sx={{
                            backgroundColor: item.visibility
                              ? "rgba(76, 175, 80, 0.8)"
                              : items.filter((item) => item.visibility)
                                  .length >= 10
                              ? "rgba(158, 158, 158, 0.4)"
                              : "rgba(158, 158, 158, 0.8)",
                            color: "white",
                            opacity:
                              !item.visibility &&
                              items.filter((item) => item.visibility).length >=
                                10
                                ? 0.5
                                : 1,
                            "&:hover": {
                              backgroundColor: item.visibility
                                ? "rgba(76, 175, 80, 1)"
                                : items.filter((item) => item.visibility)
                                    .length >= 10
                                ? "rgba(158, 158, 158, 0.4)"
                                : "rgba(158, 158, 158, 1)",
                            },
                            "&.Mui-disabled": {
                              backgroundColor: "rgba(158, 158, 158, 0.4)",
                              color: "rgba(255, 255, 255, 0.5)",
                            },
                          }}
                        >
                          {item.visibility ? (
                            <VisibilityIcon />
                          ) : (
                            <VisibilityOffIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(item);
                          }}
                          size="small"
                          sx={{
                            width: "35px",
                            backgroundColor: "rgba(0,0,0,0.6)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(0,0,0,0.8)",
                              color: "white",
                            },
                          }}
                        >
                          <EditIcon sx={{ fontSize: "1rem" }} />
                        </IconButton>
                      </Tooltip>
                      {/* <Tooltip title="Move Up">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            const newOrder = Math.max(
                              1,
                              (item.order || idx + 1) - 1
                            );
                            handleReorder(item._id, newOrder);
                          }}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(0,0,0,0.6)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#1976d2",
                              color: "white",
                            },
                          }}
                        >
                          ↑
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Move Down">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            const newOrder = (item.order || idx + 1) + 1;
                            handleReorder(item._id, newOrder);
                          }}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(0,0,0,0.6)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#1976d2",
                              color: "white",
                            },
                          }}
                        >
                          ↓
                        </IconButton>
                      </Tooltip> */}
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDelete(item._id);
                          }}
                          size="small"
                          sx={{
                            width: "35px",
                            backgroundColor: "rgba(0,0,0,0.6)",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "#db4a41",
                              color: "white",
                            },
                          }}
                        >
                          <DeleteIcon sx={{ fontSize: "1rem" }} />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </motion.div>
                );
              })}
          </div>
        )}
      </Box>

      {/* Add/Edit Modal - matches Herovideos.jsx style */}
      <Dialog
        open={openModal}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px",
          },
        }}
      >
        <DialogTitle sx={{ color: "white" }}>
          {editingItem ? "Edit Featured Work" : "Add New Featured Work"}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />

          <FormControl
            fullWidth
            margin="normal"
            required
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
              "& .MuiSelect-icon": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          >
            <InputLabel>Select Work</InputLabel>
            <Select
              name="workId"
              value={formData.workId}
              onChange={handleInputChange}
              label="Select Work"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {availableWorks && availableWorks.length > 0 ? (
                availableWorks.map((work) => (
                  <MenuItem key={work._id} value={work._id}>
                    {work.title}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled>
                  <em>Loading works...</em>
                </MenuItem>
              )}
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Order"
            name="order"
            type="number"
            value={formData.order}
            onChange={handleInputChange}
            margin="normal"
            required
            inputProps={{ min: 1 }}
            sx={{
              "& .MuiOutlinedInput-root": {
                color: "white",
                "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                "&:hover fieldset": { borderColor: "rgba(255, 255, 255, 0.5)" },
              },
              "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
            }}
          />

          <Box sx={{ mt: 2 }}>
            <input
              accept="image/*,video/*"
              style={{ display: "none" }}
              id="media-upload"
              type="file"
              name="src"
              onChange={handleFileChange}
            />
            <label htmlFor="media-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                {formData.src ? formData.src.name : "Upload Image or Video"}
              </Button>
            </label>
          </Box>

          <Box sx={{ mt: 2 }}>
            <input
              accept="image/*"
              style={{ display: "none" }}
              id="logo-upload"
              type="file"
              name="logo"
              onChange={handleFileChange}
            />
            <label htmlFor="logo-upload">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{
                  color: "white",
                  borderColor: "rgba(255, 255, 255, 0.23)",
                  "&:hover": {
                    borderColor: "#db4a41",
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                {formData.logo ? formData.logo.name : "Upload Logo"}
              </Button>
            </label>
          </Box>

          <FormControlLabel
            control={
              <Switch
                checked={formData.visibility}
                onChange={(e) => {
                  const newVisibility = e.target.checked;

                  // Check if trying to make visible when limit is reached
                  if (
                    newVisibility &&
                    items.filter((item) => item.visibility).length >= 10
                  ) {
                    showSnackbar(
                      "Cannot make item visible - maximum 10 items can be visible at once",
                      "warning"
                    );
                    return;
                  }

                  setFormData({ ...formData, visibility: newVisibility });
                }}
                sx={{
                  "& .MuiSwitch-switchBase.Mui-checked": {
                    color: "#db4a41",
                  },
                  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#db4a41",
                  },
                }}
              />
            }
            label={
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <span>Visible</span>
                {items.filter((item) => item.visibility).length >= 10 && (
                  <Typography
                    variant="caption"
                    sx={{
                      color: "#f57c00",
                      fontSize: "0.7rem",
                      fontStyle: "italic",
                    }}
                  >
                    (Max 10 reached)
                  </Typography>
                )}
              </Box>
            }
            sx={{ color: "white", mt: 2 }}
          />

          {loading && (
            <Box sx={{ mt: 2 }}>
              <Typography sx={{ color: "white", mb: 1 }}>
                Uploading... {uploadProgress}%
                {uploadTimeLeft !== null && ` • ~${uploadTimeLeft}s left`}
              </Typography>
              <Box sx={{ width: "100%" }}>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{
                    height: 8,
                    borderRadius: 5,
                    "& .MuiLinearProgress-bar": { backgroundColor: "#db4a41" },
                  }}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} sx={{ color: "white" }}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={16} sx={{ color: "white" }} />
              ) : null
            }
            sx={{
              backgroundColor: "#db4a41",
              "&:hover": { backgroundColor: "#c62828" },
              minWidth: "120px",
            }}
          >
            {loading ? "Processing..." : editingItem ? "Update" : "Add"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{
            background:
              snackbar.severity === "success"
                ? "#2e7d32"
                : snackbar.severity === "warning"
                ? "#f57c00"
                : "#d32f2f",
            color: "white",
          }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Video Modal */}
      {activeVideo && (
        <Box
          onClick={() => setActiveVideo(null)}
          sx={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 9999,
          }}
        >
          <Box
            onClick={(e) => e.stopPropagation()}
            sx={{
              position: "relative",
              borderRadius: "0px",
              overflow: "hidden",
              width: "80%",
              maxWidth: "1000px",
              aspectRatio: "16/9",
              background: "black",
            }}
          >
            <video
              src={activeVideo}
              controls
              autoPlay
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
            <Button
              onClick={() => setActiveVideo(null)}
              sx={{
                position: "absolute",
                top: "10px",
                right: "10px",
                background: "rgba(0,0,0,0.6)",
                color: "white",
                border: "none",
                borderRadius: "0%",
                width: "35px",
                height: "35px",
                fontSize: "20px",
                cursor: "pointer",
                minWidth: "35px",
                "&:hover": {
                  background: "rgba(0,0,0,0.8)",
                },
              }}
            >
              &times;
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default GalleryManager;
