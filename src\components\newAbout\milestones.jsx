import React, { useRef, useLayoutEffect, useState } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import {
  OrbitControls,
  useGLTF,
  PerspectiveCamera,
  useAnimations,
} from "@react-three/drei";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

// PRELOAD MODELS
useGLTF.preload("/assets/models/camera.glb");
useGLTF.preload("assets/models/mic.glb");
useGLTF.preload("assets/models/film-clap.glb");
useGLTF.preload("assets/models/kodak-film.glb");

// Camera Model
const CameraModel = ({ position = [0, 0, 0], scale = [3.5, 3.5, 3.5] }) => {
  const group = useRef();
  const { scene } = useGLTF("/assets/models/camera.glb");
  useFrame((state) => {
    if (group.current) {
      group.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.2;
    }
  });
  return (
    <primitive object={scene} ref={group} scale={scale} position={position} />
  );
};

const SplitTimelineScroll = () => {
  const sectionRef = useRef(null);
  const timelineWrapperRef = useRef(null);
  const lineRef = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [rotationY, setRotationY] = useState(0);
  const steps = [
    {
      title: "Branding",
      description:
        "Creative brainstorming & storyboards. Unique visual direction. We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    },
    {
      title: "Social Media",
      description:
        "Campaign strategy, communication systems & identity expression. We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    },
    {
      title: "Production",
      description:
        "Filming, editing, VFX, motion graphics & sound design. We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    },
    {
      title: "Strategy",
      description:
        "Brand strategy, digital ecosystems & cross-platform execution. We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    },
    {
      title: "Ideation",
      description:
        "Creative refinement, storytelling and final concept approval. We design brands that live beyond a logo. Naming, visual systems, campaigns that work across screens, cities, and cultures.",
    },
  ];

  useLayoutEffect(() => {
    const sectionEl = sectionRef.current;
    const timelineEl = timelineWrapperRef.current;
    const totalHeight = steps.length * window.innerHeight;
    let ctx = gsap.context(() => {
      ScrollTrigger.create({
        trigger: sectionEl,
        start: "top top",
        end: `+=${totalHeight}`,
        pin: true,
        anticipatePin: 1,
      });
      gsap.to(timelineEl, {
        y: () => -(timelineEl.scrollHeight - window.innerHeight * 0.6),
        ease: "none",
        scrollTrigger: {
          trigger: sectionEl,
          start: "top top",
          end: `+=${totalHeight}`,
          scrub: 1,
          snap: {
            snapTo: (value) => Math.round(value * steps.length) / steps.length,
            duration: 0.3,
            ease: "power1.inOut",
          },
          onUpdate: (self) => {
            const progress = self.progress;
            const stepIndex = Math.floor(progress * steps.length);
            setActiveStep(Math.min(stepIndex, steps.length - 1));
            setRotationY(progress * Math.PI * 2);
          },
        },
      });
      gsap.to(lineRef.current, {
        scaleY: 1,
        ease: "none",
        scrollTrigger: {
          trigger: sectionEl,
          start: "top top",
          end: `+=${totalHeight}`,
          scrub: 1,
        },
      });
    }, sectionEl);
    return () => ctx.revert();
  }, [steps.length]);

  return (
    <div style={{ background: "#0a0a0a", marginTop: "10vh" }}>
      <style>{`
        @media (max-width: 1024px) {
          .canvas-column {
            display: none !important;
          }
          .timeline-column {
            width: 100% !important;
            padding: 0 40px !important;
          }
        }
        @media (max-width: 768px) {
          .timeline-column {
            padding: 0 24px !important;
          }
        }
        @keyframes pulse {
          0%, 100% {
            transform: translateX(-50%) scale(1);
            opacity: 1;
          }
          50% {
            transform: translateX(-50%) scale(1.5);
            opacity: 0.5;
          }
        }
      `}</style>
      <section
        ref={sectionRef}
        style={{
          position: "relative",
          height: "100vh",
          display: "flex",
          background: "#000",
          overflow: "hidden",
        }}
      >
        {/* LEFT SIDE */}
        <div
          className="canvas-column"
          style={{
            width: "50%",
            height: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            background:
              "radial-gradient(circle at center, #1a1a1a 0%, #0d0d0d 60%, #000 100%)",
          }}
        >
          <Canvas>
            <PerspectiveCamera makeDefault position={[0, 0, 8]} />
            <ambientLight intensity={0.8} />
            <directionalLight position={[10, 5, 5]} intensity={1.5} />
            <spotLight position={[-5, 15, 5]} intensity={1.8} angle={0.3} />
            <pointLight position={[0, 2, 4]} intensity={2} color="#ff6b5e" />
            <CameraModel rotationY={rotationY} />
            <OrbitControls enableZoom={false} enablePan={false} />
          </Canvas>
        </div>

        {/* RIGHT SIDE */}
        <div
          className="timeline-column"
          style={{
            width: "50%",
            height: "100vh",
            position: "relative",
            padding: "0 80px",
          }}
        >
          {/* Grey Line with Dots */}
          <div
            style={{
              position: "absolute",
              left: "61px",
              top: "10vh",
              width: "4px",
              height: "80vh",
              background: "#333",
              borderRadius: "2px",
            }}
          >
            <div
              ref={lineRef}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                background:
                  "linear-gradient(180deg, #db4a41 0%, #ff6b5e 50%, #db4a41 100%)",
                borderRadius: "2px",
                transformOrigin: "top",
                transform: "scaleY(0)",
              }}
            />

            {/* Dots directly on the line */}
            {steps.map((step, index) => {
              const isActive = index <= activeStep;
              const isCurrent = index === activeStep;
              const dotOffset = (index / (steps.length - 1)) * 80; // % of line height

              return (
                <React.Fragment key={index}>
                  <div
                    style={{
                      position: "absolute",
                      left: "50%",
                      transform: isCurrent
                        ? "translateX(-50%) scale(1.5)"
                        : "translateX(-50%) scale(1)",
                      top: `${dotOffset}vh`,
                      width: "20px",
                      height: "20px",
                      borderRadius: "50%",
                      background: isActive ? "#db4a41" : "#2a2a2a",
                      border: `4px solid ${isActive ? "#ff6b5e" : "#1a1a1a"}`,
                      transition: "all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)",
                      boxShadow: isActive
                        ? "0 0 30px rgba(219,74,65,0.9), 0 0 60px rgba(255,107,94,0.4)"
                        : "none",
                      zIndex: 2,
                    }}
                  />
                  {isCurrent && (
                    <div
                      style={{
                        position: "absolute",
                        left: "50%",
                        transform: "translateX(-50%)",
                        top: `${dotOffset}vh`,
                        width: "30px",
                        height: "30px",
                        borderRadius: "50%",
                        background: "rgba(219,74,65,0.2)",
                        animation: "pulse 2s ease-in-out infinite",
                        zIndex: 1,
                      }}
                    />
                  )}
                </React.Fragment>
              );
            })}
          </div>

          {/* Timeline Content */}
          <div
            ref={timelineWrapperRef}
            style={{ paddingLeft: "120px", paddingTop: "15vh" }} // extra left padding so text aligns beside the line+dots
          >
            {steps.map((step, index) => {
              const isActive = index <= activeStep;
              const isCurrent = index === activeStep;

              return (
                <div
                  key={index}
                  className="timeline-step"
                  style={{
                    marginBottom: "30vh",
                    transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
                  }}
                >
                  <div
                    style={{
                      transform: isCurrent
                        ? "translateX(20px)"
                        : "translateX(0)",
                      opacity: isActive ? 1 : 0.3,
                      transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
                      filter: isCurrent
                        ? "none"
                        : isActive
                        ? "blur(0px)"
                        : "blur(1px)",
                    }}
                  >
                    <h2
                      style={{
                        color: isActive ? "#ffffff" : "#4a4a4a",
                        fontSize: isCurrent
                          ? "3.5rem"
                          : isActive
                          ? "2.5rem"
                          : "2rem",
                        margin: "0 0 1.5rem 0",
                        fontWeight: 800,
                        letterSpacing: "0.01em",
                        lineHeight: 1.1,
                        transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
                        textShadow: isCurrent
                          ? "0 4px 20px rgba(219,74,65,0.3)"
                          : "none",
                        background: isCurrent
                          ? "linear-gradient(135deg, #db4a41 0%, #ff6b5e 100%)"
                          : "transparent",
                        WebkitBackgroundClip: isCurrent ? "text" : "unset",
                        WebkitTextFillColor: isCurrent
                          ? "transparent"
                          : "inherit",
                        fontFamily: "Formula Bold",
                      }}
                    >
                      {step.title}
                    </h2>
                    <p
                      style={{
                        color: isActive ? "#b0b0b0" : "#3a3a3a",
                        fontSize: isCurrent ? "1.1rem" : "1rem",
                        lineHeight: 1.8,
                        margin: 0,
                        fontWeight: 400,
                        maxWidth: "540px",
                        transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
                        letterSpacing: "0.01em",
                        fontFamily: "Anton",
                      }}
                    >
                      {step.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>
    </div>
  );
};

export default SplitTimelineScroll;
