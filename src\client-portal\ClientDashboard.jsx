import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useClient } from "../contexts/ClientContext";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  ToggleButton,
  ToggleButtonGroup,
  Container,
  Tooltip,
  Divider,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  MoreVert as MoreVertIcon,
  Event as EventIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  PostAdd as PostAddIcon,
  Timeline as TimelineIcon,
  Home as HomeIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
} from "@mui/icons-material";
import Link from "@mui/material/Link";
import { motion } from "framer-motion";

function ClientDashboard() {
  const [anchorEl, setAnchorEl] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState([]);
  const [loadingEvents, setLoadingEvents] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDateEvents, setSelectedDateEvents] = useState([]);
  const [eventModalOpen, setEventModalOpen] = useState(false);
  const [selectedEventType, setSelectedEventType] = useState("all"); // New state for event type filter
  const eventTypes = ["all", "planning", "shooting", "editing", "other"]; // Available event types

  // Posting schedule states
  const [calendarView, setCalendarView] = useState("timeline"); // "timeline" or "posts"
  const [posts, setPosts] = useState([]);
  const [loadingPosts, setLoadingPosts] = useState(false);
  const [selectedDatePosts, setSelectedDatePosts] = useState([]);
  const [postModalOpen, setPostModalOpen] = useState(false);

  // Dark mode state
  const [darkMode, setDarkMode] = useState(false);

  // Profile modal state
  const [profileModalOpen, setProfileModalOpen] = useState(false);

  // Theme configuration
  const theme = {
    background: darkMode
      ? "linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)"
      : "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%)",
    cardBackground: darkMode
      ? "rgba(30, 41, 59, 0.9)"
      : "rgba(255, 255, 255, 0.9)",
    headerBackground: darkMode
      ? "rgba(30, 41, 59, 0.95)"
      : "rgba(255, 255, 255, 0.95)",
    textPrimary: darkMode ? "#f8fafc" : "#1e293b",
    textSecondary: darkMode ? "#94a3b8" : "#64748b",
    border: darkMode
      ? "1px solid rgba(255, 255, 255, 0.1)"
      : "1px solid rgba(0, 0, 0, 0.05)",
    calendarDayBackground: darkMode
      ? "rgba(30, 41, 59, 0.8)"
      : "rgba(255, 255, 255, 0.5)",
    calendarDayBackgroundHover: darkMode
      ? "rgba(219, 74, 65, 0.2)"
      : "rgba(248, 250, 252, 0.8)",
    modalBackground: darkMode
      ? "rgba(30, 41, 59, 0.95)"
      : "rgba(255, 255, 255, 0.95)",
  };

  const navigate = useNavigate();
  const { clientUser, loading, logout, isAuthenticated } = useClient();

  useEffect(() => {
    // Check if client is logged in
    if (!loading && !isAuthenticated()) {
      navigate("/client-portal/login");
    }
  }, [loading, isAuthenticated, navigate]);

  const fetchEvents = useCallback(async () => {
    setLoadingEvents(true);
    try {
      const token = localStorage.getItem("clientToken");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/system/client-timeline/my-events",
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setEvents(data.events || data || []);
      } else {
        showSnackbar("Failed to load events", "error");
      }
    } catch (error) {
      showSnackbar("Error loading events", "error");
    } finally {
      setLoadingEvents(false);
    }
  }, []); // no external deps

  const fetchPosts = useCallback(async () => {
    setLoadingPosts(true);
    try {
      const token = localStorage.getItem("clientToken");
      const response = await fetch(
        "https://youngproductions-768ada043db3.herokuapp.com/api/system/posting-schedule/my-posts",
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setPosts(data.posts || data || []);
      } else {
        showSnackbar("Failed to load posts", "error");
      }
    } catch (error) {
      showSnackbar("Error loading posts", "error");
    } finally {
      setLoadingPosts(false);
    }
  }, []);

  useEffect(() => {
    if (clientUser) {
      if (calendarView === "timeline") {
        fetchEvents();
      } else {
        fetchPosts();
      }
    }
  }, [clientUser, currentDate, calendarView, fetchEvents, fetchPosts]);

  const handlePrevMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentDate(
      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
    );
  };

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }

    return days;
  };

  const getEventsForDate = (day) => {
    if (!day || !events || events.length === 0) return [];

    const checkDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    // Set time to noon to avoid timezone issues
    checkDate.setHours(12, 0, 0, 0);

    console.log("Checking events for date:", checkDate.toISOString()); // Debug log

    const filteredByDate = events.filter((event) => {
      if (event.startDate && event.endDate) {
        const startDate = new Date(event.startDate);
        const endDate = new Date(event.endDate);

        // Set times to start/end of day to include full date ranges
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        const isInRange = checkDate >= startDate && checkDate <= endDate;

        if (isInRange) {
          console.log(
            `Event "${event.title}" matches date ${checkDate.toDateString()}`
          ); // Debug log
        }

        return isInRange;
      }
      return false;
    });

    if (selectedEventType === "all") {
      return filteredByDate;
    } else {
      return filteredByDate.filter(
        (event) => event.type && event.type.toLowerCase() === selectedEventType
      );
    }
  };

  const getPostsForDate = (day) => {
    if (!day || !posts || posts.length === 0) return [];

    const checkDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    // Set time to noon to avoid timezone issues
    checkDate.setHours(12, 0, 0, 0);

    console.log("Checking posts for date:", checkDate.toISOString()); // Debug log

    const filteredByDate = posts.filter((post) => {
      if (post.date) {
        const postDate = new Date(post.date);

        // Set times to start/end of day to include full date ranges
        postDate.setHours(0, 0, 0, 0);
        checkDate.setHours(0, 0, 0, 0);

        const isMatch = postDate.getTime() === checkDate.getTime();

        if (isMatch) {
          console.log(
            `Post "${post.name}" matches date ${checkDate.toDateString()}`
          ); // Debug log
        }

        return isMatch;
      }
      return false;
    });

    return filteredByDate;
  };

  const handleDayClick = (day) => {
    if (!day) return;

    if (calendarView === "timeline") {
      const dayEvents = getEventsForDate(day);
      console.log("Day clicked:", day, "Events found:", dayEvents); // Debug log

      if (dayEvents.length > 0) {
        setSelectedDate(day);
        setSelectedDateEvents(dayEvents);
        setEventModalOpen(true);
      }
    } else {
      const dayPosts = getPostsForDate(day);
      console.log("Day clicked:", day, "Posts found:", dayPosts); // Debug log

      if (dayPosts.length > 0) {
        setSelectedDate(day);
        setSelectedDatePosts(dayPosts);
        setPostModalOpen(true);
      }
    }
  };

  const handleCloseEventModal = () => {
    setEventModalOpen(false);
    setSelectedDate(null);
    setSelectedDateEvents([]);
  };

  const handleClosePostModal = () => {
    setPostModalOpen(false);
    setSelectedDate(null);
    setSelectedDatePosts([]);
  };

  const handleProfileModalOpen = () => {
    setProfileModalOpen(true);
    handleMenuClose();
  };

  const handleProfileModalClose = () => {
    setProfileModalOpen(false);
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    navigate("/client-portal/login");
    showSnackbar("Logged out successfully", "success");
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading) {
    return (
      <Box
        sx={{
          minHeight: "100vh",
          background: "linear-gradient(135deg, #db4a41 0%, #ffffff 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <CircularProgress sx={{ color: "white" }} />
      </Box>
    );
  }

  if (!clientUser) {
    navigate("/client-portal/login");
    return null;
  }

  // Debug: Log events and current date
  console.log("Current events:", events);
  console.log(
    "Current calendar month:",
    currentDate.getMonth() + 1,
    currentDate.getFullYear()
  );

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: theme.background,
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: darkMode
            ? "radial-gradient(circle at 20% 30%, rgba(219, 74, 65, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
            : "radial-gradient(circle at 20% 30%, rgba(219, 74, 65, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.05) 0%, transparent 50%)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Modern Header */}
        <Box
          sx={{
            background: theme.headerBackground,
            backdropFilter: "blur(20px)",
            borderBottom: theme.border,
            boxShadow: darkMode
              ? "0 4px 20px rgba(0, 0, 0, 0.3)"
              : "0 4px 20px rgba(0, 0, 0, 0.08)",
            position: "sticky",
            top: 0,
            zIndex: 100,
          }}
        >
          <Container
            maxWidth="xl"
            sx={{ py: { xs: 1, sm: 2 }, px: { xs: 1, sm: 2 } }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexWrap: { xs: "wrap", md: "nowrap" },
                gap: { xs: 1, sm: 2 },
              }}
            >
              {/* Logo and Brand */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: { xs: 2, sm: 3 },
                }}
              >
                <Box
                  sx={{
                    width: { xs: 40, sm: 50 },
                    height: { xs: 40, sm: 50 },
                    borderRadius: "12px",
                    background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    boxShadow: "0 8px 16px rgba(219, 74, 65, 0.3)",
                  }}
                >
                  <HomeIcon
                    sx={{ color: "white", fontSize: { xs: 24, sm: 28 } }}
                  />
                </Box>
                <Box sx={{ display: { xs: "none", sm: "block" } }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontFamily: "Formula Bold",
                      fontWeight: "bold",
                      color: theme.textPrimary,
                      letterSpacing: "-0.5px",
                      fontSize: { sm: "1.25rem", md: "1.5rem" },
                    }}
                  >
                    Client Portal
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.textSecondary,
                      fontSize: "0.9rem",
                    }}
                  >
                    Young Productions
                  </Typography>
                </Box>
                <Box sx={{ flexGrow: 1 }} />
              </Box>

              {/* User Section */}
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                {/* <Tooltip title="Notifications">
                  <IconButton
                    sx={{
                      color: theme.textSecondary,
                      "&:hover": { backgroundColor: "rgba(219, 74, 65, 0.1)" },
                    }}
                  >
                    <Badge badgeContent={3} color="error">
                      <NotificationsIcon />
                    </Badge>
                  </IconButton>
                </Tooltip> */}

                <Tooltip
                  title={
                    darkMode ? "Switch to Light Mode" : "Switch to Dark Mode"
                  }
                >
                  <IconButton
                    onClick={() => setDarkMode(!darkMode)}
                    sx={{
                      color: theme.textSecondary,
                      "&:hover": { backgroundColor: "rgba(219, 74, 65, 0.1)" },
                    }}
                  >
                    {darkMode ? <LightModeIcon /> : <DarkModeIcon />}
                  </IconButton>
                </Tooltip>

                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: { xs: 1, sm: 2 },
                    backgroundColor: darkMode
                      ? "rgba(51, 65, 85, 0.8)"
                      : "rgba(248, 250, 252, 0.8)",
                    borderRadius: "16px",
                    padding: { xs: "6px 12px", sm: "8px 16px" },
                    border: theme.border,
                  }}
                >
                  <Box
                    sx={{
                      textAlign: "right",
                      display: { xs: "none", sm: "block" },
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.textSecondary,
                        fontSize: "0.85rem",
                      }}
                    >
                      Welcome back,
                    </Typography>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: theme.textPrimary,
                        fontWeight: 600,
                      }}
                    >
                      {clientUser.name}
                    </Typography>
                  </Box>
                  <Avatar
                    sx={{
                      bgcolor: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                      width: { xs: 36, sm: 44 },
                      height: { xs: 36, sm: 44 },
                      fontWeight: "bold",
                      fontSize: { xs: "0.9rem", sm: "1rem" },
                      boxShadow: "0 4px 12px rgba(219, 74, 65, 0.3)",
                    }}
                  >
                    {clientUser.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <IconButton
                    onClick={handleMenuOpen}
                    sx={{
                      color: theme.textSecondary,
                      p: { xs: 0.5, sm: 1 },
                      "&:hover": { backgroundColor: "rgba(219, 74, 65, 0.1)" },
                    }}
                  >
                    <MoreVertIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />
                  </IconButton>
                </Box>

                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  slotProps={{
                    paper: {
                      sx: {
                        background: theme.modalBackground,
                        backdropFilter: "blur(20px)",
                        border: theme.border,
                        borderRadius: "12px",
                        boxShadow: darkMode
                          ? "0 20px 40px rgba(0, 0, 0, 0.5)"
                          : "0 20px 40px rgba(0, 0, 0, 0.15)",
                        mt: 1,
                      },
                    },
                  }}
                >
                  <MenuItem
                    onClick={handleProfileModalOpen}
                    sx={{
                      color: theme.textPrimary,
                      borderRadius: "8px",
                      mx: 1,
                      my: 0.5,
                      "&:hover": { backgroundColor: "rgba(219, 74, 65, 0.1)" },
                    }}
                  >
                    <PersonIcon sx={{ mr: 2, color: "#db4a41" }} />
                    Profile
                  </MenuItem>
                  {/* <MenuItem
                    onClick={() => {
                      handleMenuClose();
                    }}
                    sx={{
                      color: theme.textPrimary,
                      borderRadius: "8px",
                      mx: 1,
                      my: 0.5,
                      "&:hover": { backgroundColor: "rgba(219, 74, 65, 0.1)" },
                    }}
                  >
                    <SettingsIcon sx={{ mr: 2, color: theme.textSecondary }} />
                    Preferences
                  </MenuItem> */}
                  <Divider sx={{ my: 1, borderColor: theme.textSecondary }} />
                  <MenuItem
                    onClick={() => {
                      handleMenuClose();
                      handleLogout();
                    }}
                    sx={{
                      color: "#dc2626",
                      borderRadius: "8px",
                      mx: 1,
                      my: 0.5,
                      "&:hover": { backgroundColor: "rgba(220, 38, 38, 0.1)" },
                    }}
                  >
                    <LogoutIcon sx={{ mr: 2 }} />
                    Sign Out
                  </MenuItem>
                </Menu>
              </Box>
            </Box>
          </Container>
        </Box>

        <Container
          maxWidth="xl"
          sx={{ py: { xs: 2, sm: 4 }, px: { xs: 1, sm: 2 } }}
        >
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card
              sx={{
                background: theme.cardBackground,
                backdropFilter: "blur(20px)",
                border: theme.border,
                borderRadius: { xs: "16px", sm: "24px" },
                mb: { xs: 3, sm: 4 },
                boxShadow: darkMode
                  ? "0 20px 40px rgba(0, 0, 0, 0.3)"
                  : "0 20px 40px rgba(0, 0, 0, 0.08)",
                overflow: "hidden",
                position: "relative",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  height: "4px",
                  background:
                    "linear-gradient(90deg, #db4a41, #ff6b5b, #db4a41)",
                  zIndex: 1,
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: { xs: 2, sm: 3 },
                    mb: { xs: 2, sm: 3 },
                    flexDirection: { xs: "column", sm: "row" },
                    textAlign: { xs: "center", sm: "left" },
                  }}
                >
                  <Box
                    sx={{
                      width: { xs: 50, sm: 60 },
                      height: { xs: 50, sm: 60 },
                      borderRadius: "16px",
                      background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      boxShadow: "0 12px 24px rgba(219, 74, 65, 0.3)",
                    }}
                  >
                    <TrendingUpIcon
                      sx={{ color: "white", fontSize: { xs: 28, sm: 32 } }}
                    />
                  </Box>
                  <Box>
                    <Typography
                      variant="h4"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: theme.textPrimary,
                        fontWeight: "bold",
                        letterSpacing: "-0.5px",
                        mb: 1,
                        fontSize: { xs: "1.5rem", sm: "2rem", md: "2.125rem" },
                      }}
                    >
                      Welcome Back, {clientUser.name}!
                    </Typography>
                    <Typography
                      variant="h6"
                      sx={{
                        color: theme.textSecondary,
                        fontSize: { xs: "0.9rem", sm: "1rem", md: "1.125rem" },
                        fontWeight: 500,
                      }}
                    >
                      {clientUser.company} • Client Portal
                    </Typography>
                  </Box>
                </Box>

                <Typography
                  variant="body1"
                  sx={{
                    color: theme.textSecondary,
                    fontSize: "1.1rem",
                    lineHeight: 1.6,
                    mb: 3,
                  }}
                >
                  Track your project progress, view upcoming posts, and stay
                  connected with your dedicated team.
                </Typography>

                <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                  <Chip
                    icon={<PersonIcon />}
                    label={`Client: ${clientUser.clientId?.name || "N/A"}`}
                    sx={{
                      background: "rgba(59, 130, 246, 0.1)",
                      color: "#3b82f6",
                      border: "1px solid rgba(59, 130, 246, 0.2)",
                      fontWeight: 500,
                    }}
                  />
                  <Chip
                    label={`Status: ${clientUser.status}`}
                    sx={{
                      background:
                        clientUser.status === "active"
                          ? "rgba(34, 197, 94, 0.1)"
                          : "rgba(251, 146, 60, 0.1)",
                      color:
                        clientUser.status === "active" ? "#22c55e" : "#fb923c",
                      border: `1px solid ${
                        clientUser.status === "active"
                          ? "rgba(34, 197, 94, 0.2)"
                          : "rgba(251, 146, 60, 0.2)"
                      }`,
                      fontWeight: 500,
                    }}
                  />
                  {clientUser.lastLogin && (
                    <Chip
                      icon={<ScheduleIcon />}
                      label={`Last Login: ${new Date(
                        clientUser.lastLogin
                      ).toLocaleDateString()}`}
                      sx={{
                        background: "rgba(219, 74, 65, 0.1)",
                        color: "#db4a41",
                        border: "1px solid rgba(219, 74, 65, 0.2)",
                        fontWeight: 500,
                      }}
                    />
                  )}
                </Box>
              </CardContent>
            </Card>
          </motion.div>

          {/* Debug Info - Remove this in production
          <Card sx={{ mb: 2, background: "rgba(255, 255, 255, 0.05)" }}>
            <CardContent>
              <Typography variant="h6" sx={{ color: "#fff", mb: 1 }}>
                Debug Info (Remove in production):
              </Typography>
              <Typography variant="body2" sx={{ color: "#fff", opacity: 0.8 }}>
                Events loaded: {events.length}
              </Typography>
              <Typography variant="body2" sx={{ color: "#fff", opacity: 0.8 }}>
                Current month: {currentDate.getMonth() + 1}/
                {currentDate.getFullYear()}
              </Typography>
              {events.length > 0 && (
                <Typography
                  variant="body2"
                  sx={{ color: "#fff", opacity: 0.8 }}
                >
                  Sample event: {events[0].title} (
                  {new Date(events[0].startDate).toLocaleDateString()} -{" "}
                  {new Date(events[0].endDate).toLocaleDateString()})
                </Typography>
              )}
            </CardContent>
          </Card> */}

          {/* Calendar Section */}
          {clientUser.permissions && clientUser.permissions.viewCalendar && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card
                sx={{
                  background: theme.cardBackground,
                  backdropFilter: "blur(20px)",
                  border: theme.border,
                  borderRadius: { xs: "16px", sm: "24px" },
                  mb: { xs: 3, sm: 4 },
                  boxShadow: darkMode
                    ? "0 20px 40px rgba(0, 0, 0, 0.3)"
                    : "0 20px 40px rgba(0, 0, 0, 0.08)",
                  overflow: "hidden",
                }}
              >
                <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: { xs: "center", sm: "center" },
                      justifyContent: "space-between",
                      mb: { xs: 3, sm: 4 },
                      flexDirection: { xs: "column", sm: "row" },
                      gap: { xs: 2, sm: 0 },
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: { xs: 1.5, sm: 2 },
                      }}
                    >
                      <Box
                        sx={{
                          width: { xs: 40, sm: 48 },
                          height: { xs: 40, sm: 48 },
                          borderRadius: "12px",
                          background:
                            "linear-gradient(135deg, #db4a41, #ff6b5b)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          boxShadow: "0 8px 16px rgba(219, 74, 65, 0.3)",
                        }}
                      >
                        {calendarView === "timeline" ? (
                          <CalendarIcon
                            sx={{
                              color: "white",
                              fontSize: { xs: 20, sm: 24 },
                            }}
                          />
                        ) : (
                          <PostAddIcon
                            sx={{
                              color: "white",
                              fontSize: { xs: 20, sm: 24 },
                            }}
                          />
                        )}
                      </Box>
                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: theme.textPrimary,
                            fontWeight: "bold",
                            letterSpacing: "0.5px",
                            fontSize: { xs: "1.25rem", sm: "1.5rem" },
                          }}
                        >
                          {calendarView === "timeline"
                            ? "Project Timeline"
                            : "Content Schedule"}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.textSecondary,
                            fontSize: "0.9rem",
                          }}
                        >
                          {calendarView === "timeline"
                            ? "Track your project milestones and deadlines"
                            : "View your upcoming social media posts"}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          backgroundColor: darkMode
                            ? "rgba(51, 65, 85, 0.8)"
                            : "rgba(248, 250, 252, 0.8)",
                          borderRadius: "12px",
                          padding: "4px",
                          border: theme.border,
                        }}
                      >
                        <IconButton
                          onClick={handlePrevMonth}
                          sx={{
                            color: theme.textSecondary,
                            width: { xs: 40, sm: 48 },
                            height: { xs: 40, sm: 48 },
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                            "&:active": {
                              transform: "scale(0.95)",
                            },
                          }}
                        >
                          <ChevronLeftIcon
                            sx={{ fontSize: { xs: 20, sm: 24 } }}
                          />
                        </IconButton>
                        <Typography
                          variant="h6"
                          sx={{
                            color: theme.textPrimary,
                            minWidth: "180px",
                            textAlign: "center",
                            fontFamily: "Formula Bold",
                            fontWeight: 600,
                            px: 2,
                          }}
                        >
                          {currentDate.toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })}
                        </Typography>
                        <IconButton
                          onClick={handleNextMonth}
                          sx={{
                            color: theme.textSecondary,
                            width: { xs: 40, sm: 48 },
                            height: { xs: 40, sm: 48 },
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                            "&:active": {
                              transform: "scale(0.95)",
                            },
                          }}
                        >
                          <ChevronRightIcon
                            sx={{ fontSize: { xs: 20, sm: 24 } }}
                          />
                        </IconButton>
                      </Box>
                    </Box>
                  </Box>

                  {/* Calendar View Toggle */}
                  <Box
                    sx={{
                      mb: { xs: 3, sm: 4 },
                      display: "flex",
                      justifyContent: "center",
                      px: { xs: 1, sm: 0 },
                    }}
                  >
                    <ToggleButtonGroup
                      value={calendarView}
                      exclusive
                      onChange={(_, newView) => {
                        if (newView !== null) {
                          setCalendarView(newView);
                        }
                      }}
                      sx={{
                        backgroundColor: darkMode
                          ? "rgba(51, 65, 85, 0.8)"
                          : "rgba(248, 250, 252, 0.8)",
                        borderRadius: "16px",
                        padding: "4px",
                        border: theme.border,
                        "& .MuiToggleButton-root": {
                          color: theme.textSecondary,
                          border: "none",
                          borderRadius: "12px",
                          px: { xs: 2, sm: 3 },
                          py: { xs: 1, sm: 1.5 },
                          fontWeight: 600,
                          textTransform: "none",
                          fontSize: { xs: "0.85rem", sm: "0.95rem" },
                          minWidth: { xs: "auto", sm: "auto" },
                          "&.Mui-selected": {
                            backgroundColor: "#db4a41",
                            color: "#fff",
                            boxShadow: "0 4px 12px rgba(219, 74, 65, 0.3)",
                            "&:hover": {
                              backgroundColor: "#c62828",
                            },
                          },
                          "&:hover": {
                            backgroundColor: "rgba(219, 74, 65, 0.1)",
                          },
                        },
                      }}
                    >
                      <ToggleButton value="timeline">
                        <TimelineIcon
                          sx={{
                            mr: { xs: 0.5, sm: 1 },
                            fontSize: { xs: 18, sm: 20 },
                          }}
                        />
                        <Box sx={{ display: { xs: "none", sm: "block" } }}>
                          Project Timeline
                        </Box>
                        <Box sx={{ display: { xs: "block", sm: "none" } }}>
                          Timeline
                        </Box>
                      </ToggleButton>
                      <ToggleButton value="posts">
                        <PostAddIcon
                          sx={{
                            mr: { xs: 0.5, sm: 1 },
                            fontSize: { xs: 18, sm: 20 },
                          }}
                        />
                        <Box sx={{ display: { xs: "none", sm: "block" } }}>
                          Content Schedule
                        </Box>
                        <Box sx={{ display: { xs: "block", sm: "none" } }}>
                          Posts
                        </Box>
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>

                  {/* Event Type Filter - Only show for timeline view */}
                  {calendarView === "timeline" && (
                    <Box sx={{ mb: 3 }}>
                      <FormControl
                        variant="outlined"
                        sx={{
                          width: 250,
                          "& .MuiOutlinedInput-root": {
                            "& fieldset": {
                              borderColor: darkMode
                                ? "rgba(255, 255, 255, 0.3)"
                                : "rgba(0, 0, 0, 0.3)",
                            },
                            "&:hover fieldset": {
                              borderColor: darkMode
                                ? "rgba(255, 255, 255, 0.5)"
                                : "rgba(0, 0, 0, 0.5)",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "#db4a41",
                            },
                          },
                          "& .MuiInputLabel-root": {
                            color: theme.textSecondary, // ✅ dynamic color
                          },
                          "& .MuiSelect-icon": {
                            color: theme.textSecondary, // ✅ dynamic color
                          },
                          "& .MuiSelect-select": {
                            color: theme.textPrimary, // ✅ dynamic color
                          },
                        }}
                      >
                        <InputLabel id="event-type-select-label">
                          Filter by Event Type
                        </InputLabel>
                        <Select
                          labelId="event-type-select-label"
                          id="event-type-select"
                          value={selectedEventType}
                          label="Filter by Event Type"
                          onChange={(e) => setSelectedEventType(e.target.value)}
                        >
                          {eventTypes.map((type) => (
                            <MenuItem key={type} value={type}>
                              {type === "all" ? "All Types" : type}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                  )}

                  {loadingEvents || loadingPosts ? (
                    <Box
                      sx={{ display: "flex", justifyContent: "center", py: 4 }}
                    >
                      <CircularProgress sx={{ color: "#db4a41" }} />
                    </Box>
                  ) : (
                    <>
                      {/* Calendar Header */}
                      <Grid container sx={{ mb: 3 }}>
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day) => (
                            <Grid item xs={12 / 7} key={day}>
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  textAlign: "center",
                                  color: theme.textSecondary,
                                  fontFamily: "Formula Bold",
                                  fontWeight: 600,
                                  py: 2,
                                  fontSize: "0.9rem",
                                  letterSpacing: "0.5px",
                                }}
                              >
                                {day}
                              </Typography>
                            </Grid>
                          )
                        )}
                      </Grid>

                      {/* Calendar Grid */}
                      <Grid container spacing={{ xs: 0.5, sm: 1 }}>
                        {getDaysInMonth(currentDate).map((day, index) => {
                          const dayItems =
                            calendarView === "timeline"
                              ? getEventsForDate(day)
                              : getPostsForDate(day);
                          const isToday =
                            day === new Date().getDate() &&
                            currentDate.getMonth() === new Date().getMonth() &&
                            currentDate.getFullYear() ===
                              new Date().getFullYear();

                          return (
                            <Grid item xs={12 / 7} key={index}>
                              <Box
                                onClick={() => handleDayClick(day)}
                                sx={{
                                  height: {
                                    xs: "80px",
                                    sm: "100px",
                                    md: "120px",
                                  },
                                  borderRadius: { xs: "12px", sm: "16px" },
                                  p: { xs: 1, sm: 1.5, md: 2 },
                                  m: { xs: 0.25, sm: 0.5 },
                                  background: isToday
                                    ? "linear-gradient(135deg, rgba(219, 74, 65, 0.15), rgba(255, 107, 91, 0.1))"
                                    : dayItems.length > 0
                                    ? theme.calendarDayBackgroundHover
                                    : theme.calendarDayBackground,
                                  position: "relative",
                                  cursor:
                                    dayItems.length > 0 ? "pointer" : "default",
                                  border: isToday
                                    ? "2px solid #db4a41"
                                    : dayItems.length > 0
                                    ? "2px solid rgba(219, 74, 65, 0.2)"
                                    : theme.border,
                                  transition: "all 0.2s ease",
                                  // Enhanced mobile highlighting for days with events/posts
                                  ...(dayItems.length > 0 && {
                                    boxShadow: {
                                      xs: "0 0 0 2px rgba(219, 74, 65, 0.3), 0 4px 12px rgba(219, 74, 65, 0.2)",
                                      sm: "0 2px 8px rgba(219, 74, 65, 0.1)",
                                      md: "0 2px 8px rgba(219, 74, 65, 0.1)",
                                    },
                                    "&::before": {
                                      content: `"${dayItems.length}"`,
                                      position: "absolute",
                                      top: { xs: 4, sm: 6 },
                                      right: { xs: 4, sm: 6 },
                                      width: { xs: 16, sm: 12 },
                                      height: { xs: 16, sm: 12 },
                                      borderRadius: "50%",
                                      background:
                                        "linear-gradient(135deg, #db4a41, #ff6b5b)",
                                      display: { xs: "flex", sm: "none" },
                                      alignItems: "center",
                                      justifyContent: "center",
                                      fontSize: { xs: "0.6rem", sm: "0.5rem" },
                                      fontWeight: "bold",
                                      color: "white",
                                      zIndex: 10,
                                      boxShadow:
                                        "0 2px 4px rgba(219, 74, 65, 0.4)",
                                    },
                                  }),
                                  "&:hover": {
                                    background:
                                      dayItems.length > 0
                                        ? "rgba(219, 74, 65, 0.1)"
                                        : theme.calendarDayBackgroundHover,
                                    transform:
                                      dayItems.length > 0
                                        ? "translateY(-2px)"
                                        : "none",
                                    boxShadow:
                                      dayItems.length > 0
                                        ? {
                                            xs: "0 0 0 3px rgba(219, 74, 65, 0.4), 0 6px 16px rgba(219, 74, 65, 0.3)",
                                            sm: "0 8px 20px rgba(219, 74, 65, 0.2)",
                                            md: "0 8px 20px rgba(219, 74, 65, 0.2)",
                                          }
                                        : darkMode
                                        ? "0 4px 12px rgba(0, 0, 0, 0.3)"
                                        : "0 4px 12px rgba(0, 0, 0, 0.1)",
                                  },
                                  "&:active": {
                                    transform:
                                      dayItems.length > 0
                                        ? "scale(0.98)"
                                        : "none",
                                  },
                                  overflowY: "auto",
                                }}
                              >
                                {day && (
                                  <>
                                    <Typography
                                      variant="body1"
                                      sx={{
                                        color: isToday
                                          ? "#db4a41"
                                          : theme.textPrimary,
                                        fontWeight: "bold",
                                        mb: { xs: 0.5, sm: 1 },
                                        fontSize: {
                                          xs: "0.9rem",
                                          sm: "1rem",
                                          md: "1.1rem",
                                        },
                                        fontFamily: "Formula Bold",
                                      }}
                                    >
                                      {day}
                                    </Typography>
                                    {dayItems
                                      .slice(0, 2)
                                      .map((item, itemIndex) => (
                                        <Box
                                          key={itemIndex}
                                          sx={{
                                            background:
                                              "linear-gradient(135deg, #db4a41, #ff6b5b)",
                                            borderRadius: {
                                              xs: "8px",
                                              sm: "12px",
                                            },
                                            p: { xs: 1, sm: 1.5 },
                                            mb: { xs: 0.5, sm: 1 },
                                            cursor: "pointer",
                                            boxShadow:
                                              "0 2px 8px rgba(219, 74, 65, 0.3)",
                                            transition: "all 0.2s ease",
                                            minHeight: {
                                              xs: "32px",
                                              sm: "auto",
                                            },
                                            display: "flex",
                                            alignItems: "center",
                                            "&:hover": {
                                              transform: "translateY(-1px)",
                                              boxShadow:
                                                "0 4px 12px rgba(219, 74, 65, 0.4)",
                                            },
                                            "&:active": {
                                              transform: "scale(0.98)",
                                            },
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setSelectedDate(day);
                                            if (calendarView === "timeline") {
                                              setSelectedDateEvents([item]);
                                              setEventModalOpen(true);
                                            } else {
                                              setSelectedDatePosts([item]);
                                              setPostModalOpen(true);
                                            }
                                          }}
                                        >
                                          {" "}
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              color: "#fff",
                                              fontSize: {
                                                xs: "0.7rem",
                                                sm: "0.75rem",
                                              },
                                              fontWeight: 600,
                                              display: {
                                                xs: "none",
                                                sm: "block",
                                              }, // 👈 Hide on mobile (xs)
                                              overflow: "hidden",
                                              textOverflow: "ellipsis",
                                              whiteSpace: "nowrap",
                                              mb: { xs: 0, sm: 0.5 },
                                              lineHeight: 1.2,
                                              flex: 1,
                                            }}
                                          >
                                            {calendarView === "timeline"
                                              ? item.title
                                              : item.name}
                                          </Typography>
                                          <Chip
                                            label={
                                              calendarView === "timeline"
                                                ? item.type || "shooting"
                                                : "Post"
                                            }
                                            size="small"
                                            sx={{
                                              backgroundColor:
                                                "rgba(255, 255, 255, 0.2)",
                                              color: "#fff",
                                              fontSize: "0.65rem",
                                              height: "20px",
                                              fontWeight: 600,
                                              display: {
                                                xs: "none",
                                                sm: "inline-flex",
                                              },
                                              "& .MuiChip-label": {
                                                px: 1,
                                              },
                                            }}
                                          />
                                        </Box>
                                      ))}
                                    {dayItems.length > 2 && (
                                      <Chip
                                        label={`+${dayItems.length - 2} more`}
                                        size="small"
                                        sx={{
                                          backgroundColor:
                                            "rgba(219, 74, 65, 0.1)",
                                          color: "#db4a41",
                                          fontSize: {
                                            xs: "0.6rem",
                                            sm: "0.65rem",
                                          },
                                          height: { xs: "18px", sm: "20px" },
                                          fontWeight: 600,
                                          border:
                                            "1px solid rgba(219, 74, 65, 0.3)",
                                          cursor: "pointer",
                                          transition: "all 0.2s ease",
                                          "&:hover": {
                                            backgroundColor:
                                              "rgba(219, 74, 65, 0.2)",
                                            transform: "scale(1.05)",
                                          },
                                          "&:active": {
                                            transform: "scale(0.95)",
                                          },
                                          "& .MuiChip-label": {
                                            px: { xs: 0.5, sm: 1 },
                                          },
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDayClick(day);
                                        }}
                                      />
                                    )}
                                  </>
                                )}
                              </Box>
                            </Grid>
                          );
                        })}
                      </Grid>
                    </>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}
        </Container>

        {/* Event Modal */}
        <Dialog
          open={eventModalOpen}
          onClose={handleCloseEventModal}
          maxWidth="md"
          fullWidth
          fullScreen={{ xs: true, sm: false }}
          PaperProps={{
            sx: {
              background: theme.modalBackground,
              backdropFilter: "blur(20px)",
              border: theme.border,
              borderRadius: { xs: 0, sm: "24px" },
              boxShadow: darkMode
                ? "0 20px 40px rgba(0, 0, 0, 0.5)"
                : "0 20px 40px rgba(0, 0, 0, 0.15)",
              m: { xs: 0, sm: 2 },
              maxHeight: { xs: "100vh", sm: "90vh" },
            },
          }}
        >
          <DialogTitle
            sx={{
              color: theme.textPrimary,
              fontFamily: "Formula Bold",
              borderBottom: theme.border,
              pb: 3,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 8px 16px rgba(219, 74, 65, 0.3)",
                }}
              >
                <EventIcon sx={{ color: "white", fontSize: 24 }} />
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  sx={{ fontWeight: "bold", mb: 0.5, color: theme.textPrimary }}
                >
                  Timeline Events
                </Typography>
                <Typography variant="body2" sx={{ color: theme.textSecondary }}>
                  {selectedDate &&
                    new Date(
                      currentDate.getFullYear(),
                      currentDate.getMonth(),
                      selectedDate
                    ).toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                </Typography>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ p: 3, pt: 0 }}>
            {selectedDateEvents.length > 0 ? (
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                {selectedDateEvents.map((event, index) => (
                  <Card
                    key={index}
                    sx={{
                      mt: 2,
                      background: "transparent",
                      border: 0,
                      boxShadow: "none",
                    }}
                  >
                    <CardContent sx={{ p: 2, mt: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: theme.textPrimary,
                          fontFamily: "Formula Bold",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        {event.title}
                      </Typography>{" "}
                      {event.type && (
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.textPrimary,
                            opacity: 0.8,
                            mb: 2,
                          }}
                        >
                          {event.type}
                        </Typography>
                      )}
                      {event.description && (
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.textPrimary,
                            opacity: 0.8,
                            mb: 2,
                          }}
                        >
                          {event.description}
                        </Typography>
                      )}
                      <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                        <Chip
                          label={`Start: ${new Date(
                            event.startDate
                          ).toLocaleDateString()}`}
                          sx={{
                            background: "rgba(76, 175, 80, 0.2)",
                            color: "#4caf50",
                            border: "1px solid rgba(76, 175, 80, 0.3)",
                          }}
                        />
                        <Chip
                          label={`End: ${new Date(
                            event.endDate
                          ).toLocaleDateString()}`}
                          sx={{
                            background: "rgba(244, 67, 54, 0.2)",
                            color: "#f44336",
                            border: "1px solid rgba(244, 67, 54, 0.3)",
                          }}
                        />
                        {event.status && (
                          <Chip
                            label={`Status: ${event.status}`}
                            sx={{
                              background: "rgba(255, 152, 0, 0.2)",
                              color: "#ff9800",
                              border: "1px solid rgba(255, 152, 0, 0.3)",
                            }}
                          />
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Typography
                variant="body1"
                sx={{
                  color: "#000",
                  textAlign: "center",
                  py: 4,
                }}
              >
                No events found for this date.
              </Typography>
            )}
          </DialogContent>
          <DialogActions
            sx={{
              borderTop: "1px solid rgba(255, 255, 255, 0.1)",
              p: 2,
            }}
          >
            <Button
              onClick={handleCloseEventModal}
              sx={{
                color: "#db4a41",
                border: "1px solid rgba(219, 74, 65, 0.3)",
                "&:hover": {
                  background: "rgba(219, 74, 65, 0.1)",
                },
              }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Post Modal */}
        <Dialog
          open={postModalOpen}
          onClose={handleClosePostModal}
          maxWidth="md"
          fullWidth
          fullScreen={{ xs: true, sm: false }}
          PaperProps={{
            sx: {
              background: theme.modalBackground,
              backdropFilter: "blur(20px)",
              border: theme.border,
              borderRadius: { xs: 0, sm: "24px" },
              boxShadow: darkMode
                ? "0 20px 40px rgba(0, 0, 0, 0.5)"
                : "0 20px 40px rgba(0, 0, 0, 0.15)",
              m: { xs: 0, sm: 2 },
              maxHeight: { xs: "100vh", sm: "60vh" },
              maxWidth: { xs: "100vw", sm: "600px" },
            },
          }}
        >
          <DialogTitle
            sx={{
              color: theme.textPrimary,
              fontFamily: "Formula Bold",
              borderBottom: theme.border,
              pb: 3,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 8px 16px rgba(219, 74, 65, 0.3)",
                }}
              >
                <PostAddIcon sx={{ color: "white", fontSize: 24 }} />
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  sx={{ fontWeight: "bold", mb: 0.5, color: theme.textPrimary }}
                >
                  Content Schedule
                </Typography>
                <Typography variant="body2" sx={{ color: theme.textSecondary }}>
                  {selectedDate &&
                    new Date(
                      currentDate.getFullYear(),
                      currentDate.getMonth(),
                      selectedDate
                    ).toLocaleDateString("en-US", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                </Typography>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ p: 3, mt: 2 }}>
            {selectedDatePosts.length > 0 ? (
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                {selectedDatePosts.map((post, index) => (
                  <Card
                    key={index}
                    sx={{
                      background: "transparent",
                      border: "none",
                      boxShadow: "none",
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: theme.textPrimary,
                          fontFamily: "Formula Bold",
                          mb: 1,
                          letterSpacing: "0.5px",
                        }}
                      >
                        {post.name}
                      </Typography>
                      {post.caption && (
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.textPrimary,
                            opacity: 0.8,
                            mb: 2,
                            fontStyle: "italic",
                          }}
                        >
                          "{post.caption}"
                        </Typography>
                      )}
                      {post.driveLink && (
                        <Typography
                          variant="body2"
                          sx={{
                            color: theme.textPrimary,
                            opacity: 0.8,
                            mb: 2,
                            fontStyle: "italic",
                          }}
                        >
                          <Link
                            href={post.driveLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            underline="hover"
                            sx={{ color: theme.textPrimary }}
                          >
                            {post.driveLink}
                          </Link>
                        </Typography>
                      )}
                      <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
                        <Chip
                          label={`Date: ${new Date(
                            post.date
                          ).toLocaleDateString()}`}
                          sx={{
                            background: "rgba(76, 175, 80, 0.2)",
                            color: "#4caf50",
                            border: "1px solid rgba(76, 175, 80, 0.3)",
                          }}
                        />
                        <Chip
                          label={`Time: ${new Date(
                            post.date
                          ).toLocaleTimeString()}`}
                          sx={{
                            background: "rgba(33, 150, 243, 0.2)",
                            color: "#2196f3",
                            border: "1px solid rgba(33, 150, 243, 0.3)",
                          }}
                        />
                        <Chip
                          label="Scheduled Post"
                          sx={{
                            background: "rgba(255, 152, 0, 0.2)",
                            color: "#ff9800",
                            border: "1px solid rgba(255, 152, 0, 0.3)",
                          }}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Typography
                variant="body1"
                sx={{
                  color: "#fff",
                  textAlign: "center",
                  py: 4,
                }}
              >
                No posts scheduled for this date.
              </Typography>
            )}
          </DialogContent>
          <DialogActions
            sx={{
              borderTop: "1px solid rgba(255, 255, 255, 0.1)",
              p: 2,
            }}
          >
            <Button
              onClick={handleClosePostModal}
              sx={{
                color: "#db4a41",
                border: "1px solid rgba(219, 74, 65, 0.3)",
                "&:hover": {
                  background: "rgba(219, 74, 65, 0.1)",
                },
              }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Profile Modal */}
        <Dialog
          open={profileModalOpen}
          onClose={handleProfileModalClose}
          maxWidth="md"
          fullWidth
          fullScreen={{ xs: true, sm: false }}
          PaperProps={{
            sx: {
              background: theme.modalBackground,
              backdropFilter: "blur(20px)",
              border: theme.border,
              borderRadius: { xs: 0, sm: "24px" },
              boxShadow: darkMode
                ? "0 20px 40px rgba(0, 0, 0, 0.5)"
                : "0 20px 40px rgba(0, 0, 0, 0.15)",
              m: { xs: 0, sm: 2 },
              maxHeight: { xs: "100vh", sm: "90vh" },
            },
          }}
        >
          <DialogTitle
            sx={{
              color: theme.textPrimary,
              fontFamily: "Formula Bold",
              borderBottom: theme.border,
              pb: 3,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 1 }}>
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: "12px",
                  background: "linear-gradient(135deg, #db4a41, #ff6b5b)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 8px 16px rgba(219, 74, 65, 0.3)",
                }}
              >
                <PersonIcon sx={{ color: "white", fontSize: 24 }} />
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  sx={{ fontWeight: "bold", mb: 0.5, color: theme.textPrimary }}
                >
                  Profile Information
                </Typography>
                <Typography variant="body2" sx={{ color: theme.textSecondary }}>
                  Your account details and client information
                </Typography>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ p: 4, mt: 2 }}>
            <Grid container spacing={3}>
              {/* Personal Information */}
              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    background: darkMode
                      ? "rgba(51, 65, 85, 0.5)"
                      : "rgba(248, 250, 252, 0.8)",
                    border: theme.border,
                    borderRadius: "16px",
                    p: 3,
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      color: theme.textPrimary,
                      fontFamily: "Formula Bold",
                      mb: 2,
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                    }}
                  >
                    <PersonIcon sx={{ color: "#db4a41" }} />
                    Personal Information
                  </Typography>
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 2 }}
                  >
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Full Name
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: theme.textPrimary, fontWeight: 600 }}
                      >
                        {clientUser.name}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Email Address
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: theme.textPrimary, fontWeight: 600 }}
                      >
                        {clientUser.email}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Phone Number
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: theme.textPrimary, fontWeight: 600 }}
                      >
                        {clientUser.phone || "Not provided"}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Company
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: theme.textPrimary, fontWeight: 600 }}
                      >
                        {clientUser.company}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Account Status
                      </Typography>
                      <Chip
                        label={clientUser.status}
                        size="small"
                        sx={{
                          backgroundColor:
                            clientUser.status === "active"
                              ? "rgba(34, 197, 94, 0.1)"
                              : "rgba(251, 146, 60, 0.1)",
                          color:
                            clientUser.status === "active"
                              ? "#22c55e"
                              : "#fb923c",
                          border: `1px solid ${
                            clientUser.status === "active"
                              ? "rgba(34, 197, 94, 0.2)"
                              : "rgba(251, 146, 60, 0.2)"
                          }`,
                          fontWeight: 600,
                          textTransform: "capitalize",
                        }}
                      />
                    </Box>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: theme.textSecondary, mb: 0.5 }}
                      >
                        Last Login
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: theme.textPrimary, fontWeight: 600 }}
                      >
                        {clientUser.lastLogin
                          ? new Date(clientUser.lastLogin).toLocaleString()
                          : "Never"}
                      </Typography>
                    </Box>
                  </Box>
                </Card>
              </Grid>

              {/* Client Information */}
              <Grid item xs={12} md={6}>
                <Card
                  sx={{
                    background: darkMode
                      ? "rgba(51, 65, 85, 0.5)"
                      : "rgba(248, 250, 252, 0.8)",
                    border: theme.border,
                    borderRadius: "16px",
                    p: 3,
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      color: theme.textPrimary,
                      fontFamily: "Formula Bold",
                      mb: 2,
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                    }}
                  >
                    <DashboardIcon sx={{ color: "#db4a41" }} />
                    Client Details
                  </Typography>
                  {clientUser.clientId && (
                    <Box
                      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
                    >
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ color: theme.textSecondary, mb: 0.5 }}
                        >
                          Client Name
                        </Typography>

                        <Typography
                          variant="body1"
                          sx={{ color: theme.textPrimary, fontWeight: 600 }}
                        >
                          {clientUser.clientId.name}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ color: theme.textSecondary, mb: 0.5 }}
                        >
                          Client Email
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{ color: theme.textPrimary, fontWeight: 600 }}
                        >
                          {clientUser.clientId.email}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ color: theme.textSecondary, mb: 0.5 }}
                        >
                          Address
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{ color: theme.textPrimary, fontWeight: 600 }}
                        >
                          {clientUser.clientId.address || "Not provided"}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ color: theme.textSecondary, mb: 0.5 }}
                        >
                          Description
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{ color: theme.textPrimary, fontWeight: 600 }}
                        >
                          {clientUser.clientId.description || "No description"}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ color: theme.textSecondary, mb: 0.5 }}
                        >
                          Account Type
                        </Typography>
                        <Chip
                          label={clientUser.clientId.type || "Standard"}
                          size="small"
                          sx={{
                            backgroundColor: "rgba(59, 130, 246, 0.1)",
                            color: "#3b82f6",
                            border: "1px solid rgba(59, 130, 246, 0.2)",
                            fontWeight: 600,
                            textTransform: "capitalize",
                          }}
                        />
                      </Box>
                      {clientUser.clientId.website && (
                        <Box>
                          <Typography
                            variant="body2"
                            sx={{ color: theme.textSecondary, mb: 0.5 }}
                          >
                            Website
                          </Typography>
                          <Typography
                            variant="body1"
                            sx={{
                              color: "#db4a41",
                              fontWeight: 600,
                              cursor: "pointer",
                              "&:hover": { textDecoration: "underline" },
                            }}
                            onClick={() =>
                              window.open(
                                `https://${clientUser.clientId.website}`,
                                "_blank"
                              )
                            }
                          >
                            {clientUser.clientId.website}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  )}
                </Card>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{
              borderTop: theme.border,
              p: 3,
            }}
          >
            <Button
              onClick={handleProfileModalClose}
              sx={{
                color: "#db4a41",
                border: "1px solid rgba(219, 74, 65, 0.3)",
                borderRadius: "12px",
                px: 3,
                py: 1,
                fontWeight: 600,
                "&:hover": {
                  background: "rgba(219, 74, 65, 0.1)",
                },
              }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ClientDashboard;
