import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import BlurText from "./animations/BlurText";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import galleryData from "../data/gallery1.json";
import { Box, Typography } from "@mui/material";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { SlideIn } from "./animations/slideIn";
import ScrollRevealText from "./animations/scrollRevealText";

function Careers() {
  const [slidesToShow, setSlidesToShow] = useState(3);
  const [slidesToScroll, setSlidesToScroll] = useState(1);
  const [positions, setPositions] = useState([]);
  const [loading, setLoading] = useState(true);

  const slickSettings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: slidesToShow,
    slidesToScroll: slidesToScroll,
    centerMode: true,
    autoplay: true,
    autoplaySpeed: 2000,
  };

  useEffect(() => {
    const handleResize = () => {
      const newSlidesToShow = window.innerWidth < 768 ? 1 : 2.5;
      const newSlidesToScroll = window.innerWidth < 768 ? 1 : 2;

      if (newSlidesToShow !== slidesToShow) {
        setSlidesToShow(newSlidesToShow);
      }

      if (newSlidesToScroll !== slidesToScroll) {
        setSlidesToScroll(newSlidesToScroll);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [slidesToShow, slidesToScroll]);

  // Fetch jobs from API
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const res = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/jobs"
        );
        if (!res.ok) throw new Error("Failed to fetch jobs");
        const data = await res.json();
        setPositions(data);
      } catch (err) {
        console.error("Error fetching jobs:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  return (
    <div
      style={{
        backgroundColor: "#000",
        color: "white",
        display: "flex",
        flexDirection: "column",
        gap: "100px",
        width: "100%",
        overflowX: "hidden",
      }}
    >
      {/* INTRO TEXT */}
      <Box
        sx={{
          px: { xs: 3, md: 10 },
          pt: 16,
          maxWidth: "100%",
          overflow: "hidden",
        }}
      >
        {" "}
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="We believe creativity blossoms"
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#fff", "#fff", "#fff", "#fff", "#fff"]}
          />
        </Box>
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="we're at ease and amongst friends. "
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={[
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
            ]}
          />
        </Box>
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "100px",
            },
            color: "#fff",
            marginBottom: "-0.8em",
            lineHeight: 1.9,
          }}
        >
          <BlurText
            text="Producing work the world needs."
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={[
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
              "#fff",
            ]}
          />
        </Box>
        <Box
          sx={{
            fontFamily: "Formula Bold",
            fontSize: {
              xs: "42px",
              sm: "56px",
              md: "72px",
              lg: "130px",
            },
            color: "#fff",
            lineHeight: 1.6,
          }}
        >
          <BlurText
            text="come work with us."
            delay={100}
            animateBy="words"
            direction="top"
            colorMap={["#fff", "#fff", "#fff", "#fff", "#fff", "#fff", "#fff"]}
          />
        </Box>
      </Box>
      <Box sx={{ width: "100%", maxWidth: "100%", margin: "0 auto", px: 3 }}>
        {" "}
        <Slider {...slickSettings}>
          {galleryData.map((photo, index) => (
            <div key={index}>
              <img
                src={photo.src}
                alt={index + 1}
                style={{ borderRadius: "15px", width: "100%" }}
              />
            </div>
          ))}
        </Slider>
      </Box>
      {/* 🔥 LIFE AT YOUNG PRODUCTIONS (DEI STYLE SECTION) */}
      <Box
        sx={{
          width: "80%",
          margin: "0 auto",
          display: "grid",
          gridTemplateColumns: { xs: "1fr", md: "40% 60%" },
          padding: { xs: "50px 0px", md: "120px 0px" },
          gap: { xs: "40px", md: "80px" },
          backgroundColor: "#000",
          color: "white",
        }}
      >
        {/* LEFT BIG TITLE */}
        <SlideIn from="left">
          <Typography
            variant="h1"
            sx={{
              fontFamily: "Formula Bold",
              fontSize: { xs: "3rem", md: "10rem" },
              lineHeight: 1,
              textTransform: "uppercase",
              letterSpacing: "0.2rem",
            }}
          >
            Life as a <br />
            <span style={{ color: "#db4a41" }}>Young</span>
          </Typography>
        </SlideIn>

        {/* RIGHT DESCRIPTION */}
        <SlideIn from="right">
          {" "}
          <Box
            sx={{
              alignSelf: "center",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography
              variant="h5"
              sx={{
                fontFamily: "Anton",
                fontWeight: 400,
                lineHeight: 1.4,
                fontSize: { xs: "1rem", md: "2rem" },
                color: "white",
                marginBottom: "40px",
              }}
            >
              <ScrollRevealText>
                We are truly committed to fostering an environment where
                creativity, bold thinking and personal growth thrive. Our
                culture values collaboration, individuality and passion,
                ensuring that everyone who joins our family is equipped to
                create work they are proud of.
                <br />
                <br />
                Our diverse team brings unique perspectives that fuel
                innovation, storytelling and world-class production. Together,
                we push the boundaries of what's possible.
                <br />
                <br />
                We are truly committed to fostering an environment where
                creativity, bold thinking and personal growth thrive. Our
                culture values collaboration, individuality and passion,
                ensuring that everyone who joins our family is equipped to
                create work they are proud of.
                <br />
                <br />
                Our diverse team brings unique perspectives that fuel
                innovation, storytelling and world-class production. Together,
                we push the boundaries of what's possible.
              </ScrollRevealText>
            </Typography>
          </Box>
        </SlideIn>
      </Box>

      <Box sx={{ width: "80%", padding: "50px 0", margin: "0 auto" }}>
        <Typography
          variant="h2"
          sx={{ fontFamily: "Formula Bold", marginBottom: "20px" }}
        >
          Open Positions
        </Typography>
        {loading ? (
          <Typography sx={{ marginTop: 2 }}>Loading positions...</Typography>
        ) : positions.length === 0 ? (
          <Typography sx={{ marginTop: 2 }}>
            No open positions available.
          </Typography>
        ) : (
          <Box
            component="ul"
            sx={{
              listStyle: "none",
              padding: 0,
              display: "flex",
              flexDirection: "column",
              gap: "20px",
              marginTop: "30px",
            }}
          >
            {positions.map((position, index) => (
              <SlideIn from="left" key={index}>
                <li key={position._id || index}>
                  <Link
                    to={`/careers-form/${position._id}`}
                    className="position-link"
                    style={{
                      textDecoration: "none",
                      color: "black",
                      display: "flex",
                      justifyContent: "space-between",
                      padding: "20px 30px",
                      cursor: "pointer",
                      background: "#fff",
                      backdropFilter: "blur(25px)",
                      WebkitBackdropFilter: "blur(25px)",
                      borderRadius: "15px",
                    }}
                  >
                    <Box>
                      <Typography variant="body1" sx={{ color: "#777777" }}>
                        {position.jobType} | {position.department}
                      </Typography>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold" }}
                      >
                        {position.title}
                      </Typography>
                    </Box>
                    <Box sx={{ alignSelf: "center" }}>
                      <ArrowForwardIcon sx={{ fontSize: "2.5rem" }} />
                    </Box>
                  </Link>
                </li>
              </SlideIn>
            ))}
          </Box>
        )}
      </Box>
    </div>
  );
}

export default Careers;
