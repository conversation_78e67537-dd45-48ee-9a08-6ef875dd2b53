import { useState, useRef, useEffect, useMemo } from "react";
import { startTransition } from "react";

/**
 * LazySection - Lazy-loads children when approaching viewport
 * Uses IntersectionObserver with rootMargin to trigger before entering view
 * Supports staggered loading with delay
 */
const LazySection = ({
  children,
  rootMargin = "300px",
  threshold = 0,
  delay = 0,
  placeholder,
  minHeight,
  className = "",
  style = {},
}) => {
  const [shouldRender, setShouldRender] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !shouldRender) {
            // Use startTransition for non-critical rendering
            startTransition(() => {
              if (delay > 0) {
                setTimeout(() => {
                  setShouldRender(true);
                  setTimeout(() => setIsVisible(true), 10);
                }, delay);
              } else {
                setShouldRender(true);
                setTimeout(() => setIsVisible(true), 10);
              }
            });
            observerRef.current?.disconnect();
          }
        });
      },
      { rootMargin, threshold }
    );

    observerRef.current.observe(container);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [rootMargin, threshold, delay, shouldRender]);

  const containerStyle = useMemo(
    () => ({
      minHeight: minHeight || "auto",
      opacity: isVisible ? 1 : 0,
      transition: "opacity 0.3s ease-in-out",
      ...style,
    }),
    [isVisible, minHeight, style]
  );

  return (
    <div ref={containerRef} className={className} style={containerStyle}>
      {shouldRender ? (
        children
      ) : (
        placeholder || (
          <div
            style={{
              width: "100%",
              minHeight: minHeight || "200px",
              backgroundColor: "transparent",
            }}
          />
        )
      )}
    </div>
  );
};

export default LazySection;

