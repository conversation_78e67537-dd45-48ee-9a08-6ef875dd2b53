import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import JobApplicationForm from "./CareersForm";

function CareersFormPage() {
  const { id } = useParams();
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJob = async () => {
      try {
        const res = await fetch(
          `https://youngproductions-768ada043db3.herokuapp.com/api/jobs/${id}`
        );

        if (!res.ok) throw new Error("Failed to fetch job");
        const data = await res.json();
        setJob(data);
      } catch (err) {
        console.error("Error fetching job:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchJob();
  }, [id]);

  if (loading) {
    return <p>Loading job details...</p>;
  }

  if (!job) {
    return <p>Job not found.</p>;
  }

  return (
    <JobApplicationForm
      JobId={job._id} // Pass the job id
      positionName={job.title}
      description={job.description}
      requirements={job.requirements}
      responsibilities={job.responsibilities}
      benefits={job.benefits}
      department={job.department}
      jobType={job.jobType}
      workMode={job.workMode}
      experienceLevel={job.experienceLevel}
      location={job.location}
      country={job.country}
      address={job.address}
      language={job.language}
      applicationDeadline={job.applicationDeadline}
      recruiterEmail={job.recruiterEmail}
    />
  );
}

export default CareersFormPage;
