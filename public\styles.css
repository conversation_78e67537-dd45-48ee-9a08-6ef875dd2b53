@font-face {
  font-family: "Formula Bold";
  src: url("../public/fonts/FormulaFont.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
/* Scrollbar Track */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Scrollbar Thumb */
::-webkit-scrollbar-thumb {
  background-color: #db4a41;
  border-radius: 10px;
}

/* Scrollbar Track Background */
::-webkit-scrollbar-track {
  background-color: #1a1a1a; /* Optional: background color behind thumb */
}

/* Firefox Scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #db4a41 #1a1a1a;
}

/* Navbar CSS Start*/
/* Navbar.css */
.app-bar {
  background-color: transparent !important;
  padding: 10px;
  position: absolute;
  top: 0;
  transition: all 1s ease;
  margin: 0 auto;
}

.app-bar-scrolled {
  transition: all 1s ease;
  color: #000;
}

.app-bar,
.app-bar-scrolled {
  width: 100% !important;
}

.nav-drawer-options {
  display: flex;
  justify-content: space-between;
  align-self: flex-start;
  padding: 30px;
}

.nav-drawer {
  background-color: #db4a41;
  height: 100%;
}

.nav-drawer-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  flex-wrap: wrap;
  padding: 20px;
}

.drawer-social-media {
  text-transform: uppercase;
  color: white;

  & .social-media-item {
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
  }

  & .social-media-item:hover:not(.social-media-item:nth-child(1)) {
    color: rgb(56, 56, 56);
    cursor: pointer;
  }
}
/* Navbar CSS End */
ul {
  padding: 0;
  list-style: none;
}

.btn {
  border-radius: 20px;
  padding: 10px 15px;
  margin-right: 20px;
  background: none;
  transition: all 0.3s;
  text-transform: uppercase;
}

.btn-primary {
  color: white;
  border: 1px solid white;
  &:hover {
    background: #db4a41;
    cursor: pointer;
    border: 1px solid #db4a41;
  }
}

.btn-hover-alt:hover {
  background-color: rgb(56, 56, 56);
  border: 1px solid white;
}

.btn-secondry {
  border: 1px solid black;
  color: black;
  &:hover {
    background: #db4a41;
    cursor: pointer;
    border: 1px solid #db4a41;
    color: white;
  }
}
.btn-third {
  color: #db4a41;
  border: 1px solid #db4a41;
  transition: "color 0.3s ease";
  &:hover {
    background: #fff;
    color: black;
    cursor: pointer;
    border: 1px solid #fff;
    transition: "color 0.3s ease";
  }
}

.link-btn {
  display: block;
  text-decoration: none;
  margin: 30px 0 0 0;
  text-align: center;
}

.hero-container {
  background: black;
  color: white;
  height: 82vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: left;
  flex-direction: column;
  padding: 90px 60px;
  gap: 20px;
}

.hero-text {
  font-size: 3.5rem;
  letter-spacing: 0.1rem;
}

.client-section {
  background-color: black;
  padding: 60px 8%;
  text-align: left;
}

.client-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 3rem;
}

.client-list .client-item {
  max-width: 180px;
}

.client-list .client-item img {
  width: 100%;
}

.service-item {
  border-top: 1px solid #303030;
  border-bottom: 1px solid #303030;
  display: flex;
  justify-content: space-between;
  padding: 30px 0;
  flex-wrap: wrap;
}

.service-item h2 {
  font-size: 5rem;
}

.desktop-teamPhoto {
  display: block;
  width: 70%;
  margin: 0 auto;
}

.mobile-teamPhoto {
  display: none;
}

.contact-form-header {
  font-size: 2.5rem !important;
  color: #db4a41;
  padding: 20px 50px;
}

.contact-form form {
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 50px;
  padding: 0 50px;
  margin: 30px 0;
}

.form-submit {
  width: 200px;
  padding: 10px;
  border-radius: 20px;
  border: 1px solid black;
  background: none;
  font-size: 1rem;
  transition: all 0.2s;
  color: white !important;

  &:hover {
    background: #db4a41;
    color: white !important;
    cursor: pointer;
  }
}

.people-item :is(h5, p) {
  transition: all 0.3s;
}

.people-item:hover :is(h5, p) {
  color: white;
  filter: drop-shadow(1px 1px 2px rgba(255, 255, 255, 0.478));
}

.numbers-item {
  border-top: 1px solid #303030;
  border-bottom: 1px solid #303030;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 0;
  flex-wrap: wrap;
}

.numbers-item h3 {
  color: "white";
  text-align: "left";
  font-family: "Formula Bold";
  font-size: 2rem;
}

.numbers-item h4 {
  font-size: 4rem;
}

.team-info-container {
  transition: opacity 0.3s ease, max-height 0.3s ease;
}

.footer li a {
  color: white;
  text-decoration: none;
  transition: all 0.3s;
  &:hover {
    color: #db4a41;
  }
}

.footer li {
  margin: 5px 0;
}

.footer .footer-social-navigation li {
  display: flex;
  align-items: center;
  font-family: "Anton";
  gap: 10px;
}
.footer-logo {
  width: 100%;
  position: relative;
  bottom: 0;
  animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(244, 244, 244, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 20px rgb(161, 151, 152));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(250, 250, 250, 0.6));
  }
}

/* Testimonial slides */
.slick-list {
  margin: 0 -20px;
}

.slick-slide div {
  padding: 20px;
  position: relative;
}

.slick-slide > div {
  padding: 0 30px 0 30px;
  position: relative;
}

.form {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 70%;
  margin: 40px auto;
  border: 1px solid rgba(0, 0, 0, 0.151);
  box-shadow: 0px 5px 10px lightgray;
  padding: 20px 30px;
  border-radius: 15px;
}

.form .formInput {
  margin: 15px 0;
  width: 100%;
}

.iftar-form img {
  width: 100%;
  border-radius: 15px;
}

.iftar-form .locationCapture {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 50px;
  margin: 40px 0;
}

.careers-form {
  gap: 20px;
  padding: 20px 50px;
  font-family: "Formula Bold";
}

.form-group {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
  font-size: 2rem;
  gap: 10px;
}

.form-group .form-input {
  width: 100%;
  padding: 10px 10px;
}

.form-group .error-message {
  font-size: 1rem;
  color: rgb(226, 1, 1);
}

.form-group .error-message::before {
  content: "* ";
}

.form-group .error-message::after {
  content: " *";
}

.position-link {
  transition: all 0.3s;
  border-radius: 15px;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.205);
}

.position-link:hover {
  background-color: rgba(0, 0, 0, 0.106);
  transform: scale(1.01);
}

.event-page {
  background: black;
}

.event-page .event-info-container {
  margin-top: 5%;
  display: flex;
  justify-content: space-between;
  gap: 200px;
}

.event-page .event-info-container .event-desc-container {
  flex: 3;
}

.event-page .event-info-container .event-sharing-container {
  flex: 2;
}

.event-page .event-info-container .event-social-media {
  display: flex;
  gap: 10px;
}

.event-page .event-info-container .btn-read-more {
  background: none;
  color: white;
  border: none;
  padding: 0;
  margin: 0;
  display: inline;
  color: #db4a41;
}

.gff-teaser-container {
  background-image: url("/assets/gff-desktop.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}

.gff-teaser-video-container {
  position: relative;
  height: 70vh;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  overflow: hidden;
  cursor: pointer;
  /* Indicate that the container is clickable */
}

#gff-teaser-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.gff-teaser-text {
  font-family: Formula Bold !important;
  letter-spacing: 0.2rem !important;
  font-size: 12rem !important;
  filter: drop-shadow(2px 2px 5px rgba(255, 255, 255, 0.462));
  z-index: 1;
  /* Ensure text stays on top of the video */
}

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #db4a41;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 10000;
}

.loading-spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #303030;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
}

.loading-logo {
  width: 20%;
  margin: 30px 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.content {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 100;
  border-radius: 8px;
  background: transparent;
  overflow: visible;
}

.content__img {
  width: 190px;
  aspect-ratio: 1.1;
  border-radius: 15px;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  height: 250px;
  overflow: hidden;
  will-change: transform, filter;
  border: 3.5px solid rgb(219, 74, 65);
}

.content__img-inner {
  background-position: 50% 50%;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  background-size: cover;
  position: absolute;
  top: calc(-1 * 20px / 2);
  left: calc(-1 * 20px / 2);
}
.scroll-reveal {
  margin: 20px 0;
}

.scroll-reveal-text {
  font-size: clamp(1.6rem, 4vw, 3rem);
  line-height: 1.5;
  font-weight: 600;
}

.word {
  display: inline-block;
}
.card-spotlight {
  position: relative;
  overflow: hidden;
  --mouse-x: 50%;
  --mouse-y: 50%;
  --spotlight-color: rgba(255, 255, 255, 0.05);
}

.card-spotlight::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at var(--mouse-x) var(--mouse-y),
    var(--spotlight-color),
    transparent 80%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.card-spotlight:hover::before,
.card-spotlight:focus-within::before {
  opacity: 0.6;
}
.event-gallery img {
  transition: transform 0.4s ease;
}

.event-gallery img:hover {
  transform: scale(1.05);
}

.wave-text {
  display: inline-block;
  cursor: pointer;
}
.wave-letter {
  display: inline-block;
  transition: transform 0.3s, color 0.3s;
}
.wave-text:hover .wave-letter {
  animation: waveUpDown 0.5s cubic-bezier(0.4, 2, 0.6, 1) forwards;
}
@keyframes waveUpDown {
  0% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-15%);
  }
  60% {
    transform: translateY(7%);
  }
  100% {
    transform: translateY(0);
  }
}
/*new home feature work page*/
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 40px;
  padding: 0px 160px;
}

/* Item Layouts for Desktop */
.item-1 {
  grid-column: span 9;
  height: 570px;
}
.item-2 {
  grid-column: span 3;
  height: 570px;
}
.item-3 {
  grid-column: span 12;
  height: 600px;
}
.item-4,
.item-5 {
  grid-column: span 6;
  height: 500px;
}
.item-6 {
  grid-column: span 12;
  height: 600px;
}
.item-7 {
  grid-column: span 8;
  height: 570px;
}
.item-8 {
  grid-column: span 4;
  height: 570px;
}
.item-9,
.item-10,
.item-11 {
  grid-column: span 6;
  height: 570px;
}

/* General Styles remain the same */
.grid-item {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.grid-item::before {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.5s ease, transform 0.5s ease;
  z-index: 1;
  border-radius: 0px;
}

.grid-item:hover::before {
  opacity: 1;
  transform: scale(1);
}
.grid-item img,
.grid-item video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}
.title-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.95);
  color: white;
  padding: 10px 20px;
  font-size: 1.9rem;

  font-family: "Formula Bold";
  letter-spacing: 0.12em;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 2;
  text-align: center;
}
.grid-item:hover .title-overlay {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Ripple Animation stays the same */

/* Tablets: 1024px and down */
/* @media (max-width: 1024px) {
  .gallery-grid {
    padding: 0px 40px;
    gap: 24px;
  }

  .item-1,
  .item-2,
  .item-4,
  .item-5,
  .item-6,
  .item-7,
  .item-8,
  .item-9,
  .item-10 {
    grid-column: span 6;
    height: auto;
    aspect-ratio: 16/9;
  }

  .item-3 {
    grid-column: span 12;
    height: auto;
    aspect-ratio: 16/9;
  }
}


@media (max-width: 600px) {
  .gallery-grid {
    padding: 0px 20px;
    gap: 16px;
  }

  .grid-item {
    grid-column: span 12;
    height: 500px;
    aspect-ratio: auto;
  }

  .title-overlay {
    font-size: 1.2rem;
    padding: 8px 16px;
  }
} */

/*new sevice component in new home*/
.services-section {
  display: flex;
  flex-direction: row;
  height: 70vh;
  overflow: hidden;
  position: relative;
  padding: 0 160px;
  max-width: 100%;
  margin: 15rem 0rem 5rem 0;
  box-sizing: border-box;
}

.video-column {
  flex: 1;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.video-column video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  animation: fadeVideo 15s infinite;
}
.video-overlay-text {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  pointer-events: none;
  user-select: none;
  width: 100%;
  text-align: center;
  padding: 0 20px;
  letter-spacing: 0.1em;
  font-family: "Formula Bold";
  box-sizing: border-box;
  z-index: 10;
  letter-spacing: 0.2rem !important;
}
/* Top text at top center */
.video-overlay-text.top-text {
  top: -40px;
}

/* Bottom text at bottom center */
.video-overlay-text.bottom-text {
  bottom: -50px;
}
.video-column video:nth-child(1) {
  animation-delay: 0s;
}
.video-column video:nth-child(2) {
  animation-delay: 5s;
}
.video-column video:nth-child(3) {
  animation-delay: 10s;
}

@keyframes fadeVideo {
  0%,
  33.33% {
    opacity: 1;
  }
  33.34%,
  100% {
    opacity: 0;
  }
}

.text-column {
  flex: 1;
  display: flex;
  align-items: start;
  justify-content: center;
  overflow: hidden;
  background-color: #000;
  padding: 0;
  position: relative;
}

.text-wrapper {
  position: relative;
  will-change: transform;
}

.section-block {
  height: 33vh;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  opacity: 1;
}

.section-block h2 {
  font-size: 3.5rem;
  font-family: "Formula Bold";
  margin-bottom: -2rem;
  color: #db4a41;
  letter-spacing: 0em;
}

.section-block ul {
  list-style: none;
  padding: 0;
  font-size: 2.2rem;
  color: white;
  font-family: "Formula Bold";
}
.services-section-wrapper {
  position: relative;
  min-height: 300vh; /* Allow enough scroll space for all text blocks */
}
.light-rays-container {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: none;
  z-index: 3;
  overflow: hidden;
}
.galaxy-container {
  width: 100%;
  height: 100%;
  position: relative;
}
.orb-container {
  position: relative;
  z-index: 0;
  width: 100%;
  height: 100%;
}
/*work pages Project*/
.insights-section {
  background-color: black;
  padding: 60px 5%;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Default: desktop */
  gap: 25px;
}

.insights-card {
  width: 100%;
  background: #111;
  border-radius: 0;
  display: flex;
  flex-direction: column; /* stack video + content */
  transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.insights-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

.video-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9; /* Responsive height */
  overflow: hidden;
}

.video-wrapper iframe {
  width: 100% !important;
  height: 100% !important;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  padding: 20px;
  color: white;
}

.card-content h2 {
  font-family: "Formula Bold";
  font-size: 1.6rem;
  margin-bottom: 10px;
}

.card-content p {
  font-size: 0.95rem;
  color: #ccc;
  margin-bottom: 20px;
  font-family: "Anton";
  flex-grow: 1;
}
.discover-btn {
  display: inline-block;
  padding: 8px 16px;
  border: 1px solid white;
  border-radius: 8px;
  color: white;
  text-decoration: none;
  font-size: 1rem;
  align-self: flex-start;
  transition: all 0.3s ease;
  font-family: "Anton";
}
.discover-btn:hover {
  background: #db4a41;
  color: white;
}

/* Smooth scrolling experience if we want horizontal scroll instead of grid */
@media (max-width: 900px) {
  .insights-grid {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: 20px;
    padding-bottom: 20px;
  }

  .insights-card {
    min-width: 300px;
    scroll-snap-align: start;
    flex: 0 0 auto;
  }
}
/* Legacy gallery styles - keeping for backward compatibility */
.gallery-grid-event {
  display: grid;
  padding: 4rem 5vw;
  grid-template-columns: repeat(3, 1fr); /* Mobile default */
}
.gallery-item-event {
  overflow: hidden;
}

.gallery-item-event img {
  width: 100%;
  height: 350px;
  display: block;
  transition: transform 0.3s ease;
}

.gallery-item-event img:hover {
  transform: scale(1.05);
}

/* New Gallery Slider Styles */
.gallery-slider-window {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.gallery-nav-arrow {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.gallery-nav-arrow:hover {
  box-shadow: 0 0 20px rgba(219, 74, 65, 0.3);
}

@media (max-width: 480px) {
  .gallery-slider-window {
    width: 98% !important;
    height: 250px !important;
    margin: 0 auto;
  }

  .gallery-nav-arrow {
    width: 35px !important;
    height: 35px !important;
    font-size: 12px !important;
  }

  .gallery-grid {
    gap: 5px !important;
  }

  .gallery-item {
    margin-bottom: 5px !important;
    height: 100% !important;
  }
}

.circular-text {
  margin: 0 auto;
  border-radius: 50%;
  width: 200px;
  position: relative;
  height: 200px;
  font-weight: bold;
  color: #fff;
  font-weight: 900;
  text-align: center;
  cursor: pointer;
  transform-origin: 50% 50%;
  -webkit-transform-origin: 50% 50%;
}

.circular-text span {
  position: absolute;
  display: inline-block;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  font-size: 24px;
  transition: all 0.5s cubic-bezier(0, 0, 0, 1);
}
/*CMS People*/
.upload-box {
  border: 2px dashed #aaa;
  background-color: #f5f5f5;
  padding: 20px;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-box:hover {
  border-color: #555;
}

.upload-box input[type="file"] {
  display: none;
}

.upload-icon {
  font-size: 40px;
  color: #666;
  margin-bottom: 10px;
}

.upload-text {
  font-size: 14px;
  color: #444;
}
/*Reel Gallery In Homepage*/
.reel-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  max-width: 90%;
  background-color: #000;
  margin: 0 auto;
  padding: 20px;
  gap: 100px;
  font-family: "Formula Bold", sans-serif;
  min-height: 100vh;
}

.iphone-wrapper {
  position: relative;
  width: 300px;
  height: 580px;
  border-radius: 32px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* metallic border simulation */
  border: 3px solid transparent;
  background-clip: padding-box;
  box-shadow: inset 0 0 8px rgba(255, 255, 255, 0.2),
    /* inner shine */ 0 0 20px rgba(255, 255, 255, 0.2),
    /* soft glow */ 0 0 10px rgba(255, 255, 255, 0.4),
    /* bluish glow */ 0 5px 20px rgba(0, 0, 0, 0.6); /* depth shadow */
}

.iphone-wrapper::before {
  content: "";
  position: absolute;
  inset: -3px; /* border thickness */
  border-radius: 32px;
  padding: 3px;
  background: linear-gradient(145deg, #bbb, #eee, #999, #fff);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.main-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 18px;
}

.speaker-icon {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 8px;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: default;
}

.video-title {
  position: absolute;
  bottom: 90px;
  left: 0px;
  right: 10px;
  color: white;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 0.5px;
  text-align: left;
  padding: 6px 10px;
  border-radius: 10px;
  z-index: 2;
}
.video-subtitle {
  position: absolute;
  bottom: 70px;
  left: 0px;
  right: 10px;
  color: white;
  font-size: 15px;
  font-weight: bold;
  letter-spacing: 0.9px;
  text-align: left;
  padding: 6px 10px;
  border-radius: 10px;
  z-index: 2;
}
.thumbnail-container {
  display: grid;
  gap: 0px;
  grid-template-columns: repeat(6, 1fr);
  justify-items: center;
  overflow-x: auto;
  padding: 15px 10px;
  scroll-behavior: smooth;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail-item {
  position: relative;
  width: 80px;
  height: 120px;
  border-radius: 0px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #000;
}
.thumbnail-item:hover {
  transform: scale(2.05);
  border: 1px solid #db4a41;
}
.thumbnail-item.active {
  border: 2px solid #db4a41;
  box-shadow: 0 6px 20px rgba(219, 74, 65, 0.5);
}

.thumbnail-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.notch-style {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 28px;
  background-color: #000;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  z-index: 3;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 0 10px rgba(255, 255, 255, 0.4);
}

.home-bar-iphone {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 5px;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 13;
}
/* Right-side action buttons */
.actions {
  position: absolute;
  right: 10px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
  z-index: 5;
}

.action-btn {
  color: white;
  font-size: 28px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-btn span {
  font-size: 12px;
  margin-top: 4px;
}

/* Bottom navigation bar */
.bottom-nav {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #11111134;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 5;
}

.bottom-icon {
  color: white;
  font-size: 26px;
  cursor: pointer;
}
/* ========== ReelPage.css ========== */

.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.reel-container-desktop {
  position: relative;
  min-height: 200vh;
  width: 100%;
  max-width: 100vw;
  background-color: #fff;
  overflow: hidden;
  padding-bottom: 80px;
}

.reel-container-mobile {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  padding: 20px 10px 80px;
}

.mobile-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  max-width: 600px;
  margin: 0 auto;
}

.mobile-reel-card {
  aspect-ratio: 9/16;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: transform 0.2s;
}

.mobile-reel-card:active {
  transform: scale(0.95);
}

.mobile-reel-card video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

/* Desktop Reel Card */
.desktop-video-card {
  width: 140px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.desktop-video-card video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.parallax-section {
  height: 350vh;
  position: relative;
}
.parallax-page-container {
  background-color: transparent;
  position: sticky; /* instead of fixed */
  top: 0;
  left: 0;
  height: 100vh;
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
  padding: 90px 100px; /* Your padding/spacing */
  overflow: hidden;
}
.column {
  display: flex;
  flex-direction: column;
  gap: 200px;
}

.card {
  width: 200px;
  height: 280px;
  background: #e7e5de;
  border-radius: 3px;
  overflow: hidden;
  display: flex;
  justify-content: center; /* centers the image box horizontally */
  align-items: center; /* centers the image box vertically */
}

.card img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* fills the card without distortion */
  object-position: center; /* always keeps the center in view */
  display: block;
}

/* Responsive */
@media (max-width: 900px) {
  .parallax-page-container {
    gap: 30px;
    padding: 0 20px;
  }

  .card {
    width: 200px;
    height: 280px;
  }

  .column {
    gap: 40px;
  }
}
.column-wrapper {
  display: flex;
  align-items: center; /* centers the scrolling column vertically */
  justify-content: center;
  height: 100%; /* full viewport height */
  overflow: hidden;
}
/* GRID MODE (activated by GSAP) */
.grid-mode .column {
  display: contents; /* flatten children */
}

.grid-mode .card {
  position: absolute;
  transition: 0.4s ease;
}
.scroll-reveal {
  margin: 20px 0;
}

.scroll-reveal-text {
  font-size: clamp(1.6rem, 4vw, 3rem);
  line-height: 1.5;
  font-weight: 600;
}

.word {
  display: inline-block;
}
.floating-lines-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}
/* Center the TV and position it on top of everything */
/* Center the whole TV */
.tv-container {
  position: relative;
  width: 1300px; /* FIX: set an actual width */
  margin: 0 auto;
  cursor: pointer;
  z-index: 5;
}

/* TV frame on top */
.tv-frame {
  width: 100%;
  height: auto;
  display: block;
  position: relative;
  z-index: 10; /* FIX: make this ABOVE the content */
  pointer-events: none;
}

/* Content behind the TV frame */
.tv-screen {
  position: absolute;
  top: 13.5%; /* adjust to your TV PNG */
  left: 12.5%;
  width: 75%; /* FIX: true full screen width */
  height: 67%; /* FIX: true full screen height */

  z-index: 1; /* behind the TV frame */
  overflow: hidden;

  display: flex;
  justify-content: center;
  align-items: center;
}

/* Glitch animation */
.glitch {
  animation: glitchEffect 0.4s steps(2) both;
}

@keyframes glitchEffect {
  0% {
    transform: translate(0);
    filter: contrast(1);
  }
  20% {
    transform: translate(-3px, 2px);
    clip-path: inset(10% 0 30% 0);
  }
  40% {
    transform: translate(4px, -3px);
    clip-path: inset(40% 0 20% 0);
  }
  60% {
    transform: translate(-2px, 3px);
    clip-path: inset(20% 0 40% 0);
  }
  80% {
    transform: translate(2px, -1px);
    filter: contrast(2);
  }
  100% {
    transform: translate(0);
    filter: none;
    clip-path: inset(0);
  }
}
.timeline-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  min-height: 200vh;
  padding: 80px 100px;
  background: #ffffff;
  position: relative;
  gap: 80px;
}

.model-view {
  flex: 1;
  height: 100vh;
  position: sticky;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-view canvas {
  width: 100% !important;
  height: 100% !important;
  perspective: 1200px; /* add depth for 3D effect */
}

.timeline {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0;
  min-height: calc(100vh - 160px);
}

.timeline-line-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 2px;
  background: transparent;
  z-index: 1;
}

.timeline-line {
  position: absolute;
  left: 0;
  width: 4px;
  background: #000000;
  transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    top 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
  z-index: 1;
}

.milestone {
  position: relative;
  display: flex;
  align-items: flex-start;
  margin-bottom: 0;
  width: 100%;
  padding-left: 40px;
  padding-top: 0;
  padding-bottom: 0;
  flex: 1;
  justify-content: flex-start;
  z-index: 2;
  transition: opacity 0.4s ease-out, color 0.4s ease-out;
}

.milestone-node {
  position: absolute;
  left: -9px;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #cccccc;
  z-index: 3;
  transition: all 0.4s ease-out;
  border: 2px solid #ffffff;
  box-sizing: border-box;
}

.milestone-node.reached {
  background: #000000;
  border-color: #ffffff;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
}

.milestone.active .milestone-node {
  background: #000000;
}

.milestone.inactive .milestone-node {
  background: #cccccc;
  border-color: #ffffff;
}

.milestone-content {
  flex: 1;
  padding-top: 2px;
}

.milestone h3 {
  margin: 0 0 8px 0;
  font-size: 36px;
  font-weight: bold;
  color: #000000;
  font-family: "Formula Bold";
  transition: color 0.4s ease-out;
  letter-spacing: 0.02em;
}

.milestone.inactive h3 {
  color: #999999;
}

.milestone p {
  margin: 0;
  font-size: 20px;
  color: #db4a41;
  max-width: 500px;
  line-height: 1.6;
  font-family: "Anton";
  transition: color 0.4s ease-out;
}

.milestone.inactive p {
  color: #cccccc;
}

.milestone.active h3,
.milestone.active p {
  opacity: 1;
}
.scroll-float {
  overflow: hidden;
}

.scroll-float-text {
  display: inline-block;
  font-size: clamp(1.6rem, 8vw, 10rem);
  font-weight: 900;
  text-align: center;
  line-height: 1.5;
}

.char {
  display: inline-block;
}

.gradual-blur-inner {
  position: relative;
  width: 100%;
  height: 100%;
}

.gradual-blur-inner > div {
  -webkit-backdrop-filter: inherit;
  backdrop-filter: inherit;
}

.gradual-blur {
  isolation: isolate;
}

@supports not (backdrop-filter: blur(1px)) {
  .gradual-blur-inner > div {
    background: rgba(0, 0, 0, 0.3);
    opacity: 0.5;
  }
}

.gradual-blur-fixed {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

.insights-page {
  padding: 120px 80px;
  background: #fff;
  overflow: hidden;
}

/* LEFT INTRO */
.insights-intro {
  max-width: 520px;
  margin-bottom: 200px;
}

.insights-intro h1 {
  font-size: 48px;
  line-height: 1.1;
  font-weight: 500;
}

.insights-intro p {
  margin-top: 20px;
  color: #666;
  font-size: 16px;
}

/* GALLERY */
.insights-gallery {
  display: flex;
  flex-direction: column;
  gap: 180px;
}

/* WRAPPER FOR OFFSET + SCROLL */
.gallery-wrapper {
  width: 100%;
  position: relative;
}

/* CARD */
.gallery-item {
  width: 85%;
  display: block;
  text-decoration: none;
  color: inherit;
}

.gallery-wrapper:nth-child(even) .gallery-item {
  margin-left: auto;
}

/* MEDIA */
.gallery-media {
  height: 70vh;
  border-radius: 14px;
  overflow: hidden;
}

.gallery-media video,
.gallery-media iframe {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* META */
.gallery-meta {
  margin-top: 20px;
}

.gallery-meta h2 {
  font-size: 22px;
  font-weight: 500;
}

.gallery-meta p {
  font-size: 14px;
  color: #777;
}

/* HOVER POLISH */
.gallery-item:hover .gallery-media {
  transform: scale(1.02);
  transition: transform 0.6s ease;
}
