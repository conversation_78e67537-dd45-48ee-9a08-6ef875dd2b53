import React, { useEffect, useState } from "react";
import {
  <PERSON>ton,
  Typo<PERSON>,
  <PERSON>dal,
  TextField,
  Paper,
  Box,
  MenuItem,
} from "@mui/material";
import axios from "axios";
import AddIcon from "@mui/icons-material/Add";

const API_URL = "https://youngproductions-768ada043db3.herokuapp.com/api/teams";

const TeamAdmin = () => {
  const [teams, setTeams] = useState([]);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [form, setForm] = useState({
    name: "",
    email: "",
    role: "",
    status: "activated",
    order: 0,
    bio: "",
    profilePicture: null,
  });
  const [preview, setPreview] = useState(null);
  const [editingId, setEditingId] = useState(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchTeams();
  }, []);

  const fetchTeams = async () => {
    try {
      const token = localStorage.getItem("token");
      const res = await axios.get(API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setTeams(res.data);
    } catch (err) {
      console.error("Error fetching teams:", err);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setForm({ ...form, profilePicture: file });
    setPreview(file ? URL.createObjectURL(file) : null);
  };

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem("token");
      const fd = new FormData();
      Object.keys(form).forEach((key) => {
        if (form[key] !== null) fd.append(key, form[key]);
      });

      if (editingId) {
        await axios.put(`${API_URL}/${editingId}`, fd, {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        });
      } else {
        await axios.post(API_URL, fd, {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        });
      }

      resetForm();
      fetchTeams();
      setShowModal(false);
    } catch (err) {
      console.error("Error saving member:", err);
    }
  };

  const handleEdit = (member) => {
    setForm({
      name: member.name,
      email: member.email,
      role: member.role,
      status: member.status,
      order: member.order,
      bio: member.bio || "",
      profilePicture: null,
    });
    setEditingId(member._id);
    setPreview(
      member.profilePicture
        ? `https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/profiles/${member.profilePicture}`
        : null
    );
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure?")) return;
    try {
      const token = localStorage.getItem("token");
      await axios.delete(`${API_URL}/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      fetchTeams();
    } catch (err) {
      console.error("Error deleting member:", err);
    }
  };

  const resetForm = () => {
    setForm({
      name: "",
      email: "",
      role: "",
      status: "activated",
      order: 0,
      bio: "",
      profilePicture: null,
    });
    setPreview(null);
    setEditingId(null);
  };

  // Layout styles
  const applyResponsiveColumns = () => {
    const width = window.innerWidth;
    if (width >= 1024) return "repeat(4, 1fr)";
    if (width >= 768) return "repeat(2, 1fr)";
    return "repeat(1, 1fr)";
  };
  const [columns, setColumns] = useState(applyResponsiveColumns());
  useEffect(() => {
    const handleResize = () => setColumns(applyResponsiveColumns());
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const containerStyle = {
    display: "grid",
    gap: "40px",
    padding: "40px",
    backgroundColor: "#000",
    gridTemplateColumns: columns,
  };

  const cardStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "10px",
    position: "relative",
  };

  const imageWrapperStyle = (isCircle) => ({
    width: "100%",
    aspectRatio: "1 / 1",
    overflow: "hidden",
    borderRadius: isCircle ? "50%" : "12px",
    position: "relative",
  });

  const imageStyle = (hovered, isCircle) => ({
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: hovered || isCircle ? "50%" : "0",
    transition: "border-radius 0.4s ease-in-out, transform 0.3s ease-in-out",
  });

  const nameStyle = {
    marginTop: "16px",
    fontWeight: "bold",
    fontSize: "2rem",
    fontFamily: "Formula Bold",
    letterSpacing: "0.09em",
    color: "#fff",
  };

  const titleStyle = {
    fontSize: "1.2rem",
    color: "#db4a41",
    marginTop: "-1rem",
    fontFamily: "Formula Bold",
  };

  const bioStyle = {
    fontSize: "14px",
    color: "#fff",
    marginTop: "14px",
    fontFamily: "Anton",
  };
  return (
    <div>
      <div
        style={{
          padding: "20px",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "baseline",
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            textAlign: "center",
            marginBottom: { xs: "20px", sm: "25px", md: "30px" },
            fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
            textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          Team Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            resetForm();
            setShowModal(true);
          }}
          sx={{
            background: "#db4a41",
            "&:hover": {
              background: "#c43a31",
            },
          }}
        >
          Add Team Member
        </Button>
      </div>
      {/* Modal */}
      <Modal
        open={showModal}
        onClose={() => setShowModal(false)}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 400,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            {editingId ? "Edit Team Member" : "Add New Team Member"}
          </Typography>

          {/* Profile Picture Preview */}
          {preview && (
            <Box
              sx={{
                width: 120,
                height: 120,
                borderRadius: "50%",
                overflow: "hidden",
                margin: "0 auto 20px auto",
                border: "3px solid #db4a41",
              }}
            >
              <img
                src={preview}
                alt="Preview"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
              />
            </Box>
          )}

          <form onSubmit={handleSubmit}>
            {/* File Upload */}
            <Box sx={{ mb: 2 }}>
              <input
                type="file"
                name="profilePicture"
                onChange={handleFileChange}
                accept="image/*"
                style={{
                  width: "100%",
                  padding: "10px",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  border: "1px solid rgba(255, 255, 255, 0.23)",
                  borderRadius: "4px",
                  color: "white",
                }}
              />
            </Box>

            <TextField
              fullWidth
              label="Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={form.email}
              onChange={handleChange}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Role"
              name="role"
              value={form.role}
              onChange={handleChange}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              select
              label="Status"
              name="status"
              value={form.status}
              onChange={handleChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="activated">Activated</MenuItem>
              <MenuItem value="deactivated">Deactivated</MenuItem>
            </TextField>

            <TextField
              fullWidth
              label="Order"
              name="order"
              type="number"
              value={form.order}
              onChange={handleChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Bio"
              name="bio"
              multiline
              rows={3}
              value={form.bio}
              onChange={handleChange}
              sx={{
                mb: 3,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
              <Button
                onClick={() => setShowModal(false)}
                sx={{
                  color: "white",
                  borderColor: "white",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
                variant="contained"
              >
                {editingId ? "Update Member" : "Add Member"}
              </Button>
            </Box>
          </form>
        </Paper>
      </Modal>

      {/* Grid of Members */}
      <div style={containerStyle}>
        {teams.map((person, index) => (
          <div key={person._id} style={cardStyle}>
            <div
              style={imageWrapperStyle(true)}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <img
                src={person?.profilePicture?.replace(
                  "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                )}
                alt={person.name}
                style={imageStyle(hoveredIndex === index, true)}
              />
            </div>
            <h3 style={nameStyle}>{person.name}</h3>
            <p style={titleStyle}>{person.role}</p>
            <p style={bioStyle}>{person.bio}</p>
            <div
              style={{
                bottom: "5px",
                left: "5px",
                display: "flex",
                gap: "5px",
              }}
            >
              <button
                className="btn btn-primary link-btn"
                onClick={() => handleEdit(person)}
              >
                Edit
              </button>
              <button
                className="btn btn-primary link-btn"
                onClick={() => handleDelete(person._id)}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TeamAdmin;
