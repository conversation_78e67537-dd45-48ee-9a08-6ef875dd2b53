import React, { useState, useEffect } from "react";
import {
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Box,
  Paper,
  Divider,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Event as EventIcon,
  Work as WorkIcon,
  Business as BusinessIcon,
  ArrowForward as ArrowForwardIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import axios from "axios";

const StatCard = ({ title, count, icon, color, onClick }) => {
  return (
    <Card
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "15px",
        transition: "transform 0.2s, box-shadow 0.2s",
        "&:hover": {
          transform: "translateY(-5px)",
          boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
          cursor: "pointer",
        },
      }}
      onClick={onClick}
    >
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography
              variant="h6"
              sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
            >
              {title}
            </Typography>
            <Typography
              variant="h3"
              sx={{ color: "white", fontWeight: "bold" }}
            >
              {count}
            </Typography>
          </Box>
          <Box
            sx={{
              background: `${color}20`,
              borderRadius: "12px",
              p: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const RecentItem = ({ title, subtitle, icon, color }) => {
  return (
    <Paper
      sx={{
        background: "rgba(255, 255, 255, 0.05)",
        backdropFilter: "blur(10px)",
        border: "1px solid rgba(255, 255, 255, 0.1)",
        borderRadius: "12px",
        p: 2,
        mb: 2,
        transition: "transform 0.2s",
        "&:hover": {
          transform: "translateX(5px)",
        },
      }}
    >
      <Box display="flex" alignItems="center" gap={2}>
        <Box
          sx={{
            background: `${color}20`,
            borderRadius: "8px",
            p: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {icon}
        </Box>
        <Box flex={1}>
          <Typography variant="subtitle1" sx={{ color: "white" }}>
            {title}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
          >
            {subtitle}
          </Typography>
        </Box>
      </Box>
    </Paper>
  );
};

const Dashboard = () => {
  const [events, setEvents] = useState([]);
  const [jobApplications, setJobApplications] = useState([]);
  const [contactFormSubmissions, setContactFormSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("token");
      const headers = {
        Authorization: `Bearer ${token}`,
      };

      try {
        setLoading(true);
        const [eventsResponse, jobsResponse, contactResponse] = await axios.all(
          [
            axios
              .get(
                "https://youngproductions-768ada043db3.herokuapp.com/api/workvideos",
                { headers }
              )
              .catch((err) => ({ data: [] })),
            axios
              .get(
                "https://youngproductions-768ada043db3.herokuapp.com/api/jobApplications",
                { headers }
              )
              .catch((err) => ({ data: [] })),
            axios
              .get(
                "https://youngproductions-768ada043db3.herokuapp.com/api/contactform",
                { headers }
              )
              .catch((err) => ({ data: [] })),
          ]
        );

        setEvents(eventsResponse.data || []);
        setJobApplications(jobsResponse.data || []);
        setContactFormSubmissions(contactResponse.data || []);
        setError(null);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        if (error.response && error.response.status === 401) {
          localStorage.removeItem("token");
          window.location.href = "/admin/login";
        }
        setError("Failed to load dashboard data. Please try logging in again.");
        setEvents([]);
        setJobApplications([]);
        setContactFormSubmissions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Box
        sx={{
          background: "black",
          height: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography variant="h5" sx={{ color: "white" }}>
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box
        sx={{
          background: "black",
          height: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography variant="h5" sx={{ color: "#db4a41" }}>
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        background: "black",
        minHeight: "100vh",
        p: 4,
      }}
    >
      <Typography
        variant="h3"
        sx={{
          fontFamily: "Formula Bold",
          color: "#db4a41",
          textAlign: "center",
          mb: 4,
        }}
      >
        Admin Dashboard
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <StatCard
            title="Published Work"
            count={events?.length || 0}
            icon={<EventIcon sx={{ color: "#db4a41", fontSize: 30 }} />}
            color="#db4a41"
            onClick={() => navigate("/admin/events")}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <StatCard
            title="Job Applications"
            count={jobApplications?.length || 0}
            icon={<WorkIcon sx={{ color: "#4CAF50", fontSize: 30 }} />}
            color="#4CAF50"
            onClick={() => navigate("/admin/job-applications")}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <StatCard
            title="New Clients"
            count={contactFormSubmissions?.length || 0}
            icon={<BusinessIcon sx={{ color: "#2196F3", fontSize: 30 }} />}
            color="#2196F3"
            onClick={() => navigate("/admin/contact-submissions")}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
              height: "100%",
            }}
          >
            <CardContent>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                mb={2}
              >
                <Typography variant="h6" sx={{ color: "white" }}>
                  Recent Events
                </Typography>
                <Tooltip title="View all events">
                  <IconButton
                    onClick={() => navigate("/admin/events")}
                    sx={{ color: "white" }}
                  >
                    <ArrowForwardIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />
              {events.slice(0, 5).map((event) => (
                <RecentItem
                  key={event._id || event.id}
                  title={event.title}
                  subtitle={event.date}
                  icon={<EventIcon sx={{ color: "#db4a41" }} />}
                  color="#db4a41"
                />
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
              height: "100%",
            }}
          >
            <CardContent>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                mb={2}
              >
                <Typography variant="h6" sx={{ color: "white" }}>
                  Recent Applications
                </Typography>
                <Tooltip title="View all applications">
                  <IconButton
                    onClick={() => navigate("/admin/CMS/job-applications")}
                    sx={{ color: "white" }}
                  >
                    <ArrowForwardIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />
              {jobApplications.slice(0, 5).map((application) => (
                <RecentItem
                  key={application._id || application.id}
                  title={application.fullName}
                  subtitle={application.JobId?.title || "Unknown Job"}
                  icon={<WorkIcon sx={{ color: "#4CAF50" }} />}
                  color="#4CAF50"
                />
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
              height: "100%",
            }}
          >
            <CardContent>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                mb={2}
              >
                <Typography variant="h6" sx={{ color: "white" }}>
                  Recent Clients
                </Typography>
                <Tooltip title="View all clients">
                  <IconButton
                    onClick={() => navigate("/admin/contact-submissions")}
                    sx={{ color: "white" }}
                  >
                    <ArrowForwardIcon />
                  </IconButton>
                </Tooltip>
              </Box>
              <Divider sx={{ background: "rgba(255, 255, 255, 0.1)", mb: 2 }} />
              {contactFormSubmissions.slice(0, 5).map((submission) => (
                <RecentItem
                  key={submission._id || submission.id}
                  title={submission.companyName}
                  subtitle={submission.selectedType}
                  icon={<BusinessIcon sx={{ color: "#2196F3" }} />}
                  color="#2196F3"
                />
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
