import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import WarningIcon from "@mui/icons-material/Warning";
import SearchIcon from "@mui/icons-material/Search";
import { motion, AnimatePresence } from "framer-motion";

// Utility function to parse MongoDB Decimal128 values
const parseDecimal = (value) => {
  if (value && typeof value === "object" && value.$numberDecimal) {
    return parseFloat(value.$numberDecimal);
  }
  return value;
};

function ClientAccount() {
  const navigate = useNavigate();
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [viewingAccount, setViewingAccount] = useState(null);
  const [newAccount, setNewAccount] = useState({
    client_name: "",
    subscription_fee: "",
    profit_goal_percent: "",
    penalty_rate: "",
    penalty_grace_days: "",
    start_date: "",
    end_date: "",
    status: "active",
    notes: "",
    taxable: false,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";

  const fetchAccounts = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();

      // Handle different response structures and parse decimal values
      let accountsData = [];

      if (Array.isArray(result)) {
        accountsData = result;
      } else if (result.data && Array.isArray(result.data)) {
        accountsData = result.data;
      } else if (result.accounts && Array.isArray(result.accounts)) {
        accountsData = result.accounts;
      } else {
        console.error("Unexpected API response structure:", result);
        showSnackbar("Unexpected data format received", "error");
      }

      // Parse decimal values in the accounts data
      const parsedAccounts = accountsData.map((account) => ({
        ...account,
        subscription_fee: parseDecimal(account.subscription_fee),
      }));

      setAccounts(parsedAccounts);
    } catch (error) {
      console.error("Error fetching client accounts:", error);
      showSnackbar("Failed to fetch client accounts", "error");
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter and sort accounts
  const filteredAndSortedAccounts = React.useMemo(() => {
    let filtered = accounts.filter((account) => {
      // Search filter
      const searchMatch =
        searchTerm === "" ||
        account.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account._id?.toLowerCase().includes(searchTerm.toLowerCase());

      // Status filter
      const statusMatch =
        statusFilter === "" || account.status === statusFilter;

      // Type filter (based on subscription fee ranges)
      let typeMatch = true;
      if (typeFilter !== "") {
        const fee = parseDecimal(account.subscription_fee) || 0;
        switch (typeFilter) {
          case "basic":
            typeMatch = fee < 5000;
            break;
          case "standard":
            typeMatch = fee >= 5000 && fee < 15000;
            break;
          case "premium":
            typeMatch = fee >= 15000;
            break;
          default:
            typeMatch = true;
        }
      }

      return searchMatch && statusMatch && typeMatch;
    });

    // Sort accounts
    if (sortBy) {
      filtered.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
          case "end_date_nearest":
            aValue = a.end_date ? new Date(a.end_date) : new Date("9999-12-31");
            bValue = b.end_date ? new Date(b.end_date) : new Date("9999-12-31");
            return aValue - bValue;
          case "end_date_furthest":
            aValue = a.end_date ? new Date(a.end_date) : new Date("1900-01-01");
            bValue = b.end_date ? new Date(b.end_date) : new Date("1900-01-01");
            return bValue - aValue;
          case "subscription_fee":
            aValue = parseDecimal(a.subscription_fee) || 0;
            bValue = parseDecimal(b.subscription_fee) || 0;
            break;
          case "profit_goal":
            aValue = parseDecimal(a.profit_goal_percent) || 0;
            bValue = parseDecimal(b.profit_goal_percent) || 0;
            break;
          case "penalty":
            aValue = parseDecimal(a.penalty_rate) || 0;
            bValue = parseDecimal(b.penalty_rate) || 0;
            break;
          default:
            return 0;
        }

        if (sortBy !== "end_date_nearest" && sortBy !== "end_date_furthest") {
          return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
        }
        return 0;
      });
    }

    return filtered;
  }, [accounts, searchTerm, statusFilter, typeFilter, sortBy, sortOrder]);

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  const handleAdd = () => {
    setEditingAccount(null);
    setNewAccount({
      client_name: "",
      subscription_fee: "",
      profit_goal_percent: "",
      penalty_rate: "",
      penalty_grace_days: "",
      start_date: "",
      end_date: "",
      status: "active",
      notes: "",
      taxable: false,
    });
    setOpenModal(true);
  };

  const handleEdit = (account) => {
    setEditingAccount(account);
    setNewAccount({
      client_name: account.client_name,
      subscription_fee: account.subscription_fee,
      profit_goal_percent: account.profit_goal_percent,
      penalty_rate: account.penalty_rate,
      penalty_grace_days: account.penalty_grace_days,
      start_date: account.start_date
        ? new Date(account.start_date).toISOString().slice(0, 10)
        : "",
      end_date: account.end_date
        ? new Date(account.end_date).toISOString().slice(0, 10)
        : "",
      status: account.status,
      notes: account.notes || "",
      taxable: account.taxable || false,
    });
    setOpenModal(true);
  };

  const handleView = (account) => {
    setViewingAccount(account);
    setOpenViewModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingAccount(null);
    setNewAccount({
      client_name: "",
      subscription_fee: "",
      profit_goal_percent: "",
      penalty_rate: "",
      penalty_grace_days: "",
      start_date: "",
      end_date: "",
      status: "active",
      notes: "",
      taxable: false,
    });
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingAccount(null);
  };

  const handleSubmit = async () => {
    try {
      const accountData = {
        client_name: newAccount.client_name,
        subscription_fee: parseFloat(newAccount.subscription_fee),
        profit_goal_percent: parseFloat(newAccount.profit_goal_percent),
        penalty_rate: parseFloat(newAccount.penalty_rate),
        penalty_grace_days: parseInt(newAccount.penalty_grace_days),
        start_date: newAccount.start_date,
        end_date: newAccount.end_date,
        status: newAccount.status,
        notes: newAccount.notes,
        taxable: newAccount.taxable,
      };

      const url = editingAccount
        ? `${API_BASE_URL}/${editingAccount._id}`
        : API_BASE_URL;

      const method = editingAccount ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(accountData),
      });

      if (response.ok) {
        const result = await response.json();
        if (editingAccount) {
          setAccounts(
            accounts.map((acc) =>
              acc._id === editingAccount._id ? result : acc
            )
          );
          showSnackbar("Client account updated successfully", "success");
        } else {
          setAccounts([result, ...accounts]);
          showSnackbar("Client account created successfully", "success");
        }
        handleCloseModal();
        fetchAccounts();
      } else {
        throw new Error("Failed to save client account");
      }
    } catch (error) {
      console.error("Error saving client account:", error);
      showSnackbar("Failed to save client account", "error");
    }
  };

  const handleDelete = async (id) => {
    if (
      !window.confirm("Are you sure you want to delete this client account?")
    ) {
      return;
    }
    try {
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        method: "DELETE",
      });
      if (response.ok) {
        setAccounts(accounts.filter((account) => account._id !== id));
        showSnackbar("Client account deleted successfully", "success");
      } else {
        throw new Error("Failed to delete client account");
      }
    } catch (error) {
      console.error("Error deleting client account:", error);
      showSnackbar("Failed to delete client account", "error");
    }
  };

  const handleStatusChange = async (accountId, newStatus) => {
    try {
      const response = await fetch(`${API_BASE_URL}/${accountId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });
      if (response.ok) {
        setAccounts(
          accounts.map((account) =>
            account._id === accountId
              ? { ...account, status: newStatus }
              : account
          )
        );

        if (viewingAccount && viewingAccount._id === accountId) {
          setViewingAccount({ ...viewingAccount, status: newStatus });
        }

        showSnackbar(`Account status updated to ${newStatus}`, "success");
      } else {
        const errData = await response.json().catch(() => ({}));
        console.error("Backend error:", errData);
        throw new Error("Failed to update account status");
      }
    } catch (error) {
      console.error("Error updating account status:", error);
      showSnackbar("Failed to update account status", "error");
    }
  };

  const handleToggleTaxable = async (accountId, currentTaxable) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/toggle-taxble/${accountId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      if (response.ok) {
        const newTaxable = !currentTaxable;
        setAccounts(
          accounts.map((account) =>
            account._id === accountId
              ? { ...account, taxable: newTaxable }
              : account
          )
        );

        if (viewingAccount && viewingAccount._id === accountId) {
          setViewingAccount({ ...viewingAccount, taxable: newTaxable });
        }

        showSnackbar(
          `Account taxable status updated to ${newTaxable ? "Yes" : "No"}`,
          "success"
        );
      } else {
        const errData = await response.json().catch(() => ({}));
        console.error("Backend error:", errData);
        throw new Error("Failed to update taxable status");
      }
    } catch (error) {
      console.error("Error updating taxable status:", error);
      showSnackbar("Failed to update taxable status", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "#4caf50";
      case "inactive":
        return "#ff9800";
      case "suspended":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Client Accounts
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Account
          </Button>
        </Box>

        {/* Search and Filter Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                {/* Search Bar */}
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="Search by client name, notes, or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <SearchIcon
                          sx={{ color: "rgba(255, 255, 255, 0.5)", mr: 1 }}
                        />
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputBase-input::placeholder": {
                        color: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  />
                </Grid>

                {/* Status Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Status</MenuItem>
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                      <MenuItem value="suspended">Suspended</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Type Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Type
                    </InputLabel>
                    <Select
                      value={typeFilter}
                      onChange={(e) => setTypeFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Types</MenuItem>
                      <MenuItem value="basic">Basic (&lt; 5K EGP)</MenuItem>
                      <MenuItem value="standard">
                        Standard (5K - 15K EGP)
                      </MenuItem>
                      <MenuItem value="premium">
                        Premium (&gt; 15K EGP)
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort By */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Sort By
                    </InputLabel>
                    <Select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">Default</MenuItem>
                      <MenuItem value="end_date_nearest">
                        End Date (Nearest)
                      </MenuItem>
                      <MenuItem value="end_date_furthest">
                        End Date (Furthest)
                      </MenuItem>
                      <MenuItem value="subscription_fee">
                        Subscription Fee
                      </MenuItem>
                      <MenuItem value="profit_goal">Profit Goal</MenuItem>
                      <MenuItem value="penalty">Penalty Rate</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort Order */}
                {sortBy && !sortBy.includes("end_date") && (
                  <Grid item xs={12} sm={6} md={2}>
                    <FormControl fullWidth>
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Order
                      </InputLabel>
                      <Select
                        value={sortOrder}
                        onChange={(e) => setSortOrder(e.target.value)}
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                        }}
                      >
                        <MenuItem value="asc">Ascending</MenuItem>
                        <MenuItem value="desc">Descending</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Results Counter */}
          {!loading && (
            <Box
              sx={{
                mb: 2,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                }}
              >
                Showing {filteredAndSortedAccounts.length} of {accounts.length}{" "}
                accounts
              </Typography>
              {(searchTerm || statusFilter || typeFilter || sortBy) && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("");
                    setTypeFilter("");
                    setSortBy("");
                    setSortOrder("asc");
                  }}
                  sx={{
                    color: "rgba(255, 255, 255, 0.7)",
                    borderColor: "rgba(255, 255, 255, 0.3)",
                    "&:hover": {
                      borderColor: "#db4a41",
                      color: "#db4a41",
                    },
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </Box>
          )}

          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <Grid container spacing={3}>
              <AnimatePresence>
                {Array.isArray(filteredAndSortedAccounts) &&
                filteredAndSortedAccounts.length > 0 ? (
                  filteredAndSortedAccounts.map((account) => (
                    <Grid item xs={12} sm={6} md={4} key={account._id}>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card
                          sx={{
                            background: "rgba(255, 255, 255, 0.05)",
                            backdropFilter: "blur(10px)",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                            borderRadius: "12px",
                            height: "100%",
                            display: "flex",
                            flexDirection: "column",
                            position: "relative",
                            "&:hover": {
                              background: "rgba(255, 255, 255, 0.08)",
                              borderColor: "rgba(219, 74, 65, 0.3)",
                            },
                          }}
                        >
                          <Box
                            sx={{
                              position: "absolute",
                              top: 8,
                              right: 8,
                              display: "flex",
                              gap: 0.5,
                              zIndex: 10,
                            }}
                          >
                            <Tooltip title="View">
                              <IconButton
                                size="small"
                                onClick={() => handleView(account)}
                                sx={{
                                  color: "rgba(255, 255, 255, 0.7)",
                                  "&:hover": { color: "#db4a41" },
                                }}
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => handleEdit(account)}
                                sx={{
                                  color: "rgba(255, 255, 255, 0.7)",
                                  "&:hover": { color: "#2196f3" },
                                }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                onClick={() => handleDelete(account._id)}
                                sx={{
                                  color: "rgba(255, 255, 255, 0.7)",
                                  "&:hover": { color: "#f44336" },
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>

                          <CardContent sx={{ flexGrow: 1, pt: 5 }}>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                mb: 2,
                              }}
                            >
                              <Avatar
                                sx={{
                                  bgcolor: stringToColor(account.client_name),
                                  mr: 2,
                                }}
                              >
                                <AccountBalanceIcon />
                              </Avatar>
                              <Box>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontFamily: "Formula Bold",
                                    color: "white",
                                    fontSize: "1.1rem",
                                  }}
                                >
                                  {account.client_name}
                                </Typography>
                                <Chip
                                  label={account.status}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getStatusColor(
                                      account.status
                                    )}20`,
                                    color: getStatusColor(account.status),
                                    textTransform: "capitalize",
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </Box>
                            </Box>

                            <Box sx={{ mb: 2 }}>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.8)",
                                  mb: 1,
                                  fontFamily: "Anton",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                💰 Subscription: {account.subscription_fee} EGP
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.8)",
                                  mb: 1,
                                  fontFamily: "Anton",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <TrendingUpIcon fontSize="small" />
                                Profit Goal: {account.profit_goal_percent}%
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.7)",
                                  fontFamily: "Anton",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <WarningIcon fontSize="small" />
                                Penalty: {account.penalty_rate}% (
                                {account.penalty_grace_days} days grace)
                              </Typography>
                            </Box>

                            {account.start_date && (
                              <Typography
                                variant="caption"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.6)",
                                  fontFamily: "Anton",
                                  display: "block",
                                  mb: 1,
                                }}
                              >
                                Started:{" "}
                                {new Date(
                                  account.start_date
                                ).toLocaleDateString()}
                              </Typography>
                            )}
                            {account.end_date && (
                              <Typography
                                variant="caption"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.6)",
                                  fontFamily: "Anton",
                                  display: "block",
                                  mb: 1,
                                }}
                              >
                                Ends:{" "}
                                {new Date(
                                  account?.end_date
                                ).toLocaleDateString() || "N/A"}
                              </Typography>
                            )}
                            {account.notes && (
                              <Typography
                                variant="caption"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.6)",
                                  fontFamily: "Anton",
                                  display: "block",
                                  fontStyle: "italic",
                                }}
                              >
                                "{account.notes}"
                              </Typography>
                            )}
                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                mt: 2,
                              }}
                            >
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={account.taxable || false}
                                    onChange={() =>
                                      handleToggleTaxable(
                                        account._id,
                                        account.taxable
                                      )
                                    }
                                    sx={{
                                      color: "rgba(255, 255, 255, 0.5)",
                                      "&.Mui-checked": {
                                        color: "#db4a41",
                                      },
                                    }}
                                  />
                                }
                                label={
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "rgba(255, 255, 255, 0.7)",
                                      fontFamily: "Anton",
                                    }}
                                  >
                                    Taxable
                                  </Typography>
                                }
                              />
                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => {
                                  const cid = account._id || account.client_id;
                                  if (cid) {
                                    navigate(
                                      `/admin/financial/client-dashboard/${cid}`
                                    );
                                  }
                                }}
                                sx={{
                                  borderColor: "#db4a41",
                                  color: "#db4a41",
                                  fontFamily: "Formula Bold",
                                  textTransform: "none",
                                  "&:hover": {
                                    borderColor: "#c62828",
                                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                                  },
                                }}
                              >
                                Analysis
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </motion.div>
                    </Grid>
                  ))
                ) : (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        textAlign: "center",
                        mt: 4,
                      }}
                    >
                      {accounts.length === 0
                        ? "No client accounts found"
                        : "No accounts match your current filters"}
                    </Typography>
                    {accounts.length > 0 &&
                      filteredAndSortedAccounts.length === 0 && (
                        <Typography
                          variant="body2"
                          sx={{
                            color: "rgba(255, 255, 255, 0.5)",
                            textAlign: "center",
                            mt: 1,
                          }}
                        >
                          Try adjusting your search terms or filters
                        </Typography>
                      )}
                  </Grid>
                )}
              </AnimatePresence>
            </Grid>
          )}
        </Box>

        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingAccount
              ? "Edit Client Account"
              : "Create New Client Account"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Client Name"
                  value={newAccount.client_name}
                  onChange={(e) =>
                    setNewAccount({
                      ...newAccount,
                      client_name: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Subscription Fee"
                  type="number"
                  value={newAccount.subscription_fee}
                  onChange={(e) =>
                    setNewAccount({
                      ...newAccount,
                      subscription_fee: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Profit Goal (%)"
                  type="number"
                  value={newAccount.profit_goal_percent}
                  onChange={(e) =>
                    setNewAccount({
                      ...newAccount,
                      profit_goal_percent: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Penalty Rate (%)"
                  type="number"
                  value={newAccount.penalty_rate}
                  onChange={(e) =>
                    setNewAccount({
                      ...newAccount,
                      penalty_rate: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Penalty Grace Days"
                  type="number"
                  value={newAccount.penalty_grace_days}
                  onChange={(e) =>
                    setNewAccount({
                      ...newAccount,
                      penalty_grace_days: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="date"
                  value={newAccount.start_date}
                  onChange={(e) =>
                    setNewAccount({ ...newAccount, start_date: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="date"
                  value={newAccount.end_date}
                  onChange={(e) =>
                    setNewAccount({ ...newAccount, end_date: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Status
                  </InputLabel>
                  <Select
                    value={newAccount.status}
                    onChange={(e) =>
                      setNewAccount({ ...newAccount, status: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="suspended">Suspended</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newAccount.taxable}
                      onChange={(e) =>
                        setNewAccount({
                          ...newAccount,
                          taxable: e.target.checked,
                        })
                      }
                      sx={{
                        color: "rgba(255, 255, 255, 0.5)",
                        "&.Mui-checked": {
                          color: "#db4a41",
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        fontFamily: "Anton",
                      }}
                    >
                      Taxable
                    </Typography>
                  }
                  sx={{ mt: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={newAccount.notes}
                  onChange={(e) =>
                    setNewAccount({ ...newAccount, notes: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingAccount ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Client Account Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingAccount && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(viewingAccount.client_name),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <AccountBalanceIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold", color: "white" }}
                      >
                        {viewingAccount.client_name}
                      </Typography>
                      <FormControl size="small" sx={{ minWidth: 120, mt: 1 }}>
                        <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                          Status
                        </InputLabel>
                        <Select
                          value={viewingAccount.status}
                          onChange={(e) =>
                            handleStatusChange(
                              viewingAccount._id,
                              e.target.value
                            )
                          }
                          sx={{
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "rgba(255, 255, 255, 0.3)",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#db4a41",
                            },
                          }}
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                          <MenuItem value="suspended">Suspended</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Financial Details
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Subscription Fee:</strong>{" "}
                    {viewingAccount.subscription_fee} EGP
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Profit Goal:</strong>{" "}
                    {viewingAccount.profit_goal_percent}%
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Penalty Rate:</strong> {viewingAccount.penalty_rate}
                    %
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Grace Period:</strong>{" "}
                    {viewingAccount.penalty_grace_days} days
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Account Information
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Start Date:</strong>{" "}
                    {new Date(viewingAccount.start_date).toLocaleDateString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Status:</strong>
                    <Chip
                      label={viewingAccount.status}
                      size="small"
                      sx={{
                        ml: 1,
                        backgroundColor: getStatusColor(viewingAccount.status),
                        color: "white",
                        textTransform: "capitalize",
                      }}
                    />
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={viewingAccount.taxable || false}
                          onChange={() =>
                            handleToggleTaxable(
                              viewingAccount._id,
                              viewingAccount.taxable
                            )
                          }
                          sx={{
                            color: "rgba(255, 255, 255, 0.5)",
                            "&.Mui-checked": {
                              color: "#db4a41",
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          sx={{
                            color: "rgba(255, 255, 255, 0.7)",
                            fontFamily: "Anton",
                          }}
                        >
                          Taxable
                        </Typography>
                      }
                    />
                  </Box>
                </Grid>

                {viewingAccount.notes && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#db4a41",
                        mb: 1,
                        fontFamily: "Formula Bold",
                      }}
                    >
                      Notes
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ color: "white", fontStyle: "italic" }}
                    >
                      {viewingAccount.notes}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ClientAccount;
