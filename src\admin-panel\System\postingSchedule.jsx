import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  ToggleButtonGroup,
  ToggleButton,
  Paper,
  Badge,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import EventIcon from "@mui/icons-material/Event";
import CalendarViewMonthIcon from "@mui/icons-material/CalendarViewMonth";
import ViewListIcon from "@mui/icons-material/ViewList";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import FilterListIcon from "@mui/icons-material/FilterList";
import { motion } from "framer-motion";
import { useUser } from "../../contexts/UserContext";

function PostingScheduleManagement() {
  const { user } = useUser();
  const [posts, setPosts] = useState([]);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState("list"); // "list" or "calendar"
  const [selectedClient, setSelectedClient] = useState(""); // For filtering
  const [currentDate, setCurrentDate] = useState(new Date());
  const [dayDetailsModal, setDayDetailsModal] = useState(false);
  const [selectedDayPosts, setSelectedDayPosts] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [newPost, setNewPost] = useState({
    clientId: "",
    name: "",
    date: "",
    caption: "",
    driveLink: "",
  });

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/system/posting-schedule";
  const CLIENTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/clientsManagement";

  const fetchPosts = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/all`);
      const result = await response.json();
      setPosts(result || []);
    } catch (error) {
      console.error("Error fetching posts:", error);
      showSnackbar("Failed to fetch posts", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(CLIENTS_API_URL);
      const result = await response.json();
      setClients(result.data || result || []);
    } catch (error) {
      console.error("Error fetching clients:", error);
      showSnackbar("Failed to fetch clients", "error");
    }
  }, []);

  useEffect(() => {
    fetchPosts();
    fetchClients();
  }, [fetchPosts, fetchClients]);

  const handleAdd = () => {
    setEditingPost(null);
    setNewPost({
      clientId: "",
      name: "",
      date: "",
      caption: "",
      driveLink: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (post) => {
    setEditingPost(post);
    setNewPost({
      clientId: post.clientId._id || post.clientId,
      name: post.name,
      date: post.date ? new Date(post.date).toISOString().split("T")[0] : "",
      caption: post.caption || "",
      driveLink: post.driveLink || "",
    });
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingPost(null);
    setNewPost({
      clientId: "",
      name: "",
      date: "",
      caption: "",
      driveLink: "",
    });
  };

  const handleSubmit = async () => {
    if (!newPost.clientId || !newPost.name || !newPost.date) {
      showSnackbar("Please fill in all required fields", "error");
      return;
    }

    try {
      const postData = {
        ...newPost,
        createdBy: user?.id || user?._id,
      };

      let response;
      if (editingPost) {
        response = await fetch(`${API_BASE_URL}/${editingPost._id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(postData),
        });
      } else {
        response = await fetch(`${API_BASE_URL}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(postData),
        });
      }

      if (response.ok) {
        showSnackbar(
          editingPost
            ? "Post updated successfully"
            : "Post created successfully",
          "success"
        );
        fetchPosts();
        handleCloseModal();
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to save post");
      }
    } catch (error) {
      console.error("Error saving post:", error);
      showSnackbar(error.message || "Failed to save post", "error");
    }
  };

  const handleDelete = async (postId) => {
    if (!window.confirm("Are you sure you want to delete this post?")) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/${postId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        showSnackbar("Post deleted successfully", "success");
        fetchPosts();
      } else {
        throw new Error("Failed to delete post");
      }
    } catch (error) {
      console.error("Error deleting post:", error);
      showSnackbar("Failed to delete post", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Calendar utility functions
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const getMonthName = (date) => {
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" });
  };

  const navigateMonth = (direction) => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      newDate.setMonth(prev.getMonth() + direction);
      return newDate;
    });
  };

  // Filter posts by selected client
  const filteredPosts = selectedClient
    ? posts.filter(
        (post) =>
          post.clientId?._id === selectedClient ||
          post.clientId === selectedClient
      )
    : posts;

  // Get posts for a specific date
  const getPostsForDate = (date) => {
    return filteredPosts.filter((post) => {
      const postDate = new Date(post.date);
      const checkDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        date
      );

      return (
        postDate.getDate() === checkDate.getDate() &&
        postDate.getMonth() === checkDate.getMonth() &&
        postDate.getFullYear() === checkDate.getFullYear()
      );
    });
  };

  // Format date for input fields (YYYY-MM-DD)
  const formatDateForInput = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  // Handle calendar day click
  const handleDayClick = (day) => {
    const dayPosts = getPostsForDate(day);
    const clickedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    const isAuthorized =
      (user?.tier === 1 || user?.tier === 2 || user?.tier === 3) &&
      (user?.role === "account_manager" || user?.role === "general_manager");

    if (dayPosts.length > 0) {
      // ✅ Everyone can view posts
      setSelectedDayPosts(dayPosts);
      setSelectedDay(clickedDate);
      setDayDetailsModal(true);
    } else if (isAuthorized) {
      // ✅ Only authorized users can add new posts
      const dateString = formatDateForInput(clickedDate);
      setNewPost({
        clientId: "",
        name: "",
        date: dateString,
        caption: "",
        driveLink: "",
      });
      setEditingPost(null);
      setOpenModal(true);
    } else {
      // 🚫 Unauthorized users see nothing if empty
      // Optional: show a toast/snackbar to explain
      console.log("You don't have permission to add a new post.");
    }
  };

  // Close day details modal
  const handleCloseDayDetails = () => {
    setDayDetailsModal(false);
    setSelectedDayPosts([]);
    setSelectedDay(null);
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
            flexWrap: "wrap",
            gap: 2,
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Posting Schedule Management
          </Typography>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              flexWrap: "wrap",
            }}
          >
            {/* View Toggle */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(_, newView) => newView && setViewMode(newView)}
              sx={{
                "& .MuiToggleButton-root": {
                  color: "rgba(255, 255, 255, 0.7)",
                  border: "1px solid rgba(255, 255, 255, 0.3)",
                  "&.Mui-selected": {
                    backgroundColor: "#db4a41",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "#c62828",
                    },
                  },
                  "&:hover": {
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                  },
                },
              }}
            >
              <ToggleButton value="list">
                <ViewListIcon sx={{ mr: 1 }} />
                List
              </ToggleButton>
              <ToggleButton value="calendar">
                <CalendarViewMonthIcon sx={{ mr: 1 }} />
                Calendar
              </ToggleButton>
            </ToggleButtonGroup>

            {/* Client Filter */}
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                <FilterListIcon sx={{ mr: 1, fontSize: "small" }} />
                Filter by Client
              </InputLabel>
              <Select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                sx={{
                  color: "white",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.3)",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#db4a41",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                }}
              >
                <MenuItem value="">All Clients</MenuItem>
                {clients.map((client) => (
                  <MenuItem key={client._id} value={client._id}>
                    {client.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {(user?.tier === 2 || user?.tier === 3 || user?.tier === 1) &&
              (user?.role === "account_manager" ||
                user?.role === "general_manager") && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAdd}
                  sx={{
                    backgroundColor: "#db4a41",
                    color: "white",
                    fontFamily: "Formula Bold",
                    "&:hover": {
                      backgroundColor: "#c62828",
                    },
                  }}
                >
                  Add Post
                </Button>
              )}
          </Box>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* Stats Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#4caf50", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Total Posts
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {filteredPosts.length}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#2196f3", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            This Month
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredPosts.filter((post) => {
                              const postDate = new Date(post.date);
                              const now = new Date();
                              return (
                                postDate.getMonth() === now.getMonth() &&
                                postDate.getFullYear() === now.getFullYear()
                              );
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#ff9800", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#ff9800",
                            }}
                          >
                            Upcoming
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredPosts.filter((post) => {
                              const postDate = new Date(post.date);
                              const now = new Date();
                              return postDate > now;
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <EventIcon sx={{ color: "#f44336", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#f44336",
                            }}
                          >
                            Posted
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {
                            filteredPosts.filter((post) => {
                              const postDate = new Date(post.date);
                              const now = new Date();
                              return postDate < now;
                            }).length
                          }
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Calendar View */}
              {viewMode === "calendar" && (
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                    mb: 4,
                  }}
                >
                  <CardContent>
                    {/* Calendar Header */}
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mb: 3,
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                      >
                        Calendar View
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <IconButton
                          onClick={() => navigateMonth(-1)}
                          sx={{ color: "white" }}
                        >
                          <ChevronLeftIcon />
                        </IconButton>
                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            minWidth: "200px",
                            textAlign: "center",
                          }}
                        >
                          {getMonthName(currentDate)}
                        </Typography>
                        <IconButton
                          onClick={() => navigateMonth(1)}
                          sx={{ color: "white" }}
                        >
                          <ChevronRightIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Calendar Grid */}
                    <Box
                      sx={{
                        display: "grid",
                        gridTemplateColumns: "repeat(7, 1fr)",
                        gap: 1,
                        mb: 2,
                      }}
                    >
                      {/* Day Headers */}
                      {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                        (day) => (
                          <Box
                            key={day}
                            sx={{
                              p: 1,
                              textAlign: "center",
                              fontWeight: "bold",
                              color: "#db4a41",
                              borderBottom:
                                "1px solid rgba(255, 255, 255, 0.1)",
                            }}
                          >
                            {day}
                          </Box>
                        )
                      )}

                      {/* Empty cells for days before month starts */}
                      {Array.from({
                        length: getFirstDayOfMonth(currentDate),
                      }).map((_, index) => (
                        <Box
                          key={`empty-${index}`}
                          sx={{ p: 1, minHeight: 80 }}
                        />
                      ))}

                      {/* Calendar Days */}
                      {Array.from({ length: getDaysInMonth(currentDate) }).map(
                        (_, index) => {
                          const day = index + 1;
                          const dayPosts = getPostsForDate(day);
                          const isToday =
                            new Date().toDateString() ===
                            new Date(
                              currentDate.getFullYear(),
                              currentDate.getMonth(),
                              day
                            ).toDateString();

                          return (
                            <Paper
                              key={day}
                              onClick={() => handleDayClick(day)}
                              sx={{
                                p: 1,
                                minHeight: 80,
                                backgroundColor: isToday
                                  ? "rgba(219, 74, 65, 0.2)"
                                  : "rgba(255, 255, 255, 0.02)",
                                border: isToday
                                  ? "2px solid #db4a41"
                                  : "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                                cursor: "pointer",
                                transition: "all 0.2s",
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                  transform: "translateY(-2px)",
                                },
                              }}
                            >
                              <Typography
                                variant="body2"
                                sx={{
                                  color: isToday ? "#db4a41" : "white",
                                  fontWeight: isToday ? "bold" : "normal",
                                  mb: 0.5,
                                }}
                              >
                                {day}
                              </Typography>

                              {/* Post indicators */}
                              {dayPosts.slice(0, 3).map((post) => (
                                <Badge
                                  key={post._id}
                                  sx={{
                                    display: "block",
                                    mb: 0.5,
                                    "& .MuiBadge-badge": {
                                      position: "static",
                                      transform: "none",
                                      backgroundColor: "#4caf50", // Green for posts
                                      color: "white",
                                      fontSize: "10px",
                                      height: "16px",
                                      minWidth: "16px",
                                      borderRadius: "8px",
                                      padding: "0 4px",
                                    },
                                  }}
                                  badgeContent={
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontSize: "9px",
                                        fontWeight: "bold",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        whiteSpace: "nowrap",
                                        maxWidth: "60px",
                                      }}
                                    >
                                      {post.name.length > 8
                                        ? `${post.name.substring(0, 8)}...`
                                        : post.name}
                                    </Typography>
                                  }
                                />
                              ))}

                              {/* Show "+X more" if there are more posts */}
                              {dayPosts.length > 3 && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "#db4a41",
                                    fontSize: "9px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  +{dayPosts.length - 3} more
                                </Typography>
                              )}
                            </Paper>
                          );
                        }
                      )}
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Posts Table */}
              {viewMode === "list" && (
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                        mb: 3,
                      }}
                    >
                      Posting Schedules
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Name
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Client
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Date
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Caption
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Drive Link
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Status
                            </TableCell>
                            <TableCell
                              sx={{ color: "white", fontWeight: "bold" }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {filteredPosts
                            .slice(
                              page * rowsPerPage,
                              page * rowsPerPage + rowsPerPage
                            )
                            .map((post) => (
                              <TableRow key={post._id}>
                                <TableCell>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      color: "white",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    {post.name}
                                  </Typography>
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {post.clientId?.name || "N/A"}
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {new Date(post.date).toLocaleDateString()}
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {post.caption
                                    ? post.caption.length > 50
                                      ? `${post.caption.substring(0, 50)}...`
                                      : post.caption
                                    : "N/A"}
                                </TableCell>
                                <TableCell sx={{ color: "white" }}>
                                  {post.driveLink ? (
                                    <a
                                      href={post.driveLink}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      style={{ color: "#2196f3" }}
                                    >
                                      Link
                                    </a>
                                  ) : (
                                    "N/A"
                                  )}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    label={
                                      new Date(post.date) < new Date()
                                        ? "Posted"
                                        : "Scheduled"
                                    }
                                    sx={{
                                      backgroundColor:
                                        new Date(post.date) < new Date()
                                          ? "#4caf50"
                                          : "#ff9800",
                                      color: "white",
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {(user?.tier === 2 ||
                                    user?.tier === 3 ||
                                    user?.tier === 1) &&
                                    (user?.role === "account_manager" ||
                                      user?.role === "general_manager") && (
                                      <Box sx={{ display: "flex", gap: 1 }}>
                                        <Tooltip title="Edit Post">
                                          <IconButton
                                            size="small"
                                            onClick={() => handleEdit(post)}
                                            sx={{ color: "#2196f3" }}
                                          >
                                            <EditIcon />
                                          </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Delete Post">
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              handleDelete(post._id)
                                            }
                                            sx={{ color: "#f44336" }}
                                          >
                                            <DeleteIcon />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    )}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    <TablePagination
                      component="div"
                      count={filteredPosts.length}
                      page={page}
                      onPageChange={(_, newPage) => setPage(newPage)}
                      rowsPerPage={rowsPerPage}
                      onRowsPerPageChange={(event) => {
                        setRowsPerPage(parseInt(event.target.value, 10));
                        setPage(0);
                      }}
                      sx={{
                        color: "white",
                        "& .MuiTablePagination-selectIcon": {
                          color: "white",
                        },
                      }}
                    />
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </Box>

        {/* Add/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingPost ? "Edit Post" : "Add New Post"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Name"
                  value={newPost.name}
                  onChange={(e) =>
                    setNewPost({ ...newPost, name: e.target.value })
                  }
                  required
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client
                  </InputLabel>
                  <Select
                    value={newPost.clientId}
                    onChange={(e) =>
                      setNewPost({ ...newPost, clientId: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {clients.map((client) => (
                      <MenuItem key={client._id} value={client._id}>
                        {client.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date"
                  type="date"
                  value={newPost.date}
                  onChange={(e) =>
                    setNewPost({ ...newPost, date: e.target.value })
                  }
                  required
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Drive Link"
                  value={newPost.driveLink}
                  onChange={(e) =>
                    setNewPost({ ...newPost, driveLink: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Caption"
                  multiline
                  rows={3}
                  value={newPost.caption}
                  onChange={(e) =>
                    setNewPost({ ...newPost, caption: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingPost ? "Update Post" : "Create Post"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Day Details Modal */}
        <Dialog
          open={dayDetailsModal}
          onClose={handleCloseDayDetails}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box>
              Posts for{" "}
              {selectedDay?.toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </Box>
            {(user?.tier === 2 || user?.tier === 3) &&
              (user?.role === "account_manager" ||
                user?.role === "general_manager") && (
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const dateString = formatDateForInput(selectedDay);
                    setNewPost({
                      clientId: "",
                      name: "",
                      date: dateString,
                      caption: "",
                      driveLink: "",
                    });
                    setEditingPost(null);
                    handleCloseDayDetails();
                    setOpenModal(true);
                  }}
                  sx={{
                    backgroundColor: "#db4a41",
                    "&:hover": { backgroundColor: "#c62828" },
                  }}
                >
                  Add Post
                </Button>
              )}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {selectedDayPosts.length > 0 ? (
              <Grid container spacing={2}>
                {selectedDayPosts.map((post) => (
                  <Grid item xs={12} key={post._id}>
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        cursor: "pointer",
                        transition: "all 0.2s",
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          transform: "translateY(-2px)",
                        },
                      }}
                      onClick={() => {
                        handleEdit(post);
                        handleCloseDayDetails();
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            mb: 2,
                          }}
                        >
                          <Box>
                            <Typography
                              variant="h6"
                              sx={{
                                color: "white",
                                fontFamily: "Formula Bold",
                                mb: 1,
                              }}
                            >
                              {post.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{ color: "rgba(255, 255, 255, 0.7)", mb: 1 }}
                            >
                              Client: {post.clientId?.name || "N/A"}
                            </Typography>
                            {post.caption && (
                              <Typography
                                variant="body2"
                                sx={{
                                  color: "rgba(255, 255, 255, 0.6)",
                                  mb: 1,
                                }}
                              >
                                Caption:{" "}
                                {post.caption.length > 100
                                  ? `${post.caption.substring(0, 100)}...`
                                  : post.caption}
                              </Typography>
                            )}
                            {post.driveLink && (
                              <Typography
                                variant="body2"
                                sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                              >
                                Drive Link: {post.driveLink}
                              </Typography>
                            )}
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "flex-end",
                              gap: 1,
                            }}
                          >
                            <Chip
                              label="Post"
                              sx={{
                                backgroundColor: "#4caf50",
                                color: "white",
                              }}
                            />
                            <Typography
                              variant="caption"
                              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                            >
                              {new Date(post.date).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Box>
                        {(user?.tier === 2 || user?.tier === 3) &&
                          (user?.role === "account_manager" ||
                            user?.role === "general_manager") && (
                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                                gap: 1,
                              }}
                            >
                              <Tooltip title="Edit Post">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEdit(post);
                                    handleCloseDayDetails();
                                  }}
                                  sx={{ color: "#2196f3" }}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Post">
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDelete(post._id);
                                    handleCloseDayDetails();
                                  }}
                                  sx={{ color: "#f44336" }}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Typography
                variant="body1"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  textAlign: "center",
                  py: 4,
                }}
              >
                No posts scheduled for this day
              </Typography>
            )}
          </DialogContent>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default PostingScheduleManagement;
