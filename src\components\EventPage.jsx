import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import Event from "./Event";
import LoadingScreen from "./LoadingScreen";

const EventPage = () => {
  const { eventId } = useParams();
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch(
      `https://youngproductions-768ada043db3.herokuapp.com/api/work/${eventId}`
    )
      .then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        return res.json();
      })
      .then((data) => {
        console.log("Fetched event data:", data);
        // The API should return a single event object, not an array
        setEvent(data);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error fetching event:", err);
        setLoading(false);
      });
  }, [eventId]);

  if (loading) return <LoadingScreen />;

  if (!event)
    return <div style={{ color: "white", padding: 20 }}>Event not found</div>;

  return (
    <div className="event-page">
      <Event
        location={event.location}
        date={event.date}
        title={event.title}
        image={event.imageUrl}
        logoUrl={event.logoUrl}
        videoUrl={event.videoUrl}
        description={event.description}
        headquarters={event.headquarters}
        facebook={event.facebook}
        instagram={event.instagram}
        tiktok={event.tiktok}
        galleryImages={[...(event.galleryImages || [])]}
      />
    </div>
  );
};

export default EventPage;
