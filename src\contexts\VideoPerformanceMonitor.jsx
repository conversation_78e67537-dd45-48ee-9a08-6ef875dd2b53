/**
 * Video Performance Monitor
 * Tracks video downloads, enforces budgets, and provides observability
 */

class VideoPerformanceMonitor {
  constructor() {
    this.videoMetrics = new Map();
    this.totalBytesDownloaded = 0;
    this.totalRequests = 0;
    this.budgets = {
      initialRequests: 15,
      initialBytes: 2 * 1024 * 1024, // 2MB
      initialVideos: 1
    };
    this.violations = [];
    this.startTime = performance.now();
    
    this.initPerformanceObserver();
    this.initResourceTiming();
  }

  initPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      // Monitor resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.initiatorType === 'video' || 
              entry.name.includes('.mp4') || 
              entry.name.includes('.webm')) {
            this.trackVideoResource(entry);
          }
        }
      });

      resourceObserver.observe({ entryTypes: ['resource'] });

      // Monitor navigation timing
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackNavigationMetrics(entry);
        }
      });

      navigationObserver.observe({ entryTypes: ['navigation'] });
    }
  }

  initResourceTiming() {
    // Monitor existing resources
    const resources = performance.getEntriesByType('resource');
    resources.forEach(entry => {
      if (entry.initiatorType === 'video' || 
          entry.name.includes('.mp4') || 
          entry.name.includes('.webm')) {
        this.trackVideoResource(entry);
      }
    });
  }

  trackVideoResource(entry) {
    const videoId = this.extractVideoId(entry.name);
    const bytesDownloaded = entry.transferSize || entry.encodedBodySize || 0;
    
    const metrics = {
      videoId,
      url: entry.name,
      startTime: entry.startTime,
      duration: entry.duration,
      bytesDownloaded,
      timeToFirstByte: entry.responseStart - entry.requestStart,
      downloadTime: entry.responseEnd - entry.responseStart,
      timestamp: Date.now()
    };

    this.videoMetrics.set(videoId, metrics);
    this.totalBytesDownloaded += bytesDownloaded;
    this.totalRequests++;

    // Check budget violations
    this.checkBudgetViolations(metrics);

    // Log metrics
    console.log(`Video loaded: ${videoId}`, {
      bytes: this.formatBytes(bytesDownloaded),
      duration: `${metrics.duration.toFixed(2)}ms`,
      ttfb: `${metrics.timeToFirstByte.toFixed(2)}ms`
    });
  }

  trackVideoFirstFrame(videoId, videoElement) {
    const startTime = performance.now();
    
    const handleFirstFrame = () => {
      const timeToFirstFrame = performance.now() - startTime;
      
      if (this.videoMetrics.has(videoId)) {
        const metrics = this.videoMetrics.get(videoId);
        metrics.timeToFirstFrame = timeToFirstFrame;
        this.videoMetrics.set(videoId, metrics);
      }

      console.log(`Video first frame: ${videoId}`, {
        timeToFirstFrame: `${timeToFirstFrame.toFixed(2)}ms`
      });

      videoElement.removeEventListener('loadeddata', handleFirstFrame);
    };

    videoElement.addEventListener('loadeddata', handleFirstFrame, { once: true });
  }

  trackNavigationMetrics(entry) {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      largestContentfulPaint: this.getLargestContentfulPaint()
    };

    console.log('Navigation metrics:', metrics);
  }

  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcp ? fcp.startTime : null;
  }

  getLargestContentfulPaint() {
    return new Promise((resolve) => {
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          resolve(lastEntry.startTime);
          lcpObserver.disconnect();
        });

        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        
        // Timeout after 10 seconds
        setTimeout(() => {
          lcpObserver.disconnect();
          resolve(null);
        }, 10000);
      } else {
        resolve(null);
      }
    });
  }

  checkBudgetViolations(metrics) {
    const timeSinceStart = performance.now() - this.startTime;
    const isInitialLoad = timeSinceStart < 3000; // First 3 seconds

    if (isInitialLoad) {
      // Check initial load budgets
      if (this.totalRequests > this.budgets.initialRequests) {
        this.addViolation('REQUEST_BUDGET_EXCEEDED', {
          current: this.totalRequests,
          budget: this.budgets.initialRequests
        });
      }

      if (this.totalBytesDownloaded > this.budgets.initialBytes) {
        this.addViolation('BYTES_BUDGET_EXCEEDED', {
          current: this.totalBytesDownloaded,
          budget: this.budgets.initialBytes
        });
      }

      const videoCount = Array.from(this.videoMetrics.keys()).length;
      if (videoCount > this.budgets.initialVideos) {
        this.addViolation('VIDEO_COUNT_EXCEEDED', {
          current: videoCount,
          budget: this.budgets.initialVideos
        });
      }
    }
  }

  addViolation(type, data) {
    const violation = {
      type,
      data,
      timestamp: Date.now(),
      timeSinceStart: performance.now() - this.startTime
    };

    this.violations.push(violation);
    
    console.warn(`Budget violation: ${type}`, data);
    
    // In development, throw error to fail build
    if (process.env.NODE_ENV === 'development') {
      console.error(`BUDGET VIOLATION: ${type}`, violation);
    }
  }

  extractVideoId(url) {
    // Extract video ID from URL
    const parts = url.split('/');
    const filename = parts[parts.length - 1];
    return filename.split('?')[0].split('#')[0];
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Get current performance report
  getPerformanceReport() {
    const videoMetricsArray = Array.from(this.videoMetrics.values());
    
    return {
      summary: {
        totalVideos: videoMetricsArray.length,
        totalRequests: this.totalRequests,
        totalBytes: this.totalBytesDownloaded,
        totalBytesFormatted: this.formatBytes(this.totalBytesDownloaded),
        averageVideoSize: videoMetricsArray.length > 0 
          ? this.totalBytesDownloaded / videoMetricsArray.length 
          : 0,
        violations: this.violations.length
      },
      budgets: this.budgets,
      violations: this.violations,
      videoMetrics: videoMetricsArray,
      timeSinceStart: performance.now() - this.startTime
    };
  }

  // Export metrics for analysis
  exportMetrics() {
    const report = this.getPerformanceReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `video-performance-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // Reset all metrics
  reset() {
    this.videoMetrics.clear();
    this.totalBytesDownloaded = 0;
    this.totalRequests = 0;
    this.violations = [];
    this.startTime = performance.now();
  }
}

// Create singleton instance
const videoPerformanceMonitor = new VideoPerformanceMonitor();

// Expose to window for debugging
if (typeof window !== 'undefined') {
  window.videoPerformanceMonitor = videoPerformanceMonitor;
}

export default videoPerformanceMonitor;
