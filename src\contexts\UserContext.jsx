import React, { createContext, useState, useContext, useEffect } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Check for existing session on mount
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      const response = await axios.post(
        "https://youngproductions-768ada043db3.herokuapp.com/api/auth/login",
        {
          email: email.trim(),
          password,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          withCredentials: true,
        }
      );

      const data = response.data;
      console.log("Login response data:", data);
      if (data.user?.status === "inactive") {
        return {
          success: false,
          message: "Your account is inactive. Please contact admin.",
        };
      }
      if (data.token && data.user) {
        setUser(data.user);
        localStorage.setItem("token", data.token);
        localStorage.setItem("user", JSON.stringify(data.user));
        console.log("User logged in:", data.user);
        return { success: true };
      } else {
        console.log("Login failed - missing token or user:", {
          token: !!data.token,
          user: !!data.user,
        });
        return { success: false, message: data.message || "Login failed" };
      }
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        message:
          error.response?.data?.message || "An error occurred during login",
      };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await axios.post(
        "https://youngproductions-768ada043db3.herokuapp.com/api/auth/logout",
        {},
        { withCredentials: true }
      );
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      // Clear local state regardless of server response
      setUser(null);
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      navigate("/admin/login");
    }
  };

  // Get current user
  const getUser = () => {
    return user;
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user;
  };

  // Change password function
  const changePassword = async (
    currentPassword,
    newPassword,
    confirmNewPassword
  ) => {
    try {
      const token = localStorage.getItem("token");
      if (!token || !user) {
        return {
          success: false,
          message: "User not authenticated",
        };
      }

      const response = await axios.put(
        `https://youngproductions-768ada043db3.herokuapp.com/api/auth/change-password/${user.id}`,
        {
          currentPassword,
          newPassword,
          confirmNewPassword,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.data.success) {
        return {
          success: false,
          message: response.data.message || "Failed to change password",
        };
      }

      return {
        success: true,
        message: "Password changed successfully",
      };
    } catch (error) {
      console.error("Change password error:", error);
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "An error occurred while changing password",
      };
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        login,
        logout,
        getUser,
        isAuthenticated,
        changePassword,
        loading,
      }}
    >
      {!loading && children}
    </UserContext.Provider>
  );
};

// Custom hook to use the UserContext
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export default UserContext;
