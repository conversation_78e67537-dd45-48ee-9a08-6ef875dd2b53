import React, { useEffect, useState } from "react";
import {
  Box,
  Container,
  Grid,
  Typography,
  Drawer,
  IconButton,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { motion } from "framer-motion";
import CloseIcon from "@mui/icons-material/Close";
import axios from "axios";

const API_URL =
  "https://youngproductions-768ada043db3.herokuapp.com/api/cms/young-people";

// Normalize image base URL
const normalizeImage = (url) =>
  url?.replace(
    "https://youngproductionss.com",
    "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
  );

const PeoplePage = () => {
  const [employees, setEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Fetch people
  useEffect(() => {
    const fetchPeople = async () => {
      try {
        const res = await axios.get(API_URL);
        setEmployees(res.data.data);
      } catch (err) {
        console.error("Failed to fetch people", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPeople();
  }, []);

  const handleEmployeeClick = (employee) => {
    setSelectedEmployee(employee);
    setDrawerOpen(true);
  };

  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedEmployee(null);
  };

  // Animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  if (loading) {
    return (
      <Box sx={{ color: "#fff", p: 10, backgroundColor: "black" }}>
        Loading...
      </Box>
    );
  }

  return (
    <Box sx={{ py: 18, backgroundColor: "black", minHeight: "100vh" }}>
      <Container maxWidth="2xl" sx={{ width: "90%" }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          style={{ marginBottom: "4rem" }}
        >
          <Typography
            variant="h1"
            sx={{
              fontFamily: "Formula Bold",
              mb: 2,
              fontSize: { xs: "3rem", md: "15rem" },
              color: "#fff",
              lineHeight: 1.1,
            }}
          >
            Young <span style={{ color: "#db4a41" }}>People</span>
          </Typography>

          <Typography
            variant="h4"
            sx={{
              fontFamily: "Formula Bold",
              color: "#fff",
              fontSize: { xs: "1.5rem", md: "2rem" },
            }}
          >
            Meet the really good people behind Good People.
          </Typography>
        </motion.div>

        {/* Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={6}>
            {employees.map((employee) => (
              <Grid item xs={12} md={6} lg={4} key={employee._id}>
                <motion.div variants={itemVariants}>
                  <Box
                    sx={{ cursor: "pointer", overflow: "hidden" }}
                    onClick={() => handleEmployeeClick(employee)}
                  >
                    <Box
                      sx={{
                        width: "100%",
                        height: "400px",
                        overflow: "hidden",
                        borderRadius: "8px",
                        mb: 2,
                      }}
                    >
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.8, ease: "easeInOut" }}
                        style={{ width: "100%", height: "100%" }}
                      >
                        <Box
                          component="img"
                          src={normalizeImage(employee.image)}
                          alt={employee.name}
                          sx={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </motion.div>
                    </Box>

                    <Typography
                      variant="h3"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#fff",
                        fontSize: { xs: "1.5rem", md: "2rem" },
                      }}
                    >
                      {employee.name}
                    </Typography>

                    <Typography
                      sx={{
                        color: "lightgray",
                        fontFamily: "Anton",
                        fontSize: { xs: "0.7rem", md: "1rem" },
                      }}
                    >
                      {employee.shortDescription}
                    </Typography>
                  </Box>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
        {/* Drawer */}
        <Drawer
          anchor="right"
          open={drawerOpen}
          onClose={handleCloseDrawer}
          ModalProps={{
            keepMounted: false,
            BackdropProps: {
              sx: {
                backgroundColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          }}
          sx={{
            zIndex: 10001, // ensures Drawer itself is above other elements
            "& .MuiDrawer-paper": {
              width: isMobile ? "100%" : "50%", // half screen on desktop
              maxWidth: isMobile ? "100%" : "50%",
              backgroundColor: "#fff",
              overflowX: "hidden",
              position: "fixed",
              top: 0,
              right: 0,
              bottom: 0,
              backdropFilter: "blur(10px)",
              boxShadow: "-4px 0 20px rgba(0, 0, 0, 0.3)",
            },
          }}
        >
          {selectedEmployee && (
            <Box
              sx={{
                p: 4, // padding for the content inside the Drawer paper
                height: "100%",
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 4, // global spacing between sections
              }}
            >
              <IconButton
                onClick={handleCloseDrawer}
                sx={{ position: "absolute", top: 16, right: 16 }}
              >
                <CloseIcon />
              </IconButton>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "left",
                }}
              >
                <Box
                  component="img"
                  src={normalizeImage(selectedEmployee.image)}
                  alt={selectedEmployee.name}
                  sx={{
                    width: 500,
                    height: 500,
                    objectFit: "cover",
                    borderRadius: "8px",
                    maxWidth: "100%", // prevents overflow on small screens
                    maxHeight: "100%",
                  }}
                />
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Typography variant="h4" sx={{ fontFamily: "Formula Bold" }}>
                  {selectedEmployee.name}
                </Typography>

                <Typography
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  {selectedEmployee.title}
                </Typography>

                <Typography sx={{ fontFamily: "Anton", lineHeight: 1.7 }}>
                  {selectedEmployee.bio}
                </Typography>
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "Formula Bold",
                    mb: 2,
                    fontSize: { xs: "1.1rem" },
                  }}
                >
                  Hobbies & Interests
                </Typography>

                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
                  {selectedEmployee.hobbies?.map((hobby, index) => (
                    <Box
                      key={index}
                      sx={{
                        backgroundColor: "#f5f5f5",
                        px: 1.5,
                        py: 0.5,
                        borderRadius: "15px",
                        fontFamily: "Anton",
                        fontSize: { xs: "0.75rem", md: "0.85rem" },
                        color: "#333",
                        wordBreak: "break-word",
                        maxWidth: "100%",
                      }}
                    >
                      {hobby}
                    </Box>
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </Drawer>
      </Container>
    </Box>
  );
};

export default PeoplePage;
