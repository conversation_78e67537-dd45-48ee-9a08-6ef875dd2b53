import React, { useEffect, useState, useRef, Suspense, memo } from "react";
import { Link } from "react-router-dom";
import Vimeo from "@u-wave/react-vimeo";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import BlurText from "../components/animations/BlurText";
import OptimizedVideo from "../contexts/OptimizedVideoHome";

gsap.registerPlugin(ScrollTrigger);

// Memoized BlurText for performance
const MemoBlurText = memo(BlurText);

// Get Vimeo ID from URL
const getVimeoId = (url) => {
  if (!url) return null;
  const match = url.match(/vimeo\.com\/(?:video\/)?(\d+)/);
  return match ? match[1] : null;
};

const handleAnimationComplete = () => console.log("Animation completed!");

// Lazy wrapper for Vimeo videos
const LazyVimeo = memo(({ videoId }) => {
  const ref = useRef();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) setIsVisible(true);
      },
      { rootMargin: "200px" }
    );
    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, []);

  return (
    <div ref={ref}>{isVisible && <Vimeo video={videoId} responsive />}</div>
  );
});

// Lazy wrapper for OptimizedVideo
const LazyOptimizedVideo = memo(({ src, poster }) => {
  const ref = useRef();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) setIsVisible(true);
      },
      { rootMargin: "200px" }
    );
    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, []);

  return (
    <div ref={ref}>
      {isVisible && (
        <OptimizedVideo
          src={src}
          muted
          playsInline
          preload="metadata"
          lazy
          poster={poster}
        />
      )}
    </div>
  );
});

function Insights() {
  const [events, setEvents] = useState([]);
  const cardsRef = useRef([]);

  // Fetch videos
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const res = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/work"
        );
        const json = await res.json();
        const list = Array.isArray(json.data) ? json.data : [];

        const randomized = list.map((item) => ({
          ...item,
          offset: Math.random() * 160 - 80,
          speed: 0.6 + Math.random() * 0.6,
        }));

        setEvents(randomized);
      } catch (error) {
        console.error("Failed to fetch videos:", error);
        setEvents([]);
      }
    };

    fetchVideos();
  }, []);

  // GSAP scroll animations (batched for performance)
  useEffect(() => {
    if (!cardsRef.current.length) return;

    ScrollTrigger.batch(cardsRef.current, {
      onEnter: (batch) =>
        gsap.to(batch, { y: 0, opacity: 1, stagger: 0.1, ease: "easeOut" }),
      start: "top bottom",
      end: "bottom top",
      scrub: true,
    });
  }, [events]);

  return (
    <Box sx={{ backgroundColor: "#fff", overflow: "hidden" }}>
      {/* INTRO TEXT */}
      <Box sx={{ px: { xs: 3, md: 10 }, pt: 16, maxWidth: 1440 }}>
        <MemoBlurTextBlock text="Our work." colorMap={["#000", "#000"]} />
        <MemoBlurTextBlock
          text="A living archive"
          colorMap={["#000", "#000", "#000"]}
        />
        <MemoBlurTextBlock
          text="of moving stories."
          colorMap={["#000", "#000", "#000"]}
        />
      </Box>

      {/* GALLERY */}
      <Box sx={{ px: { xs: 3, md: 10 }, mt: { xs: 8, md: 16 } }}>
        {events.map((event, index) => {
          const vimeoId = event.videoUrl.includes("vimeo")
            ? getVimeoId(event.videoUrl)
            : null;

          return (
            <motion.div
              key={event._id}
              ref={(el) => (cardsRef.current[index] = el)}
              style={{ transform: `translateY(${event.offset}px)` }}
              initial={{ opacity: 0, y: 80 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <Box
                component={Link}
                to={`/test/event/${event._id}`}
                sx={{
                  display: "block",
                  width: "85%",
                  mb: 24,
                  ml: index % 2 === 0 ? "0" : "auto",
                  textDecoration: "none",
                  color: "inherit",
                }}
              >
                {/* MEDIA */}
                <Box
                  sx={{ height: "70vh", borderRadius: 2, overflow: "hidden" }}
                >
                  {vimeoId ? (
                    <Suspense fallback={<div style={{ height: "70vh" }} />}>
                      <LazyVimeo videoId={vimeoId} />
                    </Suspense>
                  ) : (
                    <Suspense fallback={<div style={{ height: "70vh" }} />}>
                      <LazyOptimizedVideo
                        src={event.videoUrl.replace(
                          "https://youngproductionss.com/",
                          "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                        )}
                        poster={event.thumbnailUrl} // add thumbnails for better performance
                      />
                    </Suspense>
                  )}
                </Box>

                {/* META */}
                <Box sx={{ mt: 2 }}>
                  <Typography sx={{ fontFamily: "Formula Bold", fontSize: 22 }}>
                    {event.title}
                  </Typography>
                  <Typography sx={{ fontSize: 14, color: "#777", mt: 0.5 }}>
                    {event.description}
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          );
        })}
      </Box>
    </Box>
  );
}

// Helper component to memoize BlurText blocks
const MemoBlurTextBlock = memo(({ text, colorMap }) => (
  <Box
    sx={{
      fontFamily: "Formula Bold",
      fontSize: { xs: "42px", sm: "56px", md: "72px", lg: "200px" },
      color: "#000",
      marginBottom: "-0.8em",
      lineHeight: 1.9,
    }}
  >
    <MemoBlurText
      text={text}
      delay={100}
      animateBy="words"
      direction="top"
      onAnimationComplete={handleAnimationComplete}
      colorMap={colorMap}
    />
  </Box>
));

export default Insights;
