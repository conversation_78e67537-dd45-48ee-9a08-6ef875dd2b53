import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Avatar,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import FilterListIcon from "@mui/icons-material/FilterList";
import CalculateIcon from "@mui/icons-material/Calculate";
import ReceiptIcon from "@mui/icons-material/Receipt";
import ClearIcon from "@mui/icons-material/Clear";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import SortIcon from "@mui/icons-material/Sort";
import { useUser } from "../../contexts/UserContext";

function AgencyExpenses() {
  const { user } = useUser();
  const [expenses, setExpenses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingExpense, setEditingExpense] = useState(null);
  const [viewingExpense, setViewingExpense] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState({
    category: "",
    date_from: "",
    date_to: "",
    amount_min: "",
    amount_max: "",
    month: "",
    year: "",
  });
  const [sortConfig, setSortConfig] = useState({
    key: null,
    direction: "asc",
  });
  const [newExpense, setNewExpense] = useState({
    title: "",
    category: "other",
    amount: "",
    date: "",
    description: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/agency-expenses";

  const fetchExpenses = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setExpenses(result.data || []);
    } catch (error) {
      console.error("Error fetching expenses:", error);
      showSnackbar("Failed to fetch expenses", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchExpenses();
  }, [fetchExpenses]);

  const handleAdd = () => {
    setEditingExpense(null);
    setNewExpense({
      title: "",
      category: "other",
      amount: "",
      date: "",
      description: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (expense) => {
    setEditingExpense(expense);
    setNewExpense({
      title: expense.title,
      category: expense.category,
      amount: expense.amount?.$numberDecimal || expense.amount,
      date: expense.date
        ? new Date(expense.date).toISOString().slice(0, 10)
        : "",
      description: expense.description || "",
    });
    setOpenModal(true);
  };

  const handleView = (expense) => {
    setViewingExpense(expense);
    setOpenViewModal(true);
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingExpense(null);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingExpense(null);
    setNewExpense({
      title: "",
      category: "other",
      amount: "",
      date: "",
      description: "",
    });
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");

      const expenseData = {
        title: newExpense.title,
        category: newExpense.category,
        amount: parseFloat(newExpense.amount),
        date: newExpense.date,
        description: newExpense.description,
        created_by: user?.id || user?._id,
      };

      const url = editingExpense
        ? `${API_BASE_URL}/${editingExpense._id}`
        : API_BASE_URL;

      const method = editingExpense ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(expenseData),
      });

      if (response.ok) {
        const result = await response.json();
        if (editingExpense) {
          setExpenses(
            expenses.map((expense) =>
              expense._id === editingExpense._id ? result.data : expense
            )
          );
          showSnackbar("Expense updated successfully", "success");
        } else {
          setExpenses([result.data, ...expenses]);
          showSnackbar("Expense created successfully", "success");
        }
        handleCloseModal();
        fetchExpenses(); // Refresh to get populated data
      } else {
        throw new Error("Failed to save expense");
      }
    } catch (error) {
      console.error("Error saving expense:", error);
      showSnackbar("Failed to save expense", "error");
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this expense?")) {
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        setExpenses(expenses.filter((expense) => expense._id !== id));
        showSnackbar("Expense deleted successfully", "success");
      } else {
        throw new Error("Failed to delete expense");
      }
    } catch (error) {
      console.error("Error deleting expense:", error);
      showSnackbar("Failed to delete expense", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case "equipment":
        return "#2196f3";
      case "office":
        return "#4caf50";
      case "miscellaneous":
        return "#ff9800";
      case "marketing":
        return "#9c27b0";
      case "other":
        return "#9e9e9e";
      default:
        return "#9e9e9e";
    }
  };

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  // Sorting function
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Clear sort function
  const clearSort = () => {
    setSortConfig({ key: null, direction: "asc" });
  };

  // Filter expenses based on current filters
  const filteredExpenses = expenses.filter((expense) => {
    const expenseDate = new Date(expense.date);

    if (filters.category && expense.category !== filters.category) return false;
    if (filters.date_from && expenseDate < new Date(filters.date_from))
      return false;
    if (filters.date_to && expenseDate > new Date(filters.date_to))
      return false;
    if (
      filters.amount_min &&
      parseFloat(expense.amount?.$numberDecimal || expense.amount) <
        parseFloat(filters.amount_min)
    )
      return false;
    if (
      filters.amount_max &&
      parseFloat(expense.amount?.$numberDecimal || expense.amount) >
        parseFloat(filters.amount_max)
    )
      return false;

    // Month filter (0-based index, so January = 0, December = 11)
    if (filters.month && expenseDate.getMonth() !== parseInt(filters.month))
      return false;

    // Year filter
    if (filters.year && expenseDate.getFullYear() !== parseInt(filters.year))
      return false;

    return true;
  });

  // Sort expenses
  const sortedExpenses = React.useMemo(() => {
    let sortableExpenses = [...filteredExpenses];

    if (sortConfig.key) {
      // Custom sorting based on selected column
      sortableExpenses.sort((a, b) => {
        let aValue, bValue;

        if (sortConfig.key === "amount") {
          aValue = parseFloat(a.amount?.$numberDecimal || a.amount || 0);
          bValue = parseFloat(b.amount?.$numberDecimal || b.amount || 0);
        } else if (sortConfig.key === "date") {
          aValue = new Date(a.date);
          bValue = new Date(b.date);
        } else {
          aValue = a[sortConfig.key];
          bValue = b[sortConfig.key];
        }

        if (aValue < bValue) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    } else {
      // Default sorting: newest to oldest by createdAt
      sortableExpenses.sort((a, b) => {
        const aCreatedAt = new Date(a.createdAt);
        const bCreatedAt = new Date(b.createdAt);
        return bCreatedAt - aCreatedAt; // Newest first (descending)
      });
    }

    return sortableExpenses;
  }, [filteredExpenses, sortConfig]);

  // Calculate totals for filtered expenses
  const calculateTotals = () => {
    const total = filteredExpenses.reduce((sum, expense) => {
      return (
        sum + parseFloat(expense.amount?.$numberDecimal || expense.amount || 0)
      );
    }, 0);

    const byCategory = filteredExpenses.reduce((acc, expense) => {
      const category = expense.category;
      const amount = parseFloat(
        expense.amount?.$numberDecimal || expense.amount || 0
      );
      acc[category] = (acc[category] || 0) + amount;
      return acc;
    }, {});

    return { total, byCategory };
  };

  const totals = calculateTotals();

  const clearFilters = () => {
    setFilters({
      category: "",
      date_from: "",
      date_to: "",
      amount_min: "",
      amount_max: "",
      month: "",
      year: "",
    });
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Agency Expenses
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Expense
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Calculator Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  Expense Calculator
                </Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(219, 74, 65, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h4"
                      sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
                    >
                      {totals.total.toLocaleString()} EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Total Expenses
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(33, 150, 243, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                    >
                      {(totals.byCategory.equipment || 0).toLocaleString()} EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Equipment
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(76, 175, 80, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      {(totals.byCategory.office || 0).toLocaleString()} EGP
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Office Expenses
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      textAlign: "center",
                      p: 2,
                      background: "rgba(255, 152, 0, 0.1)",
                      borderRadius: "8px",
                    }}
                  >
                    <Typography
                      variant="h6"
                      sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                    >
                      {filteredExpenses.length}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Total Records
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Filters Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <FilterListIcon sx={{ color: "#db4a41", mr: 1 }} />
                <Typography
                  variant="h6"
                  sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                >
                  Filters
                </Typography>
                <Box sx={{ ml: "auto", display: "flex", gap: 1 }}>
                  {sortConfig.key && (
                    <Button
                      startIcon={<SortIcon />}
                      onClick={clearSort}
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      Clear Sort
                    </Button>
                  )}
                  <Button
                    startIcon={<ClearIcon />}
                    onClick={clearFilters}
                    sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                  >
                    Clear All
                  </Button>
                </Box>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Category
                    </InputLabel>
                    <Select
                      value={filters.category}
                      onChange={(e) =>
                        setFilters({ ...filters, category: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Categories</MenuItem>
                      <MenuItem value="equipment">Equipment</MenuItem>
                      <MenuItem value="office">Office</MenuItem>
                      <MenuItem value="miscellaneous">Miscellaneous</MenuItem>
                      <MenuItem value="marketing">Marketing</MenuItem>
                      <MenuItem value="other">Other</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Month
                    </InputLabel>
                    <Select
                      value={filters.month}
                      onChange={(e) =>
                        setFilters({ ...filters, month: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Months</MenuItem>
                      <MenuItem value="0">January</MenuItem>
                      <MenuItem value="1">February</MenuItem>
                      <MenuItem value="2">March</MenuItem>
                      <MenuItem value="3">April</MenuItem>
                      <MenuItem value="4">May</MenuItem>
                      <MenuItem value="5">June</MenuItem>
                      <MenuItem value="6">July</MenuItem>
                      <MenuItem value="7">August</MenuItem>
                      <MenuItem value="8">September</MenuItem>
                      <MenuItem value="9">October</MenuItem>
                      <MenuItem value="10">November</MenuItem>
                      <MenuItem value="11">December</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Year
                    </InputLabel>
                    <Select
                      value={filters.year}
                      onChange={(e) =>
                        setFilters({ ...filters, year: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Years</MenuItem>
                      <MenuItem value="2024">2024</MenuItem>
                      <MenuItem value="2025">2025</MenuItem>
                      <MenuItem value="2026">2026</MenuItem>
                      <MenuItem value="2027">2027</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Date From"
                    type="date"
                    value={filters.date_from}
                    onChange={(e) =>
                      setFilters({ ...filters, date_from: e.target.value })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Date To"
                    type="date"
                    value={filters.date_to}
                    onChange={(e) =>
                      setFilters({ ...filters, date_to: e.target.value })
                    }
                    InputLabelProps={{ shrink: true }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Min Amount"
                    type="number"
                    value={filters.amount_min}
                    onChange={(e) =>
                      setFilters({ ...filters, amount_min: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6} md={2}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Max Amount"
                    type="number"
                    value={filters.amount_max}
                    onChange={(e) =>
                      setFilters({ ...filters, amount_max: e.target.value })
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Table Section */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Title
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Category
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "white",
                              fontFamily: "Formula Bold",
                              cursor: "pointer",
                              "&:hover": {
                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                              },
                            }}
                            onClick={() => handleSort("amount")}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              Amount
                              {sortConfig.key === "amount" &&
                                (sortConfig.direction === "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                ))}
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              color: "white",
                              fontFamily: "Formula Bold",
                              cursor: "pointer",
                              "&:hover": {
                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                              },
                            }}
                            onClick={() => handleSort("date")}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              Date
                              {sortConfig.key === "date" &&
                                (sortConfig.direction === "asc" ? (
                                  <ArrowUpwardIcon fontSize="small" />
                                ) : (
                                  <ArrowDownwardIcon fontSize="small" />
                                ))}
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Created By
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {sortedExpenses
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((expense) => (
                            <TableRow
                              key={expense._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: stringToColor(expense.title),
                                      width: 32,
                                      height: 32,
                                    }}
                                  >
                                    <ReceiptIcon fontSize="small" />
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontFamily: "Formula Bold" }}
                                    >
                                      {expense.title}
                                    </Typography>
                                    {expense.description && (
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.6)",
                                        }}
                                      >
                                        {expense.description.length > 30
                                          ? `${expense.description.substring(
                                              0,
                                              30
                                            )}...`
                                          : expense.description}
                                      </Typography>
                                    )}
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={expense.category.toUpperCase()}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getCategoryColor(
                                      expense.category
                                    )}20`,
                                    color: getCategoryColor(expense.category),
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontFamily: "Formula Bold",
                                    color: "#f44336",
                                  }}
                                >
                                  {formatCurrency(expense.amount)}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Typography variant="body2">
                                  {new Date(expense.date).toLocaleDateString()}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                >
                                  {new Date(expense.date).toLocaleTimeString()}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                  }}
                                >
                                  <Avatar
                                    sx={{
                                      bgcolor: stringToColor(
                                        expense.created_by?.name || "Unknown"
                                      ),
                                      width: 24,
                                      height: 24,
                                      fontSize: "0.75rem",
                                    }}
                                  >
                                    {(expense.created_by?.name || "U").charAt(
                                      0
                                    )}
                                  </Avatar>
                                  <Box>
                                    <Typography
                                      variant="body2"
                                      sx={{ fontSize: "0.8rem" }}
                                    >
                                      {expense.created_by?.name || "Unknown"}
                                    </Typography>
                                    {expense.created_by?.email && (
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.6)",
                                        }}
                                      >
                                        {expense.created_by.email}
                                      </Typography>
                                    )}
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(expense)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(expense)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(expense._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredExpenses.length}
                    page={page}
                    onPageChange={(event, newPage) => setPage(newPage)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(event) => {
                      setRowsPerPage(parseInt(event.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                      "& .MuiTablePagination-selectIcon": {
                        color: "white",
                      },
                      "& .MuiTablePagination-select": {
                        color: "white",
                      },
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Expense Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingExpense && (
              <Grid container spacing={3}>
                {/* Expense Header */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(viewingExpense.title),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <ReceiptIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold", color: "white" }}
                      >
                        {viewingExpense.title}
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{ color: "#f44336", fontFamily: "Formula Bold" }}
                      >
                        {formatCurrency(viewingExpense.amount)}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        <Chip
                          label={viewingExpense.category.toUpperCase()}
                          sx={{
                            backgroundColor: getCategoryColor(
                              viewingExpense.category
                            ),
                            color: "white",
                            textTransform: "capitalize",
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Expense Information */}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Expense Information
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Title:</strong> {viewingExpense.title}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Category:</strong>{" "}
                    {viewingExpense.category.charAt(0).toUpperCase() +
                      viewingExpense.category.slice(1)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Amount:</strong>{" "}
                    {formatCurrency(viewingExpense.amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Date:</strong>{" "}
                    {new Date(viewingExpense.date).toLocaleDateString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Time:</strong>{" "}
                    {new Date(viewingExpense.date).toLocaleTimeString()}
                  </Typography>
                </Grid>

                {/* Creator Information */}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Created By
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Name:</strong>{" "}
                    {viewingExpense.created_by?.name || "Unknown"}
                  </Typography>
                  {viewingExpense.created_by?.email && (
                    <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                      <strong>Email:</strong> {viewingExpense.created_by.email}
                    </Typography>
                  )}
                </Grid>

                {/* Description */}
                {viewingExpense.description && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#db4a41",
                        mb: 1,
                        fontFamily: "Formula Bold",
                      }}
                    >
                      Description
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: "white",
                        backgroundColor: "rgba(255, 255, 255, 0.05)",
                        p: 2,
                        borderRadius: "8px",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    >
                      {viewingExpense.description}
                    </Typography>
                  </Grid>
                )}

                {/* Timestamps */}
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Timestamps
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Created:</strong>{" "}
                    {new Date(viewingExpense.createdAt).toLocaleString()}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Last Updated:</strong>{" "}
                    {new Date(viewingExpense.updatedAt).toLocaleString()}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingExpense ? "Edit Expense" : "Create New Expense"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title"
                  value={newExpense.title}
                  onChange={(e) =>
                    setNewExpense({ ...newExpense, title: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Category
                  </InputLabel>
                  <Select
                    value={newExpense.category}
                    onChange={(e) =>
                      setNewExpense({
                        ...newExpense,
                        category: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    <MenuItem value="equipment">Equipment</MenuItem>
                    <MenuItem value="office">Office</MenuItem>
                    <MenuItem value="miscellaneous">Miscellaneous</MenuItem>
                    <MenuItem value="marketing">Marketing</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={newExpense.amount}
                  onChange={(e) =>
                    setNewExpense({ ...newExpense, amount: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Date"
                  type="date"
                  value={newExpense.date}
                  onChange={(e) =>
                    setNewExpense({ ...newExpense, date: e.target.value })
                  }
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={newExpense.description}
                  onChange={(e) =>
                    setNewExpense({
                      ...newExpense,
                      description: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingExpense ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default AgencyExpenses;
