import { useEffect } from "react";
import Lenis from "@studio-freight/lenis";
import { useLocation } from "react-router-dom";

export default function SmoothScroll() {
  const { pathname } = useLocation();

  useEffect(() => {
    // ❌ Disable for admin + client portal
    if (
      pathname.startsWith("/admin") ||
      pathname.startsWith("/client-portal")
    ) {
      return;
    }

    const lenis = new Lenis({
      duration: 1.2,
      smooth: true,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    });

    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      lenis.destroy();
    };
  }, [pathname]);

  return null;
}
