import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Box, IconButton, Typography, Stack } from "@mui/material";
import InstagramIcon from "@mui/icons-material/Instagram";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import YouTubeIcon from "@mui/icons-material/YouTube";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { useTheme } from "@mui/material/styles";

const logoUrl = process.env.PUBLIC_URL + "/assets/young-logo-white.webp";
const blackLogo = process.env.PUBLIC_URL + "/assets/young-logo-black.webp";

// Navigation items for the glass navbar
const navItems = [
  { label: "Home", link: "/test" },
  { label: "Our Work", link: "/test/insights" },
  { label: "Reels", link: "/test/reels" },
  { label: "Our People", link: "/test/young-people" },
  { label: "About", link: "/test/about" },
  { label: "Join Us", link: "/test/careers" },
  { label: "Contact", link: "/test/contact" },
];

const formulaBold = {
  fontFamily: "Formula Bold",
};

export default function ButtonAppBar() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [logoSrc, setLogoSrc] = useState(logoUrl);
  const theme = useTheme();
  const isLight = theme.palette.mode === "light";

  const toggleDrawer = (open) => () => {
    setDrawerOpen(open);
  };

  const closeDrawer = () => {
    setDrawerOpen(false);
  };

  const handleLinkClick = (path) => () => {
    closeDrawer();
  };

  // Navbar Scroll Effect
  useEffect(() => {
    const handleScroll = () => {
      const navbarHeight = 80;
      const sections = document.querySelectorAll(".section");

      sections.forEach((section) => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= navbarHeight && rect.bottom >= navbarHeight) {
          if (section.classList.contains("light-section")) {
            setLogoSrc(blackLogo);
          } else {
            setLogoSrc(logoUrl);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <Box sx={{ flexGrow: 1, position: "sticky", top: 0, zIndex: 9999 }}>
      {/* Fluid Glass Oval Navbar */}
      <Box
        sx={{
          position: "fixed",
          top: "20px",
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 10000,
          width: "100%",
          display: "flex",
          justifyContent: "center",
          pointerEvents: "none",
        }}
      >
        <Box
          sx={{
            width: "95vw",
            maxWidth: "1400px",
            // minWidth: "700px",
            pointerEvents: "auto",
            borderRadius: "60px",
            overflow: "hidden",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
            position: "relative",
            // Enhanced glass effect
            background:
              "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
            backdropFilter: "blur(25px) saturate(180%)",
            WebkitBackdropFilter: "blur(25px) saturate(180%)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: "60px",
              background:
                "linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%)",
              pointerEvents: "none",
            },
          }}
        >
          {/* Glass content with subtle animation */}
          <Box
            sx={{
              position: "relative",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: { xs: "15px 40px", sm: "18px 50px", md: "25px 60px" },
              background: "transparent",
              zIndex: 10,
              transition: "all 0.3s ease",
              "&:hover": {
                background: "rgba(255, 255, 255, 0.02)",
              },
            }}
          >
            {/* Logo */}
            <Link to="/">
              <img
                src={logoSrc}
                alt="Logo"
                style={{
                  width: "140px",
                  height: "35px",
                  objectFit: "contain",
                  filter:
                    "brightness(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.3))",
                }}
              />
            </Link>

            {/* Desktop Navigation - Hidden on mobile */}
            <Box
              sx={{
                display: { xs: "none", md: "flex" },
                alignItems: "center",
                gap: { md: 3, lg: 4 },
              }}
            >
              {navItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.link}
                  style={{
                    color: "white",
                    textDecoration: "none",
                    fontFamily: "Formula Bold",
                    fontSize: "1.1rem",
                    fontWeight: "bold",
                    transition: "all 0.3s ease",
                    textShadow: "0 2px 4px rgba(0,0,0,0.3)",
                    padding: "12px 18px",
                    borderRadius: "25px",
                    position: "relative",
                    overflow: "hidden",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.opacity = "0.8";
                    e.target.style.transform = "scale(1.05)";
                    e.target.style.background = "rgba(255, 255, 255, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.opacity = "1";
                    e.target.style.transform = "scale(1)";
                    e.target.style.background = "transparent";
                  }}
                >
                  {item.label}
                </Link>
              ))}
            </Box>

            {/* Right side buttons */}
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {/* CTA Button - Hidden on mobile */}
              <Box sx={{ display: { xs: "none", sm: "block" } }}>
                <Link to="/test/contact">
                  <button
                    style={{
                      borderRadius: "30px",
                      padding: "12px 24px",
                      fontSize: "0.95rem",
                      fontWeight: "bold",
                      whiteSpace: "nowrap",
                      background:
                        "linear-gradient(135deg, #db4a41 0%, #c73e36 100%)",
                      color: "white",
                      border: "none",
                      cursor: "pointer",
                      transition: "all 0.3s ease",
                      fontFamily: "Formula Bold",
                      boxShadow: "0 4px 15px rgba(219, 74, 65, 0.3)",
                      position: "relative",
                      overflow: "hidden",
                      marginRight: "20px",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.transform = "scale(1.05)";
                      e.target.style.boxShadow =
                        "0 6px 20px rgba(219, 74, 65, 0.4)";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = "scale(1)";
                      e.target.style.boxShadow =
                        "0 4px 15px rgba(219, 74, 65, 0.3)";
                    }}
                  >
                    START A PROJECT
                  </button>
                </Link>
              </Box>

              {/* Mobile Menu Button */}
              <IconButton
                edge="start"
                color="inherit"
                aria-label="menu"
                onClick={toggleDrawer(true)}
                sx={{
                  color: "white",
                  display: { xs: "flex", md: "none" },
                  background: "rgba(255, 255, 255, 0.1)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.2)",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    background: "rgba(255, 255, 255, 0.2)",
                    transform: "scale(1.1)",
                  },
                }}
              >
                <MenuIcon sx={{ fontSize: "1.6rem" }} />
              </IconButton>
            </Box>
          </Box>

          {/* Animated glass shimmer effect */}
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: "-100%",
              width: "100%",
              height: "100%",
              background:
                "linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)",
              borderRadius: "60px",
              animation: "shimmer 3s infinite",
              "@keyframes shimmer": {
                "0%": { left: "-100%" },
                "100%": { left: "100%" },
              },
            }}
          />
        </Box>
      </Box>

      {/* Mobile Drawer */}
      <Box
        sx={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 99999,
          display: drawerOpen ? "block" : "none",
          backgroundColor: "rgba(0, 0, 0, 0.4)",
          backdropFilter: "blur(20px)",
        }}
        onClick={closeDrawer}
      >
        <Box
          sx={{
            position: "absolute",
            top: "15%",
            left: "50%",
            transform: "translateX(-50%)",
            width: "90%",
            maxWidth: "400px",
            // background:  "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
            //  backdropFilter: "blur(25px) saturate(180%)",
            //WebkitBackdropFilter: "blur(25px) saturate(180%)",
            // border: "1px solid rgba(255, 255, 255, 0.2)",
            borderRadius: "30px",
            padding: "40px",
            textAlign: "center",
            // boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          <Box sx={{ position: "absolute", top: "20px", right: "20px" }}>
            <IconButton onClick={closeDrawer} sx={{ color: "white" }}>
              <CloseIcon sx={{ fontSize: "2rem" }} />
            </IconButton>
          </Box>

          {/* Logo */}
          <Box sx={{ mb: 4 }}>
            <Link to="/test" onClick={closeDrawer}>
              <img
                src={logoUrl}
                alt="Logo"
                style={{ width: "120px", height: "30px" }}
              />
            </Link>
          </Box>

          {/* Navigation Items */}
          <Box sx={{ mb: 4 }}>
            {navItems.map((item) => (
              <Box key={item.label} sx={{ mb: 3 }}>
                <Link
                  to={item.link}
                  onClick={handleLinkClick(item.link)}
                  style={{
                    color: "white",
                    textDecoration: "none",
                    fontFamily: "Formula Bold",
                    fontSize: "1.5rem",
                    fontWeight: "bold",
                    transition: "all 0.3s ease",
                    letterSpacing: "1px",
                  }}
                >
                  {item.label}
                </Link>
              </Box>
            ))}
          </Box>

          {/* CTA Button */}
          <Box sx={{ mb: 4 }}>
            <Link to="/test/contact" onClick={closeDrawer}>
              <button
                style={{
                  borderRadius: "25px",
                  padding: "12px 24px",
                  fontSize: "1rem",
                  fontWeight: "bold",
                  width: "100%",
                  background:
                    "linear-gradient(135deg, #db4a41 0%, #c73e36 100%)",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  fontFamily: "Formula Bold",
                }}
              >
                START A PROJECT
              </button>
            </Link>
          </Box>

          {/* Social Media */}
          <Stack
            spacing={2}
            direction="row"
            justifyContent="center"
            sx={{ mt: 4 }}
          >
            <Box
              sx={{
                color: "white",
                cursor: "pointer",
                transition: "transform 0.3s ease",
                "&:hover": { transform: "scale(1.1)" },
              }}
              onClick={() =>
                window.open(
                  "https://www.instagram.com/youngproductionss/",
                  "_blank",
                  "noopener,noreferrer"
                )
              }
            >
              <InstagramIcon sx={{ fontSize: "1.5rem" }} />
            </Box>
            <Box
              sx={{
                color: "white",
                cursor: "pointer",
                transition: "transform 0.3s ease",
                "&:hover": { transform: "scale(1.1)" },
              }}
              onClick={() =>
                window.open(
                  "https://www.linkedin.com/company/young-prroductions",
                  "_blank",
                  "noopener,noreferrer"
                )
              }
            >
              <LinkedInIcon sx={{ fontSize: "1.5rem" }} />
            </Box>
            <Box
              sx={{
                color: "white",
                cursor: "pointer",
                transition: "transform 0.3s ease",
                "&:hover": { transform: "scale(1.1)" },
              }}
              onClick={() =>
                window.open(
                  "https://www.youtube.com/channel/UCuXflYNaaJTcxIpaLQFcteg",
                  "_blank",
                  "noopener,noreferrer"
                )
              }
            >
              <YouTubeIcon sx={{ fontSize: "1.5rem" }} />
            </Box>
          </Stack>
        </Box>
      </Box>
    </Box>
  );
}
