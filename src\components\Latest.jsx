import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Typography,
  Card,
  CardMedia,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { Link } from "react-router-dom";
import LoadingScreen from "./LoadingScreen";
import { motion } from "framer-motion";

/**
 * The speed of the horizontal scroll is controlled by the `scrollAmount` variable
 * inside the `handleWheel` and `handleTouchEnd` functions in the useEffect below.
 *
 * For mouse wheel:
 *   const scrollAmount = isMobile ? mobileScrollAmount : 100;
 *
 * For touch (mobile):
 *   setHorizontal(Math.min(currentHorizontal + mobileScrollAmount, maxHorizontal));
 *
 * To make scrolling even slower on mobile, decrease the value of `mobileScrollAmount`.
 */

const LatestSection = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  const [latestEvents, setLatestEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Move hooks to top level
  const cardsContainerRef = useRef(null);
  const [horizontal, setHorizontal] = useState(0);
  const [cardWidth, setCardWidth] = useState(700);
  const [maxHorizontal, setMaxHorizontal] = useState(0);

  // Make mobile scroll much slower (e.g. 60px per scroll/swipe)
  const mobileScrollAmount = 60;

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(
          "https://youngproductions-768ada043db3.herokuapp.com/api/workVideos"
        );
        if (!response.ok) throw new Error("Network response was not ok");

        const data = await response.json();
        setLatestEvents(data);
        setLoading(false);
        setError(null);
      } catch (error) {
        console.error("Error fetching events:", error);
        setError(error.message);
        setLoading(false);
        if (retryCount < maxRetries) {
          setTimeout(() => setRetryCount((c) => c + 1), 1000);
        }
      }
    };

    fetchData();
  }, [retryCount, error]);

  // Calculate card width and maxHorizontal when data or window size changes
  useEffect(() => {
    const calculateDimensions = () => {
      const newCardWidth = isMobile ? window.innerWidth - 40 : 700; // 20px padding on each side
      setCardWidth(newCardWidth);

      setMaxHorizontal(
        Math.max(0, latestEvents.length * newCardWidth - window.innerWidth)
      );
    };

    calculateDimensions();

    // Debounced resize handler
    let resizeTimeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(calculateDimensions, 100);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(resizeTimeout);
    };
  }, [latestEvents.length, isMobile]);

  // Responsive horizontal scroll on wheel and touch, and prevent page scroll while in view and not at end
  useEffect(() => {
    const container = cardsContainerRef.current;
    if (!container) return;

    const handleWheel = (e) => {
      const rect = container.getBoundingClientRect();
      const isInView = rect.top <= window.innerHeight && rect.bottom >= 0;

      // Only handle if in view
      if (isInView) {
        const delta = e.deltaY;
        const currentHorizontal = horizontal;
        // Make mobile scroll much slower by using a smaller scrollAmount
        const scrollAmount = isMobile ? mobileScrollAmount : 100;

        let handled = false;

        if (delta > 0 && currentHorizontal < maxHorizontal) {
          // Scroll down - move right
          setHorizontal(
            Math.min(currentHorizontal + scrollAmount, maxHorizontal)
          );
          handled = true;
        } else if (delta < 0 && currentHorizontal > 0) {
          // Scroll up - move left
          setHorizontal(Math.max(currentHorizontal - scrollAmount, 0));
          handled = true;
        }

        // Prevent page scroll if we handled the event (i.e., not at the end)
        if (handled) {
          e.preventDefault();
        }
      }
    };

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchStartY = 0;

    const handleTouchStart = (e) => {
      touchStartX = e.touches[0].clientX;
      touchStartY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e) => {
      if (!isMobile) return;

      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;
      const deltaX = touchStartX - touchEndX;
      const deltaY = touchStartY - touchEndY;

      // Only trigger if it's a horizontal swipe (more horizontal than vertical)
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        const currentHorizontal = horizontal;
        let handled = false;

        // Make mobile swipe scroll much slower by using a smaller scroll amount
        if (deltaX > 0 && currentHorizontal < maxHorizontal) {
          // Swipe left - move right
          setHorizontal(
            Math.min(currentHorizontal + mobileScrollAmount, maxHorizontal)
          );
          handled = true;
        } else if (deltaX < 0 && currentHorizontal > 0) {
          // Swipe right - move left
          setHorizontal(Math.max(currentHorizontal - mobileScrollAmount, 0));
          handled = true;
        }

        // Prevent page scroll if we handled the event
        if (handled) {
          e.preventDefault();
        }
      }
    };

    // Use non-passive event listeners so we can call preventDefault
    window.addEventListener("wheel", handleWheel, { passive: false });
    container.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    container.addEventListener("touchend", handleTouchEnd, { passive: false });

    return () => {
      window.removeEventListener("wheel", handleWheel);
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [horizontal, maxHorizontal, isMobile, cardWidth, mobileScrollAmount]);

  if (loading) return <LoadingScreen />;

  return (
    <Box
      sx={{
        backgroundColor: "#000",
        padding: { xs: "20px 0 60px 0", md: "20px 0 120px 0" },
        minHeight: { xs: "80vh", md: "80vh" },
        overflowX: "hidden",
        position: "relative",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
      }}
    >
      {/* Title */}
      <motion.div
        initial={{ x: -30, opacity: 0 }}
        whileInView={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "20px",
          }}
        >
          <Box>
            <Typography
              variant="h3"
              sx={{ fontFamily: "Formula Bold", color: "#fff !important" }}
            >
              Latest Events
            </Typography>
          </Box>
          <Link
            to="/all-work"
            className="btn btn-secondry "
            style={{
              textDecoration: "none",
              color: "#fff !important",
              border: "1px solid #fff !important",
            }}
          >
            All Work
          </Link>
        </Box>
      </motion.div>

      {/* Cards */}
      <Box
        ref={cardsContainerRef}
        sx={{
          display: "flex",
          gap: isMobile ? "20px" : "10px",
          padding: isMobile ? "0 20px" : "2px",
          flexDirection: "row",
          flexWrap: "nowrap",
          justifyContent: "flex-start",
          overflowX: "hidden",
          alignContent: "center",
          width: `${
            latestEvents.length * cardWidth +
            (isMobile ? (latestEvents.length - 1) * 20 : 0)
          }px`,
          minWidth: "100vw",
          willChange: "transform",
          transform: `translate3d(${-horizontal}px, 0, 0)`,
          transition: "transform 0.3s ease-out",
        }}
      >
        {latestEvents.map((event, index) => (
          <div key={event._id}>
            <Card
              sx={{
                width: isMobile ? `${cardWidth}px` : "700px",
                height: isMobile ? "50vh" : "50vh",
                background: "#fff",
                borderRadius: isMobile ? "12px" : "20px",
                boxShadow: 3,
                overflow: "hidden",
                position: "relative",
                marginRight: index < latestEvents.length - 1 ? 0 : undefined,
                display: "flex",
                flexDirection: "column",
                justifyContent: "flex-end",
              }}
            >
              <CardMedia
                component="img"
                height="100%"
                image={
                  event.image ||
                  process.env.PUBLIC_URL + "/assets/placeholder.jpg"
                }
                alt={event.title}
              />
              {/* Title overlay */}
              <Box
                sx={{
                  position: "absolute",
                  bottom: 0,
                  left: 0,
                  width: "100%",
                  background: "rgba(0, 0, 0, 0.4)",
                  color: "#fff",
                  padding: isMobile ? "20px" : "16px",
                }}
              >
                <Typography
                  variant={isMobile ? "h5" : "h4"}
                  sx={{
                    fontFamily: "Formula Bold",
                    textShadow: "1px 1px 4px rgba(0,0,0,0.8)",
                    fontSize: isMobile ? "1.5rem" : undefined,
                  }}
                >
                  {event.title}
                </Typography>
                <motion.Link
                  to={`/event/${event._id}`}
                  className="btn btn-secondry link-btn"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: "0px 4px 10px rgba(0,0,0,0.2)",
                  }}
                  whileTap={{ scale: 0.97 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  style={{
                    marginTop: "10px",
                    display: "inline-block",
                    color: "#fff",
                    border: "1px solid #fff",
                  }}
                >
                  Discover
                </motion.Link>
              </Box>
            </Card>
          </div>
        ))}
      </Box>
    </Box>
  );
};

export default LatestSection;
