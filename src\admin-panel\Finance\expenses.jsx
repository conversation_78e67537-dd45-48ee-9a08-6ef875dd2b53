import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CheckIcon from "@mui/icons-material/CheckCircle";
import CloseIcon from "@mui/icons-material/Cancel";
import { useUser } from "../../contexts/UserContext";

function Expenses() {
  const { user } = useUser();
  const [expenses, setExpenses] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [cycles, setCycles] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectionType, setSelectionType] = useState("cycle"); // "cycle" or "project"
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingExpense, setEditingExpense] = useState(null);
  const [viewingExpense, setViewingExpense] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [filters, setFilters] = useState({
    status: "",
    client_id: "",
    cycle_id: "",
  });

  const role = useMemo(
    () => user?.role || localStorage.getItem("role") || "employee",
    [user]
  );
  const isManager = useMemo(
    () =>
      ["finance_manager", "general_manager", "admin", "manager"].includes(role),
    [role]
  );
  const [newExpense, setNewExpense] = useState({
    client_id: "",
    cycle_id: "",
    project_id: "",
    type: "marketing",
    description: "",
    amount: "",
    date: new Date().toISOString().slice(0, 10),
  });
  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/expenses";
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";
  const CYCLES_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles";
  const PROJECTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/one-time-projects";

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "approved":
        return "#4caf50";
      case "rejected":
        return "#f44336";
      case "requested":
      default:
        return "#ff9800";
    }
  };

  const showSnackbar = (message, severity = "success") =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const fetchExpenses = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      setExpenses(result.data || result || []);
    } catch (err) {
      console.error("Error fetching expenses:", err);
      showSnackbar("Failed to fetch expenses", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const list = result?.data || (Array.isArray(result) ? result : []);
      setClientAccounts(list);
    } catch (err) {
      console.error("Error fetching client accounts:", err);
      setClientAccounts([]);
    }
  }, []);

  const fetchCyclesByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setCycles([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${CYCLES_API_URL}/${clientId}/get-client-cycles`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      const result = await response.json();

      // ✅ endpoint returns { success: true, cycles: [...] }
      setCycles(result.cycles || []);
    } catch (err) {
      console.error("Error fetching client cycles:", err);
      setCycles([]);
    }
  }, []);

  const fetchProjectsByClient = useCallback(async (clientId) => {
    if (!clientId) {
      setProjects([]);
      return;
    }

    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${PROJECTS_API_URL}/client/${clientId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();

      // Handle the response structure based on the provided data format
      if (result.success && result.data) {
        // If single project returned, wrap in array
        const projectsData = Array.isArray(result.data)
          ? result.data
          : [result.data];
        setProjects(projectsData);
      } else {
        setProjects([]);
      }
    } catch (err) {
      console.error("Error fetching client projects:", err);
      setProjects([]);
    }
  }, []);

  // when client changes, fetch cycles and projects
  useEffect(() => {
    if (newExpense.client_id) {
      fetchCyclesByClient(newExpense.client_id);
      fetchProjectsByClient(newExpense.client_id);
    }
  }, [newExpense.client_id, fetchCyclesByClient, fetchProjectsByClient]);

  useEffect(() => {
    fetchExpenses();
    fetchClientAccounts();
    fetchCyclesByClient();
  }, [fetchExpenses, fetchClientAccounts, fetchCyclesByClient]);

  const filteredExpenses = useMemo(() => {
    return (expenses || []).filter((e) => {
      if (filters.status && e.status !== filters.status) return false;
      if (
        filters.client_id &&
        (e.client_id?._id || e.client_id) !== filters.client_id
      )
        return false;
      if (
        filters.cycle_id &&
        (e.cycle_id?._id || e.cycle_id) !== filters.cycle_id
      )
        return false;
      return true;
    });
  }, [expenses, filters]);

  const handleAdd = () => {
    setEditingExpense(null);
    setNewExpense({
      client_id: "",
      cycle_id: "",
      type: "marketing",
      description: "",
      amount: "",
      date: new Date().toISOString().slice(0, 10),
    });
    setOpenModal(true);
  };

  const handleView = async (e) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${e._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      const full = result?.data || e;
      setViewingExpense(full);
      setOpenViewModal(true);
    } catch (err) {
      console.error("Failed to fetch expense details", err);
      setViewingExpense(e);
      setOpenViewModal(true);
    }
  };
  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingExpense(null);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingExpense(null);
    setNewExpense({
      client_id: "",
      cycle_id: "",
      project_id: "",
      type: "marketing",
      description: "",
      amount: "",
      date: new Date().toISOString().slice(0, 10),
    });
    setSelectionType("cycle");
    setCycles([]);
    setProjects([]);
  };

  const handleSelectionTypeChange = (event, newType) => {
    if (newType !== null) {
      setSelectionType(newType);
      // Clear the opposite selection when switching
      setNewExpense((prev) => ({
        ...prev,
        cycle_id: newType === "cycle" ? prev.cycle_id : "",
        project_id: newType === "project" ? prev.project_id : "",
      }));
    }
  };

  const handleCreate = async () => {
    try {
      const token = localStorage.getItem("token");
      const amountNumber = parseFloat(newExpense.amount || "");

      // Validate required fields based on selection type
      const hasValidSelection =
        selectionType === "cycle" ? newExpense.cycle_id : newExpense.project_id;

      if (
        !newExpense.client_id ||
        !hasValidSelection ||
        !newExpense.type ||
        !Number.isFinite(amountNumber) ||
        amountNumber <= 0
      ) {
        showSnackbar(
          `Please fill all required fields with valid values (including ${selectionType})`,
          "warning"
        );
        return;
      }

      const payload = {
        client_id: newExpense.client_id,
        type: newExpense.type,
        description: newExpense.description || "",
        amount: amountNumber,
        date: newExpense.date,
        created_by: user?._id || user?.id,
      };

      // Add either cycle_id or project_id based on selection type
      if (selectionType === "cycle") {
        payload.cycle_id = newExpense.cycle_id;
      } else {
        payload.project_id = newExpense.project_id;
      }
      const response = await fetch(API_BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const body = await response.json().catch(() => ({}));
        throw new Error(body.message || "Failed to create expense");
      }
      await fetchExpenses();
      showSnackbar("Expense requested successfully", "success");
      setOpenModal(false);
    } catch (err) {
      console.error(err);
      showSnackbar(err.message || "Failed to create expense", "error");
    }
  };

  const approve = async (id) => {
    try {
      const token = localStorage.getItem("token");
      const body = { approved_by: user?._id || user?.id };
      const response = await fetch(`${API_BASE_URL}/${id}/approve`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) throw new Error("Failed to approve expense");
      await fetchExpenses();
      showSnackbar("Expense approved", "success");
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to approve expense", "error");
    }
  };

  const reject = async (id, reason) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}/reject`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          rejection_reason: reason,
          rejected_by: user?._id || user?.id,
        }),
      });
      if (!response.ok) throw new Error("Failed to reject expense");
      await fetchExpenses();
      showSnackbar("Expense rejected", "success");
    } catch (err) {
      console.error(err);
      showSnackbar("Failed to reject expense", "error");
    }
  };

  const [decision, setDecision] = useState({
    open: false,
    id: null,
    action: "approve",
    reason: "",
  });
  const openDecision = (id, action) =>
    setDecision({ open: true, id, action, reason: "" });
  const closeDecision = () =>
    setDecision({ open: false, id: null, action: "approve", reason: "" });
  const submitDecision = async () => {
    if (decision.action === "approve") {
      await approve(decision.id);
      closeDecision();
    } else {
      if (!decision.reason) {
        showSnackbar("Rejection reason is required", "warning");
        return;
      }
      await reject(decision.id, decision.reason);
      closeDecision();
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Expenses
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": { backgroundColor: "#c62828" },
            }}
          >
            New Expense
          </Button>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={filters.status}
                      onChange={(e) =>
                        setFilters({ ...filters, status: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      <MenuItem value="requested">Requested</MenuItem>
                      <MenuItem value="approved">Approved</MenuItem>
                      <MenuItem value="rejected">Rejected</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Client
                    </InputLabel>
                    <Select
                      value={filters.client_id}
                      onChange={(e) =>
                        setFilters({ ...filters, client_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(clientAccounts) &&
                        clientAccounts.map((c) => (
                          <MenuItem key={c._id} value={c._id}>
                            {c.client_name}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Cycle
                    </InputLabel>
                    <Select
                      value={filters.cycle_id}
                      onChange={(e) =>
                        setFilters({ ...filters, cycle_id: e.target.value })
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All</MenuItem>
                      {Array.isArray(cycles) &&
                        cycles.map((cy) => (
                          <MenuItem key={cy._id} value={cy._id}>
                            {cy.cycle_name ||
                              `${cy.client_id?.client_name} - ${cy.month}`}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
            }}
          >
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </Box>
              ) : (
                <>
                  <TableContainer
                    component={Paper}
                    sx={{ background: "transparent" }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: "rgba(219, 74, 65, 0.1)" }}
                        >
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Client
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Cycle
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Type
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Amount
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Date
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Status
                          </TableCell>
                          <TableCell
                            sx={{ color: "white", fontFamily: "Formula Bold" }}
                          >
                            Actions
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {filteredExpenses
                          .slice(
                            page * rowsPerPage,
                            page * rowsPerPage + rowsPerPage
                          )
                          .map((e) => (
                            <TableRow
                              key={e._id}
                              sx={{
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 0.05)",
                                },
                                borderBottom:
                                  "1px solid rgba(255, 255, 255, 0.1)",
                              }}
                            >
                              <TableCell sx={{ color: "white" }}>
                                {e.client_id?.client_name || "-"}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {e.cycle_id?.cycle_name ||
                                  e.cycle_id?.month ||
                                  "-"}
                              </TableCell>
                              <TableCell
                                sx={{
                                  color: "white",
                                  textTransform: "capitalize",
                                }}
                              >
                                {e.type}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {formatCurrency(e.amount)}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                {new Date(
                                  e.date || e.createdAt
                                ).toLocaleDateString()}
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Chip
                                  label={e.status}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${getStatusColor(
                                      e.status
                                    )}20`,
                                    color: getStatusColor(e.status),
                                    textTransform: "capitalize",
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </TableCell>
                              <TableCell sx={{ color: "white" }}>
                                <Box sx={{ display: "flex", gap: 0.5 }}>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(e)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  {isManager && e.status === "requested" && (
                                    <>
                                      <Tooltip title="Approve">
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            openDecision(e._id, "approve")
                                          }
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                            "&:hover": { color: "#4caf50" },
                                          }}
                                        >
                                          <CheckIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                      <Tooltip title="Reject">
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            openDecision(e._id, "reject")
                                          }
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.7)",
                                            "&:hover": { color: "#f44336" },
                                          }}
                                        >
                                          <CloseIcon fontSize="small" />
                                        </IconButton>
                                      </Tooltip>
                                    </>
                                  )}
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  <TablePagination
                    component="div"
                    count={filteredExpenses.length}
                    page={page}
                    onPageChange={(e, p) => setPage(p)}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={(e) => {
                      setRowsPerPage(parseInt(e.target.value, 10));
                      setPage(0);
                    }}
                    sx={{
                      color: "white",
                      borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                    }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Box>

        {/* Create Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingExpense ? "Edit Expense" : "Create New Expense"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newExpense.client_id}
                    onChange={(e) =>
                      setNewExpense({
                        ...newExpense,
                        client_id: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((c) => (
                        <MenuItem key={c._id} value={c._id}>
                          {c.client_name}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Toggle Button for Cycle/Project Selection */}
              <Grid item xs={12}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Typography
                    variant="body2"
                    sx={{ color: "rgba(255, 255, 255, 0.7)", mr: 2 }}
                  >
                    Select Type:
                  </Typography>
                  <ToggleButtonGroup
                    value={selectionType}
                    exclusive
                    onChange={handleSelectionTypeChange}
                    sx={{
                      "& .MuiToggleButton-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&.Mui-selected": {
                          backgroundColor: "#db4a41",
                          color: "white",
                          "&:hover": {
                            backgroundColor: "#c43a31",
                          },
                        },
                        "&:hover": {
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                        },
                      },
                    }}
                  >
                    <ToggleButton value="cycle">
                      Subscription Cycle
                    </ToggleButton>
                    <ToggleButton value="project">
                      One-Time Project
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    {selectionType === "cycle"
                      ? "Subscription Cycle"
                      : "One-Time Project"}
                  </InputLabel>
                  <Select
                    value={
                      selectionType === "cycle"
                        ? newExpense.cycle_id
                        : newExpense.project_id
                    }
                    onChange={(e) =>
                      setNewExpense({
                        ...newExpense,
                        [selectionType === "cycle" ? "cycle_id" : "project_id"]:
                          e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {selectionType === "cycle"
                      ? // Render Cycles
                        Array.isArray(cycles) &&
                        cycles
                          .filter((cy) => {
                            if (user.role === "general_manager") {
                              return true; // GM sees all
                            }

                            const now = new Date();
                            const currentMonth = String(
                              now.getMonth() + 1
                            ).padStart(2, "0"); // "09"
                            const currentYear = now.getFullYear();

                            if (cy.year && cy.month) {
                              // case 1: schema has separate month/year
                              return (
                                String(cy.year) === String(currentYear) &&
                                String(cy.month).padStart(2, "0") ===
                                  currentMonth
                              );
                            }

                            if (cy.start_date) {
                              // case 2: schema has a date field
                              const cycleDate = new Date(cy.start_date);
                              return (
                                cycleDate.getFullYear() === currentYear &&
                                String(cycleDate.getMonth() + 1).padStart(
                                  2,
                                  "0"
                                ) === currentMonth
                              );
                            }

                            if (cy.cycle_name) {
                              // case 3: fallback check by name "CY2025-09"
                              return cy.cycle_name.includes(
                                `CY${currentYear}-${currentMonth}`
                              );
                            }
                            return false;
                          })
                          .map((cy) => (
                            <MenuItem key={cy._id} value={cy._id}>
                              {cy.cycle_name ||
                                `${cy.client_id?.client_name} - ${cy.month}/${cy.year}`}
                            </MenuItem>
                          ))
                      : // Render Projects
                        Array.isArray(projects) &&
                        projects.map((project) => (
                          <MenuItem key={project._id} value={project._id}>
                            {`${project.name} - ${new Date(
                              project.date
                            ).toLocaleDateString()} - ${formatCurrency(
                              project.fees
                            )}`}
                          </MenuItem>
                        ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Type
                  </InputLabel>
                  <Select
                    value={newExpense.type}
                    onChange={(e) =>
                      setNewExpense({ ...newExpense, type: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                  >
                    {[
                      "marketing",
                      "production",
                      "travel",
                      "meeting",
                      "other",
                      "dinner",
                    ].map((t) => (
                      <MenuItem
                        key={t}
                        value={t}
                        sx={{ textTransform: "capitalize" }}
                      >
                        {t}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Amount"
                  type="number"
                  value={newExpense.amount}
                  onChange={(e) =>
                    setNewExpense({ ...newExpense, amount: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={newExpense.description}
                  onChange={(e) =>
                    setNewExpense({
                      ...newExpense,
                      description: e.target.value,
                    })
                  }
                  multiline
                  minRows={2}
                  maxRows={4}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingExpense ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Expense Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingExpense && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Client:</strong>{" "}
                    {viewingExpense.client_id?.client_name || "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Cycle:</strong>{" "}
                    {viewingExpense.cycle_id?.cycle_name ||
                      viewingExpense.cycle_id?.month ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Status:</strong> {viewingExpense.status}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Type:</strong> {viewingExpense.type}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Amount:</strong>{" "}
                    {formatCurrency(viewingExpense.amount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Created By:</strong>{" "}
                    {viewingExpense.created_by?.name ||
                      viewingExpense.created_by?.email ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Date:</strong>{" "}
                    {new Date(
                      viewingExpense.date || viewingExpense.createdAt
                    ).toLocaleDateString()}
                  </Typography>
                </Grid>
                {viewingExpense.description && (
                  <Grid item xs={12}>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      <strong>Description:</strong> {viewingExpense.description}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Decision Modal */}
        <Dialog
          open={decision.open}
          onClose={closeDecision}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {decision.action === "approve"
              ? "Approve Expense"
              : "Reject Expense"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {decision.action === "reject" && (
              <TextField
                fullWidth
                label="Rejection Reason"
                value={decision.reason}
                onChange={(e) =>
                  setDecision((d) => ({ ...d, reason: e.target.value }))
                }
                multiline
                rows={3}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    color: "white",
                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                    "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                  },
                  "& .MuiInputLabel-root": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={closeDecision}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitDecision}
              variant="contained"
              sx={{
                backgroundColor:
                  decision.action === "approve" ? "#4caf50" : "#f44336",
                "&:hover": { opacity: 0.9 },
              }}
            >
              {decision.action === "approve" ? "Approve" : "Reject"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default Expenses;
