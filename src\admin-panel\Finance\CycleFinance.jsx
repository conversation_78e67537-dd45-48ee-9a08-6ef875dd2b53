import React, { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  LinearProgress,
  But<PERSON>,
} from "@mui/material";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ReceiptIcon from "@mui/icons-material/Receipt";
import CalculateIcon from "@mui/icons-material/Calculate";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { motion } from "framer-motion";
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip as RechartsTooltip,
} from "recharts";

function CycleFinance() {
  const { id } = useParams();
  const navigate = useNavigate();

  const [cycle, setCycle] = useState(null);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const API_BASE_URL =
    "http://localhost:5000/api/financial/subscription-cycles";

  const fetchCycle = useCallback(async () => {
    if (!id) return;
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const result = await response.json();
      setCycle(result.data || result);
    } catch (error) {
      console.error("Error fetching cycle details:", error);
      showSnackbar("Failed to fetch cycle details", "error");
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchCycle();
  }, [fetchCycle]);

  const showSnackbar = (message, severity) =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const sumNumbers = (arr, accessor) => {
    if (!Array.isArray(arr)) return 0;
    return arr.reduce((sum, item) => sum + parseFloat(accessor(item) || 0), 0);
  };

  const totalQuotationCosts = sumNumbers(
    cycle?.quotations,
    (q) => q.total_cost?.$numberDecimal || q.total_cost
  );
  const totalExpenses = sumNumbers(
    cycle?.expenses,
    (e) => e.amount?.$numberDecimal || e.amount
  );
  const totalAdjustments = sumNumbers(
    cycle?.adjustments,
    (a) => a.impact_amount?.$numberDecimal || a.impact_amount
  );

  const COLORS = ["#4caf50", "#f44336", "#9c27b0", "#2196f3", "#ff9800"];

  const expenseBreakdown = [
    { name: "Quotations", value: totalQuotationCosts },
    { name: "Expenses", value: totalExpenses },
    { name: "Adjustments", value: totalAdjustments },
  ].filter((item) => item.value > 0);

  const getStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "#4caf50";
      case "pending":
        return "#ff9800";
      case "overdue":
        return "#f44336";
      case "cancelled":
        return "#9e9e9e";
      default:
        return "#2196f3";
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Cycle Finance Analysis
          </Typography>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate("/admin/financial/subscription-cycle")}
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
                "&:hover": {
                  borderColor: "#c62828",
                  backgroundColor: "rgba(219, 74, 65, 0.1)",
                },
              }}
            >
              Back
            </Button>
            {cycle && (
              <Chip
                label={cycle.status}
                sx={{
                  backgroundColor: getStatusColor(cycle.status),
                  color: "white",
                  textTransform: "capitalize",
                  fontFamily: "Formula Bold",
                  fontSize: "0.9rem",
                }}
              />
            )}
          </Box>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {loading || !cycle ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : (
            <>
              {/* KPI Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#4caf50", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#4caf50",
                            }}
                          >
                            Due Amount
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(cycle.due_amount)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                        >
                          Month: {cycle.month}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <ReceiptIcon sx={{ color: "#ff9800", mr: 1 }} />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#ff9800",
                            }}
                          >
                            Paid Amount
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(cycle.paid_amount)}
                        </Typography>
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                          <Chip
                            label={`Inflow: ${formatCurrency(cycle.inflow)}`}
                            size="small"
                            sx={{
                              backgroundColor: "#2196f3",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                          <Chip
                            label={`Outflow: ${formatCurrency(cycle.outflow)}`}
                            size="small"
                            sx={{
                              backgroundColor: "#f44336",
                              color: "white",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          {parseFloat(
                            cycle.actual_profit?.$numberDecimal ||
                              cycle.actual_profit ||
                              0
                          ) >= 0 ? (
                            <TrendingUpIcon sx={{ color: "#4caf50", mr: 1 }} />
                          ) : (
                            <TrendingDownIcon
                              sx={{ color: "#f44336", mr: 1 }}
                            />
                          )}
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color:
                                parseFloat(
                                  cycle.actual_profit?.$numberDecimal ||
                                    cycle.actual_profit ||
                                    0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            }}
                          >
                            Net Profit
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color:
                              parseFloat(
                                cycle.actual_profit?.$numberDecimal ||
                                  cycle.actual_profit ||
                                  0
                              ) >= 0
                                ? "#4caf50"
                                : "#f44336",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(cycle.actual_profit)}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(
                            (Math.abs(
                              parseFloat(
                                cycle.actual_profit?.$numberDecimal ||
                                  cycle.actual_profit ||
                                  0
                              )
                            ) /
                              Math.max(
                                parseFloat(
                                  cycle.inflow?.$numberDecimal ||
                                    cycle.inflow ||
                                    1
                                ),
                                1
                              )) *
                              100,
                            100
                          )}
                          sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                            "& .MuiLinearProgress-bar": {
                              backgroundColor:
                                parseFloat(
                                  cycle.actual_profit?.$numberDecimal ||
                                    cycle.actual_profit ||
                                    0
                                ) >= 0
                                  ? "#4caf50"
                                  : "#f44336",
                            },
                          }}
                        />
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        backdropFilter: "blur(10px)",
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                        borderRadius: "12px",
                        height: "100%",
                      }}
                    >
                      <CardContent>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <AccountBalanceIcon
                            sx={{ color: "#2196f3", mr: 1 }}
                          />
                          <Typography
                            variant="h6"
                            sx={{
                              fontFamily: "Formula Bold",
                              color: "#2196f3",
                            }}
                          >
                            Cash Flow
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "white",
                            mb: 1,
                          }}
                        >
                          {formatCurrency(cycle.inflow)}
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mt: 1,
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{ color: "#4caf50" }}
                          >
                            In: {formatCurrency(cycle.inflow)}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "#f44336" }}
                          >
                            Out: {formatCurrency(cycle.outflow)}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>

              {/* Expense Breakdown Pie Chart */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} lg={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <CalculateIcon sx={{ color: "#db4a41", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                        >
                          Cost Breakdown
                        </Typography>
                      </Box>
                      {expenseBreakdown.length > 0 ? (
                        <Box sx={{ height: 350 }}>
                          <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                              <Pie
                                data={expenseBreakdown}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                dataKey="value"
                                labelLine={false}
                                label={({
                                  cx,
                                  cy,
                                  midAngle,
                                  innerRadius,
                                  outerRadius,
                                  percent,
                                }) => {
                                  const RADIAN = Math.PI / 180;
                                  const radius =
                                    innerRadius +
                                    (outerRadius - innerRadius) / 2;
                                  const x =
                                    cx + radius * Math.cos(-midAngle * RADIAN);
                                  const y =
                                    cy + radius * Math.sin(-midAngle * RADIAN);
                                  return (
                                    <text
                                      x={x}
                                      y={y}
                                      fill="white"
                                      textAnchor="middle"
                                      dominantBaseline="central"
                                      fontSize={12}
                                      fontWeight="bold"
                                    >
                                      {(percent * 100).toFixed(0)}%
                                    </text>
                                  );
                                }}
                              >
                                {expenseBreakdown.map((entry, index) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={COLORS[index % COLORS.length]}
                                    name={entry.name}
                                  />
                                ))}
                              </Pie>
                              <Legend
                                layout="horizontal"
                                verticalAlign="top"
                                align="center"
                                formatter={(value) => (
                                  <span style={{ color: "#fff" }}>{value}</span>
                                )}
                              />
                              <RechartsTooltip
                                contentStyle={{
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "8px",
                                  color: "white",
                                }}
                                formatter={(value) => [
                                  `${parseFloat(value).toLocaleString()} EGP`,
                                  "Amount",
                                ]}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No costs recorded for this cycle
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} lg={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <AccountBalanceIcon sx={{ color: "#db4a41", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#db4a41" }}
                        >
                          Cycle Info
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Client:</strong>{" "}
                        {cycle.client_id?.client_name || "Unknown"}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Month:</strong> {cycle.month}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Fees:</strong>{" "}
                        {formatCurrency(cycle.client_id.subscription_fee)}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Goal Profit:</strong>{" "}
                        {formatCurrency(cycle.goal_profit)}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Actual Profit:</strong>{" "}
                        {formatCurrency(cycle.actual_profit)}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{ color: "white", mb: 1 }}
                      >
                        <strong>Penalties:</strong>{" "}
                        {formatCurrency(cycle.penalties)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Detailed Lists */}
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <ReceiptIcon sx={{ color: "#ff9800", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#ff9800" }}
                        >
                          Expenses ({cycle.expenses?.length || 0})
                        </Typography>
                      </Box>
                      {cycle.expenses && cycle.expenses.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {cycle.expenses.map((expense, index) => (
                            <Box
                              key={expense._id || index}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(255, 152, 0, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(255, 152, 0, 0.2)",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {expense.title || expense.category}
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#ff9800",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(expense.amount)}
                              </Typography>
                              {expense.description && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.6)",
                                    fontStyle: "italic",
                                  }}
                                >
                                  {expense.description}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No expenses for this cycle
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <MonetizationOnIcon sx={{ color: "#4caf50", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#4caf50" }}
                        >
                          Quotations ({cycle.quotations?.length || 0})
                        </Typography>
                      </Box>
                      {cycle.quotations && cycle.quotations.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {cycle.quotations.map((quotation, index) => (
                            <Box
                              key={quotation._id || index}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(76, 175, 80, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(76, 175, 80, 0.2)",
                              }}
                            >
                              <Typography
                                variant="body1"
                                sx={{
                                  color: "white",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {quotation.status}
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#4caf50",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(quotation.total_cost)}
                              </Typography>
                              {quotation.dates?.requested_at && (
                                <Typography
                                  variant="caption"
                                  sx={{ color: "rgba(255, 255, 255, 0.6)" }}
                                >
                                  {new Date(
                                    quotation.dates.requested_at
                                  ).toLocaleDateString()}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No quotations for this cycle
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card
                    sx={{
                      background: "rgba(255, 255, 255, 0.05)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(255, 255, 255, 0.1)",
                      borderRadius: "12px",
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 3 }}
                      >
                        <CalculateIcon sx={{ color: "#9c27b0", mr: 1 }} />
                        <Typography
                          variant="h6"
                          sx={{ fontFamily: "Formula Bold", color: "#9c27b0" }}
                        >
                          Adjustments ({cycle.adjustments?.length || 0})
                        </Typography>
                      </Box>
                      {cycle.adjustments && cycle.adjustments.length > 0 ? (
                        <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                          {cycle.adjustments.map((adj, index) => (
                            <Box
                              key={adj._id || index}
                              sx={{
                                p: 2,
                                mb: 2,
                                background: "rgba(156, 39, 176, 0.1)",
                                borderRadius: "8px",
                                border: "1px solid rgba(156, 39, 176, 0.2)",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                  mb: 0.5,
                                }}
                              >
                                <Typography
                                  variant="body1"
                                  sx={{
                                    color: "white",
                                    fontFamily: "Formula Bold",
                                    textTransform: "capitalize",
                                  }}
                                >
                                  {adj.category}
                                </Typography>
                                <Chip
                                  label={
                                    adj.resolved ? "Resolved" : "Unresolved"
                                  }
                                  size="small"
                                  sx={{
                                    backgroundColor: adj.resolved
                                      ? "#4caf5020"
                                      : "#ff980020",
                                    color: adj.resolved ? "#4caf50" : "#ff9800",
                                    fontFamily: "Formula Bold",
                                  }}
                                />
                              </Box>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#9c27b0",
                                  fontFamily: "Formula Bold",
                                }}
                              >
                                {formatCurrency(adj.impact_amount)}
                              </Typography>
                              {adj.description && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: "rgba(255, 255, 255, 0.6)",
                                    fontStyle: "italic",
                                  }}
                                >
                                  {adj.description}
                                </Typography>
                              )}
                            </Box>
                          ))}
                        </Box>
                      ) : (
                        <Box sx={{ textAlign: "center", py: 4 }}>
                          <Typography
                            variant="body1"
                            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                          >
                            No adjustments yet
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </>
          )}
        </Box>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default CycleFinance;
