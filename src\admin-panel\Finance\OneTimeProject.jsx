import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Collapse,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";

function OneTimeProject() {
  const [projects, setProjects] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingProject, setEditingProject] = useState(null);
  const [viewingProject, setViewingProject] = useState(null);
  const [newProject, setNewProject] = useState({
    client_id: "",
    name: "",
    fees: "",
    description: "",
    date: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [expandedMonths, setExpandedMonths] = useState({});

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");

  const { user } = useUser();

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/one-time-projects";
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";

  const fetchProjects = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setProjects(result.data || []);
    } catch (error) {
      console.error("Error fetching one-time projects:", error);
      showSnackbar("Failed to fetch one-time projects", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();

      // Handle different response structures
      if (result.success && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else if (Array.isArray(result)) {
        setClientAccounts(result);
      } else if (result.data && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else {
        console.warn("Unexpected client accounts response structure:", result);
        setClientAccounts([]);
      }
    } catch (error) {
      console.error("Error fetching client accounts:", error);
      setClientAccounts([]);
    }
  }, []);

  useEffect(() => {
    fetchProjects();
    fetchClientAccounts();
  }, [fetchProjects, fetchClientAccounts]);

  // Filter and sort projects
  const filteredAndSortedProjects = React.useMemo(() => {
    let filtered = projects.filter((project) => {
      // Search filter by description or client name
      const searchMatch =
        searchTerm === "" ||
        (project.description &&
          project.description
            .toLowerCase()
            .includes(searchTerm.toLowerCase())) ||
        (project.client_id?.client_name &&
          project.client_id.client_name
            .toLowerCase()
            .includes(searchTerm.toLowerCase()));

      // Status filter
      const statusMatch =
        statusFilter === "" || project.status === statusFilter;

      return searchMatch && statusMatch;
    });

    // Sort projects
    if (sortBy) {
      filtered.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
          case "fees":
            aValue = parseFloat(a.fees?.$numberDecimal || a.fees || 0);
            bValue = parseFloat(b.fees?.$numberDecimal || b.fees || 0);
            break;
          case "paid_amount":
            aValue = parseFloat(
              a.paid_amount?.$numberDecimal || a.paid_amount || 0
            );
            bValue = parseFloat(
              b.paid_amount?.$numberDecimal || b.paid_amount || 0
            );
            break;
          case "due_amount":
            aValue = parseFloat(
              a.due_amount?.$numberDecimal || a.due_amount || 0
            );
            bValue = parseFloat(
              b.due_amount?.$numberDecimal || b.due_amount || 0
            );
            break;
          case "profit":
            aValue = parseFloat(a.profit?.$numberDecimal || a.profit || 0);
            bValue = parseFloat(b.profit?.$numberDecimal || b.profit || 0);
            break;
          default:
            return 0;
        }

        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      });
    }

    return filtered;
  }, [projects, searchTerm, statusFilter, sortBy, sortOrder]);

  // Group projects by month
  const groupProjectsByMonth = useCallback(() => {
    const grouped = {};
    filteredAndSortedProjects.forEach((project) => {
      const date = new Date(project.date);
      const month = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;
      if (!grouped[month]) {
        grouped[month] = [];
      }
      grouped[month].push(project);
    });

    // Sort months in descending order (newest first)
    const sortedMonths = Object.keys(grouped).sort((a, b) => {
      return new Date(b) - new Date(a);
    });

    const sortedGrouped = {};
    sortedMonths.forEach((month) => {
      sortedGrouped[month] = grouped[month];
    });

    return sortedGrouped;
  }, [filteredAndSortedProjects]);

  const toggleMonthSection = (month) => {
    setExpandedMonths((prev) => ({
      ...prev,
      [month]: !prev[month],
    }));
  };

  const formatMonthName = (monthString) => {
    if (!monthString) return "Unknown Month";
    try {
      const [year, month] = monthString.split("-");
      const date = new Date(parseInt(year), parseInt(month) - 1);
      return date.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });
    } catch (error) {
      return monthString;
    }
  };

  const handleAdd = () => {
    setEditingProject(null);
    setNewProject({
      client_id: "",
      name: "",
      fees: "",
      description: "",
      date: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (project) => {
    setEditingProject(project);
    setNewProject({
      client_id: project.client_id?._id || project.client_id,
      name: project.name || "",
      fees: project.fees?.$numberDecimal || project.fees,
      description: project.description || "",
      date: project.date
        ? new Date(project.date).toISOString().split("T")[0]
        : "",
    });
    setOpenModal(true);
  };

  const handleView = (project) => {
    if (!project?._id) return;
    setViewingProject(project);
    setOpenViewModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingProject(null);
    setNewProject({
      client_id: "",
      name: "",
      fees: "",
      description: "",
      date: "",
    });
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingProject(null);
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");

      const projectData = {
        client_id: newProject.client_id,
        name: newProject.name,
        fees: parseFloat(newProject.fees),
        description: newProject.description,
        date: newProject.date,
        created_by: user?.id || user?._id,
      };

      const url = editingProject
        ? `${API_BASE_URL}/${editingProject._id}`
        : API_BASE_URL;

      const method = editingProject ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(projectData),
      });

      if (response.ok) {
        const result = await response.json();
        if (editingProject) {
          setProjects(
            projects.map((project) =>
              project._id === editingProject._id ? result : project
            )
          );
          showSnackbar("One-time project updated successfully", "success");
        } else {
          setProjects([result, ...projects]);
          showSnackbar("One-time project created successfully", "success");
        }
        handleCloseModal();
        fetchProjects(); // Refresh to get populated data
      } else {
        throw new Error("Failed to save one-time project");
      }
    } catch (error) {
      console.error("Error saving one-time project:", error);
      showSnackbar("Failed to save one-time project", "error");
    }
  };

  const handleDelete = async (id) => {
    if (
      !window.confirm("Are you sure you want to delete this one-time project?")
    ) {
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        setProjects(projects.filter((project) => project._id !== id));
        showSnackbar("One-time project deleted successfully", "success");
      } else {
        throw new Error("Failed to delete one-time project");
      }
    } catch (error) {
      console.error("Error deleting one-time project:", error);
      showSnackbar("Failed to delete one-time project", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "#4caf50";
      case "partially_paid":
        return "#ff9800";
      case "unpaid":
        return "#f44336";
      default:
        return "#2196f3";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const groupedProjects = groupProjectsByMonth();

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            One-Time Projects
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              backgroundColor: "#db4a41",
              color: "white",
              fontFamily: "Formula Bold",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
          >
            Add Project
          </Button>
        </Box>

        {/* Search and Filter Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                {/* Search Bar */}
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="Search by description or client name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <SearchIcon
                          sx={{ color: "rgba(255, 255, 255, 0.5)", mr: 1 }}
                        />
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputBase-input::placeholder": {
                        color: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  />
                </Grid>

                {/* Status Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Status</MenuItem>
                      <MenuItem value="paid">Paid</MenuItem>
                      <MenuItem value="partially_paid">Partially Paid</MenuItem>
                      <MenuItem value="unpaid">Unpaid</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort By */}
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Sort By
                    </InputLabel>
                    <Select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">Default</MenuItem>
                      <MenuItem value="fees">Fees</MenuItem>
                      <MenuItem value="paid_amount">Paid Amount</MenuItem>
                      <MenuItem value="due_amount">Due Amount</MenuItem>
                      <MenuItem value="profit">Profit</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort Order */}
                {sortBy && (
                  <Grid item xs={12} sm={6} md={2}>
                    <FormControl fullWidth>
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Order
                      </InputLabel>
                      <Select
                        value={sortOrder}
                        onChange={(e) => setSortOrder(e.target.value)}
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.5)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                        }}
                      >
                        <MenuItem value="asc">Ascending</MenuItem>
                        <MenuItem value="desc">Descending</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                {/* Clear Filters Button */}
                {(searchTerm || statusFilter || sortBy) && (
                  <Grid item xs={12} sm={6} md={1}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => {
                        setSearchTerm("");
                        setStatusFilter("");
                        setSortBy("");
                        setSortOrder("asc");
                      }}
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&:hover": {
                          borderColor: "#db4a41",
                          color: "#db4a41",
                        },
                        minWidth: "auto",
                        px: 2,
                      }}
                    >
                      Clear
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Results Counter */}
          {!loading && (
            <Box
              sx={{
                mb: 2,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                }}
              >
                Showing {filteredAndSortedProjects.length} of {projects.length}{" "}
                projects
              </Typography>
            </Box>
          )}

          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : projects.length === 0 ? (
            <Box sx={{ textAlign: "center", mt: 4 }}>
              <Typography
                variant="h6"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                No one-time projects found
              </Typography>
            </Box>
          ) : filteredAndSortedProjects.length === 0 ? (
            <Box sx={{ textAlign: "center", mt: 4 }}>
              <Typography
                variant="h6"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                No projects match your current filters
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "rgba(255, 255, 255, 0.5)",
                  mt: 1,
                }}
              >
                Try adjusting your search terms or filters
              </Typography>
            </Box>
          ) : (
            <Box>
              {Object.entries(groupedProjects).map(([month, monthProjects]) => (
                <Box key={month} sx={{ mb: 4 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                      padding: "12px 20px",
                      borderRadius: "8px",
                      border: "1px solid rgba(219, 74, 65, 0.3)",
                      cursor: "pointer",
                      mb: 2,
                    }}
                    onClick={() => toggleMonthSection(month)}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                      }}
                    >
                      {formatMonthName(month)}
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Chip
                        label={`${monthProjects.length} project${
                          monthProjects.length !== 1 ? "s" : ""
                        }`}
                        sx={{
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          color: "white",
                        }}
                      />
                      {expandedMonths[month] ? (
                        <ExpandLessIcon sx={{ color: "#db4a41" }} />
                      ) : (
                        <ExpandMoreIcon sx={{ color: "#db4a41" }} />
                      )}
                    </Box>
                  </Box>

                  <Collapse in={expandedMonths[month] !== false}>
                    <Grid container spacing={3}>
                      <AnimatePresence>
                        {monthProjects.map((project) => (
                          <Grid item xs={12} sm={6} md={4} key={project._id}>
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Card
                                sx={{
                                  background: "rgba(255, 255, 255, 0.05)",
                                  backdropFilter: "blur(10px)",
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "12px",
                                  height: "100%",
                                  display: "flex",
                                  flexDirection: "column",
                                  position: "relative",
                                  "&:hover": {
                                    background: "rgba(255, 255, 255, 0.08)",
                                    borderColor: "rgba(219, 74, 65, 0.3)",
                                  },
                                }}
                              >
                                <Box
                                  sx={{
                                    position: "absolute",
                                    top: 8,
                                    right: 8,
                                    display: "flex",
                                    gap: 0.5,
                                    zIndex: 10,
                                  }}
                                >
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(project)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(project)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(project._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>

                                <CardContent sx={{ flexGrow: 1, pt: 5 }}>
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      mb: 2,
                                    }}
                                  >
                                    <Avatar
                                      sx={{
                                        bgcolor: stringToColor(
                                          project.client_id?.client_name ||
                                            "Unknown"
                                        ),
                                        mr: 2,
                                      }}
                                    >
                                      <MonetizationOnIcon />
                                    </Avatar>
                                    <Box>
                                      <Typography
                                        variant="h6"
                                        sx={{
                                          fontFamily: "Formula Bold",
                                          color: "white",
                                          fontSize: "1.1rem",
                                        }}
                                      >
                                        {project.name || "Unnamed Project"}
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          fontSize: "0.9rem",
                                          mb: 0.5,
                                        }}
                                      >
                                        Client:{" "}
                                        {project.client_id?.client_name ||
                                          "Unknown Client"}
                                      </Typography>
                                      <Box
                                        sx={{
                                          display: "flex",
                                          gap: 1,
                                          mt: 0.5,
                                        }}
                                      >
                                        <Chip
                                          label={project.status}
                                          size="small"
                                          sx={{
                                            backgroundColor: `${getStatusColor(
                                              project.status
                                            )}20`,
                                            color: getStatusColor(
                                              project.status
                                            ),
                                            textTransform: "capitalize",
                                            fontFamily: "Formula Bold",
                                          }}
                                        />
                                      </Box>
                                    </Box>
                                  </Box>

                                  <Box sx={{ mb: 2 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.8)",
                                        mb: 1,
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      💰 Fees: {formatCurrency(project.fees)}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.8)",
                                        mb: 1,
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      💳 Paid:{" "}
                                      {formatCurrency(project.paid_amount)}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.8)",
                                        mb: 1,
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      📅 Due:{" "}
                                      {formatCurrency(project.due_amount)}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color:
                                          project.profit?.$numberDecimal > 0
                                            ? "#4caf50"
                                            : "#f44336",
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      {project.profit?.$numberDecimal > 0 ? (
                                        <TrendingUpIcon fontSize="small" />
                                      ) : (
                                        <TrendingDownIcon fontSize="small" />
                                      )}
                                      Profit: {formatCurrency(project.profit)}
                                    </Typography>
                                  </Box>

                                  <Box sx={{ mb: 2 }}>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.6)",
                                        fontFamily: "Anton",
                                        display: "block",
                                        mb: 0.5,
                                      }}
                                    >
                                      Date:{" "}
                                      {new Date(
                                        project.date
                                      ).toLocaleDateString()}
                                    </Typography>
                                    {project.description && (
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.6)",
                                          fontFamily: "Anton",
                                          display: "block",
                                        }}
                                      >
                                        Description: {project.description}
                                      </Typography>
                                    )}
                                  </Box>
                                </CardContent>
                              </Card>
                            </motion.div>
                          </Grid>
                        ))}
                      </AnimatePresence>
                    </Grid>
                  </Collapse>
                </Box>
              ))}
            </Box>
          )}
        </Box>

        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingProject
              ? "Edit One-Time Project"
              : "Create New One-Time Project"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newProject.client_id}
                    onChange={(e) =>
                      setNewProject({
                        ...newProject,
                        client_id: e.target.value,
                      })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          backgroundColor: "rgba(26, 26, 26, 0.95)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          "& .MuiMenuItem-root": {
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          },
                        },
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((account) => (
                        <MenuItem key={account._id} value={account._id}>
                          {account.client_name}
                        </MenuItem>
                      ))}
                    {!Array.isArray(clientAccounts) && (
                      <MenuItem disabled>No client accounts available</MenuItem>
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Project Name"
                  value={newProject.name}
                  onChange={(e) =>
                    setNewProject({ ...newProject, name: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Fees"
                  type="number"
                  value={newProject.fees}
                  onChange={(e) =>
                    setNewProject({ ...newProject, fees: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date"
                  type="date"
                  value={newProject.date}
                  onChange={(e) =>
                    setNewProject({ ...newProject, date: e.target.value })
                  }
                  InputLabelProps={{
                    shrink: true,
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={newProject.description}
                  onChange={(e) =>
                    setNewProject({
                      ...newProject,
                      description: e.target.value,
                    })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingProject ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            One-Time Project Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingProject && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          viewingProject.client_id?.client_name || "Unknown"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <MonetizationOnIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold", color: "white" }}
                      >
                        {viewingProject.client_id?.client_name ||
                          "Unknown Client"}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        <Chip
                          label={viewingProject.status}
                          sx={{
                            backgroundColor: getStatusColor(
                              viewingProject.status
                            ),
                            color: "white",
                            textTransform: "capitalize",
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Financial Summary
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Fees:</strong> {formatCurrency(viewingProject.fees)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Paid Amount:</strong>{" "}
                    {formatCurrency(viewingProject.paid_amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Due Amount:</strong>{" "}
                    {formatCurrency(viewingProject.due_amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Profit:</strong>{" "}
                    {formatCurrency(viewingProject.profit)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Date:</strong>{" "}
                    {new Date(viewingProject.date).toLocaleDateString()}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Project Details
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Description:</strong>{" "}
                    {viewingProject.description || "No description"}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Created By:</strong>{" "}
                    {viewingProject.created_by?.name || "Unknown"}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Created At:</strong>{" "}
                    {new Date(viewingProject.createdAt).toLocaleDateString()}
                  </Typography>
                </Grid>

                {viewingProject.expenses &&
                  viewingProject.expenses.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#db4a41",
                          mb: 1,
                          fontFamily: "Formula Bold",
                        }}
                      >
                        Expenses ({viewingProject.expenses.length})
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {viewingProject.expenses.map((expense, index) => (
                          <Chip
                            key={expense._id || index}
                            label={`${expense.category} - ${formatCurrency(
                              expense.amount
                            )}`}
                            sx={{
                              backgroundColor: "#f44336",
                              color: "white",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}

                {viewingProject.quotations &&
                  viewingProject.quotations.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#db4a41",
                          mb: 1,
                          fontFamily: "Formula Bold",
                        }}
                      >
                        Quotations ({viewingProject.quotations.length})
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {viewingProject.quotations.map((quotation, index) => (
                          <Chip
                            key={quotation._id || index}
                            label={`${quotation.status} - ${formatCurrency(
                              quotation.total_cost
                            )}`}
                            sx={{
                              backgroundColor:
                                quotation.status === "approved"
                                  ? "#4caf50"
                                  : "#ff9800",
                              color: "white",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default OneTimeProject;
