import React from "react";
import { Box, Typography, TextField } from "@mui/material";

const Leads = () => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: "50px",
        backgroundColor: "black",
      }}
    >
      <Typography
        variant="h2"
        sx={{
          fontFamily: "Formula Bold",
          marginBottom: "20px",
          color: "white",
        }}
      >
        Join Our Creative Community
      </Typography>
      <Typography
        variant="body1"
        sx={{
          marginBottom: "20px",
          textAlign: "center",
          color: "grey",

          fontFamily: "<PERSON>",
        }}
      >
        Subscribe to the Young newsletter to receive studio updates, new work
        and to our latest thoughts.
      </Typography>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          flexDirection: "column",
          gap: "20px",
          width: "100%",
          color: "white",
        }}
      >
        <TextField
          id="email"
          variant="standard"
          placeholder="Enter your email"
          sx={{
            width: "70%",

            // Input text
            "& input": {
              color: "white",
              fontFamily: "'Anton', sans-serif",
              fontSize: "16px",
            },

            // Placeholder text
            "& input::placeholder": {
              color: "white",
              opacity: 1,
              fontFamily: "'Anton', sans-serif",
            },

            // Underline styles
            "& .MuiInput-underline:before": {
              borderBottomColor: "rgba(255, 255, 255, 0.3)",
            },
            "& .MuiInput-underline:hover:before": {
              borderBottomColor: "white",
            },
            "& .MuiInput-underline:after": {
              borderBottomColor: "white",
            },
          }}
        />

        <button
          className="btn btn-secondry btn-hover-alt"
          variant="contained"
          style={{
            borderRadius: "10px",
            color: "white",
            backgroundColor: "#db4a41",
          }}
        >
          Subscribe
        </button>
      </Box>
    </Box>
  );
};

export default Leads;
