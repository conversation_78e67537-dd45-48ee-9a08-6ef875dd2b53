import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  LinearProgress,
  Collapse,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import CalculateIcon from "@mui/icons-material/Calculate";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import SearchIcon from "@mui/icons-material/Search";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";

function SubscriptionCycle() {
  const navigate = useNavigate();
  const [cycles, setCycles] = useState([]);
  const [clientAccounts, setClientAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openViewModal, setOpenViewModal] = useState(false);
  const [editingCycle, setEditingCycle] = useState(null);
  const [viewingCycle, setViewingCycle] = useState(null);
  const [newCycle, setNewCycle] = useState({
    client_id: "",
    month: "",
    due_amount: "",
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [expandedMonths, setExpandedMonths] = useState({});
  const [openGenerateModal, setOpenGenerateModal] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState("");
  const [confirmGenerateOpen, setConfirmGenerateOpen] = useState(false);
  const [generating, setGenerating] = useState(false);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [sortBy, setSortBy] = useState("");
  const [sortOrder, setSortOrder] = useState("asc");

  const { user } = useUser();

  const API_BASE_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/subscription-cycles";
  const CLIENT_ACCOUNTS_API_URL =
    "https://youngproductions-768ada043db3.herokuapp.com/api/financial/clients-account";

  const fetchCycles = useCallback(async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(API_BASE_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();
      setCycles(result.data || []);
    } catch (error) {
      console.error("Error fetching subscription cycles:", error);
      showSnackbar("Failed to fetch subscription cycles", "error");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchClientAccounts = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(CLIENT_ACCOUNTS_API_URL, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const result = await response.json();

      // Handle different response structures
      if (result.success && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else if (Array.isArray(result)) {
        setClientAccounts(result);
      } else if (result.data && Array.isArray(result.data)) {
        setClientAccounts(result.data);
      } else {
        console.warn("Unexpected client accounts response structure:", result);
        setClientAccounts([]);
      }
    } catch (error) {
      console.error("Error fetching client accounts:", error);
      setClientAccounts([]);
    }
  }, []);

  useEffect(() => {
    fetchCycles();
    fetchClientAccounts();
  }, [fetchCycles, fetchClientAccounts]);

  // Filter and sort cycles
  const filteredAndSortedCycles = React.useMemo(() => {
    let filtered = cycles.filter((cycle) => {
      // Search filter by cycle_name
      const searchMatch =
        searchTerm === "" ||
        (cycle.cycle_name &&
          cycle.cycle_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (cycle.client_id?.client_name &&
          cycle.client_id.client_name
            .toLowerCase()
            .includes(searchTerm.toLowerCase()));

      // Status filter
      const statusMatch = statusFilter === "" || cycle.status === statusFilter;

      return searchMatch && statusMatch;
    });

    // Sort cycles
    if (sortBy) {
      filtered.sort((a, b) => {
        let aValue, bValue;

        switch (sortBy) {
          case "goal_profit":
            aValue = parseFloat(
              a.goal_profit?.$numberDecimal || a.goal_profit || 0
            );
            bValue = parseFloat(
              b.goal_profit?.$numberDecimal || b.goal_profit || 0
            );
            break;
          case "actual_profit":
            aValue = parseFloat(
              a.actual_profit?.$numberDecimal || a.actual_profit || 0
            );
            bValue = parseFloat(
              b.actual_profit?.$numberDecimal || b.actual_profit || 0
            );
            break;
          case "inflow":
            aValue = parseFloat(a.inflow?.$numberDecimal || a.inflow || 0);
            bValue = parseFloat(b.inflow?.$numberDecimal || b.inflow || 0);
            break;
          case "outflow":
            aValue = parseFloat(a.outflow?.$numberDecimal || a.outflow || 0);
            bValue = parseFloat(b.outflow?.$numberDecimal || b.outflow || 0);
            break;
          default:
            return 0;
        }

        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      });
    }

    return filtered;
  }, [cycles, searchTerm, statusFilter, sortBy, sortOrder]);

  // Group cycles by month
  const groupCyclesByMonth = useCallback(() => {
    const grouped = {};
    filteredAndSortedCycles.forEach((cycle) => {
      const month = cycle.month;
      if (!grouped[month]) {
        grouped[month] = [];
      }
      grouped[month].push(cycle);
    });

    // Sort months in descending order (newest first)
    const sortedMonths = Object.keys(grouped).sort((a, b) => {
      return new Date(b) - new Date(a);
    });

    const sortedGrouped = {};
    sortedMonths.forEach((month) => {
      sortedGrouped[month] = grouped[month];
    });

    return sortedGrouped;
  }, [filteredAndSortedCycles]);

  const toggleMonthSection = (month) => {
    setExpandedMonths((prev) => ({
      ...prev,
      [month]: !prev[month],
    }));
  };

  const formatMonthName = (monthString) => {
    if (!monthString) return "Unknown Month";
    try {
      const [year, month] = monthString.split("-");
      const date = new Date(parseInt(year), parseInt(month) - 1);
      return date.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });
    } catch (error) {
      return monthString;
    }
  };

  const handleAdd = () => {
    setEditingCycle(null);
    setNewCycle({
      client_id: "",
      month: "",
      due_amount: "",
    });
    setOpenModal(true);
  };

  const handleEdit = (cycle) => {
    setEditingCycle(cycle);
    setNewCycle({
      client_id: cycle.client_id?._id || cycle.client_id,
      month: cycle.month,
      due_amount: cycle.due_amount?.$numberDecimal || cycle.due_amount,
    });
    setOpenModal(true);
  };

  const handleView = (cycle) => {
    if (!cycle?._id) return;
    navigate(`/admin/financial/subscription-cycle/${cycle._id}`);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditingCycle(null);
    setNewCycle({
      client_id: "",
      month: "",
      due_amount: "",
    });
  };

  const handleCloseViewModal = () => {
    setOpenViewModal(false);
    setViewingCycle(null);
  };

  const handleOpenGenerateModal = () => {
    setSelectedClientId("");
    setOpenGenerateModal(true);
  };

  const handleCloseGenerateModal = () => {
    setOpenGenerateModal(false);
    setSelectedClientId("");
  };

  const submitGenerate = () => {
    if (!selectedClientId) {
      showSnackbar("Please select a client account", "warning");
      return;
    }
    setConfirmGenerateOpen(true);
  };

  const handleConfirmGenerate = async () => {
    try {
      setGenerating(true);
      const token = localStorage.getItem("token");
      const url = `${API_BASE_URL}/${selectedClientId}/generate-cycles-based-date`;
      const response = await fetch(url, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("Failed to generate cycles");
      }

      // Response might be informational only
      await response.json().catch(() => ({}));

      showSnackbar("Cycles generated successfully", "success");
      setOpenGenerateModal(false);
      setConfirmGenerateOpen(false);
      fetchCycles();
    } catch (error) {
      console.error("Error generating cycles:", error);
      showSnackbar("Failed to generate cycles", "error");
    } finally {
      setGenerating(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const token = localStorage.getItem("token");

      const cycleData = {
        client_id: newCycle.client_id,
        month: newCycle.month,
        due_amount: parseFloat(newCycle.due_amount),
        created_by: user?.id || user?._id,
      };

      const url = editingCycle
        ? `${API_BASE_URL}/${editingCycle._id}`
        : API_BASE_URL;

      const method = editingCycle ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(cycleData),
      });

      if (response.ok) {
        const result = await response.json();
        if (editingCycle) {
          setCycles(
            cycles.map((cycle) =>
              cycle._id === editingCycle._id ? result : cycle
            )
          );
          showSnackbar("Subscription cycle updated successfully", "success");
        } else {
          setCycles([result, ...cycles]);
          showSnackbar("Subscription cycle created successfully", "success");
        }
        handleCloseModal();
        fetchCycles(); // Refresh to get populated data
      } else {
        throw new Error("Failed to save subscription cycle");
      }
    } catch (error) {
      console.error("Error saving subscription cycle:", error);
      showSnackbar("Failed to save subscription cycle", "error");
    }
  };

  const handleDelete = async (id) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this subscription cycle?"
      )
    ) {
      return;
    }
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(`${API_BASE_URL}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.ok) {
        setCycles(cycles.filter((cycle) => cycle._id !== id));
        showSnackbar("Subscription cycle deleted successfully", "success");
      } else {
        throw new Error("Failed to delete subscription cycle");
      }
    } catch (error) {
      console.error("Error deleting subscription cycle:", error);
      showSnackbar("Failed to delete subscription cycle", "error");
    }
  };

  const handleCalculateProfit = async (cycleId) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${API_BASE_URL}/${cycleId}/calculate-profit`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        // Update the cycle in the list
        setCycles(
          cycles.map((cycle) =>
            cycle._id === cycleId ? { ...cycle, ...result } : cycle
          )
        );

        // Update viewing cycle if it's the same one
        if (viewingCycle && viewingCycle._id === cycleId) {
          setViewingCycle({ ...viewingCycle, ...result });
        }

        showSnackbar("Profit calculated successfully", "success");
      } else {
        throw new Error("Failed to calculate profit");
      }
    } catch (error) {
      console.error("Error calculating profit:", error);
      showSnackbar("Failed to calculate profit", "error");
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "paid":
        return "#4caf50";
      case "pending":
        return "#ff9800";
      case "overdue":
        return "#f44336";
      case "cancelled":
        return "#9e9e9e";
      default:
        return "#2196f3";
    }
  };

  const getProfitStatusColor = (profitStatus) => {
    switch (profitStatus) {
      case "exceeded":
        return "#4caf50";
      case "met":
        return "#2196f3";
      case "below":
        return "#ff9800";
      case "loss":
        return "#f44336";
      default:
        return "#9e9e9e";
    }
  };

  const stringToColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  const formatCurrency = (amount) => {
    if (!amount) return "0 EGP";
    const value = amount.$numberDecimal || amount;
    return `${parseFloat(value).toLocaleString()} EGP`;
  };

  const calculateTotalCosts = (cycle) => {
    let totalCosts = 0;

    // Add quotations total costs
    if (cycle.quotations) {
      totalCosts += cycle.quotations.reduce((sum, quotation) => {
        const cost =
          quotation.total_cost?.$numberDecimal || quotation.total_cost || 0;
        return sum + parseFloat(cost);
      }, 0);
    }

    // Add expenses amounts
    if (cycle.expenses) {
      totalCosts += cycle.expenses.reduce((sum, expense) => {
        const amount = expense.amount?.$numberDecimal || expense.amount || 0;
        return sum + parseFloat(amount);
      }, 0);
    }

    // Add adjustments impact amounts
    if (cycle.adjustments) {
      totalCosts += cycle.adjustments.reduce((sum, adjustment) => {
        const impact =
          adjustment.impact_amount?.$numberDecimal ||
          adjustment.impact_amount ||
          0;
        return sum + parseFloat(impact);
      }, 0);
    }

    return totalCosts;
  };

  const groupedCycles = groupCyclesByMonth();

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "60px 5% 40px",
          }}
        >
          <Typography
            variant="h3"
            sx={{
              fontFamily: "Formula Bold",
              color: "#db4a41",
              textShadow: "0 2px 4px rgba(0,0,0,0.3)",
            }}
          >
            Subscription Cycles
          </Typography>
          <Box sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              onClick={handleOpenGenerateModal}
              sx={{
                borderColor: "#db4a41",
                color: "#db4a41",
                fontFamily: "Formula Bold",
                "&:hover": {
                  borderColor: "#c62828",
                  backgroundColor: "rgba(219, 74, 65, 0.08)",
                },
              }}
            >
              Generate Cycles
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{
                backgroundColor: "#db4a41",
                color: "white",
                fontFamily: "Formula Bold",
                "&:hover": {
                  backgroundColor: "#c62828",
                },
              }}
            >
              Add Cycle
            </Button>
          </Box>
        </Box>

        {/* Search and Filter Section */}
        <Box sx={{ padding: "0 5% 20px" }}>
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                {/* Search Bar */}
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    placeholder="Search by cycle name or client name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <SearchIcon
                          sx={{ color: "rgba(255, 255, 255, 0.5)", mr: 1 }}
                        />
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&:hover fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.5)",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#db4a41",
                        },
                      },
                      "& .MuiInputBase-input::placeholder": {
                        color: "rgba(255, 255, 255, 0.5)",
                      },
                    }}
                  />
                </Grid>

                {/* Status Filter */}
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Status
                    </InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">All Status</MenuItem>
                      <MenuItem value="paid">Paid</MenuItem>
                      <MenuItem value="partially_paid">Partially Paid</MenuItem>
                      <MenuItem value="unpaid">Unpaid</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort By */}
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth>
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Sort By
                    </InputLabel>
                    <Select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="">Default</MenuItem>
                      <MenuItem value="goal_profit">Goal Profit</MenuItem>
                      <MenuItem value="actual_profit">Actual Profit</MenuItem>
                      <MenuItem value="inflow">Inflow</MenuItem>
                      <MenuItem value="outflow">Outflow</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sort Order */}
                {sortBy && (
                  <Grid item xs={12} sm={6} md={2}>
                    <FormControl fullWidth>
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Order
                      </InputLabel>
                      <Select
                        value={sortOrder}
                        onChange={(e) => setSortOrder(e.target.value)}
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                        }}
                      >
                        <MenuItem value="asc">Ascending</MenuItem>
                        <MenuItem value="desc">Descending</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                {/* Clear Filters Button */}
                {(searchTerm || statusFilter || sortBy) && (
                  <Grid item xs={12} sm={6} md={1}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => {
                        setSearchTerm("");
                        setStatusFilter("");
                        setSortBy("");
                        setSortOrder("asc");
                      }}
                      sx={{
                        color: "rgba(255, 255, 255, 0.7)",
                        borderColor: "rgba(255, 255, 255, 0.3)",
                        "&:hover": {
                          borderColor: "#db4a41",
                          color: "#db4a41",
                        },
                        minWidth: "auto",
                        px: 2,
                      }}
                    >
                      Clear
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ padding: "0 5% 40px" }}>
          {/* Results Counter */}
          {!loading && (
            <Box
              sx={{
                mb: 2,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                }}
              >
                Showing {filteredAndSortedCycles.length} of {cycles.length}{" "}
                cycles
              </Typography>
            </Box>
          )}

          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
              <CircularProgress sx={{ color: "#db4a41" }} />
            </Box>
          ) : cycles.length === 0 ? (
            <Box sx={{ textAlign: "center", mt: 4 }}>
              <Typography
                variant="h6"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                No subscription cycles found
              </Typography>
            </Box>
          ) : filteredAndSortedCycles.length === 0 ? (
            <Box sx={{ textAlign: "center", mt: 4 }}>
              <Typography
                variant="h6"
                sx={{ color: "rgba(255, 255, 255, 0.7)" }}
              >
                No cycles match your current filters
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "rgba(255, 255, 255, 0.5)",
                  mt: 1,
                }}
              >
                Try adjusting your search terms or filters
              </Typography>
            </Box>
          ) : (
            <Box>
              {Object.entries(groupedCycles).map(([month, monthCycles]) => (
                <Box key={month} sx={{ mb: 4 }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: "rgba(219, 74, 65, 0.1)",
                      padding: "12px 20px",
                      borderRadius: "8px",
                      border: "1px solid rgba(219, 74, 65, 0.3)",
                      cursor: "pointer",
                      mb: 2,
                    }}
                    onClick={() => toggleMonthSection(month)}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        fontFamily: "Formula Bold",
                        color: "#db4a41",
                      }}
                    >
                      {formatMonthName(month)}
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Chip
                        label={`${monthCycles.length} cycle${
                          monthCycles.length !== 1 ? "s" : ""
                        }`}
                        sx={{
                          backgroundColor: "rgba(255, 255, 255, 0.1)",
                          color: "white",
                        }}
                      />
                      {expandedMonths[month] ? (
                        <ExpandLessIcon sx={{ color: "#db4a41" }} />
                      ) : (
                        <ExpandMoreIcon sx={{ color: "#db4a41" }} />
                      )}
                    </Box>
                  </Box>

                  <Collapse in={expandedMonths[month] !== false}>
                    <Grid container spacing={3}>
                      <AnimatePresence>
                        {monthCycles.map((cycle) => (
                          <Grid item xs={12} sm={6} md={4} key={cycle._id}>
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Card
                                sx={{
                                  background: "rgba(255, 255, 255, 0.05)",
                                  backdropFilter: "blur(10px)",
                                  border: "1px solid rgba(255, 255, 255, 0.1)",
                                  borderRadius: "12px",
                                  height: "100%",
                                  display: "flex",
                                  flexDirection: "column",
                                  position: "relative",
                                  "&:hover": {
                                    background: "rgba(255, 255, 255, 0.08)",
                                    borderColor: "rgba(219, 74, 65, 0.3)",
                                  },
                                }}
                              >
                                <Box
                                  sx={{
                                    position: "absolute",
                                    top: 8,
                                    right: 8,
                                    display: "flex",
                                    gap: 0.5,
                                    zIndex: 10,
                                  }}
                                >
                                  <Tooltip title="Calculate Profit">
                                    <IconButton
                                      size="small"
                                      onClick={() =>
                                        handleCalculateProfit(cycle._id)
                                      }
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#4caf50" },
                                      }}
                                    >
                                      <CalculateIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="View">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleView(cycle)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#db4a41" },
                                      }}
                                    >
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Edit">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEdit(cycle)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#2196f3" },
                                      }}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                  <Tooltip title="Delete">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleDelete(cycle._id)}
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.7)",
                                        "&:hover": { color: "#f44336" },
                                      }}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </Box>

                                <CardContent sx={{ flexGrow: 1, pt: 5 }}>
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      mb: 2,
                                    }}
                                  >
                                    <Avatar
                                      sx={{
                                        bgcolor: stringToColor(
                                          cycle.client_id?.client_name ||
                                            "Unknown"
                                        ),
                                        mr: 2,
                                      }}
                                    >
                                      <MonetizationOnIcon />
                                    </Avatar>
                                    <Box>
                                      <Typography
                                        variant="h6"
                                        sx={{
                                          fontFamily: "Formula Bold",
                                          color: "white",
                                          fontSize: "1.1rem",
                                        }}
                                      >
                                        {cycle.cycle_name ||
                                          `${cycle.client_id?.client_name} - ${cycle.month}`}
                                      </Typography>
                                      <Box
                                        sx={{
                                          display: "flex",
                                          gap: 1,
                                          mt: 0.5,
                                        }}
                                      >
                                        <Chip
                                          label={cycle.status}
                                          size="small"
                                          sx={{
                                            backgroundColor: `${getStatusColor(
                                              cycle.status
                                            )}20`,
                                            color: getStatusColor(cycle.status),
                                            textTransform: "capitalize",
                                            fontFamily: "Formula Bold",
                                          }}
                                        />
                                        {cycle.profit_status && (
                                          <Chip
                                            label={cycle.profit_status}
                                            size="small"
                                            sx={{
                                              backgroundColor: `${getProfitStatusColor(
                                                cycle.profit_status
                                              )}20`,
                                              color: getProfitStatusColor(
                                                cycle.profit_status
                                              ),
                                              textTransform: "capitalize",
                                              fontFamily: "Formula Bold",
                                            }}
                                          />
                                        )}
                                      </Box>
                                    </Box>
                                  </Box>

                                  <Box sx={{ mb: 2 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.8)",
                                        mb: 1,
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      💰 Due: {formatCurrency(cycle.due_amount)}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.8)",
                                        mb: 1,
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      💳 Paid:{" "}
                                      {formatCurrency(cycle.paid_amount)}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color:
                                          cycle.actual_profit?.$numberDecimal >
                                          0
                                            ? "#4caf50"
                                            : "#f44336",
                                        fontFamily: "Anton",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                      }}
                                    >
                                      {cycle.actual_profit?.$numberDecimal >
                                      0 ? (
                                        <TrendingUpIcon fontSize="small" />
                                      ) : (
                                        <TrendingDownIcon fontSize="small" />
                                      )}
                                      Profit:{" "}
                                      {formatCurrency(cycle.actual_profit)}
                                    </Typography>
                                  </Box>

                                  <Box sx={{ mb: 2 }}>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.6)",
                                        fontFamily: "Anton",
                                        display: "block",
                                        mb: 0.5,
                                      }}
                                    >
                                      Month: {cycle.month}
                                    </Typography>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: "rgba(255, 255, 255, 0.6)",
                                        fontFamily: "Anton",
                                        display: "block",
                                        mb: 0.5,
                                      }}
                                    >
                                      Total Costs:{" "}
                                      {calculateTotalCosts(
                                        cycle
                                      ).toLocaleString()}{" "}
                                      EGP
                                    </Typography>
                                    {cycle.quotations &&
                                      cycle.quotations.length > 0 && (
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            color: "rgba(255, 255, 255, 0.6)",
                                            fontFamily: "Anton",
                                            display: "block",
                                          }}
                                        >
                                          Quotations: {cycle.quotations.length}
                                        </Typography>
                                      )}
                                  </Box>

                                  {cycle.inflow && cycle.outflow && (
                                    <Box sx={{ mb: 1 }}>
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          color: "rgba(255, 255, 255, 0.7)",
                                          fontFamily: "Anton",
                                          display: "block",
                                          mb: 0.5,
                                        }}
                                      >
                                        Cash Flow
                                      </Typography>
                                      <LinearProgress
                                        variant="determinate"
                                        value={Math.min(
                                          (parseFloat(
                                            cycle.outflow?.$numberDecimal || 0
                                          ) /
                                            parseFloat(
                                              cycle.inflow?.$numberDecimal || 1
                                            )) *
                                            100,
                                          100
                                        )}
                                        sx={{
                                          height: 6,
                                          borderRadius: 3,
                                          backgroundColor:
                                            "rgba(255, 255, 255, 0.1)",
                                          "& .MuiLinearProgress-bar": {
                                            backgroundColor:
                                              cycle.actual_profit
                                                ?.$numberDecimal > 0
                                                ? "#4caf50"
                                                : "#f44336",
                                          },
                                        }}
                                      />
                                      <Box
                                        sx={{
                                          display: "flex",
                                          justifyContent: "space-between",
                                          mt: 0.5,
                                        }}
                                      >
                                        <Typography
                                          variant="caption"
                                          sx={{ color: "#4caf50" }}
                                        >
                                          In: {formatCurrency(cycle.inflow)}
                                        </Typography>
                                        <Typography
                                          variant="caption"
                                          sx={{ color: "#f44336" }}
                                        >
                                          Out: {formatCurrency(cycle.outflow)}
                                        </Typography>
                                      </Box>
                                    </Box>
                                  )}
                                </CardContent>
                              </Card>
                            </motion.div>
                          </Grid>
                        ))}
                      </AnimatePresence>
                    </Grid>
                  </Collapse>
                </Box>
              ))}
            </Box>
          )}
        </Box>
        {/* Create/Edit Modal */}
        <Dialog
          open={openModal}
          onClose={handleCloseModal}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            {editingCycle
              ? "Edit Subscription Cycle"
              : "Create New Subscription Cycle"}
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={newCycle.client_id}
                    onChange={(e) =>
                      setNewCycle({ ...newCycle, client_id: e.target.value })
                    }
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          backgroundColor: "rgba(26, 26, 26, 0.95)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          "& .MuiMenuItem-root": {
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          },
                        },
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((account) => (
                        <MenuItem key={account._id} value={account._id}>
                          {account.client_name} - $
                          {account.subscription_fee?.$numberDecimal ||
                            account.subscription_fee}
                        </MenuItem>
                      ))}
                    {!Array.isArray(clientAccounts) && (
                      <MenuItem disabled>No client accounts available</MenuItem>
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Month (YYYY-MM)"
                  value={newCycle.month}
                  onChange={(e) =>
                    setNewCycle({ ...newCycle, month: e.target.value })
                  }
                  placeholder="2025-10"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Due Amount"
                  type="number"
                  value={newCycle.due_amount}
                  onChange={(e) =>
                    setNewCycle({ ...newCycle, due_amount: e.target.value })
                  }
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                      "&:hover fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                    },
                    "& .MuiInputLabel-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {editingCycle ? "Update" : "Create"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Generate Cycles Modal */}
        <Dialog
          open={openGenerateModal}
          onClose={handleCloseGenerateModal}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Generate Past Cycles
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                    Client Account
                  </InputLabel>
                  <Select
                    value={selectedClientId}
                    onChange={(e) => setSelectedClientId(e.target.value)}
                    sx={{
                      color: "white",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.3)",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "rgba(255, 255, 255, 0.5)",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#db4a41",
                      },
                    }}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          backgroundColor: "rgba(26, 26, 26, 0.95)",
                          backdropFilter: "blur(10px)",
                          border: "1px solid rgba(255, 255, 255, 0.1)",
                          "& .MuiMenuItem-root": {
                            color: "white",
                            "&:hover": {
                              backgroundColor: "rgba(219, 74, 65, 0.1)",
                            },
                          },
                        },
                      },
                    }}
                  >
                    {Array.isArray(clientAccounts) &&
                      clientAccounts.map((account) => (
                        <MenuItem key={account._id} value={account._id}>
                          {account.client_name} - $
                          {account.subscription_fee?.$numberDecimal ||
                            account.subscription_fee}
                        </MenuItem>
                      ))}
                    {!Array.isArray(clientAccounts) && (
                      <MenuItem disabled>No client accounts available</MenuItem>
                    )}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={handleCloseGenerateModal}
              disabled={generating}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={submitGenerate}
              variant="contained"
              disabled={generating}
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {generating ? "Processing..." : "Submit"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Confirm Generate Dialog */}
        <Dialog
          open={confirmGenerateOpen}
          onClose={() => setConfirmGenerateOpen(false)}
          maxWidth="xs"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Confirm Generation
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            <Typography sx={{ color: "rgba(255, 255, 255, 0.9)" }}>
              Are you sure you want to generate past cycles for this client?
            </Typography>
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button
              onClick={() => setConfirmGenerateOpen(false)}
              disabled={generating}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmGenerate}
              variant="contained"
              disabled={generating}
              sx={{
                backgroundColor: "#db4a41",
                "&:hover": { backgroundColor: "#c62828" },
              }}
            >
              {generating ? "Generating..." : "Confirm"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Modal */}
        <Dialog
          open={openViewModal}
          onClose={handleCloseViewModal}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            Subscription Cycle Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {viewingCycle && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      mb: 2,
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: stringToColor(
                          viewingCycle.client_id?.client_name || "Unknown"
                        ),
                        width: 56,
                        height: 56,
                      }}
                    >
                      <MonetizationOnIcon />
                    </Avatar>
                    <Box>
                      <Typography
                        variant="h4"
                        sx={{ fontFamily: "Formula Bold", color: "white" }}
                      >
                        {viewingCycle.cycle_name ||
                          `${viewingCycle.client_id?.client_name} - ${viewingCycle.month}`}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        <Chip
                          label={viewingCycle.status}
                          sx={{
                            backgroundColor: getStatusColor(
                              viewingCycle.status
                            ),
                            color: "white",
                            textTransform: "capitalize",
                          }}
                        />
                        {viewingCycle.profit_status && (
                          <Chip
                            label={viewingCycle.profit_status}
                            sx={{
                              backgroundColor: getProfitStatusColor(
                                viewingCycle.profit_status
                              ),
                              color: "white",
                              textTransform: "capitalize",
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Financial Summary
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Due Amount:</strong>{" "}
                    {formatCurrency(viewingCycle.due_amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Paid Amount:</strong>{" "}
                    {formatCurrency(viewingCycle.paid_amount)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Penalties:</strong>{" "}
                    {formatCurrency(viewingCycle.penalties)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Actual Profit:</strong>{" "}
                    {formatCurrency(viewingCycle.actual_profit)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Goal Profit:</strong>{" "}
                    {formatCurrency(viewingCycle.goal_profit)}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 1, fontFamily: "Formula Bold" }}
                  >
                    Cash Flow
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Inflow:</strong>{" "}
                    {formatCurrency(viewingCycle.inflow)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Outflow:</strong>{" "}
                    {formatCurrency(viewingCycle.outflow)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Carryover Debt:</strong>{" "}
                    {formatCurrency(viewingCycle.carryover_debt)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Carryover Credit:</strong>{" "}
                    {formatCurrency(viewingCycle.carryover_credit)}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Extra Credit:</strong>{" "}
                    {formatCurrency(viewingCycle.extra_credit)}
                  </Typography>
                </Grid>

                {viewingCycle.quotations &&
                  viewingCycle.quotations.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#db4a41",
                          mb: 1,
                          fontFamily: "Formula Bold",
                        }}
                      >
                        Quotations ({viewingCycle.quotations.length})
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {viewingCycle.quotations.map((quotation, index) => (
                          <Chip
                            key={quotation._id || index}
                            label={`${quotation.status} - ${formatCurrency(
                              quotation.total_cost
                            )} - ${new Date(
                              quotation.dates.requested_at
                            ).toLocaleDateString()}`}
                            sx={{
                              backgroundColor:
                                quotation.status === "approved"
                                  ? "#4caf50"
                                  : "#ff9800",
                              color: "white",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}

                {viewingCycle.adjustments &&
                  viewingCycle.adjustments.length > 0 && (
                    <Grid item xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#db4a41",
                          mb: 1,
                          fontFamily: "Formula Bold",
                        }}
                      >
                        Adjustments ({viewingCycle.adjustments.length})
                      </Typography>
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                        {viewingCycle.adjustments.map((adjustment, index) => (
                          <Chip
                            key={adjustment._id || index}
                            label={`${adjustment.category} - ${formatCurrency(
                              adjustment.impact_amount
                            )}`}
                            sx={{
                              backgroundColor: adjustment.resolved
                                ? "#4caf50"
                                : "#f44336",
                              color: "white",
                            }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => handleCalculateProfit(viewingCycle._id)}
              variant="outlined"
              startIcon={<CalculateIcon />}
              sx={{
                color: "#4caf50",
                borderColor: "#4caf50",
                "&:hover": {
                  backgroundColor: "rgba(76, 175, 80, 0.1)",
                  borderColor: "#4caf50",
                },
              }}
            >
              Calculate Profit
            </Button>
            <Button
              onClick={handleCloseViewModal}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default SubscriptionCycle;
