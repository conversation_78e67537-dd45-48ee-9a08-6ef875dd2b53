import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
} from "@mui/material";
import PeopleIcon from "@mui/icons-material/People";
import VideocamIcon from "@mui/icons-material/Videocam";
import AssignmentIcon from "@mui/icons-material/Assignment";
import SearchIcon from "@mui/icons-material/Search";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "../../contexts/UserContext";
import axios from "axios";

const cardStyle = {
  background: "rgba(255, 255, 255, 0.05)",
  backdropFilter: "blur(10px)",
  borderRadius: "12px",
  border: "1px solid rgba(255, 255, 255, 0.1)",
  color: "white",
  height: "100%",
  transition: "transform 0.3s ease, box-shadow 0.3s ease",
  "&:hover": {
    transform: "translateY(-5px)",
    boxShadow: "0 8px 20px rgba(0, 0, 0, 0.2)",
  },
};

const EmployeeHistoryProfile = () => {
  const { user } = useUser();
  const [clients, setClients] = useState([]);
  const [createdTasks, setCreatedTasks] = useState([]);
  const [assignedTasks, setAssignedTasks] = useState([]);
  const [shoots, setShoots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [search, setSearch] = useState(""); // ✅ added

  // Fetch Clients
  const fetchClients = async (userId) => {
    try {
      const res = await axios.get(
        `https://youngproductions-768ada043db3.herokuapp.com/api/clientsmanagement/account-manager/${userId}`
      );
      setClients(res.data);
      console.log("Clients:", res.data);
    } catch (err) {
      console.error("Error fetching clients:", err);
      setClients([]);
    }
  };

  // Fetch Tasks Created By
  const fetchCreatedTasks = async (userId) => {
    try {
      const res = await axios.get(
        `https://youngproductions-768ada043db3.herokuapp.com/api/tasks/createdBy/${userId}`
      );
      setCreatedTasks(res.data);
      console.log("Created Tasks:", res.data);
    } catch (err) {
      console.error("Error fetching created tasks:", err);
      setCreatedTasks([]);
    }
  };

  // Fetch Tasks Assigned To
  const fetchAssignedTasks = async (userId) => {
    try {
      const res = await axios.get(
        `https://youngproductions-768ada043db3.herokuapp.com/api/tasks/assignedTo/${userId}`
      );
      setAssignedTasks(res.data);
      console.log("Assigned Tasks:", res.data);
    } catch (err) {
      console.error("Error fetching assigned tasks:", err);
      setAssignedTasks([]);
    }
  };

  // Fetch Shoots
  const fetchShoots = async (userId) => {
    try {
      const res = await axios.get(
        `https://youngproductions-768ada043db3.herokuapp.com/api/shoots/crew/${userId}`
      );
      setShoots(res.data);
      console.log("Shoots:", res.data);
    } catch (err) {
      console.error("Error fetching shoots:", err);
      setShoots([]);
    }
  };

  useEffect(() => {
    if (user?.id) {
      setLoading(true);

      fetchClients(user.id);
      fetchCreatedTasks(user.id);
      fetchAssignedTasks(user.id);
      fetchShoots(user.id);

      setLoading(false);
    }
  }, [user?.id]);
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  // ✅ Filtering logic
  const filterData = (data) => {
    const query = search.toLowerCase();
    if (!query) return data;

    switch (activeTab) {
      case 0: // Clients: search by name, type
        return data.filter(
          (c) =>
            c.name?.toLowerCase().includes(query) ||
            c.type?.toLowerCase().includes(query)
        );
      case 1: // Shoots: search by title, location
        return data.filter(
          (s) =>
            s.title?.toLowerCase().includes(query) ||
            s.location?.toLowerCase().includes(query)
        );
      case 2: // Created Tasks: title, project, client
      case 3: // Assigned Tasks: title, project, client
        return data.filter(
          (t) =>
            t.title?.toLowerCase().includes(query) ||
            t.projectId?.title?.toLowerCase().includes(query) ||
            t.clientId?.name?.toLowerCase().includes(query)
        );
      default:
        return data;
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return (
          <Grid container spacing={2}>
            {filterData(clients).map(
              (
                client // ✅ applied filterData
              ) => (
                <Grid item xs={12} sm={6} md={4} key={client._id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card sx={cardStyle}>
                      <CardContent>
                        <CardMedia
                          component="img"
                          height="100px"
                          width="90%"
                          image={client.logoImage.replace(
                            "https://a29dbeb11704750c5e1d4b4544ae5595.r2.cloudflarestorage.com/young/",
                            "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev/"
                          )}
                          alt={client.name}
                          sx={{
                            objectFit: "contain",

                            backgroundColor: "rgba(255, 255, 255, 0.05)",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        />
                        <Typography
                          variant="h4"
                          fontFamily="Formula Bold"
                          mt={2}
                        >
                          {client.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Email: {client.email}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Phone: {client.phone}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Type: {client.type}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Status: {client.status}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              )
            )}
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={2}>
            {filterData(shoots).map((shoot) => (
              <Grid item xs={12} sm={6} md={4} key={shoot._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card sx={cardStyle}>
                    <CardContent>
                      <Typography variant="h4" fontFamily="Formula Bold">
                        {shoot.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Project: {shoot.projectId?.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Date: {new Date(shoot.date).toLocaleDateString()}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Location: {shoot.location}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Status: {shoot.status}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={2}>
            {filterData(createdTasks).map((task) => (
              <Grid item xs={12} sm={6} md={4} key={task._id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card sx={cardStyle}>
                    <CardContent>
                      <Typography variant="h4" fontFamily="Formula Bold">
                        {task.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Client: {task.clientId?.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Project: {task.projectId?.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Due: {new Date(task.dueDate).toLocaleDateString()}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Priority: {task.priority}
                      </Typography>
                      <Typography
                        variant="body2"
                        fontFamily="Anton"
                        color="rgba(255,255,255,0.7)"
                      >
                        Status: {task.status}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        );
      case 3:
        return (
          <Grid container spacing={2}>
            {filterData(assignedTasks).map((task) => {
              return (
                <Grid item xs={12} sm={6} md={4} key={task._id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card sx={cardStyle}>
                      <CardContent>
                        <Typography variant="h4" fontFamily="Formula Bold">
                          {task.title}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="rgba(255,255,255,0.7)"
                        >
                          Client: {task.clientId?.name || "No Client"}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Project: {task.projectId?.title || "No Project"}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Due: {new Date(task.dueDate).toLocaleDateString()}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Priority: {task.priority}
                        </Typography>
                        <Typography
                          variant="body2"
                          fontFamily="Anton"
                          color="rgba(255,255,255,0.7)"
                        >
                          Status: {task.status}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              );
            })}
          </Grid>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        }}
      >
        <CircularProgress sx={{ color: "#db4a41" }} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(45deg, #000000 30%, #1a1a1a 90%)",
        color: "white",
        padding: { xs: "15px 10px", sm: "20px 15px", md: "25px 20px" },
      }}
    >
      <Typography
        variant="h3"
        sx={{
          fontFamily: "Formula Bold",
          color: "#db4a41",
          textAlign: "center",
          marginBottom: { xs: "20px", sm: "25px", md: "30px" },
          fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
          textShadow: "0 2px 4px rgba(0,0,0,0.3)",
        }}
      >
        Profile
      </Typography>
      <Grid container justifyContent="center" sx={{ mb: 4 }}>
        <Grid item xs={12} md={10} lg={8} sx={{ mb: 4 }}>
          <Card
            sx={{
              ...cardStyle,
              display: "flex",
              alignItems: "center",
              padding: 2,
              width: "100%",
              margin: "0 auto",
            }}
          >
            {/* Profile Picture (Left side) */}
            <CardMedia
              component="img"
              image={user?.profilePicture}
              alt="Employee Profile"
              sx={{
                width: 180,
                height: 180,
                borderRadius: "12px",
                objectFit: "cover",
                mr: 3,
              }}
            />

            {/* Info (Right side) */}
            <CardContent sx={{ flex: 1 }}>
              <Typography variant="h4" gutterBottom fontFamily="Formula Bold">
                {user?.name}
              </Typography>
              <Typography
                variant="body1"
                fontFamily="Anton"
                color="rgba(255,255,255,0.7)"
              >
                {user?.email}
              </Typography>
              <Typography
                variant="body1"
                fontFamily="Anton"
                color="rgba(255,255,255,0.7)"
              >
                Role: {user?.role}
              </Typography>
              <Typography
                variant="body1"
                fontFamily="Anton"
                color="rgba(255,255,255,0.7)"
              >
                Tier: {user?.tier}
              </Typography>
              <Typography
                variant="body1"
                fontFamily="Anton"
                color="rgba(255,255,255,0.7)"
              >
                Status: {user?.status}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        centered
        sx={{
          mb: 3,
          "& .MuiTabs-indicator": {
            backgroundColor: "#db4a41",
          },
          "& .MuiTab-root": {
            color: "rgba(255, 255, 255, 0.7)",
            "&.Mui-selected": {
              color: "#db4a41",
            },
          },
        }}
      >
        <Tab icon={<PeopleIcon />} label="Clients" />
        <Tab icon={<VideocamIcon />} label="Shoots" />
        <Tab icon={<AssignmentIcon />} label="Tasks Created" />
        <Tab icon={<AssignmentIcon />} label="Tasks Assigned" />
      </Tabs>
      {/* ✅ Search Bar */}
      <Box display="flex" justifyContent="flex-end" mb={3}>
        <TextField
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          placeholder="Search..."
          variant="outlined"
          size="small"
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <SearchIcon style={{ color: "white" }} />
              </InputAdornment>
            ),
          }}
          sx={{
            width: "20%",
            background: "rgba(255,255,255,0.1)",
            borderRadius: "8px",
            input: { color: "white" },
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "rgba(255,255,255,0.3)",
            },
          }}
        />
      </Box>
      <AnimatePresence mode="wait">{renderContent()}</AnimatePresence>
    </Box>
  );
};

export default EmployeeHistoryProfile;
