import React, { useEffect, useState, useRef } from "react";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";

function LoadingScreen({ onFinish }) {
  const images = [
    "/assets/events-thumbnails/abbys-kitchen.webp",
    "/assets/events-thumbnails/aly-mazhar.webp",
    "/assets/events-thumbnails/beauty-911.webp",
    "/assets/events-thumbnails/circlek-ramadan.webp",
    "/assets/events-thumbnails/circlek-summer.webp",
    "/assets/events-thumbnails/marriot-thumb-1.webp",
    "/assets/events-thumbnails/tutoruu-ad1.webp",
  ];

  const [currentImg, setCurrentImg] = useState(0);
  const [stage, setStage] = useState("shuffle");
  const shuffleTick = useRef(0);

  /* 🔥 PRELOAD IMAGES ONCE (NO RE-DECODE) */
  useEffect(() => {
    images.forEach((src) => {
      const img = new Image();
      img.src = src;
    });
  }, []);

  /* 🔥 FAST TIMELINE (TOTAL ~1.6s) */
  useEffect(() => {
    const t1 = setTimeout(() => setStage("merge"), 600);
    const t2 = setTimeout(() => setStage("spread"), 900);
    const t3 = setTimeout(() => setStage("exit"), 1300);
    const t4 = setTimeout(onFinish, 1600);

    return () => [t1, t2, t3, t4].forEach(clearTimeout);
  }, [onFinish]);

  /* 🔥 OPTIMIZED SHUFFLE (LESS RE-RENDERS) */
  useEffect(() => {
    if (stage !== "shuffle") return;

    const interval = setInterval(() => {
      shuffleTick.current += 1;

      // update image every 2 ticks instead of every tick
      if (shuffleTick.current % 2 === 0) {
        setCurrentImg((prev) => (prev + 1) % images.length);
      }
    }, 120);

    return () => clearInterval(interval);
  }, [stage]);

  return (
    <Box
      component={motion.div}
      animate={{ opacity: stage === "exit" ? 0 : 1 }}
      transition={{ duration: 0.4 }}
      sx={{
        height: "100vh",
        width: "100vw",
        bgcolor: "white",
        position: "fixed",
        inset: 0,
        zIndex: 2000,
        overflow: "hidden",
      }}
    >
      <Box
        sx={{
          height: "100%",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          px: 5,
        }}
      >
        <Typography
          component={motion.div}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          sx={{
            fontFamily: "formula bold",
            fontWeight: 700,
            fontSize: "1.4rem",
          }}
        >
          Young House
        </Typography>

        <Typography
          component={motion.div}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          sx={{
            fontFamily: "formula bold",
            fontWeight: 700,
            fontSize: "1.4rem",
          }}
        >
          Creative Hub
        </Typography>

        {/* CENTER CONTENT (UNCHANGED DESIGN) */}
        <Box
          component={motion.div}
          animate={
            stage === "merge"
              ? { width: 120, height: 1, borderRadius: 0 }
              : stage === "spread" || stage === "exit"
              ? { width: "100vw", height: "100vh", borderRadius: 0 }
              : { width: 150, height: 150, borderRadius: 12 }
          }
          transition={{ duration: 0.6, ease: "easeInOut" }}
          sx={{
            position: "absolute",
            left: "50%",
            top: "50%",
            transform: "translate(-50%, -50%)",
            display: "flex",
            flexDirection:
              stage === "spread" || stage === "exit" ? "column" : "unset",
            justifyContent: "center",
            alignItems: "center",
            gap: stage === "spread" || stage === "exit" ? 1.5 : 0,
            overflow: "hidden",
            bgcolor: stage === "merge" ? "black" : "transparent",
          }}
        >
          {(stage === "spread" || stage === "exit") &&
            images.map((img, index) => (
              <motion.img
                key={index}
                src={img}
                loading="lazy"
                decoding="async"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: stage === "exit" ? 1.1 : 1 }}
                transition={{
                  duration: 0.4,
                  delay: index * 0.05,
                }}
                style={{
                  width: 150,
                  height: 100,
                  objectFit: "cover",
                  borderRadius: 12,
                  marginBottom: 25,
                }}
              />
            ))}

          {stage === "shuffle" && (
            <motion.img
              src={images[currentImg]}
              loading="eager"
              decoding="async"
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.12 }}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          )}

          {stage === "merge" && (
            <motion.img
              src={images[currentImg]}
              loading="eager"
              decoding="async"
              animate={{ opacity: 1 }}
              transition={{ duration: 0.25 }}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
}

export default LoadingScreen;
