import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Paper,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Pagination,
  FormControlLabel,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import DownloadIcon from "@mui/icons-material/Download";
import axios from "axios";

const JobApplications = () => {
  // State Management
  const [state, setState] = useState({
    // Data
    applications: [],
    currentApplication: null,
    loading: false,
    error: null,

    // Filters
    searchTerm: "",
    selectedDepartment: "",
    selectedJobType: "",
    selectedWorkMode: "",
    selectedExperienceLevel: "",
    showRecentOnly: false,

    // Pagination
    currentPage: 1,
    pageSize: 10,
    totalApplications: 0,

    // Modals
    showViewModal: false,

    // Form
    formErrors: {},
  });

  // Filter options
  const jobTypeOptions = [
    { value: "full-time", label: "Full Time" },
    { value: "part-time", label: "Part Time" },
    { value: "contract", label: "Contract" },
    { value: "internship", label: "Internship" },
    { value: "freelance", label: "Freelance" },
  ];

  const workModeOptions = [
    { value: "remote", label: "Remote" },
    { value: "on-site", label: "On-site" },
    { value: "hybrid", label: "Hybrid" },
  ];

  const experienceLevelOptions = [
    { value: "entry-level", label: "Entry Level" },
    { value: "mid-level", label: "Mid Level" },
    { value: "senior-level", label: "Senior Level" },
    { value: "executive", label: "Executive" },
  ];

  // API Functions
  const fetchApplications = async (filters = {}) => {
    try {
      setState((prev) => ({ ...prev, loading: true }));
      const response = await axios.get(
        "https://youngproductions-768ada043db3.herokuapp.com/api/jobApplications",
        {
          params: filters,
        }
      );
      setState((prev) => ({
        ...prev,
        applications: response.data,
        totalApplications: response.data.length,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching applications:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to fetch applications",
        loading: false,
      }));
    }
  };

  // UI Functions
  const handleSearch = (searchTerm) => {
    setState((prev) => ({ ...prev, searchTerm }));
  };

  const handleFilterChange = (filterType, value) => {
    setState((prev) => ({ ...prev, [filterType]: value }));
  };

  const clearFilters = () => {
    setState((prev) => ({
      ...prev,
      searchTerm: "",
      selectedDepartment: "",
      selectedJobType: "",
      selectedWorkMode: "",
      selectedExperienceLevel: "",
      showRecentOnly: false,
    }));
    fetchApplications();
  };

  // Modal Functions
  const openViewModal = (application) => {
    setState((prev) => ({
      ...prev,
      currentApplication: application,
      showViewModal: true,
    }));
  };

  const closeAllModals = () => {
    setState((prev) => ({
      ...prev,
      showViewModal: false,
      currentApplication: null,
    }));
  };

  // Download resume function
  const downloadResume = (resumePath, applicantName) => {
    const link = document.createElement("a");
    link.href = `${resumePath}`;
    link.download = `${applicantName}_resume.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Effects
  useEffect(() => {
    fetchApplications();
  }, []);

  // Get filtered applications
  const getFilteredApplications = () => {
    let filtered = [...state.applications];

    if (state.searchTerm) {
      filtered = filtered.filter(
        (app) =>
          app.fullName
            ?.toLowerCase()
            .includes(state.searchTerm.toLowerCase()) ||
          app.email?.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
          app.JobId?.title
            ?.toLowerCase()
            .includes(state.searchTerm.toLowerCase()) ||
          app.JobId?.department
            ?.toLowerCase()
            .includes(state.searchTerm.toLowerCase())
      );
    }

    if (state.selectedDepartment) {
      filtered = filtered.filter(
        (app) => app.JobId?.department === state.selectedDepartment
      );
    }

    if (state.selectedJobType) {
      filtered = filtered.filter(
        (app) => app.JobId?.jobType === state.selectedJobType
      );
    }

    if (state.selectedWorkMode) {
      filtered = filtered.filter(
        (app) => app.JobId?.workMode === state.selectedWorkMode
      );
    }

    if (state.selectedExperienceLevel) {
      filtered = filtered.filter(
        (app) => app.JobId?.experienceLevel === state.selectedExperienceLevel
      );
    }

    if (state.showRecentOnly) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      filtered = filtered.filter(
        (app) => new Date(app.createdAt) > thirtyDaysAgo
      );
    }

    return filtered;
  };

  const filteredApplications = getFilteredApplications();
  const paginatedApplications = filteredApplications.slice(
    (state.currentPage - 1) * state.pageSize,
    state.currentPage * state.pageSize
  );

  // Get unique departments for filter
  const departments = [
    ...new Set(
      state.applications.map((app) => app.JobId?.department).filter(Boolean)
    ),
  ];

  // Statistics
  const totalApplications = state.applications.length;
  const recentApplications = state.applications.filter((app) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return new Date(app.createdAt) > thirtyDaysAgo;
  }).length;
  const thisMonthApplications = state.applications.filter((app) => {
    const now = new Date();
    const appDate = new Date(app.createdAt);
    return (
      appDate.getMonth() === now.getMonth() &&
      appDate.getFullYear() === now.getFullYear()
    );
  }).length;

  return (
    <div
      style={{
        backgroundColor: "black",
        padding: "20px 5vw",
        minHeight: "100vh",
        color: "white",
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography
          variant="h4"
          sx={{
            fontFamily: "Formula Bold",
            color: "white",
          }}
        >
          Job Applications
        </Typography>
      </Box>

      {/* Search and Filters */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              placeholder="Search applications..."
              value={state.searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: "white", mr: 1 }} />,
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Department"
              value={state.selectedDepartment}
              onChange={(e) =>
                handleFilterChange("selectedDepartment", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {departments.map((dept) => (
                <MenuItem key={dept} value={dept}>
                  {dept}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Job Type"
              value={state.selectedJobType}
              onChange={(e) =>
                handleFilterChange("selectedJobType", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {jobTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Work Mode"
              value={state.selectedWorkMode}
              onChange={(e) =>
                handleFilterChange("selectedWorkMode", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {workModeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2}>
            <TextField
              select
              fullWidth
              label="Experience"
              value={state.selectedExperienceLevel}
              onChange={(e) =>
                handleFilterChange("selectedExperienceLevel", e.target.value)
              }
              sx={{
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            >
              <MenuItem value="">All</MenuItem>
              {experienceLevelOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={1}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              startIcon={<ClearIcon />}
              sx={{
                color: "white",
                borderColor: "white",
                "&:hover": {
                  borderColor: "#db4a41",
                  color: "#db4a41",
                },
              }}
            >
              Clear
            </Button>
          </Grid>
        </Grid>
        <Box sx={{ mt: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={state.showRecentOnly}
                onChange={(e) =>
                  handleFilterChange("showRecentOnly", e.target.checked)
                }
                sx={{
                  "& .MuiSwitch-switchBase.Mui-checked": {
                    color: "#db4a41",
                  },
                  "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#db4a41",
                  },
                }}
              />
            }
            label="Show Recent Applications Only (Last 30 Days)"
            sx={{ color: "white" }}
          />
        </Box>
      </Paper>

      {/* Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                sx={{ color: "#db4a41", fontFamily: "Formula Bold" }}
              >
                Total Applications
              </Typography>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Formula Bold" }}
              >
                {totalApplications}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
              >
                Recent Applications
              </Typography>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Formula Bold" }}
              >
                {recentApplications}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: "rgba(255, 255, 255, 0.05)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
            }}
          >
            <CardContent>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <Typography
                  variant="h6"
                  sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                >
                  This Month
                </Typography>
                <Typography
                  variant="body2"
                  style={{
                    color: "#999",
                    fontFamily: "Formula Bold",
                    letterSpacing: "1px",
                  }}
                >
                  {new Date().toLocaleString("default", { month: "long" })}
                </Typography>
              </Box>
              <Typography
                variant="h4"
                sx={{ color: "white", fontFamily: "Formula Bold" }}
              >
                {thisMonthApplications}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Applications Table */}
      <TableContainer
        component={Paper}
        sx={{
          backgroundColor: "rgba(255, 255, 255, 0.05)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          mb: 3,
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Applicant
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Job Title
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Department
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Job Type
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Work Mode
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Experience
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Applied Date
              </TableCell>
              <TableCell sx={{ color: "white", fontWeight: "bold" }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.loading ? (
              <TableRow>
                <TableCell colSpan={8} sx={{ textAlign: "center", py: 4 }}>
                  <CircularProgress sx={{ color: "#db4a41" }} />
                </TableCell>
              </TableRow>
            ) : paginatedApplications.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={8}
                  sx={{ textAlign: "center", py: 4, color: "white" }}
                >
                  No applications found
                </TableCell>
              </TableRow>
            ) : (
              paginatedApplications.map((application) => (
                <TableRow
                  key={application._id}
                  sx={{
                    "&:hover": { backgroundColor: "rgba(255, 255, 255, 0.05)" },
                  }}
                >
                  <TableCell sx={{ color: "white" }}>
                    <Box>
                      <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                        {application.fullName}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "#999" }}>
                        {application.email}
                      </Typography>
                      <Typography variant="body2" sx={{ color: "#999" }}>
                        {application.phone}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {application.JobId?.title || "N/A"}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {application.JobId?.department || "N/A"}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    <Chip
                      label={
                        jobTypeOptions.find(
                          (opt) => opt.value === application.JobId?.jobType
                        )?.label ||
                        application.JobId?.jobType ||
                        "N/A"
                      }
                      size="small"
                      sx={{
                        backgroundColor: "rgba(219, 74, 65, 0.2)",
                        color: "#db4a41",
                        border: "1px solid #db4a41",
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {application.JobId?.workMode || "N/A"}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {application.JobId?.experienceLevel || "N/A"}
                  </TableCell>
                  <TableCell sx={{ color: "white" }}>
                    {new Date(application.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Tooltip title="View Details">
                        <IconButton
                          onClick={() => openViewModal(application)}
                          sx={{
                            color: "white",
                            "&:hover": { color: "#db4a41" },
                          }}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download Resume">
                        <IconButton
                          onClick={() =>
                            downloadResume(
                              application.resumePath,
                              application.fullName
                            )
                          }
                          sx={{
                            color: "white",
                            "&:hover": { color: "#4caf50" },
                          }}
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: "flex", justifyContent: "center", mb: 3 }}>
        <Pagination
          count={Math.ceil(filteredApplications.length / state.pageSize)}
          page={state.currentPage}
          onChange={(e, page) =>
            setState((prev) => ({ ...prev, currentPage: page }))
          }
          sx={{
            "& .MuiPaginationItem-root": {
              color: "white",
              "&:hover": {
                backgroundColor: "rgba(219, 74, 65, 0.2)",
              },
            },
            "& .Mui-selected": {
              backgroundColor: "#db4a41 !important",
              color: "white",
            },
          }}
        />
      </Box>

      {/* View Application Modal */}
      <Dialog
        open={state.showViewModal}
        onClose={closeAllModals}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle sx={{ color: "white", fontFamily: "Formula Bold" }}>
          Application Details: {state.currentApplication?.fullName}
        </DialogTitle>
        <DialogContent sx={{ overflow: "auto" }}>
          {state.currentApplication && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {/* Applicant Information */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ color: "#db4a41", mb: 2 }}>
                  Applicant Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Full Name
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.fullName}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Email
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.email}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Phone
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.phone}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Applied Date
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {new Date(
                    state.currentApplication.createdAt
                  ).toLocaleString()}
                </Typography>
              </Grid>

              {/* Job Information */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                >
                  Job Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Job Title
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.JobId?.title || "N/A"}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Department
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.JobId?.department || "N/A"}
                </Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Job Type
                </Typography>
                <Chip
                  label={
                    jobTypeOptions.find(
                      (opt) =>
                        opt.value === state.currentApplication.JobId?.jobType
                    )?.label ||
                    state.currentApplication.JobId?.jobType ||
                    "N/A"
                  }
                  sx={{
                    backgroundColor: "rgba(219, 74, 65, 0.2)",
                    color: "#db4a41",
                    border: "1px solid #db4a41",
                  }}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Work Mode
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.JobId?.workMode || "N/A"}
                </Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ color: "#999", mb: 1 }}>
                  Experience Level
                </Typography>
                <Typography sx={{ color: "white" }}>
                  {state.currentApplication.JobId?.experienceLevel || "N/A"}
                </Typography>
              </Grid>

              {/* Job Description */}
              {state.currentApplication.JobId?.description && (
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                  >
                    Job Description
                  </Typography>
                  <Typography sx={{ color: "white", whiteSpace: "pre-wrap" }}>
                    {state.currentApplication.JobId.description}
                  </Typography>
                </Grid>
              )}

              {/* Resume */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  sx={{ color: "#db4a41", mb: 2, mt: 2 }}
                >
                  Resume
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography sx={{ color: "white" }}>
                    {state.currentApplication.resumePath}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<DownloadIcon />}
                    onClick={() =>
                      downloadResume(
                        state.currentApplication.resumePath,
                        state.currentApplication.fullName
                      )
                    }
                    sx={{
                      backgroundColor: "#4caf50",
                      color: "white",
                      "&:hover": { backgroundColor: "#45a049" },
                    }}
                  >
                    Download Resume
                  </Button>
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={closeAllModals} sx={{ color: "white" }}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default JobApplications;
