import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Paper,
  Box,
  Chip,
} from "@mui/material";
import axios from "axios";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";

const API_URL =
  "https://youngproductions-768ada043db3.herokuapp.com/api/cms/young-people";

const YoungPeopleAdmin = () => {
  const [youngPeople, setYoungPeople] = useState([]);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [form, setForm] = useState({
    name: "",
    title: "",
    shortDescription: "",
    bio: "",
    image: null,
    hobbies: [],
  });
  const [preview, setPreview] = useState(null);
  const [editingId, setEditingId] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [hobbyInput, setHobbyInput] = useState("");

  useEffect(() => {
    fetchYoungPeople();
  }, []);

  const fetchYoungPeople = async () => {
    try {
      const res = await axios.get(API_URL);
      // Extract data from the response structure
      setYoungPeople(res.data.data || res.data);
    } catch (err) {
      console.error("Error fetching young people:", err);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setForm({ ...form, image: file });
    setPreview(file ? URL.createObjectURL(file) : null);
  };

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleAddHobby = () => {
    if (hobbyInput.trim() && !form.hobbies.includes(hobbyInput.trim())) {
      setForm({ ...form, hobbies: [...form.hobbies, hobbyInput.trim()] });
      setHobbyInput("");
    }
  };

  const handleRemoveHobby = (hobbyToRemove) => {
    setForm({
      ...form,
      hobbies: form.hobbies.filter((hobby) => hobby !== hobbyToRemove),
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const fd = new FormData();

      // Add all form fields except hobbies
      Object.keys(form).forEach((key) => {
        if (key !== "hobbies" && form[key] !== null) {
          fd.append(key, form[key]);
        }
      });

      // Add hobbies as JSON string
      fd.append("hobbies", JSON.stringify(form.hobbies));

      if (editingId) {
        await axios.put(`${API_URL}/${editingId}`, fd, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } else {
        await axios.post(API_URL, fd, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      }

      resetForm();
      fetchYoungPeople();
      setShowModal(false);
    } catch (err) {
      console.error("Error saving young person:", err);
    }
  };

  const handleEdit = (person) => {
    setForm({
      name: person.name,
      title: person.title,
      shortDescription: person.shortDescription || "",
      bio: person.bio || "",
      image: null,
      hobbies: person.hobbies || [],
    });
    setEditingId(person._id);
    setPreview(person.image || null);
    setShowModal(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure?")) return;
    try {
      await axios.delete(`${API_URL}/${id}`);
      fetchYoungPeople();
    } catch (err) {
      console.error("Error deleting young person:", err);
    }
  };

  const resetForm = () => {
    setForm({
      name: "",
      title: "",
      shortDescription: "",
      bio: "",
      image: null,
      hobbies: [],
    });
    setPreview(null);
    setEditingId(null);
    setHobbyInput("");
  };

  // Layout styles
  const applyResponsiveColumns = () => {
    const width = window.innerWidth;
    if (width >= 1024) return "repeat(4, 1fr)";
    if (width >= 768) return "repeat(2, 1fr)";
    return "repeat(1, 1fr)";
  };
  const [columns, setColumns] = useState(applyResponsiveColumns());
  useEffect(() => {
    const handleResize = () => setColumns(applyResponsiveColumns());
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const containerStyle = {
    display: "grid",
    gap: "40px",
    padding: "40px",
    backgroundColor: "#000",
    gridTemplateColumns: columns,
  };

  const cardStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "10px",
    position: "relative",
  };

  const imageWrapperStyle = (isCircle) => ({
    width: "100%",
    aspectRatio: "1 / 1",
    overflow: "hidden",
    borderRadius: isCircle ? "50%" : "12px",
    position: "relative",
  });

  const imageStyle = (hovered, isCircle) => ({
    width: "100%",
    height: "100%",
    objectFit: "cover",
    borderRadius: hovered || isCircle ? "50%" : "0",
    transition: "border-radius 0.4s ease-in-out, transform 0.3s ease-in-out",
  });

  const nameStyle = {
    marginTop: "16px",
    fontWeight: "bold",
    fontSize: "2rem",
    fontFamily: "Formula Bold",
    letterSpacing: "0.09em",
    color: "#fff",
  };

  const titleStyle = {
    fontSize: "1.2rem",
    color: "#db4a41",
    marginTop: "-1rem",
    fontFamily: "Formula Bold",
  };

  const bioStyle = {
    fontSize: "14px",
    color: "#fff",
    marginTop: "14px",
    fontFamily: "Anton",
  };

  return (
    <div>
      <div
        style={{
          padding: "20px",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "baseline",
        }}
      >
        <Typography
          variant="h3"
          sx={{
            fontFamily: "Formula Bold",
            color: "#db4a41",
            textAlign: "center",
            marginBottom: { xs: "20px", sm: "25px", md: "30px" },
            fontSize: { xs: "1.75rem", sm: "2rem", md: "2.25rem" },
            textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          Young People Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            resetForm();
            setShowModal(true);
          }}
          sx={{
            background: "#db4a41",
            "&:hover": {
              background: "#c43a31",
            },
          }}
        >
          Add Young Person
        </Button>
      </div>

      {/* Modal */}
      <Modal
        open={showModal}
        onClose={() => setShowModal(false)}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper
          sx={{
            position: "relative",
            width: 500,
            bgcolor: "rgba(0, 0, 0, 0.9)",
            backdropFilter: "blur(10px)",
            p: 4,
            borderRadius: 2,
            border: "1px solid rgba(255, 255, 255, 0.1)",
            maxHeight: "90vh",
            overflowY: "auto",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Formula Bold",
              color: "white",
              mb: 3,
              textAlign: "center",
            }}
          >
            {editingId ? "Edit Young Person" : "Add New Young Person"}
          </Typography>

          {/* Image Preview */}
          {preview && (
            <Box
              sx={{
                width: 120,
                height: 120,
                borderRadius: "50%",
                overflow: "hidden",
                margin: "0 auto 20px auto",
                border: "3px solid #db4a41",
              }}
            >
              <img
                src={preview}
                alt="Preview"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
              />
            </Box>
          )}

          <form onSubmit={handleSubmit}>
            {/* File Upload */}
            <Box sx={{ mb: 2 }}>
              <input
                type="file"
                name="image"
                onChange={handleFileChange}
                accept="image/*"
                style={{
                  width: "100%",
                  padding: "10px",
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                  border: "1px solid rgba(255, 255, 255, 0.23)",
                  borderRadius: "4px",
                  color: "white",
                }}
              />
            </Box>

            <TextField
              fullWidth
              label="Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Title"
              name="title"
              value={form.title}
              onChange={handleChange}
              required
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Short Description"
              name="shortDescription"
              value={form.shortDescription}
              onChange={handleChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            <TextField
              fullWidth
              label="Bio"
              name="bio"
              multiline
              rows={3}
              value={form.bio}
              onChange={handleChange}
              sx={{
                mb: 2,
                "& .MuiInputLabel-root": { color: "white" },
                "& .MuiOutlinedInput-root": {
                  color: "white",
                  "& fieldset": { borderColor: "rgba(255, 255, 255, 0.23)" },
                  "&:hover fieldset": { borderColor: "white" },
                },
              }}
            />

            {/* Hobbies Section */}
            <Box sx={{ mb: 2 }}>
              <Typography sx={{ color: "white", mb: 1 }}>Hobbies</Typography>
              <Box sx={{ display: "flex", gap: 1, mb: 1 }}>
                <TextField
                  fullWidth
                  label="Add Hobby"
                  value={hobbyInput}
                  onChange={(e) => setHobbyInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleAddHobby();
                    }
                  }}
                  sx={{
                    "& .MuiInputLabel-root": { color: "white" },
                    "& .MuiOutlinedInput-root": {
                      color: "white",
                      "& fieldset": {
                        borderColor: "rgba(255, 255, 255, 0.23)",
                      },
                      "&:hover fieldset": { borderColor: "white" },
                    },
                  }}
                />
                <Button
                  onClick={handleAddHobby}
                  variant="outlined"
                  sx={{
                    color: "white",
                    borderColor: "white",
                    "&:hover": {
                      borderColor: "#db4a41",
                      color: "#db4a41",
                    },
                  }}
                >
                  Add
                </Button>
              </Box>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {form.hobbies.map((hobby, index) => (
                  <Chip
                    key={index}
                    label={hobby}
                    onDelete={() => handleRemoveHobby(hobby)}
                    deleteIcon={<DeleteIcon />}
                    sx={{
                      backgroundColor: "#db4a41",
                      color: "white",
                      "& .MuiChip-deleteIcon": {
                        color: "white",
                        "&:hover": {
                          color: "#ffcccb",
                        },
                      },
                    }}
                  />
                ))}
              </Box>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
              <Button
                onClick={() => setShowModal(false)}
                sx={{
                  color: "white",
                  borderColor: "white",
                  "&:hover": {
                    borderColor: "#db4a41",
                    color: "#db4a41",
                  },
                }}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                sx={{
                  backgroundColor: "#db4a41",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#c62828",
                  },
                }}
                variant="contained"
              >
                {editingId ? "Update Person" : "Add Person"}
              </Button>
            </Box>
          </form>
        </Paper>
      </Modal>

      {/* Grid of Young People */}
      <div style={containerStyle}>
        {youngPeople.map((person, index) => (
          <div key={person._id} style={cardStyle}>
            <div
              style={imageWrapperStyle(true)}
              onMouseEnter={() => setHoveredIndex(index)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <img
                src={person?.image?.replace(
                  "https://youngproductionss.com",
                  "https://pub-8dd67a8768c44c6db115723df6f7f228.r2.dev"
                )}
                alt={person.name}
                style={imageStyle(hoveredIndex === index, true)}
              />
            </div>
            <h3 style={nameStyle}>{person.name}</h3>
            <p style={titleStyle}>{person.title}</p>
            <p style={bioStyle}>{person.shortDescription}</p>

            {/* Hobbies Display */}
            {person.hobbies && person.hobbies.length > 0 && (
              <Box sx={{ mt: 1, display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {person.hobbies.slice(0, 3).map((hobby, hobbyIndex) => (
                  <Chip
                    key={hobbyIndex}
                    label={hobby}
                    size="small"
                    sx={{
                      backgroundColor: "rgba(219, 74, 65, 0.2)",
                      color: "#db4a41",
                      fontSize: "0.7rem",
                    }}
                  />
                ))}
                {person.hobbies.length > 3 && (
                  <Chip
                    label={`+${person.hobbies.length - 3} more`}
                    size="small"
                    sx={{
                      backgroundColor: "rgba(255, 255, 255, 0.1)",
                      color: "white",
                      fontSize: "0.7rem",
                    }}
                  />
                )}
              </Box>
            )}

            <div
              style={{
                bottom: "5px",
                left: "5px",
                display: "flex",
                gap: "5px",
                marginTop: "10px",
              }}
            >
              <button
                className="btn btn-primary link-btn"
                onClick={() => handleEdit(person)}
              >
                Edit
              </button>
              <button
                className="btn btn-primary link-btn"
                onClick={() => handleDelete(person._id)}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default YoungPeopleAdmin;
