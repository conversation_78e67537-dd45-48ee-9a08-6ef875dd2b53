import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Snackbar,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import RefreshIcon from "@mui/icons-material/Refresh";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import MoneyOffIcon from "@mui/icons-material/MoneyOff";
import RequestQuoteIcon from "@mui/icons-material/RequestQuote";
import ReceiptIcon from "@mui/icons-material/Receipt";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { motion } from "framer-motion";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
} from "recharts";
function ClientDashboardAnalysis() {
  const { clientId } = useParams();
  const API_BASE_URL = `https://youngproductions-768ada043db3.herokuapp.com/api/financial/get-client-dashboard/clients/${clientId}/financials`;

  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const [groupBy, setGroupBy] = useState("month");
  const [from, setFrom] = useState("");
  const [to, setTo] = useState("");
  const [selectedYear, setSelectedYear] = useState("");
  const [selectedQuarter, setSelectedQuarter] = useState("");
  const [selectedMonth, setSelectedMonth] = useState("");
  const [includeDetails, setIncludeDetails] = useState(true);
  const [rawData, setRawData] = useState(null);

  const [activeTab, setActiveTab] = useState("payments");

  const [openQuotation, setOpenQuotation] = useState(null);
  const [openExpense, setOpenExpense] = useState(null);
  const [openAdjustment, setOpenAdjustment] = useState(null);
  const [openProject, setOpenProject] = useState(null);

  const showSnackbar = (message, severity) =>
    setSnackbar({ open: true, message, severity });
  const handleCloseSnackbar = () => setSnackbar((s) => ({ ...s, open: false }));

  const formatCurrency = (amount) => {
    if (amount && amount.$numberDecimal)
      return `${parseFloat(amount.$numberDecimal).toLocaleString()} EGP`;
    if (typeof amount === "number") return `${amount.toLocaleString()} EGP`;
    const num = parseFloat(amount || 0);
    return `${(isNaN(num) ? 0 : num).toLocaleString()} EGP`;
  };

  const formatCurrencyForCSV = (amount) => {
    if (amount && amount.$numberDecimal)
      return parseFloat(amount.$numberDecimal);
    if (typeof amount === "number") return amount;
    const num = parseFloat(amount || 0);
    return isNaN(num) ? 0 : num;
  };

  const exportToCSV = () => {
    if (!data) {
      showSnackbar("No data available to export", "error");
      return;
    }

    const csvData = [];

    // Add report header
    csvData.push(["**CLIENT FINANCIAL DASHBOARD REPORT**"]);
    csvData.push([""]);
    csvData.push(["*Report Information*"]);
    csvData.push(["Client Name", data.client?.name || "Unknown"]);
    csvData.push(["Report Generated", new Date().toLocaleString()]);
    csvData.push([
      "Active Tab Filter",
      activeTab.charAt(0).toUpperCase() + activeTab.slice(1),
    ]);
    csvData.push([
      "Report Period",
      `${selectedYear || "All Years"} ${selectedQuarter || ""} ${
        selectedMonth || ""
      }`,
    ]);
    csvData.push([""]);

    // Add financial summary
    if (data.summary) {
      csvData.push(["**FINANCIAL SUMMARY**"]);
      csvData.push([""]);
      csvData.push(["*Metric*", "*Amount (EGP)*", "*Percentage*"]);
      csvData.push([
        "Total Inflow",
        formatCurrencyForCSV(data.summary.total_inflow),
        "100%",
      ]);
      csvData.push([
        "Total Outflow",
        formatCurrencyForCSV(data.summary.total_outflow),
        data.summary.total_inflow > 0
          ? `${(
              (parseFloat(data.summary.total_outflow || 0) /
                parseFloat(data.summary.total_inflow || 1)) *
              100
            ).toFixed(1)}%`
          : "0%",
      ]);
      csvData.push([
        "Total Profit",
        formatCurrencyForCSV(
          data.summary.total_profit || data.summary.net_profit
        ),
        data.summary.total_inflow > 0
          ? `${(
              (parseFloat(
                data.summary.total_profit || data.summary.net_profit || 0
              ) /
                parseFloat(data.summary.total_inflow || 1)) *
              100
            ).toFixed(1)}%`
          : "0%",
      ]);
      csvData.push([
        "Total Penalties",
        formatCurrencyForCSV(data.summary.total_penalties || 0),
        data.summary.total_inflow > 0
          ? `${(
              (parseFloat(data.summary.total_penalties || 0) /
                parseFloat(data.summary.total_inflow || 1)) *
              100
            ).toFixed(1)}%`
          : "0%",
      ]);
      csvData.push([""]);
    }

    // Add monthly breakdown analysis
    if (data.details?.breakdown && data.details.breakdown.length > 0) {
      csvData.push(["**MONTHLY BREAKDOWN ANALYSIS**"]);
      csvData.push([""]);
      csvData.push([
        "*Month*",
        "*Type*",
        "*Project Name*",
        "*Inflow (EGP)*",
        "*Outflow (EGP)*",
        "*Profit (EGP)*",
        "*Penalties (EGP)*",
      ]);

      data.details.breakdown.forEach((item) => {
        csvData.push([
          item.month || "Unknown",
          item.type?.toUpperCase() || "Unknown",
          item.project_name ||
            (item.type === "subscription" ? "Monthly Subscription" : "N/A"),
          formatCurrencyForCSV(item.inflow),
          formatCurrencyForCSV(item.outflow),
          formatCurrencyForCSV(item.profit),
          formatCurrencyForCSV(item.penalties || 0),
        ]);
      });
      csvData.push([""]);
    }

    // Add tabs data analysis
    if (data.tabs) {
      csvData.push(["**MONTHLY TABS ANALYSIS**"]);
      csvData.push([""]);

      // Inflow data
      if (data.tabs.inflow && data.tabs.inflow.length > 0) {
        csvData.push(["*Monthly Inflow Analysis*"]);
        csvData.push(["*Month*", "*Inflow Amount (EGP)*"]);
        data.tabs.inflow.forEach((item) => {
          csvData.push([
            item.month || "Unknown",
            formatCurrencyForCSV(item.amount),
          ]);
        });
        csvData.push([""]);
      }

      // Outflow data
      if (data.tabs.outflow && data.tabs.outflow.length > 0) {
        csvData.push(["*Monthly Outflow Analysis*"]);
        csvData.push(["*Month*", "*Outflow Amount (EGP)*"]);
        data.tabs.outflow.forEach((item) => {
          csvData.push([
            item.month || "Unknown",
            formatCurrencyForCSV(item.amount),
          ]);
        });
        csvData.push([""]);
      }

      // Payments data
      if (data.tabs.payments && data.tabs.payments.length > 0) {
        csvData.push(["*Payments Analysis*"]);
        csvData.push(["*Amount (EGP)*", "*Method*", "*Date*"]);
        data.tabs.payments.forEach((payment) => {
          csvData.push([
            formatCurrencyForCSV(payment.amount),
            payment.method?.toUpperCase() || "Unknown",
            payment.date ? new Date(payment.date).toLocaleDateString() : "N/A",
          ]);
        });
        csvData.push([""]);
      }

      // Penalties data
      if (data.tabs.penalties && data.tabs.penalties.length > 0) {
        csvData.push(["*Penalties Analysis*"]);
        csvData.push(["*Month*", "*Penalty Amount (EGP)*", "*Reason*"]);
        data.tabs.penalties.forEach((penalty) => {
          csvData.push([
            penalty.month || "Unknown",
            formatCurrencyForCSV(penalty.amount),
            penalty.reason || "N/A",
          ]);
        });
        csvData.push([""]);
      }
    }

    // Add subscription cycles analysis
    if (data.details?.cycles && data.details.cycles.length > 0) {
      csvData.push(["**SUBSCRIPTION CYCLES ANALYSIS**"]);
      csvData.push([""]);
      csvData.push([
        "*Cycle ID*",
        "*Cycle Name*",
        "*Month*",
        "*Status*",
        "*Due Amount (EGP)*",
        "*Paid Amount (EGP)*",
        "*Inflow (EGP)*",
        "*Outflow (EGP)*",
        "*Goal Profit (EGP)*",
        "*Actual Profit (EGP)*",
        "*Profit Status*",
        "*Penalties (EGP)*",
        "*Carryover Debt (EGP)*",
        "*Carryover Credit (EGP)*",
        "*Extra Credit (EGP)*",
        "*Created Date*",
        "*Updated Date*",
      ]);

      data.details.cycles.forEach((cycle) => {
        csvData.push([
          cycle._id?.slice(-8) || "Unknown",
          cycle.cycle_name || "Unknown",
          cycle.month || "Unknown",
          cycle.status?.toUpperCase() || "Unknown",
          formatCurrencyForCSV(cycle.due_amount),
          formatCurrencyForCSV(cycle.paid_amount),
          formatCurrencyForCSV(cycle.inflow),
          formatCurrencyForCSV(cycle.outflow),
          formatCurrencyForCSV(cycle.goal_profit),
          formatCurrencyForCSV(cycle.actual_profit),
          cycle.profit_status?.toUpperCase().replace("_", " ") || "Unknown",
          formatCurrencyForCSV(cycle.penalties || 0),
          formatCurrencyForCSV(cycle.carryover_debt || 0),
          formatCurrencyForCSV(cycle.carryover_credit || 0),
          formatCurrencyForCSV(cycle.extra_credit || 0),
          cycle.createdAt
            ? new Date(cycle.createdAt).toLocaleDateString()
            : "N/A",
          cycle.updatedAt
            ? new Date(cycle.updatedAt).toLocaleDateString()
            : "N/A",
        ]);
      });

      // Add cycles summary
      csvData.push([""]);
      csvData.push(["*Cycles Summary*"]);
      const totalCycles = data.details.cycles.length;
      const paidCycles = data.details.cycles.filter(
        (c) => c.status === "paid"
      ).length;
      const unpaidCycles = data.details.cycles.filter(
        (c) => c.status === "unpaid" || c.status === "partially_paid"
      ).length;
      const totalGoalProfit = data.details.cycles.reduce(
        (sum, c) => sum + parseFloat(c.goal_profit || 0),
        0
      );
      const totalActualProfit = data.details.cycles.reduce(
        (sum, c) => sum + parseFloat(c.actual_profit || 0),
        0
      );
      const totalDueAmount = data.details.cycles.reduce(
        (sum, c) => sum + parseFloat(c.due_amount || 0),
        0
      );
      const totalPaidAmount = data.details.cycles.reduce(
        (sum, c) => sum + parseFloat(c.paid_amount || 0),
        0
      );

      csvData.push(["Total Cycles", totalCycles, ""]);
      csvData.push(["Paid Cycles", paidCycles, ""]);
      csvData.push(["Unpaid Cycles", unpaidCycles, ""]);
      csvData.push([
        "Total Goal Profit",
        "",
        formatCurrencyForCSV(totalGoalProfit),
      ]);
      csvData.push([
        "Total Actual Profit",
        "",
        formatCurrencyForCSV(totalActualProfit),
      ]);
      csvData.push([
        "Total Due Amount",
        "",
        formatCurrencyForCSV(totalDueAmount),
      ]);
      csvData.push([
        "Total Paid Amount",
        "",
        formatCurrencyForCSV(totalPaidAmount),
      ]);
      csvData.push([
        "Profit Achievement Rate",
        "",
        totalGoalProfit > 0
          ? `${((totalActualProfit / totalGoalProfit) * 100).toFixed(1)}%`
          : "N/A",
      ]);
      csvData.push([""]);
    }

    // Get filtered data based on current tab
    const getFilteredData = () => {
      if (!data.details)
        return { quotations: [], expenses: [], adjustments: [], projects: [] };

      switch (activeTab) {
        case "payments":
          return {
            quotations: data.details.quotations || [],
            expenses: [],
            adjustments: [],
            projects: [],
          };
        case "inflow":
          return {
            quotations: [],
            expenses: data.details.expenses || [],
            adjustments: [],
            projects: [],
          };
        case "outflow":
          return {
            quotations: [],
            expenses: [],
            adjustments: data.details.adjustments || [],
            projects: [],
          };
        case "penalties":
          return {
            quotations: [],
            expenses: [],
            adjustments: [],
            projects: [],
          };
        case "projects":
          return {
            quotations: [],
            expenses: [],
            adjustments: [],
            projects: data.details.projects || [],
          };
        default:
          return data.details;
      }
    };

    const filteredData = getFilteredData();

    // Add quotations section
    if (filteredData.quotations && filteredData.quotations.length > 0) {
      csvData.push(["**QUOTATIONS SECTION**"]);
      csvData.push([""]);
      csvData.push([
        "*Quotation ID*",
        "*Status*",
        "*Total Cost (EGP)*",
        "*Requested Date*",
        "*Client*",
        "*Cycle*",
        "*Items Count*",
        "*Notes*",
      ]);

      filteredData.quotations.forEach((quotation) => {
        csvData.push([
          quotation._id?.slice(-8) || "Unknown",
          quotation.status?.toUpperCase() || "Unknown",
          formatCurrencyForCSV(quotation.total_cost),
          quotation.dates?.requested_at
            ? new Date(quotation.dates.requested_at).toLocaleDateString()
            : "N/A",
          quotation.client_id?.client_name || data.client?.name || "Unknown",
          quotation.cycle_id?.cycle_name || quotation.cycle_id?.month || "N/A",
          (quotation.items?.equipment?.length || 0) +
            (quotation.items?.props?.length || 0) +
            (quotation.items?.day_rates?.length || 0) +
            (quotation.items?.others?.length || 0),
          quotation.notes || "No notes",
        ]);
      });

      // Add detailed quotation items breakdown
      csvData.push([""]);
      csvData.push(["*Quotation Items Breakdown*"]);
      csvData.push([
        "*Quotation ID*",
        "*Category*",
        "*Item/Tool*",
        "*Description*",
        "*Contact*",
        "*Unit Price (EGP)*",
        "*Quantity*",
        "*Total Price (EGP)*",
      ]);

      filteredData.quotations.forEach((quotation) => {
        const quotationId = quotation._id?.slice(-8) || "Unknown";

        // Equipment items
        if (quotation.items?.equipment) {
          quotation.items.equipment.forEach((item) => {
            csvData.push([
              quotationId,
              "Equipment",
              item.tool || "N/A",
              "Equipment rental/purchase",
              "N/A",
              formatCurrencyForCSV(item.price),
              item.qty || 1,
              formatCurrencyForCSV(item.total_price),
            ]);
          });
        }

        // Props items
        if (quotation.items?.props) {
          quotation.items.props.forEach((item) => {
            csvData.push([
              quotationId,
              "Props",
              item.item || item.name || "N/A",
              "Props for production",
              "N/A",
              formatCurrencyForCSV(item.price),
              item.qty || 1,
              formatCurrencyForCSV(item.total_price),
            ]);
          });
        }

        // Models
        if (quotation.items?.models) {
          quotation.items.models.forEach((item) => {
            csvData.push([
              quotationId,
              "Models",
              item.name || "N/A",
              "Model services",
              item.contact || "N/A",
              formatCurrencyForCSV(item.rate),
              "1 day",
              formatCurrencyForCSV(item.total_price || item.rate),
            ]);
          });
        }

        // Day rates
        if (quotation.items?.day_rates) {
          quotation.items.day_rates.forEach((item) => {
            csvData.push([
              quotationId,
              "Day Rates",
              item.name || "N/A",
              "Daily rate for services",
              item.contact || "N/A",
              formatCurrencyForCSV(item.rate),
              "1 day",
              formatCurrencyForCSV(item.total_price || item.rate),
            ]);
          });
        }

        // Others
        if (quotation.items?.others) {
          quotation.items.others.forEach((item) => {
            csvData.push([
              quotationId,
              "Others",
              "Miscellaneous",
              item.description || "Other expenses",
              "N/A",
              formatCurrencyForCSV(item.price),
              item.qty || 1,
              formatCurrencyForCSV(item.total_price),
            ]);
          });
        }
      });
      csvData.push([""]);
    }

    // Add expenses section
    if (filteredData.expenses && filteredData.expenses.length > 0) {
      csvData.push(["**EXPENSES SECTION**"]);
      csvData.push([""]);
      csvData.push([
        "*Expense ID*",
        "*Type*",
        "*Status*",
        "*Amount (EGP)*",
        "*Date*",
        "*Description*",
        "*Client*",
        "*Cycle*",
        "*Approved By*",
        "*Approved Date*",
      ]);

      filteredData.expenses.forEach((expense) => {
        csvData.push([
          expense._id?.slice(-8) || "Unknown",
          expense.type?.toUpperCase() || "Unknown",
          expense.status?.toUpperCase() || "Unknown",
          formatCurrencyForCSV(expense.amount),
          expense.date ? new Date(expense.date).toLocaleDateString() : "N/A",
          expense.description || "No description",
          expense.client_id?.client_name || data.client?.name || "Unknown",
          expense.cycle_id?.cycle_name || expense.cycle_id?.month || "N/A",
          expense.approved_by?.name || "N/A",
          expense.approved_at
            ? new Date(expense.approved_at).toLocaleDateString()
            : "N/A",
        ]);
      });
      csvData.push([""]);
    }

    // Add adjustments section
    if (filteredData.adjustments && filteredData.adjustments.length > 0) {
      csvData.push(["**ADJUSTMENTS SECTION**"]);
      csvData.push([""]);
      csvData.push([
        "*Adjustment ID*",
        "*Category*",
        "*Status*",
        "*Impact Amount (EGP)*",
        "*Description*",
        "*Client*",
        "*Cycle*",
        "*Project*",
        "*Created By*",
        "*Created Date*",
        "*Notes*",
      ]);

      filteredData.adjustments.forEach((adjustment) => {
        csvData.push([
          adjustment._id?.slice(-8) || "Unknown",
          adjustment.category?.toUpperCase() || "Unknown",
          adjustment.resolved ? "RESOLVED" : "PENDING",
          formatCurrencyForCSV(adjustment.impact_amount),
          adjustment.description || "No description",
          adjustment.client_id?.client_name || data.client?.name || "Unknown",
          adjustment.cycle_id?.cycle_name ||
            adjustment.cycle_id?.month ||
            "N/A",
          adjustment.project_id?.name || "N/A",
          adjustment.created_by?.name || "Unknown",
          adjustment.createdAt
            ? new Date(adjustment.createdAt).toLocaleDateString()
            : "N/A",
          adjustment.notes || "No notes",
        ]);
      });
      csvData.push([""]);
    }

    // Add projects section
    if (filteredData.projects && filteredData.projects.length > 0) {
      csvData.push(["**ONE-TIME PROJECTS SECTION**"]);
      csvData.push([""]);
      csvData.push([
        "*Project ID*",
        "*Project Name*",
        "*Status*",
        "*Project Fees (EGP)*",
        "*Paid Amount (EGP)*",
        "*Due Amount (EGP)*",
        "*Project Date*",
        "*Inflow (EGP)*",
        "*Outflow (EGP)*",
        "*Profit (EGP)*",
        "*Description*",
        "*Created By*",
      ]);

      filteredData.projects.forEach((project) => {
        csvData.push([
          project._id?.slice(-8) || "Unknown",
          project.name || "Unnamed Project",
          project.status?.toUpperCase() || "Unknown",
          formatCurrencyForCSV(project.fees),
          formatCurrencyForCSV(project.paid_amount),
          formatCurrencyForCSV(project.due_amount),
          project.date ? new Date(project.date).toLocaleDateString() : "N/A",
          formatCurrencyForCSV(project.project_inflow),
          formatCurrencyForCSV(project.project_outflow),
          formatCurrencyForCSV(project.profit),
          project.description || "No description",
          project.created_by?.name || "Unknown",
        ]);
      });
      csvData.push([""]);
    }

    // Add comprehensive final summary and totals
    csvData.push(["**COMPREHENSIVE REPORT SUMMARY**"]);
    csvData.push([""]);
    csvData.push(["*Section*", "*Count*", "*Total Amount (EGP)*", "*Notes*"]);

    const quotationsTotal =
      filteredData.quotations?.reduce(
        (sum, q) => sum + parseFloat(q.total_cost || 0),
        0
      ) || 0;
    const expensesTotal =
      filteredData.expenses?.reduce(
        (sum, e) => sum + parseFloat(e.amount || 0),
        0
      ) || 0;
    const adjustmentsTotal =
      filteredData.adjustments?.reduce(
        (sum, a) => sum + parseFloat(a.impact_amount || 0),
        0
      ) || 0;
    const projectsTotal =
      filteredData.projects?.reduce(
        (sum, p) => sum + parseFloat(p.fees || 0),
        0
      ) || 0;
    const cyclesTotal = data.details?.cycles?.length || 0;
    const breakdownTotal = data.details?.breakdown?.length || 0;

    csvData.push([
      "Quotations",
      filteredData.quotations?.length || 0,
      formatCurrencyForCSV(quotationsTotal),
      "Total quotation value",
    ]);
    csvData.push([
      "Expenses",
      filteredData.expenses?.length || 0,
      formatCurrencyForCSV(expensesTotal),
      "Total expenses amount",
    ]);
    csvData.push([
      "Adjustments",
      filteredData.adjustments?.length || 0,
      formatCurrencyForCSV(adjustmentsTotal),
      "Total adjustments impact",
    ]);
    csvData.push([
      "Projects",
      filteredData.projects?.length || 0,
      formatCurrencyForCSV(projectsTotal),
      "Total project fees",
    ]);
    csvData.push([
      "Subscription Cycles",
      cyclesTotal,
      data.summary?.total_inflow
        ? formatCurrencyForCSV(data.summary.total_inflow)
        : "N/A",
      "Total subscription revenue",
    ]);
    csvData.push([
      "Monthly Breakdown Items",
      breakdownTotal,
      "N/A",
      "Monthly analysis entries",
    ]);

    if (data.tabs?.inflow) {
      const tabInflowTotal = data.tabs.inflow.reduce(
        (sum, item) => sum + parseFloat(item.amount || 0),
        0
      );
      csvData.push([
        "Monthly Inflow Entries",
        data.tabs.inflow.length,
        formatCurrencyForCSV(tabInflowTotal),
        "Monthly inflow tracking",
      ]);
    }

    if (data.tabs?.outflow) {
      const tabOutflowTotal = data.tabs.outflow.reduce(
        (sum, item) => sum + parseFloat(item.amount || 0),
        0
      );
      csvData.push([
        "Monthly Outflow Entries",
        data.tabs.outflow.length,
        formatCurrencyForCSV(tabOutflowTotal),
        "Monthly outflow tracking",
      ]);
    }

    if (data.tabs?.payments) {
      const paymentsTotal = data.tabs.payments.reduce(
        (sum, payment) => sum + parseFloat(payment.amount || 0),
        0
      );
      csvData.push([
        "Payment Records",
        data.tabs.payments.length,
        formatCurrencyForCSV(paymentsTotal),
        "Total payments received",
      ]);
    }

    csvData.push([""]);
    csvData.push(["**FINANCIAL TOTALS VERIFICATION**"]);
    csvData.push([
      "Summary Total Inflow",
      "",
      data.summary?.total_inflow
        ? formatCurrencyForCSV(data.summary.total_inflow)
        : "N/A",
      "From summary data",
    ]);
    csvData.push([
      "Summary Total Outflow",
      "",
      data.summary?.total_outflow
        ? formatCurrencyForCSV(data.summary.total_outflow)
        : "N/A",
      "From summary data",
    ]);
    csvData.push([
      "Summary Total Profit",
      "",
      data.summary?.total_profit
        ? formatCurrencyForCSV(data.summary.total_profit)
        : "N/A",
      "From summary data",
    ]);
    csvData.push([
      "Calculated Transactions Total",
      "",
      formatCurrencyForCSV(
        quotationsTotal + expensesTotal + adjustmentsTotal + projectsTotal
      ),
      "From detailed transactions",
    ]);
    csvData.push([""]);
    csvData.push(["*Report Generation Details*"]);
    csvData.push([
      "Report Generated",
      new Date().toLocaleString(),
      "",
      "Timestamp",
    ]);
    csvData.push(["Data Source", "Client Dashboard API", "", "API endpoint"]);
    csvData.push([
      "Filter Applied",
      activeTab.charAt(0).toUpperCase() + activeTab.slice(1),
      "",
      "Active tab filter",
    ]);
    csvData.push([
      "Client Subscription Period",
      data.client?.start_date
        ? new Date(data.client.start_date).toLocaleDateString()
        : "N/A",
      data.client?.end_date
        ? new Date(data.client.end_date).toLocaleDateString()
        : "N/A",
      "Contract period",
    ]);
    csvData.push([
      "Monthly Subscription Fee",
      "",
      data.client?.subscription_fee
        ? formatCurrencyForCSV(data.client.subscription_fee)
        : "N/A",
      "Per month",
    ]);
    csvData.push([""]);
    csvData.push([
      "*Report End*",
      new Date().toLocaleString(),
      "",
      "End of comprehensive report",
    ]);

    // Convert to CSV string with proper escaping
    const csvContent = csvData
      .map((row) =>
        row
          .map((field) => {
            const cellStr = String(field || "");
            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            if (
              cellStr.includes(",") ||
              cellStr.includes('"') ||
              cellStr.includes("\n")
            ) {
              return `"${cellStr.replace(/"/g, '""')}"`;
            }
            return cellStr;
          })
          .join(",")
      )
      .join("\n");

    // Create and download file with enhanced naming
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    const clientName = (data.client?.name || clientId || "unknown").replace(
      /[^a-zA-Z0-9]/g,
      "-"
    );
    const tabFilter = activeTab !== "payments" ? `-${activeTab}` : "";
    const dateStr = new Date().toISOString().split("T")[0];
    const timeStr = new Date().toTimeString().split(" ")[0].replace(/:/g, "");

    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `client-dashboard-${clientName}${tabFilter}-${dateStr}-${timeStr}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showSnackbar("Enhanced dashboard report exported successfully", "success");
  };

  const fetchData = useCallback(async () => {
    if (!clientId) return;
    setLoading(true);
    try {
      const params = new URLSearchParams();
      // Always fetch with details=true once, filtering is client-side
      params.set("details", "true");
      const token = localStorage.getItem("token");
      const res = await fetch(`${API_BASE_URL}?${params.toString()}`, {
        headers: token ? { Authorization: `Bearer ${token}` } : undefined,
      });
      if (!res.ok) {
        throw new Error(`Request failed (${res.status})`);
      }
      const result = await res.json();
      const payload = result?.data || result; // support both shapes
      setRawData(payload);
    } catch (err) {
      console.error("Fetch client dashboard error:", err);
      showSnackbar("Failed to load dashboard", "error");
    } finally {
      setLoading(false);
    }
  }, [clientId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Helpers for client-side filtering/grouping
  const parseYM = (s) => {
    if (!s) return { y: 0, m: 0 };
    const [y, m] = s.split("-").map((v) => parseInt(v, 10));
    return { y: y || 0, m: m || 0 };
  };
  const ymToNum = ({ y, m }) => y * 100 + m;
  const inRange = (label) => {
    const { y, m } = parseYM(label);
    const n = ymToNum({ y, m });
    const fromN = from ? ymToNum(parseYM(from)) : -Infinity;
    const toN = to ? ymToNum(parseYM(to)) : Infinity;
    return n >= fromN && n <= toN;
  };
  const toQuarterKey = ({ y, m }) => `Q${Math.ceil(m / 3)}-${y}`;
  const toYearKey = ({ y }) => `${y}`;

  const derived = useMemo(() => {
    const src = rawData;
    if (!src)
      return { client: null, summary: null, graph: [], tabs: {}, details: {} };
    const allLabels = (src.graph || []).map((g) => g.label);
    const years = Array.from(
      new Set(allLabels.map((l) => (l || "").split("-")[0]))
    )
      .filter(Boolean)
      .sort();
    const months = Array.from(new Set(allLabels)).filter(Boolean).sort();
    const quarters = ["Q1", "Q2", "Q3", "Q4"];
    // Filter and group graph
    const grouped = new Map();
    (src.graph || []).forEach((pt) => {
      if (!inRange(pt.label)) return;
      const ym = parseYM(pt.label);
      let key = pt.label;
      if (groupBy === "quarter") key = toQuarterKey(ym);
      if (groupBy === "year") key = toYearKey(ym);
      const prev = grouped.get(key) || {
        label: key,
        inflow: 0,
        outflow: 0,
        profit: 0,
        penalties: 0,
      };
      grouped.set(key, {
        label: key,
        inflow: prev.inflow + (pt.inflow || 0),
        outflow: prev.outflow + (pt.outflow || 0),
        profit: prev.profit + (pt.profit || 0),
        penalties: prev.penalties + (pt.penalties || 0),
      });
    });
    let graph = Array.from(grouped.values()).sort((a, b) =>
      a.label.localeCompare(b.label)
    );
    if (groupBy === "year" && selectedYear) {
      graph = graph.filter((g) => g.label === selectedYear);
    }
    if (groupBy === "quarter" && selectedYear && selectedQuarter) {
      const key = `${selectedQuarter}-${selectedYear}`;
      graph = graph.filter((g) => g.label === key);
    }
    if (groupBy === "month" && selectedMonth) {
      graph = graph.filter((g) => g.label === selectedMonth);
    }

    // Derive summary from filtered graph
    const summary = {
      total_inflow: graph.reduce((s, g) => s + g.inflow, 0),
      total_outflow: graph.reduce((s, g) => s + g.outflow, 0),
      total_profit: graph.reduce((s, g) => s + g.profit, 0),
      total_penalties: graph.reduce((s, g) => s + g.penalties, 0),
    };

    // Filter tabs by from/to only (keep original granularity)
    const filterTabList = (arr, key = "month") =>
      (arr || []).filter((it) => inRange(it[key] || ""));
    let payments = (src.tabs?.payments || []).filter((p) =>
      inRange((p.date || "").slice(0, 7))
    );
    let inflow = filterTabList(src.tabs?.inflow, "month");
    let outflow = filterTabList(src.tabs?.outflow, "month");
    const penalties = src.tabs?.penalties || [];
    if (groupBy === "year" && selectedYear) {
      payments = payments.filter((p) =>
        (p.date || "").startsWith(selectedYear)
      );
      inflow = inflow.filter((i) => (i.month || "").startsWith(selectedYear));
      outflow = outflow.filter((o) => (o.month || "").startsWith(selectedYear));
    }
    if (groupBy === "quarter" && selectedYear && selectedQuarter) {
      const qMonths =
        {
          Q1: ["01", "02", "03"],
          Q2: ["04", "05", "06"],
          Q3: ["07", "08", "09"],
          Q4: ["10", "11", "12"],
        }[selectedQuarter] || [];
      const matchQ = (val) =>
        qMonths.some((mm) => (val || "").startsWith(`${selectedYear}-${mm}`));
      payments = payments.filter((p) => matchQ((p.date || "").slice(0, 7)));
      inflow = inflow.filter((i) => matchQ(i.month));
      outflow = outflow.filter((o) => matchQ(o.month));
    }
    if (groupBy === "month" && selectedMonth) {
      const eq = (val) => (val || "") === selectedMonth;
      payments = payments.filter((p) => eq((p.date || "").slice(0, 7)));
      inflow = inflow.filter((i) => eq(i.month));
      outflow = outflow.filter((o) => eq(o.month));
    }
    const tabs = { payments, inflow, outflow, penalties };

    return {
      client: src.client,
      summary,
      graph,
      tabs,
      details: src.details || {},
      years,
      months,
      quarters,
    };
  }, [
    rawData,
    groupBy,
    from,
    to,
    selectedYear,
    selectedQuarter,
    selectedMonth,
  ]);

  const data = derived; // maintain existing variable usage
  const chartData = data?.graph || [];
  // const tabsData = data?.tabs || {};

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "#000",
        color: "white",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(ellipse at top, rgba(219, 74, 65, 0.1) 0%, transparent 50%), radial-gradient(ellipse at bottom, rgba(255, 255, 255, 0.05) 0%, transparent 70%)",
          filter: "blur(100px)",
          zIndex: 0,
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: "20%",
          right: "10%",
          width: "300px",
          height: "300px",
          background:
            "radial-gradient(circle, rgba(219, 74, 65, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(80px)",
          zIndex: 0,
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "60px 5% 40px",
            background: "rgba(255, 255, 255, 0.02)",
            backdropFilter: "blur(10px)",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 3 }}>
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Button
                variant="text"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(-1)}
                sx={{
                  color: "#db4a41",
                  fontFamily: "Formula Bold",
                  "&:hover": {
                    backgroundColor: "rgba(219, 74, 65, 0.1)",
                  },
                }}
              >
                Back
              </Button>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <AccountBalanceIcon sx={{ color: "#db4a41", fontSize: 40 }} />
                <Box>
                  <Typography
                    variant="h3"
                    sx={{
                      fontFamily: "Formula Bold",
                      color: "#db4a41",
                      lineHeight: 1.2,
                    }}
                  >
                    Client Financial Dashboard
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      color: "rgba(255, 255, 255, 0.8)",
                      fontFamily: "Formula Bold",
                    }}
                  >
                    {data?.client?.name || clientId}
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchData}
                disabled={loading}
                sx={{
                  borderColor: "#db4a41",
                  color: "#db4a41",
                  fontFamily: "Formula Bold",
                  background: "rgba(219, 74, 65, 0.1)",
                  backdropFilter: "blur(10px)",
                  "&:hover": {
                    borderColor: "#c62828",
                    backgroundColor: "rgba(219, 74, 65, 0.2)",
                  },
                  "&:disabled": {
                    borderColor: "rgba(219, 74, 65, 0.3)",
                    color: "rgba(219, 74, 65, 0.3)",
                  },
                }}
              >
                {loading ? "Refreshing..." : "Refresh"}
              </Button>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={exportToCSV}
                disabled={loading || !data}
                sx={{
                  borderColor: "#4caf50",
                  color: "#4caf50",
                  fontFamily: "Formula Bold",
                  background: "rgba(76, 175, 80, 0.1)",
                  backdropFilter: "blur(10px)",
                  "&:hover": {
                    borderColor: "#388e3c",
                    backgroundColor: "rgba(76, 175, 80, 0.2)",
                  },
                  "&:disabled": {
                    borderColor: "rgba(76, 175, 80, 0.3)",
                    color: "rgba(76, 175, 80, 0.3)",
                  },
                }}
              >
                Export CSV
              </Button>
            </motion.div>
          </Box>
        </Box>

        <Box sx={{ px: "5%", pb: 6 }}>
          {/* Filters */}
          <Card
            sx={{
              background: "rgba(255, 255, 255, 0.05)",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "12px",
              mb: 3,
            }}
          >
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Group By
                    </InputLabel>
                    <Select
                      value={groupBy}
                      onChange={(e) => setGroupBy(e.target.value)}
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="month">Month</MenuItem>
                      <MenuItem value="quarter">Quarter</MenuItem>
                      <MenuItem value="year">Year</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                {groupBy === "year" && (
                  <Grid item xs={12} sm={4} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Year
                      </InputLabel>
                      <Select
                        value={selectedYear}
                        onChange={(e) => setSelectedYear(e.target.value)}
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                        }}
                      >
                        {(data?.years || []).map((y) => (
                          <MenuItem key={y} value={y}>
                            {y}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                )}
                {groupBy === "quarter" && (
                  <>
                    <Grid item xs={12} sm={4} md={3}>
                      <FormControl fullWidth size="small">
                        <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                          Year
                        </InputLabel>
                        <Select
                          value={selectedYear}
                          onChange={(e) => setSelectedYear(e.target.value)}
                          sx={{
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "rgba(255, 255, 255, 0.3)",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#db4a41",
                            },
                          }}
                        >
                          {(data?.years || []).map((y) => (
                            <MenuItem key={y} value={y}>
                              {y}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                      <FormControl fullWidth size="small">
                        <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                          Quarter
                        </InputLabel>
                        <Select
                          value={selectedQuarter}
                          onChange={(e) => setSelectedQuarter(e.target.value)}
                          sx={{
                            color: "white",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "rgba(255, 255, 255, 0.3)",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#db4a41",
                            },
                          }}
                        >
                          {(data?.quarters || []).map((q) => (
                            <MenuItem key={q} value={q}>
                              {q}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}
                {groupBy === "month" && (
                  <Grid item xs={12} sm={4} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Month
                      </InputLabel>
                      <Select
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        sx={{
                          color: "white",
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "rgba(255, 255, 255, 0.3)",
                          },
                          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#db4a41",
                          },
                        }}
                      >
                        {(data?.months || []).map((m) => (
                          <MenuItem key={m} value={m}>
                            {m}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                )}
                <Grid item xs={12} sm={4} md={3}>
                  <TextField
                    fullWidth
                    size="small"
                    label="From (YYYY-MM)"
                    value={from}
                    onChange={(e) => setFrom(e.target.value)}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4} md={3}>
                  <TextField
                    fullWidth
                    size="small"
                    label="To (YYYY-MM)"
                    value={to}
                    onChange={(e) => setTo(e.target.value)}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        color: "white",
                        "& fieldset": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused fieldset": { borderColor: "#db4a41" },
                      },
                      "& .MuiInputLabel-root": {
                        color: "rgba(255, 255, 255, 0.7)",
                      },
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      Details
                    </InputLabel>
                    <Select
                      value={includeDetails ? "true" : "false"}
                      onChange={(e) =>
                        setIncludeDetails(e.target.value === "true")
                      }
                      sx={{
                        color: "white",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "rgba(255, 255, 255, 0.3)",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#db4a41",
                        },
                      }}
                    >
                      <MenuItem value="true">Include</MenuItem>
                      <MenuItem value="false">Exclude</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={() => {
                      setFrom("");
                      setTo("");
                      setSelectedYear("");
                      setSelectedQuarter("");
                      setSelectedMonth("");
                    }}
                    sx={{
                      borderColor: "#db4a41",
                      color: "#db4a41",
                      fontFamily: "Formula Bold",
                      height: "40px",
                      mt: { xs: 1.5, md: 0 },
                      "&:hover": {
                        borderColor: "#c62828",
                        backgroundColor: "rgba(219, 74, 65, 0.1)",
                      },
                    }}
                  >
                    Clear Filters
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* KPIs and Client Info */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={9}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(76, 175, 80, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #4caf50, #81c784)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(76, 175, 80, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <TrendingUpIcon
                            sx={{ color: "#4caf50", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            Total Inflow
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#4caf50",
                            fontSize: "1.8rem",
                          }}
                        >
                          {formatCurrency(data?.summary?.total_inflow)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(244, 67, 54, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #f44336, #ef5350)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(244, 67, 54, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <TrendingDownIcon
                            sx={{ color: "#f44336", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            Total Outflow
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#f44336",
                            fontSize: "1.8rem",
                          }}
                        >
                          {formatCurrency(data?.summary?.total_outflow)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(255, 152, 0, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #ff9800, #ffb74d)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(255, 152, 0, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MonetizationOnIcon
                            sx={{ color: "#ff9800", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            Total Profit
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#ff9800",
                            fontSize: "1.8rem",
                          }}
                        >
                          {formatCurrency(data?.summary?.total_profit)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(42, 169, 185, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #2AA9B9FF, #5AA5AEFF)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(42, 169, 185, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <MoneyOffIcon
                            sx={{ color: "#2AA9B9FF", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            Total Penalties
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#2AA9B9FF",
                            fontSize: "1.8rem",
                          }}
                        >
                          {formatCurrency(data?.summary?.penalties)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(42, 169, 185, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #2A44B9FF, #5265BAFF)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(42, 169, 185, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <RequestQuoteIcon
                            sx={{ color: "#2A44B9FF", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            No. of Quotations
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#2A44B9FF",
                            fontSize: "1.8rem",
                          }}
                        >
                          # {data?.details?.quotations?.length}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    whileHover={{ y: -5 }}
                  >
                    <Card
                      sx={{
                        background: "rgba(255, 255, 255, 0.08)",
                        backdropFilter: "blur(20px)",
                        border: "1px solid rgba(104, 40, 194, 0.3)",
                        borderRadius: "16px",
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          right: 0,
                          height: "4px",
                          background:
                            "linear-gradient(90deg, #6828C2FF, #8457C3FF)",
                        },
                        "&:hover": {
                          transform: "translateY(-2px)",
                          boxShadow: "0 8px 25px rgba(104, 40, 194, 0.2)",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", mb: 2 }}
                        >
                          <RequestQuoteIcon
                            sx={{ color: "#6828C2FF", fontSize: 28, mr: 1 }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              color: "rgba(255,255,255,0.8)",
                              fontFamily: "Formula Bold",
                              textTransform: "uppercase",
                              letterSpacing: "0.5px",
                            }}
                          >
                            No. of Expenses
                          </Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontFamily: "Formula Bold",
                            color: "#6828C2FF",
                            fontSize: "1.8rem",
                          }}
                        >
                          # {data?.details?.expenses?.length}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={3}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.08)",
                    backdropFilter: "blur(20px)",
                    border: "1px solid rgba(219, 74, 65, 0.3)",
                    borderRadius: "16px",
                    height: "100%",
                    position: "relative",
                    overflow: "hidden",
                    "&::before": {
                      content: '""',
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      height: "4px",
                      background: "linear-gradient(90deg, #db4a41, #f57c00)",
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                      <ReceiptIcon
                        sx={{ color: "#db4a41", fontSize: 28, mr: 1 }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Formula Bold",
                          color: "#db4a41",
                          textTransform: "uppercase",
                          letterSpacing: "0.5px",
                        }}
                      >
                        Client Information
                      </Typography>
                    </Box>
                    <Box sx={{ space: 2 }}>
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="caption"
                          sx={{
                            color: "rgba(255,255,255,0.6)",
                            textTransform: "uppercase",
                            fontSize: "0.7rem",
                            letterSpacing: "1px",
                          }}
                        >
                          Client Name
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            color: "white",
                            fontFamily: "Formula Bold",
                            fontSize: "1rem",
                          }}
                        >
                          {data?.client?.name || "N/A"}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="caption"
                          sx={{
                            color: "rgba(255,255,255,0.6)",
                            textTransform: "uppercase",
                            fontSize: "0.7rem",
                            letterSpacing: "1px",
                          }}
                        >
                          Subscription Fee
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#4caf50",
                            fontFamily: "Formula Bold",
                            fontSize: "1rem",
                          }}
                        >
                          {formatCurrency(data?.client?.subscription_fee)}
                        </Typography>
                      </Box>
                      <Box sx={{ mb: 2 }}>
                        <Typography
                          variant="caption"
                          sx={{
                            color: "rgba(255,255,255,0.6)",
                            textTransform: "uppercase",
                            fontSize: "0.7rem",
                            letterSpacing: "1px",
                          }}
                        >
                          Contract Period
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "rgba(255,255,255,0.9)" }}
                        >
                          {data?.client?.start_date
                            ? new Date(
                                data.client.start_date
                              ).toLocaleDateString()
                            : "N/A"}{" "}
                          -{" "}
                          {data?.client?.end_date
                            ? new Date(
                                data.client.end_date
                              ).toLocaleDateString()
                            : "N/A"}
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>

          {/* Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card
              sx={{
                background: "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(20px)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                borderRadius: "16px",
                mb: 4,
              }}
            >
              <CardContent sx={{ p: 0 }}>
                <Tabs
                  value={activeTab}
                  onChange={(e, v) => setActiveTab(v)}
                  sx={{
                    "& .MuiTabs-indicator": {
                      backgroundColor: "#db4a41",
                      height: "3px",
                    },
                    "& .MuiTab-root": {
                      color: "rgba(255, 255, 255, 0.7)",
                      fontFamily: "Formula Bold",
                      textTransform: "uppercase",
                      letterSpacing: "0.5px",
                      fontSize: "1rem",
                      minHeight: "60px",
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "space-around",
                      alignItems: "center",
                      gap: "15px",
                      "&.Mui-selected": {
                        color: "#db4a41",
                      },
                      "&:hover": {
                        color: "rgba(219, 74, 65, 0.8)",
                        backgroundColor: "rgba(219, 74, 65, 0.05)",
                      },
                    },
                  }}
                >
                  <Tab value="payments" label="Payments" />
                  <Tab value="inflow" label="Inflow" />
                  <Tab value="outflow" label="Outflow" />
                  <Tab value="penalties" label="Penalties" />
                  <Tab value="projects" label="Projects" />
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card
              sx={{
                background: "rgba(255, 255, 255, 0.08)",
                backdropFilter: "blur(20px)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                borderRadius: "16px",
                mb: 4,
                position: "relative",
                overflow: "hidden",
                "&::before": {
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  height: "4px",
                  background:
                    "linear-gradient(90deg, #4caf50, #f44336, #ff9800, #9c27b0)",
                },
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontFamily: "Formula Bold",
                    color: "white",
                    mb: 3,
                    textAlign: "center",
                  }}
                >
                  Financial Trends Overview
                </Typography>
                {loading ? (
                  <Box
                    sx={{ display: "flex", justifyContent: "center", py: 10 }}
                  >
                    <CircularProgress sx={{ color: "#db4a41" }} />
                  </Box>
                ) : (
                  <Box sx={{ height: 400 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          stroke="rgba(255, 255, 255, 0.1)"
                        />
                        <XAxis
                          dataKey="label"
                          stroke="rgba(255, 255, 255, 0.8)"
                          fontSize={12}
                          fontFamily="Formula Bold"
                        />
                        <YAxis
                          stroke="rgba(255, 255, 255, 0.8)"
                          fontSize={12}
                          fontFamily="Formula Bold"
                          tickFormatter={(value) =>
                            `${(value / 1000).toFixed(0)}K`
                          }
                        />
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: "rgba(0,0,0,0.9)",
                            border: "1px solid rgba(219, 74, 65, 0.3)",
                            borderRadius: "8px",
                            color: "white",
                            fontFamily: "Formula Bold",
                          }}
                          formatter={(val) => [
                            `${parseFloat(val).toLocaleString()} EGP`,
                          ]}
                        />
                        <Legend
                          wrapperStyle={{
                            fontFamily: "Formula Bold",
                            fontSize: "14px",
                          }}
                        />
                        <Line
                          type="monotone"
                          dataKey="inflow"
                          stroke="#4caf50"
                          name="Inflow"
                          strokeWidth={3}
                          dot={{ r: 4, fill: "#4caf50" }}
                          activeDot={{ r: 6, fill: "#4caf50" }}
                        />
                        <Line
                          type="monotone"
                          dataKey="outflow"
                          stroke="#f44336"
                          name="Outflow"
                          strokeWidth={3}
                          dot={{ r: 4, fill: "#f44336" }}
                          activeDot={{ r: 6, fill: "#f44336" }}
                        />
                        <Line
                          type="monotone"
                          dataKey="profit"
                          stroke="#ff9800"
                          name="Profit"
                          strokeWidth={3}
                          dot={{ r: 4, fill: "#ff9800" }}
                          activeDot={{ r: 6, fill: "#ff9800" }}
                        />
                        <Line
                          type="monotone"
                          dataKey="penalties"
                          stroke="#9c27b0"
                          name="Penalties"
                          strokeWidth={3}
                          dot={{ r: 4, fill: "#9c27b0" }}
                          activeDot={{ r: 6, fill: "#9c27b0" }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Details Sections: Quotations, Expenses, Adjustments, Projects */}
          {includeDetails && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6} lg={3}>
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{ fontFamily: "Formula Bold", mb: 2, color: "white" }}
                    >
                      Quotations ({data?.details?.quotations?.length || 0})
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                      {(data?.details?.quotations || []).map((q) => (
                        <Box
                          key={q._id}
                          sx={{
                            p: 2,
                            mb: 1,
                            background: "rgba(255,255,255,0.05)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.1)",
                            cursor: "pointer",
                          }}
                          onClick={() => setOpenQuotation(q)}
                        >
                          <Typography
                            variant="body1"
                            sx={{ fontFamily: "Formula Bold", color: "white" }}
                          >
                            {q.status?.toUpperCase()}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "rgba(255,255,255,0.7)" }}
                          >
                            Total: {formatCurrency(q.total_cost)}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{ fontFamily: "Formula Bold", mb: 2, color: "white" }}
                    >
                      Expenses ({data?.details?.expenses?.length || 0})
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                      {(data?.details?.expenses || []).map((e) => (
                        <Box
                          key={e._id}
                          sx={{
                            p: 2,
                            mb: 1,
                            background: "rgba(255,255,255,0.05)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.1)",
                            cursor: "pointer",
                          }}
                          onClick={() => setOpenExpense(e)}
                        >
                          <Typography
                            variant="body1"
                            sx={{ fontFamily: "Formula Bold", color: "white" }}
                          >
                            {e.type}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "rgba(255,255,255,0.7)" }}
                          >
                            Amount: {formatCurrency(e.amount)}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{ fontFamily: "Formula Bold", mb: 2, color: "white" }}
                    >
                      Adjustments ({data?.details?.adjustments?.length || 0})
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                      {(data?.details?.adjustments || []).map((a) => (
                        <Box
                          key={a._id}
                          sx={{
                            p: 2,
                            mb: 1,
                            background: "rgba(255,255,255,0.05)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.1)",
                            cursor: "pointer",
                          }}
                          onClick={() => setOpenAdjustment(a)}
                        >
                          <Typography
                            variant="body1"
                            sx={{ fontFamily: "Formula Bold", color: "white" }}
                          >
                            {a.category}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "rgba(255,255,255,0.7)" }}
                          >
                            Impact: {formatCurrency(a.impact_amount)}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6} lg={3}>
                <Card
                  sx={{
                    background: "rgba(255, 255, 255, 0.05)",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    borderRadius: "12px",
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{ fontFamily: "Formula Bold", mb: 2, color: "white" }}
                    >
                      Projects ({data?.details?.projects?.length || 0})
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
                      {(data?.details?.projects || []).map((p) => (
                        <Box
                          key={p._id}
                          sx={{
                            p: 2,
                            mb: 1,
                            background: "rgba(255,255,255,0.05)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.1)",
                            cursor: "pointer",
                          }}
                          onClick={() => setOpenProject(p)}
                        >
                          <Typography
                            variant="body1"
                            sx={{ fontFamily: "Formula Bold", color: "white" }}
                          >
                            {p.name}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: "rgba(255,255,255,0.7)" }}
                          >
                            Fees: {formatCurrency(p.fees)}
                          </Typography>
                          <br />
                          <Chip
                            label={p.status?.toUpperCase() || "Unknown"}
                            size="small"
                            sx={{
                              mt: 1,
                              backgroundColor:
                                p.status === "paid"
                                  ? "rgba(76, 175, 80, 0.2)"
                                  : p.status === "pending"
                                  ? "rgba(255, 152, 0, 0.2)"
                                  : "rgba(244, 67, 54, 0.2)",
                              color:
                                p.status === "paid"
                                  ? "#4caf50"
                                  : p.status === "pending"
                                  ? "#ff9800"
                                  : "#f44336",
                              fontSize: "0.7rem",
                            }}
                          />
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Box>

        {/* Quotation Modal */}
        <Dialog
          open={!!openQuotation}
          onClose={() => setOpenQuotation(null)}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            <RequestQuoteIcon sx={{ color: "#db4a41" }} />
            Quotation Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {openQuotation && (
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Client:</strong>{" "}
                    {openQuotation.client_id?.client_name ||
                      data?.client?.name ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Cycle:</strong>{" "}
                    {openQuotation.cycle_id?.cycle_name ||
                      openQuotation.cycle_id?.month ||
                      "-"}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={{ color: "white" }}>
                    <strong>Status:</strong>{" "}
                    <Chip
                      label={openQuotation.status?.toUpperCase()}
                      size="small"
                      sx={{
                        backgroundColor:
                          openQuotation.status === "approved"
                            ? "#4caf50"
                            : openQuotation.status === "rejected"
                            ? "#f44336"
                            : "#ff9800",
                        color: "white",
                        fontFamily: "Formula Bold",
                      }}
                    />
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography
                    variant="h6"
                    sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                  >
                    Total: {formatCurrency(openQuotation.total_cost)}
                  </Typography>
                </Grid>

                {/* Items breakdown */}
                {openQuotation.items && (
                  <Grid item xs={12}>
                    <Grid container spacing={2}>
                      {[
                        {
                          key: "equipment",
                          title: "Equipment",
                          cols: [
                            { field: "tool", label: "Tool" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                        {
                          key: "props",
                          title: "Props",
                          cols: [
                            { field: "item", label: "Item" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                        {
                          key: "models",
                          title: "Models",
                          cols: [
                            { field: "name", label: "Name" },
                            { field: "rate", label: "Rate", isMoney: true },
                          ],
                        },
                        {
                          key: "day_rates",
                          title: "Day Rates",
                          cols: [
                            { field: "name", label: "Name" },
                            { field: "rate", label: "Rate", isMoney: true },
                            { field: "contact", label: "Contact" },
                          ],
                        },
                        {
                          key: "others",
                          title: "Others",
                          cols: [
                            { field: "description", label: "Description" },
                            { field: "price", label: "Price", isMoney: true },
                            { field: "qty", label: "Qty" },
                          ],
                        },
                      ].map((section) => {
                        const rows = openQuotation.items[section.key] || [];
                        if (!rows.length) return null;
                        return (
                          <Grid item xs={12} key={section.key}>
                            <Card
                              sx={{
                                background: "rgba(255, 255, 255, 0.05)",
                                border: "1px solid rgba(255, 255, 255, 0.1)",
                                borderRadius: "8px",
                              }}
                            >
                              <CardContent>
                                <Typography
                                  variant="h6"
                                  sx={{
                                    color: "#db4a41",
                                    fontFamily: "Formula Bold",
                                    mb: 1,
                                  }}
                                >
                                  {section.title}
                                </Typography>
                                <TableContainer
                                  component={Paper}
                                  sx={{ background: "transparent" }}
                                >
                                  <Table size="small">
                                    <TableHead>
                                      <TableRow>
                                        {section.cols.map((c) => (
                                          <TableCell
                                            key={c.field}
                                            sx={{
                                              color: "white",
                                              fontFamily: "Formula Bold",
                                            }}
                                          >
                                            {c.label}
                                          </TableCell>
                                        ))}
                                        <TableCell
                                          sx={{
                                            color: "white",
                                            fontFamily: "Formula Bold",
                                          }}
                                        >
                                          Total
                                        </TableCell>
                                      </TableRow>
                                    </TableHead>
                                    <TableBody>
                                      {rows.map((r, idx) => (
                                        <TableRow key={idx}>
                                          {section.cols.map((c) => {
                                            const raw = r[c.field];
                                            const val =
                                              raw?.$numberDecimal ?? raw;
                                            return (
                                              <TableCell
                                                key={c.field}
                                                sx={{ color: "white" }}
                                              >
                                                {c.isMoney
                                                  ? formatCurrency(val)
                                                  : val || "-"}
                                              </TableCell>
                                            );
                                          })}
                                          <TableCell sx={{ color: "white" }}>
                                            {formatCurrency(
                                              r.total_price ||
                                                (section.key === "equipment" ||
                                                section.key === "props"
                                                  ? parseFloat(
                                                      r.price?.$numberDecimal ||
                                                        r.price ||
                                                        0
                                                    ) * (r.qty || 0)
                                                  : section.key === "others"
                                                  ? parseFloat(
                                                      r.price?.$numberDecimal ||
                                                        r.price ||
                                                        0
                                                    ) * (r.qty || 1)
                                                  : parseFloat(
                                                      r.rate?.$numberDecimal ||
                                                        r.rate ||
                                                        0
                                                    ))
                                            )}
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </TableContainer>
                              </CardContent>
                            </Card>
                          </Grid>
                        );
                      })}

                      {/* Subtotals */}
                      {openQuotation.items.totals && (
                        <Grid item xs={12}>
                          <Card
                            sx={{
                              background: "rgba(255, 255, 255, 0.05)",
                              border: "1px solid rgba(255, 255, 255, 0.1)",
                              borderRadius: "8px",
                            }}
                          >
                            <CardContent>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: "#db4a41",
                                  fontFamily: "Formula Bold",
                                  mb: 1,
                                }}
                              >
                                Subtotals
                              </Typography>
                              <Grid container spacing={2}>
                                {Object.entries(openQuotation.items.totals).map(
                                  ([k, v]) => (
                                    <Grid item key={k} xs={12} sm={6} md={4}>
                                      <Typography sx={{ color: "white" }}>
                                        <strong>{k.replace("_", " ")}</strong>:{" "}
                                        {formatCurrency(v)}
                                      </Typography>
                                    </Grid>
                                  )
                                )}
                              </Grid>
                            </CardContent>
                          </Card>
                        </Grid>
                      )}
                    </Grid>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenQuotation(null)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Expense Modal */}
        <Dialog
          open={!!openExpense}
          onClose={() => setOpenExpense(null)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            <TrendingDownIcon sx={{ color: "#f44336" }} />
            Expense Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {openExpense && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Type:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#ff9800", fontFamily: "Formula Bold" }}
                    >
                      {openExpense.type}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Amount:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#f44336", fontFamily: "Formula Bold" }}
                    >
                      {formatCurrency(openExpense.amount)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Date:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "rgba(255, 255, 255, 0.8)" }}
                    >
                      {openExpense.date
                        ? new Date(openExpense.date).toLocaleDateString()
                        : "N/A"}
                    </Typography>
                  </Typography>
                </Grid>
                {openExpense.description && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#db4a41",
                        fontFamily: "Formula Bold",
                        mb: 1,
                      }}
                    >
                      Description
                    </Typography>
                    <Box
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        borderRadius: "8px",
                        p: 2,
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.9)" }}
                      >
                        {openExpense.description}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenExpense(null)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Adjustment Modal */}
        <Dialog
          open={!!openAdjustment}
          onClose={() => setOpenAdjustment(null)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            <MonetizationOnIcon sx={{ color: "#9c27b0" }} />
            Adjustment Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {openAdjustment && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Category:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#9c27b0", fontFamily: "Formula Bold" }}
                    >
                      {openAdjustment.category}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Impact Amount:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{
                        color:
                          openAdjustment.impact_amount >= 0
                            ? "#4caf50"
                            : "#f44336",
                        fontFamily: "Formula Bold",
                      }}
                    >
                      {formatCurrency(openAdjustment.impact_amount)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Status:</strong>{" "}
                    <Chip
                      label={openAdjustment.resolved ? "Resolved" : "Pending"}
                      size="small"
                      sx={{
                        backgroundColor: openAdjustment.resolved
                          ? "#4caf50"
                          : "#ff9800",
                        color: "white",
                        fontFamily: "Formula Bold",
                      }}
                    />
                  </Typography>
                </Grid>
                {openAdjustment.description && (
                  <Grid item xs={12}>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#db4a41",
                        fontFamily: "Formula Bold",
                        mb: 1,
                      }}
                    >
                      Description
                    </Typography>
                    <Box
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        borderRadius: "8px",
                        p: 2,
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.9)" }}
                      >
                        {openAdjustment.description}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenAdjustment(null)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Project Modal */}
        <Dialog
          open={!!openProject}
          onClose={() => setOpenProject(null)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              background: "rgba(0, 0, 0, 0.9)",
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "15px",
            },
          }}
        >
          <DialogTitle
            sx={{
              color: "white",
              fontFamily: "Formula Bold",
              borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
              display: "flex",
              alignItems: "center",
              gap: 2,
            }}
          >
            <RequestQuoteIcon sx={{ color: "#2196f3" }} />
            Project Details
          </DialogTitle>
          <DialogContent sx={{ mt: 2 }}>
            {openProject && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Name:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#2196f3", fontFamily: "Formula Bold" }}
                    >
                      {openProject.name}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Status:</strong>{" "}
                    <Chip
                      label={openProject.status?.toUpperCase() || "Unknown"}
                      size="small"
                      sx={{
                        backgroundColor:
                          openProject.status === "paid"
                            ? "rgba(76, 175, 80, 0.2)"
                            : openProject.status === "pending"
                            ? "rgba(255, 152, 0, 0.2)"
                            : "rgba(244, 67, 54, 0.2)",
                        color:
                          openProject.status === "paid"
                            ? "#4caf50"
                            : openProject.status === "pending"
                            ? "#ff9800"
                            : "#f44336",
                        fontFamily: "Formula Bold",
                      }}
                    />
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Fees:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      {formatCurrency(openProject.fees)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Paid Amount:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      {formatCurrency(openProject.paid_amount)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Due Amount:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{
                        color:
                          parseFloat(openProject.due_amount || 0) > 0
                            ? "#f44336"
                            : "#4caf50",
                        fontFamily: "Formula Bold",
                      }}
                    >
                      {formatCurrency(openProject.due_amount)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Date:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "rgba(255, 255, 255, 0.9)" }}
                    >
                      {new Date(openProject.date).toLocaleDateString()}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Inflow:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#4caf50", fontFamily: "Formula Bold" }}
                    >
                      {formatCurrency(openProject.project_inflow)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Outflow:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{ color: "#f44336", fontFamily: "Formula Bold" }}
                    >
                      {formatCurrency(openProject.project_outflow)}
                    </Typography>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                    <strong>Project Profit:</strong>{" "}
                    <Typography
                      component="span"
                      sx={{
                        color:
                          parseFloat(openProject.profit || 0) >= 0
                            ? "#4caf50"
                            : "#f44336",
                        fontFamily: "Formula Bold",
                      }}
                    >
                      {formatCurrency(openProject.profit)}
                    </Typography>
                  </Typography>
                </Grid>
                {openProject.description && (
                  <Grid item xs={12}>
                    <Typography variant="body1" sx={{ color: "white", mb: 1 }}>
                      <strong>Description:</strong>
                    </Typography>
                    <Box
                      sx={{
                        background: "rgba(255, 255, 255, 0.05)",
                        borderRadius: "8px",
                        p: 2,
                        border: "1px solid rgba(255, 255, 255, 0.1)",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ color: "rgba(255, 255, 255, 0.9)" }}
                      >
                        {openProject.description}
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            )}
          </DialogContent>
          <DialogActions
            sx={{ p: 3, borderTop: "1px solid rgba(255, 255, 255, 0.1)" }}
          >
            <Button
              onClick={() => setOpenProject(null)}
              sx={{ color: "rgba(255, 255, 255, 0.7)" }}
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: "100%" }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
}

export default ClientDashboardAnalysis;
